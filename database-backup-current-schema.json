{"backup_date": "2025-07-09T04:15:00.000Z", "description": "Current production database schema backup before migration cleanup", "tables": ["admin_users", "business_access_requests", "business_discount_codes", "business_scan_analytics", "business_visits", "businesses", "network_applications", "physical_cards", "portal_roles", "profiles", "purchases", "qr_interactions", "referrals", "schema_metadata", "todos", "user_qr_codes", "user_roles", "vip_codes", "vip_redemptions"], "key_tables_status": {"businesses": "74 active records", "profiles": "User profiles with auth integration", "purchases": "Payment tracking", "business_visits": "QR scan tracking", "qr_interactions": "QR interaction system"}, "rls_policies_confirmed": true, "auth_integration": "Working", "main_issue": "Schema cache conflicts from 27+ migrations causing PostgREST connection issues"}