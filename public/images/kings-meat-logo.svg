<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 500">
  <!-- Black background -->
  <rect width="400" height="500" fill="#000000"/>
  
  <!-- KINGS MEAT text -->
  <text x="200" y="80" font-family="Arial Black, sans-serif" font-size="42" font-weight="900" fill="#FFFFFF" text-anchor="middle" letter-spacing="2px">KINGS MEAT</text>
  
  <!-- Crown -->
  <g transform="translate(200, 120)">
    <!-- Crown base -->
    <ellipse cx="0" cy="80" rx="85" ry="12" fill="#B8860B" opacity="0.6"/>
    
    <!-- Crown body -->
    <path d="M-80 60 L-60 20 L-40 40 L-20 15 L0 35 L20 15 L40 40 L60 20 L80 60 L80 80 L-80 80 Z" 
          fill="url(#crownGradient)" stroke="#B8860B" stroke-width="2"/>
    
    <!-- Crown jewels -->
    <circle cx="-50" cy="45" r="4" fill="#FF6B35"/>
    <circle cx="-15" cy="35" r="4" fill="#FF6B35"/>
    <circle cx="15" cy="35" r="4" fill="#FF6B35"/>
    <circle cx="50" cy="45" r="4" fill="#FF6B35"/>
    <circle cx="0" cy="25" r="5" fill="#FF6B35"/>
    
    <!-- Crown highlights -->
    <path d="M-70 65 Q-50 55 -30 65" fill="none" stroke="#FFD700" stroke-width="2" opacity="0.8"/>
    <path d="M-20 65 Q0 55 20 65" fill="none" stroke="#FFD700" stroke-width="2" opacity="0.8"/>
    <path d="M30 65 Q50 55 70 65" fill="none" stroke="#FFD700" stroke-width="2" opacity="0.8"/>
  </g>
  
  <!-- AND text -->
  <text x="200" y="240" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFD700" text-anchor="middle">AND</text>
  
  <!-- BBQ SUPPLY CO. text -->
  <text x="200" y="290" font-family="Arial Black, sans-serif" font-size="36" font-weight="900" fill="#FFFFFF" text-anchor="middle" letter-spacing="1px">BBQ SUPPLY CO.</text>
  
  <!-- Located on Historic text -->
  <text x="200" y="340" font-family="Brush Script MT, cursive" font-size="24" font-style="italic" fill="#FFD700" text-anchor="middle">Located on Historic</text>
  
  <!-- Route 66 badge -->
  <g transform="translate(200, 380)">
    <!-- Badge background -->
    <path d="M-40 -20 L40 -20 Q50 -20 50 -10 L50 10 Q50 20 40 20 L-40 20 Q-50 20 -50 10 L-50 -10 Q-50 -20 -40 -20 Z" 
          fill="#FFD700" stroke="#B8860B" stroke-width="2"/>
    
    <!-- ROUTE text -->
    <text x="0" y="-5" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#000000" text-anchor="middle">ROUTE</text>
    
    <!-- 66 text -->
    <text x="0" y="12" font-family="Arial Black, sans-serif" font-size="18" font-weight="900" fill="#000000" text-anchor="middle">66</text>
  </g>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="crownGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
