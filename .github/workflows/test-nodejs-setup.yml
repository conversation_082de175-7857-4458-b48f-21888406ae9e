name: 'Test Node.js Setup'

on:
  workflow_dispatch:
  push:
    branches: [ test-nodejs ]

env:
  NODE_VERSION: '18'

jobs:
  test-nodejs:
    name: 'Test Node.js Setup'
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js (without cache)
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Verify Node.js installation
      run: |
        node --version
        npm --version
        
    - name: Check package.json
      run: |
        echo "📦 Checking package.json..."
        if [ -f "package.json" ]; then
          echo "✅ package.json found"
          cat package.json | head -20
        else
          echo "❌ package.json not found"
          exit 1
        fi
        
    - name: Check package-lock.json
      run: |
        echo "🔒 Checking package-lock.json..."
        if [ -f "package-lock.json" ]; then
          echo "✅ package-lock.json found"
          echo "Size: $(wc -l < package-lock.json) lines"
        else
          echo "❌ package-lock.json not found"
          exit 1
        fi
        
    - name: Install dependencies (clean)
      run: |
        echo "🧹 Clean install..."
        npm ci --prefer-offline --no-audit
        
    - name: Test basic commands
      run: |
        echo "🧪 Testing basic commands..."
        npm run lint --if-present || echo "Lint not available"
        npm run type-check --if-present || echo "Type check not available"
        
    - name: Test with cache
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install with cache
      run: npm ci --prefer-offline --no-audit
