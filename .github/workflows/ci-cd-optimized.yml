name: 'FUSE.VIP Optimized CI/CD Pipeline'

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # =============================================
  # CODE QUALITY & SECURITY ANALYSIS
  # =============================================
  code-quality:
    name: 'Code Quality & Security'
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Install dependencies
      run: npm ci --prefer-offline --no-audit
      
    - name: Run ESLint
      run: npm run lint
      continue-on-error: true
      
    - name: Run TypeScript check
      run: npx tsc --noEmit
      
    - name: Security audit
      run: npm audit --audit-level=high
      continue-on-error: true
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # =============================================
  # TESTING & COVERAGE
  # =============================================
  test:
    name: 'Testing & Coverage'
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Install dependencies
      run: npm ci --prefer-offline --no-audit
      
    - name: Run tests with coverage
      run: npm run test:coverage
      continue-on-error: true
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # =============================================
  # BUILD & OPTIMIZATION
  # =============================================
  build:
    name: 'Build & Optimization'
    runs-on: ubuntu-latest
    needs: [code-quality, test]
    
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
          
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          BUILDKIT_INLINE_CACHE=1
          
    - name: Run Trivy vulnerability scanner on image
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-image-results.sarif'
        
    - name: Upload Trivy image scan results
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-image-results.sarif'

  # =============================================
  # PERFORMANCE TESTING
  # =============================================
  performance:
    name: 'Performance Testing'
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Install dependencies
      run: npm ci --prefer-offline --no-audit
      
    - name: Build application
      run: npm run build
      
    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        configPath: './lighthouse.config.js'
        uploadArtifacts: true
        temporaryPublicStorage: true

  # =============================================
  # DEPLOYMENT
  # =============================================
  deploy:
    name: 'Deploy to Production'
    runs-on: ubuntu-latest
    needs: [build, performance]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Deploy to production
      run: |
        echo "🚀 Deploying to production..."
        echo "Image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}"
        # Add your deployment commands here
        
    - name: Run post-deployment health checks
      run: |
        echo "🏥 Running health checks..."
        # Add health check commands here
        
    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        channel: '#deployments'
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}

  # =============================================
  # MONITORING SETUP
  # =============================================
  monitoring:
    name: 'Setup Monitoring'
    runs-on: ubuntu-latest
    needs: deploy
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Setup monitoring alerts
      run: |
        echo "📊 Setting up monitoring alerts..."
        # Add monitoring setup commands here
        
    - name: Configure performance dashboards
      run: |
        echo "📈 Configuring performance dashboards..."
        # Add dashboard configuration commands here
