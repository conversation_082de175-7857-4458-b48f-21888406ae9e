"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { getSupabaseClient } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";

interface RedeemCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function RedeemCodeModal({ isOpen, onClose }: RedeemCodeModalProps) {
  const { user } = useAuth();
  const [vipCode, setVipCode] = useState("");
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [statusMsg, setStatusMsg] = useState("");

  // Try hosted Supabase edge function
  const callHostedEdgeFunction = async (code: string) => {
    try {
      const supabase = getSupabaseClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session?.access_token) {
        throw new Error('No valid session found');
      }

      const response = await fetch('https://haqbtbpmyadkocakqnew.supabase.co/functions/v1/redeem-vip-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          code: code.trim()
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to redeem VIP code');
      }

      return result;
    } catch (error) {
      console.error('Hosted edge function error:', error);
      throw error;
    }
  };

  // Try API endpoint as fallback when edge function fails
  const fallbackRedeemViaAPI = async (code: string) => {
    try {
      const response = await fetch('/api/dashboard/vip-redeem', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: code.trim(),
          userId: user?.id
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to redeem VIP code');
      }

      return result;
    } catch (error) {
      console.error('API fallback error:', error);
      throw error;
    }
  };

  const handleCodeRedeem = async (e) => {
    e.preventDefault();
    setIsRedeeming(true);
    setErrorMsg("");
    setStatusMsg("Redeeming code...");

    try {
      // Check if user is authenticated using auth context
      if (!user) {
        throw new Error("Please sign in first");
      }

      if (!vipCode.trim()) {
        throw new Error("Please enter a VIP code");
      }

      // Get Supabase client instance
      const supabase = getSupabaseClient();
      if (!supabase) {
        throw new Error("Database connection not available");
      }

      let redemptionResult;
      let usedHostedFunction = false;
      let usedFallback = false;

      // Primary approach: Try the hosted Supabase edge function first
      try {
        const edgeResult = await callHostedEdgeFunction(vipCode);
        redemptionResult = edgeResult;
        usedHostedFunction = true;
      } catch (edgeError) {
        console.warn('Hosted edge function failed, trying local RPC:', edgeError.message);
        
        // Secondary approach: Try the local RPC function
        try {
          const { data: rpcResult, error: rpcError } = await supabase
            .rpc('redeem_vip_code', {
              p_code: vipCode.trim(),
              p_user_id: user.id
            });

          if (rpcError) {
            console.warn('Local RPC function failed, trying fallback API:', rpcError.message);
            throw rpcError;
          }

          if (!rpcResult || !rpcResult.success) {
            throw new Error(rpcResult?.message || "Failed to redeem VIP code");
          }

          redemptionResult = rpcResult;
        } catch (rpcError) {
          // Fallback approach: Use the API endpoint
          setStatusMsg("Trying alternative redemption method...");
          
          try {
            const apiResult = await fallbackRedeemViaAPI(vipCode);
            redemptionResult = {
              success: apiResult.success,
              card_tier: apiResult.data.card_tier,
              membership_start_date: apiResult.data.membership_start_date,
              membership_end_date: apiResult.data.membership_end_date,
              redemption_id: apiResult.data.redemption_id
            };
            usedFallback = true;
          } catch (apiError) {
            // If all methods fail, show the original edge function error
            throw new Error(`Redemption failed: ${edgeError.message}`);
          }
        }
      }

      // If we used the fallback API or hosted edge function, the profile is already updated
      if (!usedFallback && !usedHostedFunction) {
        // Calculate membership dates (for local RPC function result)
        const now = new Date();
        const oneYearFromNow = new Date(now);
        oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
        
        // Update the user's profile with card tier from VIP code
        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            is_card_holder: true,
            card_tier: redemptionResult.card_tier || 'Premium',
            membership_start_date: redemptionResult.membership_start_date || now.toISOString(),
            membership_end_date: redemptionResult.membership_end_date || oneYearFromNow.toISOString(),
            updated_at: now.toISOString()
          })
          .eq('id', user.id);
        
        if (profileError) {
          console.error("Profile update error:", profileError);
          throw new Error("Failed to update membership status. Please contact support.");
        }
      }

      setVipCode("");
      setStatusMsg("Code redeemed successfully! Your VIP membership is now active. Redirecting...");
      
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 2000);

    } catch (error) {
      console.error("Error redeeming code:", error);
      setErrorMsg(error.message || "An unexpected error occurred");
      setStatusMsg("");
    } finally {
      setIsRedeeming(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Redeem VIP Code</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleCodeRedeem} className="space-y-4">
          <div>
            <Input
              placeholder="Enter your VIP code"
              value={vipCode}
              onChange={(e) => setVipCode(e.target.value)}
              disabled={isRedeeming}
              className="w-full"
            />
          </div>
          
          {errorMsg && (
            <p className="text-red-500 text-sm">{errorMsg}</p>
          )}
          
          {statusMsg && (
            <p className="text-green-500 text-sm">{statusMsg}</p>
          )}
          
          <div className="flex justify-end">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose} 
              disabled={isRedeeming}
              className="mr-2"
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!vipCode || isRedeeming}
            >
              {isRedeeming ? "Processing..." : "Redeem Code"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}