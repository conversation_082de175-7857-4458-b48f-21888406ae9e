"use client"

import { useState, useEffect } from 'react'
// Removed deprecated auth-client imports - use auth context instead
import { Button } from '@/components/ui/button'

export function AuthTest() {
  const [authStatus, setAuthStatus] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)

  const checkAuth = async () => {
    setIsLoading(true)
    try {
      // Temporarily disable auth check until auth-client is created
      // const user = await getCurrentUser()
      // const isAuth = await checkAuthStatus()
      setAuthStatus({
        user: null,
        isAuthenticated: false,
        timestamp: new Date().toISOString(),
        message: 'Auth client temporarily disabled - needs auth-client.ts file'
      })
    } catch (error) {
      setAuthStatus({
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      })
    } finally {
      setIsLoading(false)
    }
  }

  const refreshToken = async () => {
    setIsLoading(true)
    try {
      const result = await refreshAuthToken()
      setAuthStatus(prev => ({
        ...prev,
        refreshResult: result,
        timestamp: new Date().toISOString()
      }))
    } catch (error) {
      setAuthStatus(prev => ({
        ...prev,
        refreshError: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }))
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    checkAuth()
  }, [])

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Client-side JWT Auth Test</h3>
      
      <div className="space-x-2 mb-4">
        <Button onClick={checkAuth} disabled={isLoading}>
          Check Auth Status
        </Button>
        <Button onClick={refreshToken} disabled={isLoading}>
          Refresh Token
        </Button>
      </div>

      {isLoading && (
        <div className="text-blue-600 mb-2">Loading...</div>
      )}

      {authStatus && (
        <div className="bg-gray-100 p-4 rounded">
          <h4 className="font-medium mb-2">Auth Status:</h4>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(authStatus, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
