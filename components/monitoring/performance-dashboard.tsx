'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Activity, 
  Database, 
  Server, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  TrendingUp,
  TrendingDown,
  RefreshCw
} from 'lucide-react'

interface MonitoringData {
  timestamp: string
  timeRange: string
  summary: {
    status: 'healthy' | 'warning' | 'critical'
    alerts: string[]
    activeBusinesses: number
    activeUsers: number
  }
  performance: {
    api?: {
      totalRequests: number
      avgResponseTime: number
      errorRate: string
      slowRequests: number
    }
    database?: {
      totalQueries: number
      avgQueryTime: number
      slowQueries: number
    }
    frontend?: {
      totalRenders: number
      avgRenderTime: number
      slowRenders: number
    }
    topEndpoints?: Array<{ endpoint: string; count: number }>
  }
  database: {
    connectionHealth: string
    avgResponseTime: number
    totalQueries: number
    slowQueries: number
    queryPerformance?: Array<{
      query_name: string
      total_queries: number
      avg_time: number
      p95_time: number
      slow_queries: number
    }>
  }
  system: {
    uptime: number
    memory: {
      rss: number
      heapTotal: number
      heapUsed: number
      external: number
    }
    nodeVersion: string
    platform: string
  }
  errors: {
    totalErrors: number
    errorRate: string
    errorsByStatus: Record<number, number>
  }
}

export function PerformanceDashboard() {
  const [data, setData] = useState<MonitoringData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('1h')
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/monitoring/dashboard?range=${timeRange}`)
      const result = await response.json()
      
      if (result.success) {
        setData(result.data)
        setLastUpdated(new Date())
      }
    } catch (error) {
      console.error('Failed to fetch monitoring data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [timeRange])

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchData, 30000) // Refresh every 30 seconds
      return () => clearInterval(interval)
    }
  }, [autoRefresh, timeRange])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-500'
      case 'warning': return 'text-yellow-500'
      case 'critical': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'critical': return <AlertTriangle className="h-5 w-5 text-red-500" />
      default: return <Activity className="h-5 w-5 text-gray-500" />
    }
  }

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${days}d ${hours}h ${minutes}m`
  }

  if (loading && !data) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-lg">Loading monitoring data...</span>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center p-8">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Failed to load monitoring data</h3>
        <Button onClick={fetchData}>Retry</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Performance Dashboard</h1>
          <p className="text-gray-600">
            Last updated: {lastUpdated?.toLocaleTimeString() || 'Never'}
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border rounded-lg"
          >
            <option value="1h">Last Hour</option>
            <option value="6h">Last 6 Hours</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
          </select>
          
          <Button
            variant={autoRefresh ? "default" : "outline"}
            onClick={() => setAutoRefresh(!autoRefresh)}
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh
          </Button>
          
          <Button onClick={fetchData} size="sm" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            {getStatusIcon(data.summary.status)}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(data.summary.status)}`}>
              {data.summary.status.toUpperCase()}
            </div>
            {data.summary.alerts.length > 0 && (
              <div className="mt-2">
                {data.summary.alerts.map((alert, index) => (
                  <Badge key={index} variant="destructive" className="text-xs mb-1 block">
                    {alert}
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Businesses</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.activeBusinesses}</div>
            <p className="text-xs text-gray-600">Registered & active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.summary.activeUsers}</div>
            <p className="text-xs text-gray-600">Last 7 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Uptime</CardTitle>
            <Server className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatUptime(data.system.uptime)}</div>
            <p className="text-xs text-gray-600">System uptime</p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* API Performance */}
        {data.performance.api && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                API Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>Total Requests:</span>
                <span className="font-semibold">{data.performance.api.totalRequests}</span>
              </div>
              <div className="flex justify-between">
                <span>Avg Response Time:</span>
                <span className={`font-semibold ${data.performance.api.avgResponseTime > 2000 ? 'text-red-500' : 'text-green-500'}`}>
                  {data.performance.api.avgResponseTime}ms
                </span>
              </div>
              <div className="flex justify-between">
                <span>Error Rate:</span>
                <span className={`font-semibold ${parseFloat(data.performance.api.errorRate) > 5 ? 'text-red-500' : 'text-green-500'}`}>
                  {data.performance.api.errorRate}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>Slow Requests:</span>
                <span className={`font-semibold ${data.performance.api.slowRequests > 10 ? 'text-red-500' : 'text-green-500'}`}>
                  {data.performance.api.slowRequests}
                </span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Database Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Database Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>Connection:</span>
              <Badge variant={data.database.connectionHealth === 'healthy' ? 'default' : 'destructive'}>
                {data.database.connectionHealth}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Total Queries:</span>
              <span className="font-semibold">{data.database.totalQueries}</span>
            </div>
            <div className="flex justify-between">
              <span>Avg Query Time:</span>
              <span className={`font-semibold ${data.database.avgResponseTime > 300 ? 'text-red-500' : 'text-green-500'}`}>
                {data.database.avgResponseTime}ms
              </span>
            </div>
            <div className="flex justify-between">
              <span>Slow Queries:</span>
              <span className={`font-semibold ${data.database.slowQueries > 10 ? 'text-red-500' : 'text-green-500'}`}>
                {data.database.slowQueries}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* System Resources */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Server className="h-5 w-5 mr-2" />
              System Resources
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>Memory (RSS):</span>
              <span className="font-semibold">{data.system.memory.rss}MB</span>
            </div>
            <div className="flex justify-between">
              <span>Heap Used:</span>
              <span className="font-semibold">{data.system.memory.heapUsed}MB</span>
            </div>
            <div className="flex justify-between">
              <span>Heap Total:</span>
              <span className="font-semibold">{data.system.memory.heapTotal}MB</span>
            </div>
            <div className="flex justify-between">
              <span>Node Version:</span>
              <span className="font-semibold">{data.system.nodeVersion}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Endpoints */}
      {data.performance.topEndpoints && data.performance.topEndpoints.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Top API Endpoints</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {data.performance.topEndpoints.slice(0, 5).map((endpoint, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm font-mono">{endpoint.endpoint}</span>
                  <Badge variant="outline">{endpoint.count} requests</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
