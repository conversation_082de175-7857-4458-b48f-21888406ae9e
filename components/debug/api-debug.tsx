'use client'

import { useState, useEffect } from 'react'

interface ApiTestResult {
  endpoint: string
  status: 'loading' | 'success' | 'error'
  data?: any
  error?: string
  duration?: number
}

export function ApiDebug() {
  const [results, setResults] = useState<ApiTestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const apiEndpoints = [
    '/api/businesses',
    '/api/test-connection',
    '/api/verify-database',
    '/api/test-direct-db',
    '/api/referring-businesses'
  ]

  const testEndpoint = async (endpoint: string): Promise<ApiTestResult> => {
    const startTime = Date.now()
    
    try {
      const response = await fetch(endpoint)
      const duration = Date.now() - startTime
      
      if (!response.ok) {
        return {
          endpoint,
          status: 'error',
          error: `HTTP ${response.status}: ${response.statusText}`,
          duration
        }
      }
      
      const data = await response.json()
      
      return {
        endpoint,
        status: 'success',
        data,
        duration
      }
    } catch (error) {
      const duration = Date.now() - startTime
      return {
        endpoint,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration
      }
    }
  }

  const runAllTests = async () => {
    setIsRunning(true)
    setResults([])
    
    // Initialize with loading states
    const initialResults = apiEndpoints.map(endpoint => ({
      endpoint,
      status: 'loading' as const
    }))
    setResults(initialResults)
    
    // Test each endpoint
    for (let i = 0; i < apiEndpoints.length; i++) {
      const endpoint = apiEndpoints[i]
      const result = await testEndpoint(endpoint)
      
      setResults(prev => prev.map((r, index) => 
        index === i ? result : r
      ))
    }
    
    setIsRunning(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'loading': return 'text-yellow-400'
      case 'success': return 'text-green-400'
      case 'error': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'loading': return '⏳'
      case 'success': return '✅'
      case 'error': return '❌'
      default: return '⚪'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">API Debug Dashboard</h2>
        <button
          onClick={runAllTests}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {isRunning ? 'Running Tests...' : 'Run All Tests'}
        </button>
      </div>

      <div className="grid gap-4">
        {results.map((result, index) => (
          <div key={result.endpoint} className="bg-gray-800 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getStatusIcon(result.status)}</span>
                <span className="font-mono text-sm text-gray-300">{result.endpoint}</span>
              </div>
              <div className="flex items-center space-x-2">
                {result.duration && (
                  <span className="text-xs text-gray-400">{result.duration}ms</span>
                )}
                <span className={`text-sm font-semibold ${getStatusColor(result.status)}`}>
                  {result.status.toUpperCase()}
                </span>
              </div>
            </div>

            {result.error && (
              <div className="mt-2 p-2 bg-red-900/20 border border-red-500/30 rounded">
                <p className="text-red-400 text-sm font-mono">{result.error}</p>
              </div>
            )}

            {result.data && (
              <details className="mt-2">
                <summary className="cursor-pointer text-sm text-gray-400 hover:text-gray-300">
                  View Response Data
                </summary>
                <pre className="mt-2 p-2 bg-gray-900 rounded text-xs text-gray-300 overflow-auto max-h-40">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </details>
            )}
          </div>
        ))}
      </div>

      {results.length === 0 && !isRunning && (
        <div className="text-center py-8">
          <p className="text-gray-400">Click "Run All Tests" to start API debugging</p>
        </div>
      )}
    </div>
  )
}
