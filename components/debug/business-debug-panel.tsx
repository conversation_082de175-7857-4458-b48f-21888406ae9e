"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/auth-context';
import { useDashboardData } from '@/components/containers/dashboard-data-container';

interface DebugInfo {
  authUser: any;
  businessApiResponse: any;
  businessAccessResponse: any;
  dashboardDataState: any;
  timestamp: string;
}

export function BusinessDebugPanel() {
  const { user, profile, isBusinessOwner } = useAuth();
  const { userBusiness, hasUserBusiness, isLoading, error } = useDashboardData();
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [isDebugging, setIsDebugging] = useState(false);

  const runDiagnostics = async () => {
    setIsDebugging(true);
    console.log('🔍 Running business assignment diagnostics...');

    try {
      // Test business API
      const businessResponse = await fetch('/api/dashboard/business', {
        credentials: 'include',
        headers: { 'Cache-Control': 'no-cache' },
      });
      const businessResult = await businessResponse.json();

      // Test business access API
      const accessResponse = await fetch('/api/business-access-optimized', {
        credentials: 'include'
      });
      const accessResult = await accessResponse.json();

      const debugData: DebugInfo = {
        authUser: {
          id: user?.id,
          email: user?.email,
          authenticated: !!user,
        },
        businessApiResponse: {
          status: businessResponse.status,
          ok: businessResponse.ok,
          data: businessResult,
        },
        businessAccessResponse: {
          status: accessResponse.status,
          ok: accessResponse.ok,
          data: accessResult,
        },
        dashboardDataState: {
          userBusiness: userBusiness,
          hasUserBusiness: hasUserBusiness,
          isLoading: isLoading,
          error: error,
          isBusinessOwner: isBusinessOwner,
        },
        timestamp: new Date().toISOString(),
      };

      setDebugInfo(debugData);
      console.log('🔍 Debug info:', debugData);

    } catch (error) {
      console.error('❌ Diagnostics failed:', error);
      setDebugInfo({
        authUser: { error: 'Failed to get auth user' },
        businessApiResponse: { error: error.message },
        businessAccessResponse: { error: error.message },
        dashboardDataState: { error: error.message },
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsDebugging(false);
    }
  };

  const clearCache = () => {
    console.log('🧹 Clearing all cache...');

    // Clear profile cache service
    if (user?.id) {
      try {
        const { profileCache } = require('@/lib/profile-cache');
        profileCache.clearUserCache(user.id);
        console.log('✅ Profile cache cleared');
      } catch (error) {
        console.warn('Failed to clear profile cache:', error);
      }
    }

    // Clear all localStorage
    localStorage.clear();

    // Clear session storage
    sessionStorage.clear();

    console.log('✅ All cache cleared, please refresh the page to see updated status');

    // Force a page reload to refresh all components
    window.location.reload();
  };

  return (
    <Card className="w-full max-w-4xl mx-auto mt-4 border-yellow-200 bg-yellow-50">
      <CardHeader>
        <CardTitle className="text-yellow-800">🔧 Business Assignment Debug Panel</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button
            onClick={runDiagnostics}
            disabled={isDebugging}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isDebugging ? 'Running Diagnostics...' : '🔍 Run Diagnostics'}
          </Button>
          <Button
            onClick={clearCache}
            variant="outline"
          >
            🧹 Clear Cache
          </Button>
          <Button
            onClick={() => {
              if (user?.id) {
                // Force refresh business owner status
                fetch('/api/business-access-optimized', {
                  credentials: 'include',
                  headers: { 'Cache-Control': 'no-cache' }
                }).then(response => response.json())
                .then(result => {
                  console.log('🔄 Business access check result:', result);
                  if (result.success) {
                    console.log('✅ Has business access:', result.data.has_business_access);
                    console.log('✅ Is admin:', result.data.is_admin);
                  }
                });
              }
            }}
            variant="outline"
            className="bg-green-50 hover:bg-green-100"
          >
            🔄 Refresh Status
          </Button>
        </div>

        {/* Current State Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="bg-white p-3 rounded border">
            <div className="font-semibold text-gray-700">Auth State</div>
            <div>User ID: {user?.id || 'None'}</div>
            <div>Email: {user?.email || 'None'}</div>
            <div>Is Business Owner: {isBusinessOwner ? '✅' : '❌'}</div>
          </div>
          <div className="bg-white p-3 rounded border">
            <div className="font-semibold text-gray-700">Dashboard Data</div>
            <div>Has Business: {hasUserBusiness ? '✅' : '❌'}</div>
            <div>Business Name: {userBusiness?.name || 'None'}</div>
            <div>Loading: {isLoading ? '⏳' : '✅'}</div>
            <div>Error: {error || 'None'}</div>
          </div>
          <div className="bg-white p-3 rounded border">
            <div className="font-semibold text-gray-700">Profile</div>
            <div>Profile ID: {profile?.id || 'None'}</div>
            <div>Is Applicant: {profile?.is_business_applicant ? '✅' : '❌'}</div>
            <div>Card Holder: {profile?.is_card_holder ? '✅' : '❌'}</div>
          </div>
        </div>

        {/* Debug Results */}
        {debugInfo && (
          <div className="bg-white p-4 rounded border">
            <h3 className="font-semibold mb-2">🔍 Diagnostic Results ({debugInfo.timestamp})</h3>
            <div className="space-y-3 text-sm">
              
              {/* Business API Results */}
              <div>
                <div className="font-medium text-blue-700">Business API (/api/dashboard/business)</div>
                <div className="ml-4 space-y-1">
                  <div>Status: {debugInfo.businessApiResponse.status} {debugInfo.businessApiResponse.ok ? '✅' : '❌'}</div>
                  {debugInfo.businessApiResponse.data?.business ? (
                    <div className="text-green-700">
                      ✅ Business Found: {debugInfo.businessApiResponse.data.business.name}
                      <br />Business ID: {debugInfo.businessApiResponse.data.business.id}
                      <br />User ID: {debugInfo.businessApiResponse.data.business.user_id}
                    </div>
                  ) : (
                    <div className="text-orange-600">
                      ⚠️ No business found: {debugInfo.businessApiResponse.data?.message || 'Unknown'}
                    </div>
                  )}
                  {debugInfo.businessApiResponse.data?.error && (
                    <div className="text-red-600">❌ Error: {debugInfo.businessApiResponse.data.error}</div>
                  )}
                </div>
              </div>

              {/* Business Access API Results */}
              <div>
                <div className="font-medium text-purple-700">Business Access API (/api/business-access-optimized)</div>
                <div className="ml-4 space-y-1">
                  <div>Status: {debugInfo.businessAccessResponse.status} {debugInfo.businessAccessResponse.ok ? '✅' : '❌'}</div>
                  {debugInfo.businessAccessResponse.data?.data ? (
                    <div className="text-green-700">
                      ✅ Access Check: Has Business Access = {debugInfo.businessAccessResponse.data.data.has_business_access ? '✅' : '❌'}
                      <br />Is Admin: {debugInfo.businessAccessResponse.data.data.is_admin ? '✅' : '❌'}
                    </div>
                  ) : (
                    <div className="text-red-600">❌ Error: {debugInfo.businessAccessResponse.data?.error || 'Unknown'}</div>
                  )}
                </div>
              </div>

              {/* Dashboard State */}
              <div>
                <div className="font-medium text-green-700">Dashboard Data State</div>
                <div className="ml-4 space-y-1">
                  <div>Has User Business: {debugInfo.dashboardDataState.hasUserBusiness ? '✅' : '❌'}</div>
                  <div>User Business: {debugInfo.dashboardDataState.userBusiness?.name || 'None'}</div>
                  <div>Is Loading: {debugInfo.dashboardDataState.isLoading ? '⏳' : '✅'}</div>
                  <div>Error: {debugInfo.dashboardDataState.error || 'None'}</div>
                  <div>Is Business Owner (Auth): {debugInfo.dashboardDataState.isBusinessOwner ? '✅' : '❌'}</div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="text-xs text-gray-600">
          💡 This panel helps diagnose business assignment issues. Run diagnostics to see detailed API responses and state.
        </div>
      </CardContent>
    </Card>
  );
}
