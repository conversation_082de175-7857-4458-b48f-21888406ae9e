"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { getSupabaseClient } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";

interface RedeemCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function RedeemCodeModal({ isOpen, onClose }: RedeemCodeModalProps) {
  const { user } = useAuth();
  const [vipCode, setVipCode] = useState("");
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [statusMsg, setStatusMsg] = useState("");

  const handleCodeRedeem = async (e) => {
    e.preventDefault();
    setIsRedeeming(true);
    setErrorMsg("");
    setStatusMsg("Redeeming code...");

    try {
      // Check if user is authenticated using auth context
      if (!user) {
        throw new Error("Please sign in first");
      }

      if (!vipCode.trim()) {
        throw new Error("Please enter a VIP code");
      }

      // Get Supabase client instance
      const supabase = getSupabaseClient();
      if (!supabase) {
        throw new Error("Database connection not available");
      }

      // Use the atomic RPC function for redemption
      const { data: redemptionResult, error: rpcError } = await supabase
        .rpc('redeem_vip_code', {
          p_code: vipCode.trim(),
          p_user_id: user.id
        });

      if (rpcError) {
        throw new Error(`Redemption failed: ${rpcError.message}`);
      }

      if (!redemptionResult.success) {
        throw new Error(redemptionResult.error || "Failed to redeem VIP code");
      }

      // Calculate membership dates
      const now = new Date();
      const oneYearFromNow = new Date(now);
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      
      // Update the user's profile with card tier from VIP code
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_card_holder: true,
          card_tier: redemptionResult.card_tier || 'Premium',
          membership_start_date: now.toISOString(),
          membership_end_date: oneYearFromNow.toISOString(),
          updated_at: now.toISOString()
        })
        .eq('id', user.id);
      
      if (profileError) {
        console.error("Profile update error:", profileError);
        throw new Error("Failed to update membership status. Please contact support.");
      }

      setVipCode("");
      setStatusMsg("Code redeemed successfully! Your VIP membership is now active. Redirecting...");
      
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 2000);

    } catch (error) {
      console.error("Error redeeming code:", error);
      setErrorMsg(error.message || "An unexpected error occurred");
      setStatusMsg("");
    } finally {
      setIsRedeeming(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Redeem VIP Code</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleCodeRedeem} className="space-y-4">
          <div>
            <Input
              placeholder="Enter your VIP code"
              value={vipCode}
              onChange={(e) => setVipCode(e.target.value)}
              disabled={isRedeeming}
              className="w-full"
            />
          </div>
          
          {errorMsg && (
            <p className="text-red-500 text-sm">{errorMsg}</p>
          )}
          
          {statusMsg && (
            <p className="text-green-500 text-sm">{statusMsg}</p>
          )}
          
          <div className="flex justify-end">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose} 
              disabled={isRedeeming}
              className="mr-2"
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!vipCode || isRedeeming}
            >
              {isRedeeming ? "Processing..." : "Redeem Code"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
