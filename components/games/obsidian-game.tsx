"use client"

import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface GameStats {
  score: number
  level: number
  gemsCollected: number
  timeLeft: number
  lives: number
}

interface Gem {
  id: number
  x: number
  y: number
  type: 'diamond' | 'ruby' | 'emerald' | 'sapphire'
  value: number
}

interface Obstacle {
  id: number
  x: number
  y: number
  width: number
  height: number
}

const GEM_TYPES = {
  diamond: { emoji: '💎', value: 50, rarity: 0.1 },
  ruby: { emoji: '💍', value: 30, rarity: 0.2 },
  emerald: { emoji: '🟢', value: 20, rarity: 0.3 },
  sapphire: { emoji: '🔵', value: 10, rarity: 0.4 }
}

interface ObsidianGameProps {
  isOpen: boolean
  onClose: () => void
}

export function ObsidianGame({ isOpen, onClose }: ObsidianGameProps) {
  const [gameState, setGameState] = useState<'menu' | 'playing' | 'paused' | 'gameOver'>('menu')
  const [stats, setStats] = useState<GameStats>({
    score: 0,
    level: 1,
    gemsCollected: 0,
    timeLeft: 60,
    lives: 3
  })
  const [gems, setGems] = useState<Gem[]>([])
  const [obstacles, setObstacles] = useState<Obstacle[]>([])
  const [playerPosition, setPlayerPosition] = useState({ x: 50, y: 80 })
  const [gameArea, setGameArea] = useState({ width: 400, height: 300 })

  // Game initialization
  const initializeGame = useCallback(() => {
    setStats({
      score: 0,
      level: 1,
      gemsCollected: 0,
      timeLeft: 60,
      lives: 3
    })
    setPlayerPosition({ x: 50, y: 80 })
    setGems([])
    setObstacles([])
    generateInitialGems()
  }, [])

  // Generate gems on the game field
  const generateInitialGems = () => {
    const newGems: Gem[] = []
    for (let i = 0; i < 8 + Math.floor(Math.random() * 4); i++) {
      const random = Math.random()
      let gemType: keyof typeof GEM_TYPES = 'sapphire'
      
      if (random < 0.1) gemType = 'diamond'
      else if (random < 0.3) gemType = 'ruby'
      else if (random < 0.6) gemType = 'emerald'
      
      newGems.push({
        id: i,
        x: Math.random() * 85 + 5, // 5% to 90% of width
        y: Math.random() * 60 + 5, // 5% to 65% of height
        type: gemType,
        value: GEM_TYPES[gemType].value
      })
    }
    setGems(newGems)
  }

  // Generate obstacles
  const generateObstacles = () => {
    const newObstacles: Obstacle[] = []
    const numObstacles = Math.floor(stats.level / 2) + 1
    
    for (let i = 0; i < numObstacles; i++) {
      newObstacles.push({
        id: i,
        x: Math.random() * 80 + 10,
        y: Math.random() * 50 + 10,
        width: 8 + Math.random() * 4,
        height: 8 + Math.random() * 4
      })
    }
    setObstacles(newObstacles)
  }

  // Start game
  const startGame = () => {
    setGameState('playing')
    initializeGame()
    generateObstacles()
  }

  // Pause/Resume game
  const togglePause = () => {
    setGameState(gameState === 'playing' ? 'paused' : 'playing')
  }

  // Restart game
  const restartGame = () => {
    initializeGame()
    setGameState('playing')
    generateObstacles()
  }

  // Handle gem collection
  const collectGem = (gemId: number) => {
    const gem = gems.find(g => g.id === gemId)
    if (gem) {
      setStats(prev => ({
        ...prev,
        score: prev.score + gem.value,
        gemsCollected: prev.gemsCollected + 1
      }))
      setGems(prev => prev.filter(g => g.id !== gemId))
      
      // Check if all gems collected
      if (gems.length === 1) {
        nextLevel()
      }
    }
  }

  // Advance to next level
  const nextLevel = () => {
    setStats(prev => ({
      ...prev,
      level: prev.level + 1,
      timeLeft: Math.min(prev.timeLeft + 20, 99),
      score: prev.score + prev.level * 100 // Level bonus
    }))
    generateInitialGems()
    generateObstacles()
  }

  // Check collision with obstacles
  const checkCollisions = () => {
    const playerSize = 6
    const collision = obstacles.some(obstacle => {
      return playerPosition.x < obstacle.x + obstacle.width &&
             playerPosition.x + playerSize > obstacle.x &&
             playerPosition.y < obstacle.y + obstacle.height &&
             playerPosition.y + playerSize > obstacle.y
    })
    
    if (collision) {
      setStats(prev => {
        const newLives = prev.lives - 1
        if (newLives <= 0) {
          setGameState('gameOver')
        }
        return { ...prev, lives: newLives }
      })
      // Reset player position
      setPlayerPosition({ x: 50, y: 80 })
    }
  }

  // Handle player movement
  const movePlayer = (direction: 'up' | 'down' | 'left' | 'right') => {
    if (gameState !== 'playing') return
    
    setPlayerPosition(prev => {
      let newX = prev.x
      let newY = prev.y
      const speed = 4
      
      switch (direction) {
        case 'up':
          newY = Math.max(0, prev.y - speed)
          break
        case 'down':
          newY = Math.min(95, prev.y + speed)
          break
        case 'left':
          newX = Math.max(0, prev.x - speed)
          break
        case 'right':
          newX = Math.min(95, prev.x + speed)
          break
      }
      
      return { x: newX, y: newY }
    })
  }

  // Game timer
  useEffect(() => {
    if (gameState === 'playing' && stats.timeLeft > 0) {
      const timer = setTimeout(() => {
        setStats(prev => {
          const newTime = prev.timeLeft - 1
          if (newTime <= 0) {
            setGameState('gameOver')
          }
          return { ...prev, timeLeft: newTime }
        })
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [gameState, stats.timeLeft])

  // Check for gem collection
  useEffect(() => {
    if (gameState === 'playing') {
      const playerSize = 6
      gems.forEach(gem => {
        const distance = Math.sqrt(
          Math.pow(playerPosition.x - gem.x, 2) + Math.pow(playerPosition.y - gem.y, 2)
        )
        if (distance < playerSize) {
          collectGem(gem.id)
        }
      })
      
      // Check collisions
      checkCollisions()
    }
  }, [playerPosition, gameState, gems])

  // Keyboard controls
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowUp':
        case 'w':
        case 'W':
          event.preventDefault()
          movePlayer('up')
          break
        case 'ArrowDown':
        case 's':
        case 'S':
          event.preventDefault()
          movePlayer('down')
          break
        case 'ArrowLeft':
        case 'a':
        case 'A':
          event.preventDefault()
          movePlayer('left')
          break
        case 'ArrowRight':
        case 'd':
        case 'D':
          event.preventDefault()
          movePlayer('right')
          break
        case ' ':
          event.preventDefault()
          if (gameState === 'playing' || gameState === 'paused') {
            togglePause()
          }
          break
      }
    }

    if (isOpen) {
      window.addEventListener('keydown', handleKeyPress)
    }
    
    return () => {
      window.removeEventListener('keydown', handleKeyPress)
    }
  }, [isOpen, gameState, movePlayer, togglePause])

  const getScoreColor = (score: number) => {
    if (score < 500) return 'text-blue-400'
    if (score < 1000) return 'text-green-400'
    if (score < 2000) return 'text-yellow-400'
    if (score < 5000) return 'text-orange-400'
    return 'text-purple-400'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl bg-gradient-to-b from-slate-900 to-black border-purple-500/50">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
            ⚫ Obsidian Gem Collector ⚫
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Game Stats */}
          <div className="flex justify-between items-center text-sm">
            <div className="flex gap-4">
              <Badge variant="outline" className="bg-purple-900/50 border-purple-400">
                Score: <span className={`ml-1 font-bold ${getScoreColor(stats.score)}`}>{stats.score}</span>
              </Badge>
              <Badge variant="outline" className="bg-blue-900/50 border-blue-400">
                Level: <span className="ml-1 font-bold text-blue-300">{stats.level}</span>
              </Badge>
              <Badge variant="outline" className="bg-green-900/50 border-green-400">
                Gems: <span className="ml-1 font-bold text-green-300">{stats.gemsCollected}</span>
              </Badge>
            </div>
            <div className="flex gap-2">
              <Badge variant="outline" className="bg-red-900/50 border-red-400">
                Lives: <span className="ml-1 font-bold text-red-300">{'❤️'.repeat(stats.lives)}</span>
              </Badge>
              <Badge variant="outline" className="bg-yellow-900/50 border-yellow-400">
                Time: <span className="ml-1 font-bold text-yellow-300">{stats.timeLeft}s</span>
              </Badge>
            </div>
          </div>

          {/* Game Area */}
          <Card className="bg-gradient-to-b from-slate-800 to-slate-900 border-purple-500/30">
            <CardContent className="p-4">
              <div 
                className="relative bg-gradient-to-b from-indigo-950 to-black border-2 border-purple-400/50 rounded-lg overflow-hidden"
                style={{ width: '100%', height: '300px' }}
                onMouseMove={(e) => {
                  if (gameState === 'playing') {
                    const rect = e.currentTarget.getBoundingClientRect()
                    const x = ((e.clientX - rect.left) / rect.width) * 100
                    const y = ((e.clientY - rect.top) / rect.height) * 100
                    setPlayerPosition({ x: Math.max(0, Math.min(95, x)), y: Math.max(0, Math.min(95, y)) })
                  }
                }}
              >
                {/* Background pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-indigo-900/20"></div>
                
                {/* Gems */}
                {gems.map(gem => (
                  <div
                    key={gem.id}
                    className="absolute text-2xl cursor-pointer hover:scale-110 transition-transform"
                    style={{
                      left: `${gem.x}%`,
                      top: `${gem.y}%`,
                      transform: 'translate(-50%, -50%)'
                    }}
                  >
                    {GEM_TYPES[gem.type].emoji}
                  </div>
                ))}

                {/* Obstacles */}
                {obstacles.map(obstacle => (
                  <div
                    key={obstacle.id}
                    className="absolute bg-red-600/70 border border-red-400"
                    style={{
                      left: `${obstacle.x}%`,
                      top: `${obstacle.y}%`,
                      width: `${obstacle.width}%`,
                      height: `${obstacle.height}%`
                    }}
                  ></div>
                ))}

                {/* Player */}
                <div
                  className="absolute text-xl transition-all duration-75 z-10"
                  style={{
                    left: `${playerPosition.x}%`,
                    top: `${playerPosition.y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  ⚫
                </div>

                {/* Game State Overlays */}
                {gameState === 'menu' && (
                  <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
                    <div className="text-center space-y-4">
                      <h3 className="text-xl font-bold text-white">Obsidian Gem Collector</h3>
                      <p className="text-gray-300 text-sm max-w-xs">
                        Collect all gems while avoiding obstacles. Use arrow keys or WASD to move, or hover your mouse!
                      </p>
                      <Button onClick={startGame} className="bg-purple-600 hover:bg-purple-700">
                        Start Game
                      </Button>
                    </div>
                  </div>
                )}

                {gameState === 'paused' && (
                  <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
                    <div className="text-center space-y-4">
                      <h3 className="text-xl font-bold text-white">Game Paused</h3>
                      <Button onClick={togglePause} className="bg-green-600 hover:bg-green-700">
                        Resume
                      </Button>
                    </div>
                  </div>
                )}

                {gameState === 'gameOver' && (
                  <div className="absolute inset-0 bg-black/90 flex items-center justify-center">
                    <div className="text-center space-y-4">
                      <h3 className="text-xl font-bold text-white">Game Over!</h3>
                      <p className="text-gray-300">Final Score: <span className={`font-bold ${getScoreColor(stats.score)}`}>{stats.score}</span></p>
                      <p className="text-gray-300">Level Reached: <span className="font-bold text-blue-300">{stats.level}</span></p>
                      <div className="flex gap-2 justify-center">
                        <Button onClick={restartGame} className="bg-purple-600 hover:bg-purple-700">
                          Play Again
                        </Button>
                        <Button onClick={onClose} variant="outline">
                          Close
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Controls Info */}
              <div className="mt-3 text-xs text-gray-400 text-center">
                <p>Use Arrow Keys, WASD, or Mouse to move • Spacebar to pause • Collect all gems to advance!</p>
              </div>
            </CardContent>
          </Card>

          {/* Game Controls */}
          <div className="flex justify-center gap-2">
            {gameState === 'menu' && (
              <Button onClick={startGame} className="bg-purple-600 hover:bg-purple-700">
                Start Game
              </Button>
            )}
            {(gameState === 'playing' || gameState === 'paused') && (
              <>
                <Button onClick={togglePause} variant="outline" size="sm">
                  {gameState === 'paused' ? 'Resume' : 'Pause'}
                </Button>
                <Button onClick={restartGame} variant="outline" size="sm">
                  Restart
                </Button>
              </>
            )}
            <Button onClick={onClose} variant="outline" size="sm">
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}