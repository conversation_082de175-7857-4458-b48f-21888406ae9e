"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { performanceMonitor } from '@/lib/performance-monitor'
import { 
  Clock, 
  Database, 
  Globe, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  RefreshCw,
  BarChart3
} from 'lucide-react'

interface PerformanceStats {
  totalMetrics: number
  slowOperations: number
  averageApiResponseTime: number
  cacheHitRate: number
  recentErrors: number
}

export function PerformanceDashboard() {
  const [stats, setStats] = useState<PerformanceStats | null>(null)
  const [cacheStats, setCacheStats] = useState<any>(null)
  const [slowOps, setSlowOps] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const refreshStats = () => {
    setIsLoading(true)
    
    try {
      // Get performance summary
      const summary = performanceMonitor.getSummary()
      setStats(summary)
      
      // Cache system removed - no cache stats available
      setCacheStats({ totalEntries: 0, validEntries: 0, expiredEntries: 0, maxSize: 0 })
      
      // Get slow operations
      const slowOperations = performanceMonitor.getSlowOperations(500) // 500ms threshold
      setSlowOps(slowOperations.slice(-10)) // Last 10 slow operations
      
    } catch (error) {
      console.error('Failed to refresh performance stats:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    refreshStats()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(refreshStats, 30000)
    return () => clearInterval(interval)
  }, [])

  if (isLoading && !stats) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600'
    if (value <= thresholds.warning) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getStatusBadge = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return <Badge variant="default" className="bg-green-100 text-green-800">Good</Badge>
    if (value <= thresholds.warning) return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>
    return <Badge variant="destructive">Critical</Badge>
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Performance Dashboard</h2>
        <Button 
          onClick={refreshStats} 
          disabled={isLoading}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.averageApiResponseTime.toFixed(0)}ms
            </div>
            <div className="flex items-center space-x-2 mt-2">
              {getStatusBadge(stats?.averageApiResponseTime || 0, { good: 200, warning: 500 })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.cacheHitRate.toFixed(1)}%
            </div>
            <div className="flex items-center space-x-2 mt-2">
              {getStatusBadge(100 - (stats?.cacheHitRate || 0), { good: 20, warning: 50 })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Slow Operations</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.slowOperations || 0}
            </div>
            <div className="flex items-center space-x-2 mt-2">
              {getStatusBadge(stats?.slowOperations || 0, { good: 0, warning: 5 })}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Errors</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.recentErrors || 0}
            </div>
            <div className="flex items-center space-x-2 mt-2">
              {getStatusBadge(stats?.recentErrors || 0, { good: 0, warning: 3 })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cache Statistics */}
      {cacheStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Cache Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">Total Entries</div>
                <div className="text-lg font-semibold">{cacheStats.totalEntries}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Valid Entries</div>
                <div className="text-lg font-semibold text-green-600">{cacheStats.validEntries}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Expired Entries</div>
                <div className="text-lg font-semibold text-red-600">{cacheStats.expiredEntries}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Cache Usage</div>
                <div className="text-lg font-semibold">
                  {((cacheStats.totalEntries / cacheStats.maxSize) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Slow Operations */}
      {slowOps.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Recent Slow Operations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {slowOps.map((op, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{op.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {op.endpoint || op.component || op.table || 'Unknown'}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-red-600">{op.duration.toFixed(0)}ms</div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(op.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Performance Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {stats?.averageApiResponseTime && stats.averageApiResponseTime > 500 && (
              <div className="flex items-center text-yellow-600">
                <AlertTriangle className="h-4 w-4 mr-2" />
                API response times are high. Consider optimizing database queries or adding more caching.
              </div>
            )}
            {stats?.cacheHitRate && stats.cacheHitRate < 70 && (
              <div className="flex items-center text-yellow-600">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Cache hit rate is low. Review caching strategy for frequently accessed data.
              </div>
            )}
            {stats?.slowOperations && stats.slowOperations > 5 && (
              <div className="flex items-center text-red-600">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Multiple slow operations detected. Check database indexes and query optimization.
              </div>
            )}
            {(!stats?.recentErrors || stats.recentErrors === 0) && 
             stats?.averageApiResponseTime && stats.averageApiResponseTime < 300 && 
             stats?.cacheHitRate && stats.cacheHitRate > 80 && (
              <div className="flex items-center text-green-600">
                <CheckCircle className="h-4 w-4 mr-2" />
                System performance is optimal!
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
