"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>, <PERSON>, Clock, Building2, User, Settings } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getSupabaseClient } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";
import { formatDistanceToNow } from "date-fns";
import { useRouter } from "next/navigation";

interface Notification {
  id: string;
  type: 'business_access_request' | 'new_business' | 'system_alert';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
}

interface BusinessAccessRequest {
  id: string;
  user_id: string;
  business_id: string;
  status: 'pending' | 'approved' | 'denied';
  message?: string;
  requested_at: string;
  business?: {
    name: string;
  };
  profiles?: {
    first_name?: string;
    last_name?: string;
    user_email: string;
  };
}

const ADMIN_USER_ID = "58dc33cf-44eb-4fa2-9da2-789db8a12913";

export function AdminNotifications() {
  const { user } = useAuth();
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Check if user is admin
  const isAdmin = user?.id === ADMIN_USER_ID;

  useEffect(() => {
    if (isAdmin) {
      // Load initial unread count without loading full notifications
      loadUnreadCount();
      // Setup realtime subscription
      setupRealtimeSubscription();
    }
  }, [isAdmin]);

  const loadUnreadCount = async () => {
    const supabase = getSupabaseClient();
    if (!supabase || !isAdmin) return;

    try {
      // Get count of pending business access requests
      const { count, error } = await supabase
        .from('business_access_requests')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending');

      if (error) {
        console.error('Error loading unread count:', error);
        return;
      }

      setUnreadCount(count || 0);
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  const loadNotifications = async () => {
    const supabase = getSupabaseClient();
    if (!supabase || !isAdmin) return;

    setIsLoading(true);
    try {
      // Load pending business access requests
      const { data: requests, error } = await supabase
        .from('business_access_requests')
        .select(`
          *,
          business:businesses(name),
          profiles(first_name, last_name, user_email)
        `)
        .eq('status', 'pending')
        .order('requested_at', { ascending: false });

      if (error) {
        console.error('Error loading notifications:', error);
        return;
      }

      // Convert requests to notifications
      const requestNotifications: Notification[] = (requests || []).map((request: BusinessAccessRequest) => ({
        id: `request_${request.id}`,
        type: 'business_access_request' as const,
        title: 'New Business Access Request',
        message: `${request.profiles?.first_name || ''} ${request.profiles?.last_name || ''} wants access to ${request.business?.name || 'a business'}`,
        data: request,
        read: false,
        created_at: request.requested_at,
      }));

      setNotifications(requestNotifications);
      setUnreadCount(requestNotifications.length);
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const setupRealtimeSubscription = () => {
    const supabase = getSupabaseClient();
    if (!supabase || !isAdmin) return;

    // Subscribe to new business access requests
    const subscription = supabase
      .channel('admin_notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'business_access_requests',
        },
        (payload) => {
          console.log('New business access request:', payload);

          // Update unread count immediately
          setUnreadCount(prev => prev + 1);

          // Only reload notifications if dropdown is open
          if (isOpen) {
            loadNotifications();
          }

          // Show browser notification if permission granted
          if (Notification.permission === 'granted') {
            new Notification('New Business Access Request', {
              body: 'A user has requested access to a business',
              icon: '/favicon.ico',
            });
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  };

  const handleApproveRequest = async (requestId: string) => {
    const supabase = getSupabaseClient();
    if (!supabase) return;

    try {
      const { error } = await supabase
        .from('business_access_requests')
        .update({
          status: 'approved',
          reviewed_at: new Date().toISOString(),
          reviewed_by: user?.id
        })
        .eq('id', requestId);

      if (error) {
        console.error('Error approving request:', error);
        return;
      }

      // Remove from notifications
      setNotifications(prev => prev.filter(n => n.data?.id !== requestId));
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error approving request:', error);
    }
  };

  const handleDenyRequest = async (requestId: string) => {
    const supabase = getSupabaseClient();
    if (!supabase) return;

    try {
      const { error } = await supabase
        .from('business_access_requests')
        .update({
          status: 'denied',
          reviewed_at: new Date().toISOString(),
          reviewed_by: user?.id
        })
        .eq('id', requestId);

      if (error) {
        console.error('Error denying request:', error);
        return;
      }

      // Remove from notifications
      setNotifications(prev => prev.filter(n => n.data?.id !== requestId));
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error denying request:', error);
    }
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      await Notification.requestPermission();
    }
  };

  // Request notification permission on component mount
  useEffect(() => {
    if (isAdmin) {
      requestNotificationPermission();
    }
  }, [isAdmin]);

  if (!isAdmin) {
    return null;
  }

  return (
    <div className="relative">
      {/* Notification Bell */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => {
          if (!isOpen) {
            // Load notifications when opening the dropdown
            loadNotifications();
          }
          setIsOpen(!isOpen);
        }}
        className="relative p-2"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Notifications Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-96 z-50">
          <Card className="shadow-lg border">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Admin Notifications</CardTitle>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push('/admin')}
                    className="p-1"
                    title="Go to Admin Panel"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="p-1"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500">
                  Loading notifications...
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  No new notifications
                </div>
              ) : (
                <div className="max-h-96 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className="border-b border-gray-100 p-4 hover:bg-gray-50"
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          {notification.type === 'business_access_request' ? (
                            <Building2 className="h-5 w-5 text-blue-600" />
                          ) : (
                            <User className="h-5 w-5 text-gray-600" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">
                            {notification.title}
                          </p>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          <div className="flex items-center mt-2 text-xs text-gray-500">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                          </div>
                          
                          {notification.type === 'business_access_request' && notification.data && (
                            <div className="flex space-x-2 mt-3">
                              <Button
                                size="sm"
                                onClick={() => handleApproveRequest(notification.data.id)}
                                className="bg-green-600 hover:bg-green-700 text-white"
                              >
                                <Check className="h-3 w-3 mr-1" />
                                Approve
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => handleDenyRequest(notification.data.id)}
                              >
                                <X className="h-3 w-3 mr-1" />
                                Deny
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
