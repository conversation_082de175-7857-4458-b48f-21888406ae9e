"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { getSupabaseClient } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";
import { EnhancedLogoUpload } from "@/components/business/enhanced-logo-upload";
import { uploadBusinessLogo } from "@/lib/simple-upload";
import {
  Building2,
  Users,
  Check,
  X,
  Eye,
  Edit,
  Search,
  Mail,
  Phone,
  MapPin,
  Clock,
  UserCheck,
  UserX,
  Upload,
  Image as ImageIcon
} from "lucide-react";

const ADMIN_USER_ID = "58dc33cf-44eb-4fa2-9da2-789db8a12913";

interface Business {
  id: string;
  name: string;
  contact_email?: string;
  contact_phone?: string;
  business_address?: string;
  contact_name?: string;
  website?: string;
  category?: string;
  premium_discount?: string;
  is_active: boolean;
  business_spotlight?: boolean;
  user_id?: string;
  logo_url?: string;
  created_at: string;
  updated_at: string;
}

interface AccessRequest {
  id: string;
  user_id: string;
  business_id: string;
  status: 'pending' | 'approved' | 'denied';
  message?: string;
  requested_at: string;
  reviewed_at?: string;
  reviewed_by?: string;
  business?: {
    name: string;
  };
  profiles?: {
    first_name?: string;
    last_name?: string;
    user_email: string;
  };
}

export function AdminBusinessManagement() {
  const { user } = useAuth();
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [accessRequests, setAccessRequests] = useState<AccessRequest[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);
  const [editingBusiness, setEditingBusiness] = useState<Business | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  // Check if current user is admin
  const isAdmin = user?.id === ADMIN_USER_ID;

  useEffect(() => {
    if (isAdmin) {
      loadData();
    }
  }, [isAdmin]);

  const loadData = async () => {
    if (!supabase) return;

    setIsLoading(true);
    try {
      // Load businesses using optimized API
      try {
        const response = await fetch('/api/businesses-fast?fields=full&limit=500');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log(`✅ Admin: Loaded ${result.data.length} businesses in ${result.performance.queryTime}ms`);
            setBusinesses(result.data || []);
          } else {
            console.error('Error loading businesses:', result.error);
            // Fallback to Supabase direct query
            const { data: businessData, error: businessError } = await supabase
              .from('businesses')
              .select('*')
              .order('created_at', { ascending: false });

            if (businessError) {
              console.error('Fallback error loading businesses:', businessError);
            } else {
              setBusinesses(businessData || []);
            }
          }
        } else {
          throw new Error(`API responded with status ${response.status}`);
        }
      } catch (apiError) {
        console.error('API error, falling back to Supabase:', apiError);
        // Fallback to original Supabase query
        const { data: businessData, error: businessError } = await supabase
          .from('businesses')
          .select('*')
          .order('created_at', { ascending: false });

        if (businessError) {
          console.error('Error loading businesses:', businessError);
        } else {
          setBusinesses(businessData || []);
        }
      }

      // Load access requests
      const { data: requestData, error: requestError } = await supabase
        .from('business_access_requests')
        .select(`
          *,
          business:businesses(name),
          profiles(first_name, last_name, user_email)
        `)
        .order('requested_at', { ascending: false });

      if (requestError) {
        console.error('Error loading access requests:', requestError);
      } else {
        setAccessRequests(requestData || []);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAccessRequestAction = async (requestId: string, action: 'approved' | 'denied') => {
    if (!supabase || !user) return;

    try {
      const { error } = await supabase
        .from('business_access_requests')
        .update({
          status: action,
          reviewed_at: new Date().toISOString(),
          reviewed_by: user.id
        })
        .eq('id', requestId);

      if (error) {
        throw new Error('Failed to update request');
      }

      // If approved, we might want to add user permissions here
      // For now, just reload the data
      await loadData();
    } catch (error) {
      console.error('Error updating access request:', error);
    }
  };

  const handleLogoUpload = async (file: File) => {
    if (!editingBusiness) return;

    setIsUploadingLogo(true);
    setUploadError(null);

    try {
      // Upload logo using simple upload service
      const uploadResult = await uploadBusinessLogo(editingBusiness.id, file);

      if (uploadResult.error) {
        throw new Error(uploadResult.error);
      }

      if (!uploadResult.url) {
        throw new Error('Upload succeeded but no URL returned');
      }

      // Update business record with new logo URL
      const { error: updateError } = await supabase
        .from('businesses')
        .update({
          logo_url: uploadResult.url,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingBusiness.id);

      if (updateError) {
        throw new Error(`Failed to update business logo: ${updateError.message}`);
      }

      // Update local state
      setEditingBusiness(prev => prev ? { ...prev, logo_url: uploadResult.url } : null);

      // Reload data to ensure consistency
      await loadData();

    } catch (error) {
      console.error('Logo upload error:', error);
      setUploadError(error instanceof Error ? error.message : 'Logo upload failed');
    } finally {
      setIsUploadingLogo(false);
    }
  };

  const handleBusinessUpdate = async () => {
    if (!supabase || !editingBusiness) return;

    setIsEditing(true);
    try {
      const { error } = await supabase
        .from('businesses')
        .update({
          name: editingBusiness.name,
          contact_email: editingBusiness.contact_email,
          contact_phone: editingBusiness.contact_phone,
          business_address: editingBusiness.business_address,
          contact_name: editingBusiness.contact_name,
          website: editingBusiness.website,
          category: editingBusiness.category,
          premium_discount: editingBusiness.premium_discount,
          is_active: editingBusiness.is_active,
          business_spotlight: editingBusiness.business_spotlight,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingBusiness.id);

      if (error) {
        throw new Error('Failed to update business');
      }

      setEditingBusiness(null);
      setUploadError(null);
      await loadData();
    } catch (error) {
      console.error('Error updating business:', error);
    } finally {
      setIsEditing(false);
    }
  };

  const filteredBusinesses = businesses.filter(business =>
    business.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    business.business_address?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    business.contact_email?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const pendingRequests = accessRequests.filter(req => req.status === 'pending');

  if (!isAdmin) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <div className="text-red-500 mb-2">
            <UserX className="h-12 w-12 mx-auto" />
          </div>
          <p className="text-lg font-semibold text-red-800">Access Denied</p>
          <p className="text-red-600 mt-1">You don't have permission to access this page.</p>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading admin dashboard...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Business Management</h1>
          <p className="text-gray-600">Manage all businesses and access requests</p>
        </div>
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          <UserCheck className="h-4 w-4 mr-1" />
          Admin Access
        </Badge>
      </div>

      <Tabs defaultValue="businesses" className="space-y-4">
        <TabsList>
          <TabsTrigger value="businesses" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Businesses ({businesses.length})
          </TabsTrigger>
          <TabsTrigger value="requests" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Access Requests ({pendingRequests.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="businesses" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>All Businesses</CardTitle>
                <div className="relative w-64">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search businesses..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Business Name</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Discount</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredBusinesses.map((business) => (
                      <TableRow key={business.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            {business.logo_url && (
                              <img
                                src={business.logo_url}
                                alt={business.name}
                                className="w-8 h-8 rounded object-cover"
                              />
                            )}
                            <div>
                              <div className="font-medium">{business.name}</div>
                              {business.website && (
                                <div className="text-sm text-gray-500">{business.website}</div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {business.contact_email && (
                              <div className="flex items-center gap-1 text-sm">
                                <Mail className="h-3 w-3" />
                                {business.contact_email}
                              </div>
                            )}
                            {business.contact_phone && (
                              <div className="flex items-center gap-1 text-sm">
                                <Phone className="h-3 w-3" />
                                {business.contact_phone}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <MapPin className="h-3 w-3" />
                            {business.business_address}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <Badge variant={business.is_active ? "default" : "secondary"}>
                              {business.is_active ? "Active" : "Inactive"}
                            </Badge>
                            {business.business_spotlight && (
                              <Badge variant="outline" className="bg-yellow-50 text-yellow-700">
                                Spotlight
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {business.premium_discount || 'None'}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setSelectedBusiness(business)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingBusiness({ ...business })}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="requests" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Business Access Requests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Business</TableHead>
                      <TableHead>Message</TableHead>
                      <TableHead>Requested</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {accessRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {request.profiles?.first_name} {request.profiles?.last_name}
                            </div>
                            <div className="text-sm text-gray-500">{request.profiles?.user_email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{request.business?.name}</div>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-xs truncate">
                            {request.message || 'No message'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <Clock className="h-3 w-3" />
                            {new Date(request.requested_at).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              request.status === 'approved' ? 'default' :
                              request.status === 'denied' ? 'destructive' : 'secondary'
                            }
                          >
                            {request.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {request.status === 'pending' && (
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                onClick={() => handleAccessRequestAction(request.id, 'approved')}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => handleAccessRequestAction(request.id, 'denied')}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Business Edit Modal */}
      <Dialog open={!!editingBusiness} onOpenChange={() => setEditingBusiness(null)}>
        <DialogContent className="sm:max-w-lg max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Business</DialogTitle>
          </DialogHeader>
          {editingBusiness && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Business Name</Label>
                  <Input
                    id="name"
                    value={editingBusiness.name}
                    onChange={(e) => setEditingBusiness({
                      ...editingBusiness,
                      name: e.target.value
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="contact_email">Email</Label>
                  <Input
                    id="contact_email"
                    type="email"
                    value={editingBusiness.contact_email || ''}
                    onChange={(e) => setEditingBusiness({
                      ...editingBusiness,
                      contact_email: e.target.value
                    })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contact_phone">Phone</Label>
                  <Input
                    id="contact_phone"
                    value={editingBusiness.contact_phone || ''}
                    onChange={(e) => setEditingBusiness({
                      ...editingBusiness,
                      contact_phone: e.target.value
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={editingBusiness.website || ''}
                    onChange={(e) => setEditingBusiness({
                      ...editingBusiness,
                      website: e.target.value
                    })}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="business_address">Address</Label>
                <Input
                  id="business_address"
                  value={editingBusiness.business_address || ''}
                  onChange={(e) => setEditingBusiness({
                    ...editingBusiness,
                    business_address: e.target.value
                  })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={editingBusiness.category || ''}
                    onChange={(e) => setEditingBusiness({
                      ...editingBusiness,
                      category: e.target.value
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="contact_name">Contact Name</Label>
                  <Input
                    id="contact_name"
                    value={editingBusiness.contact_name || ''}
                    onChange={(e) => setEditingBusiness({
                      ...editingBusiness,
                      contact_name: e.target.value
                    })}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="premium_discount">Premium Discount</Label>
                <Input
                  id="premium_discount"
                  value={editingBusiness.premium_discount || ''}
                  onChange={(e) => setEditingBusiness({
                    ...editingBusiness,
                    premium_discount: e.target.value
                  })}
                />
              </div>

              <div className="space-y-2">
                <Label>Status</Label>
                <div className="flex gap-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={editingBusiness.is_active}
                      onChange={(e) => setEditingBusiness({
                        ...editingBusiness,
                        is_active: e.target.checked
                      })}
                    />
                    Active
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={editingBusiness.business_spotlight}
                      onChange={(e) => setEditingBusiness({
                        ...editingBusiness,
                        business_spotlight: e.target.checked
                      })}
                    />
                    Spotlight
                  </label>
                </div>
              </div>

              {/* Logo Upload Section */}
              <div className="space-y-3">
                <Label className="flex items-center gap-2">
                  <ImageIcon className="h-4 w-4" />
                  Business Logo
                </Label>
                {uploadError && (
                  <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                    {uploadError}
                  </div>
                )}
                <EnhancedLogoUpload
                  onUpload={handleLogoUpload}
                  currentLogoUrl={editingBusiness.logo_url}
                  isUploading={isUploadingLogo}
                  maxFileSize={5 * 1024 * 1024} // 5MB
                  acceptedFormats={['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml']}
                  showMetadata={true}
                  className="border-2 border-dashed border-gray-200 rounded-lg"
                />
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setEditingBusiness(null);
                    setUploadError(null);
                  }}
                  disabled={isEditing || isUploadingLogo}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleBusinessUpdate}
                  disabled={isEditing || isUploadingLogo}
                >
                  {isEditing ? 'Saving...' : isUploadingLogo ? 'Uploading Logo...' : 'Save Changes'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}