/**
 * SuperUser Test Component
 * Demonstrates how to use the SuperUser service to fetch businesses and profiles
 */

'use client'

import { useState } from 'react'
import { useSuperUserData } from '@/hooks/use-superuser-data'

export default function SuperUserTest() {
  const [showDetails, setShowDetails] = useState(false)
  
  const {
    businesses,
    profiles,
    loading,
    error,
    lastFetch,
    fetchAllData,
    fetchBusinesses,
    fetchProfiles,
    clearCache,
    refresh,
    activeBusinesses,
    cardHolders,
    businessApplicants
  } = useSuperUserData({
    autoFetch: false, // Don't auto-fetch, let user trigger manually
    activeBusinessesOnly: false,
    cardHoldersOnly: false,
    useCache: true
  })

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6 text-gray-800">
          SuperUser Service Test
        </h1>

        {/* Control Buttons */}
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={fetchAllData}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Fetch All Data'}
          </button>
          
          <button
            onClick={fetchBusinesses}
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            Fetch Businesses Only
          </button>
          
          <button
            onClick={fetchProfiles}
            disabled={loading}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
          >
            Fetch Profiles Only
          </button>
          
          <button
            onClick={clearCache}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Clear Cache
          </button>
          
          <button
            onClick={refresh}
            disabled={loading}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50"
          >
            Refresh
          </button>
        </div>

        {/* Status */}
        <div className="mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-semibold text-gray-700">Status</h3>
              <p className={`text-sm ${loading ? 'text-yellow-600' : error ? 'text-red-600' : 'text-green-600'}`}>
                {loading ? 'Loading...' : error ? 'Error' : 'Ready'}
              </p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-semibold text-gray-700">Last Fetch</h3>
              <p className="text-sm text-gray-600">
                {lastFetch ? lastFetch.toLocaleTimeString() : 'Never'}
              </p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded">
              <h3 className="font-semibold text-gray-700">Cache</h3>
              <p className="text-sm text-gray-600">Enabled</p>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded">
            <h3 className="font-semibold text-red-800">Error</h3>
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Statistics */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-800">Statistics</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded text-center">
              <div className="text-2xl font-bold text-blue-600">{businesses.length}</div>
              <div className="text-sm text-blue-800">Total Businesses</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded text-center">
              <div className="text-2xl font-bold text-green-600">{activeBusinesses.length}</div>
              <div className="text-sm text-green-800">Active Businesses</div>
            </div>
            
            <div className="bg-purple-50 p-4 rounded text-center">
              <div className="text-2xl font-bold text-purple-600">{profiles.length}</div>
              <div className="text-sm text-purple-800">Total Profiles</div>
            </div>
            
            <div className="bg-yellow-50 p-4 rounded text-center">
              <div className="text-2xl font-bold text-yellow-600">{cardHolders.length}</div>
              <div className="text-sm text-yellow-800">Card Holders</div>
            </div>
          </div>
        </div>

        {/* Toggle Details */}
        <div className="mb-4">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
        </div>

        {/* Detailed Data */}
        {showDetails && (
          <div className="space-y-6">
            {/* Businesses */}
            <div>
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                Businesses ({businesses.length})
              </h3>
              <div className="bg-gray-50 p-4 rounded max-h-64 overflow-y-auto">
                {businesses.length > 0 ? (
                  <div className="space-y-2">
                    {businesses.slice(0, 10).map((business) => (
                      <div key={business.id} className="text-sm">
                        <span className="font-medium">{business.name}</span>
                        {business.is_active ? (
                          <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-xs">
                            Active
                          </span>
                        ) : (
                          <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                            Inactive
                          </span>
                        )}
                      </div>
                    ))}
                    {businesses.length > 10 && (
                      <div className="text-sm text-gray-500">
                        ... and {businesses.length - 10} more
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500">No businesses loaded</p>
                )}
              </div>
            </div>

            {/* Profiles */}
            <div>
              <h3 className="text-lg font-semibold mb-3 text-gray-800">
                Profiles ({profiles.length})
              </h3>
              <div className="bg-gray-50 p-4 rounded max-h-64 overflow-y-auto">
                {profiles.length > 0 ? (
                  <div className="space-y-2">
                    {profiles.slice(0, 10).map((profile) => (
                      <div key={profile.id} className="text-sm">
                        <span className="font-medium">
                          {profile.first_name} {profile.last_name}
                        </span>
                        <span className="ml-2 text-gray-600">
                          ({profile.user_email})
                        </span>
                        {profile.is_card_holder && (
                          <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                            Card Holder
                          </span>
                        )}
                        {profile.is_business_applicant && (
                          <span className="ml-2 px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs">
                            Business Applicant
                          </span>
                        )}
                      </div>
                    ))}
                    {profiles.length > 10 && (
                      <div className="text-sm text-gray-500">
                        ... and {profiles.length - 10} more
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500">No profiles loaded</p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
