import { Link } from "@/components/ui/link" // Import our custom Link component
import { Mail, MapPin, Twitter, Instagram, Youtube, Music, MessageSquare, Send } from "lucide-react"
import Image from "next/image"

export function SiteFooter() {
  return (
    <>
      <footer className="bg-[#1A1A1A] text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center mb-6">
                <div className="mr-2">
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png"
                    width={40}
                    height={40}
                    alt="Fuse.vip Logo"
                    className="w-10 h-10"
                  />
                </div>
                <span className="font-medium text-white">fuse.vip</span>
              </div>


            </div>

            <div>
              <h3 className="text-[#3A56FF] font-bold mb-4">Quick Links</h3>
              <ul className="space-y-3">
                <li>
                  <Link href="/" className="text-sm text-gray-300 hover:text-[#3A56FF]">
                    Home
                  </Link>
                </li>

                <li>
                  <Link href="/industry" className="text-sm text-gray-300 hover:text-[#3A56FF]">
                    Industry
                  </Link>
                </li>
                <li>
                  <Link href="/fuse" className="text-sm text-gray-300 hover:text-[#3A56FF]">
                    FUSE
                  </Link>
                </li>

                <li>
                  <Link href="/upgrade" className="text-sm text-gray-300 hover:text-[#3A56FF]">
                    Upgrade
                  </Link>
                </li>
                <li>
                  <Link href="/about-us" className="text-sm text-gray-300 hover:text-[#3A56FF]">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/reviews" className="text-sm text-gray-300 hover:text-[#3A56FF]">
                    Reviews
                  </Link>
                </li>
                <li>
                  <Link href="/resources" className="text-sm text-gray-300 hover:text-[#3A56FF]">
                    Resources
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-[#3A56FF] font-bold mb-4">Contact Us</h3>
              <ul className="space-y-4">
                <li className="flex">
                  <MapPin className="h-5 w-5 text-[#3A56FF] mr-2 flex-shrink-0" />
                  <span className="text-sm text-gray-300">Fuse Vip LLC</span>
                </li>
                <li className="flex">
                  <Mail className="h-5 w-5 text-[#3A56FF] mr-2 flex-shrink-0" />
                  <a href="mailto:<EMAIL>" className="text-sm text-gray-300 hover:text-[#3A56FF]">
                    <EMAIL>
                  </a>
                </li>
                <li className="flex">
                  <Twitter className="h-5 w-5 text-[#3A56FF] mr-2 flex-shrink-0" />
                  <a
                    href="https://x.com/fuserewards"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-300 hover:text-[#3A56FF]"
                  >
                    @fuserewards
                  </a>
                </li>
                <li className="flex">
                  <Instagram className="h-5 w-5 text-[#3A56FF] mr-2 flex-shrink-0" />
                  <a
                    href="https://instagram.com/fuse.vip"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-300 hover:text-[#3A56FF]"
                  >
                    @fuse.vip
                  </a>
                </li>
                <li className="flex">
                  <Youtube className="h-5 w-5 text-[#3A56FF] mr-2 flex-shrink-0" />
                  <a
                    href="https://youtube.com/@fuserewardsonx"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-300 hover:text-[#3A56FF]"
                  >
                    @fuserewardsonx
                  </a>
                </li>
                <li className="flex">
                  <Music className="h-5 w-5 text-[#3A56FF] mr-2 flex-shrink-0" />
                  <a
                    href="https://open.spotify.com/show/6XbeSx86qJz9o6Q6z19z1O?si=JxlZQaQnQH6dWu8omGtmsQ"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-300 hover:text-[#3A56FF]"
                  >
                    Spotify Podcast
                  </a>
                </li>
                <li className="flex">
                  <MessageSquare className="h-5 w-5 text-[#3A56FF] mr-2 flex-shrink-0" />
                  <a
                    href="https://discord.gg/n9d7PEbm"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-300 hover:text-[#3A56FF]"
                  >
                    Join our Discord
                  </a>
                </li>
                <li className="flex">
                  <Send className="h-5 w-5 text-[#3A56FF] mr-2 flex-shrink-0" />
                  <a
                    href="https://t.me/TheSmallBizRevolution"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-gray-300 hover:text-[#3A56FF]"
                  >
                    Join our Telegram
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </footer>

      <div className="bg-[#3A56FF] py-3 text-center text-white text-sm">
        Copyright © {new Date().getFullYear()} Fuse Vip LLC. All Rights Reserved.
      </div>
    </>
  )
}
