"use client"

import { useState, useEffect } from "react"
import { X, Monitor } from "lucide-react"
import { Button } from "@/components/ui/button"

export function CookieNotice() {
  const [isVisible, setIsVisible] = useState(false)
  const [isAccepted, setIsAccepted] = useState(false)

  useEffect(() => {
    // Check if user has already accepted cookies
    const accepted = localStorage.getItem('fuse_cookies_accepted')
    if (!accepted) {
      setIsVisible(true)
    } else {
      setIsAccepted(true)
    }
  }, [])

  const acceptCookies = () => {
    localStorage.setItem('fuse_cookies_accepted', 'true')
    setIsVisible(false)
    setIsAccepted(true)
  }

  const declineCookies = () => {
    localStorage.setItem('fuse_cookies_accepted', 'false')
    setIsVisible(false)
  }

  if (!isVisible) return null

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-black/95 backdrop-blur-sm border border-cyan-500/30 rounded-lg p-6 shadow-2xl">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Monitor className="w-6 h-6 text-cyan-400" />
              <div>
                <div className="font-mono text-cyan-400/80 text-sm tracking-wide">
                  &gt; COOKIE.PROTOCOL &gt; ACTIVE
                </div>
                <div className="font-mono text-cyan-100 text-lg font-bold">
                  SITE ENHANCEMENT DETECTED
                </div>
              </div>
            </div>
            <button
              onClick={declineCookies}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <div className="font-mono text-cyan-100/80 text-sm leading-relaxed mb-6">
            Our galactic transmission system uses cookies to enhance your experience.
            <br />
            These data fragments help us optimize the resistance network and improve site functionality.
            <br />
            <span className="text-cyan-300">No personal data is transmitted to hostile entities.</span>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={acceptCookies}
              className="bg-green-600 hover:bg-green-700 text-white font-mono text-sm px-6 py-2 rounded-lg border border-green-500/30 transition-all duration-300"
            >
              &gt; ACCEPT.ENHANCEMENT
            </Button>
            <Button
              onClick={declineCookies}
              variant="outline"
              className="border-red-500/30 text-red-300 hover:bg-red-900/30 hover:text-red-200 font-mono text-sm px-6 py-2 rounded-lg transition-all duration-300"
            >
              &gt; DECLINE.PROTOCOL
            </Button>
          </div>

          {/* Fine Print */}
          <div className="font-mono text-cyan-400/40 text-xs mt-4 text-center">
            Cookies help us make the site better • Essential for VIP card functionality
          </div>
        </div>
      </div>
    </div>
  )
}