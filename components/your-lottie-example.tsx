"use client"

import { useLottie } from "lottie-react"
import { useEffect, useState } from "react"

/**
 * Your specific Lottie animation component
 * Following the exact pattern from the Medium article
 */
export function YourLottieExample() {
  const [animationData, setAnimationData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchAnimation = async () => {
      try {
        const response = await fetch("https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie")
        const data = await response.json()
        setAnimationData(data)
      } catch (error) {
        console.error("Failed to load animation:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchAnimation()
  }, [])

  const defaultOptions = {
    animationData: animationData,
    loop: true,
  }

  const { View } = useLottie(defaultOptions)

  if (loading) {
    return (
      <div className="w-[300px] h-[300px] flex items-center justify-center bg-gray-100 rounded-lg">
        <div className="text-gray-500">Loading animation...</div>
      </div>
    )
  }

  return (
    <>
      <div className="">
        <div className="w-full" style={{ width: "300px", height: "300px" }}>
          {View}
        </div>
      </div>
    </>
  )
}

/**
 * Simplified version - even easier to use
 */
export function SimpleLottieExample() {
  const [animationData, setAnimationData] = useState(null)

  useEffect(() => {
    fetch("https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie")
      .then(response => response.json())
      .then(data => setAnimationData(data))
      .catch(error => console.error("Animation load error:", error))
  }, [])

  const { View } = useLottie({
    animationData: animationData,
    loop: true,
  })

  return (
    <div style={{ width: "300px", height: "300px" }}>
      {View}
    </div>
  )
}

/**
 * Example with all the features you might want
 */
export function AdvancedLottieExample() {
  const [animationData, setAnimationData] = useState(null)
  const [isPlaying, setIsPlaying] = useState(true)

  useEffect(() => {
    fetch("https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie")
      .then(response => response.json())
      .then(data => setAnimationData(data))
  }, [])

  const { View, play, pause, setSpeed } = useLottie({
    animationData: animationData,
    loop: true,
    autoplay: true,
  })

  const togglePlayPause = () => {
    if (isPlaying) {
      pause && pause()
    } else {
      play && play()
    }
    setIsPlaying(!isPlaying)
  }

  return (
    <div className="flex flex-col items-center space-y-4">
      <div style={{ width: "300px", height: "300px" }}>
        {View}
      </div>
      
      <div className="flex space-x-2">
        <button 
          onClick={togglePlayPause}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          {isPlaying ? "Pause" : "Play"}
        </button>
        <button 
          onClick={() => setSpeed && setSpeed(0.5)}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          0.5x
        </button>
        <button 
          onClick={() => setSpeed && setSpeed(1)}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          1x
        </button>
        <button 
          onClick={() => setSpeed && setSpeed(2)}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          2x
        </button>
      </div>
    </div>
  )
}
