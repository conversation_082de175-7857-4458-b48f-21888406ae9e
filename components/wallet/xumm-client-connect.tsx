'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Smartphone, Monitor, Wallet, Copy, CheckCircle, XCircle, RefreshCw } from 'lucide-react'
import { useXummClient } from '@/hooks/use-xumm-client'
import { useToast } from '@/hooks/use-toast'
import { QRCodeSVG as QRCode } from 'qrcode.react'

interface XummClientConnectProps {
  onConnected?: (account: string) => void
  onDisconnected?: () => void
  className?: string
}

export function XummClientConnect({ 
  onConnected, 
  onDisconnected, 
  className 
}: XummClientConnectProps) {
  const { toast } = useToast()
  const {
    isConnected,
    isLoading,
    account,
    networkType,
    error,
    sdkReady,
    connect,
    disconnect,
    isMobile
  } = useXummClient()

  const [showQR, setShowQR] = useState(false)
  const [qrData, setQrData] = useState<string | null>(null)

  // Handle connection success
  useEffect(() => {
    if (isConnected && account) {
      onConnected?.(account)
      toast({
        title: "Wallet Connected",
        description: `Connected to ${account.slice(0, 6)}...${account.slice(-4)}`,
        duration: 3000
      })
    }
  }, [isConnected, account, onConnected, toast])

  // Handle disconnection
  useEffect(() => {
    if (!isConnected && account === null) {
      onDisconnected?.()
    }
  }, [isConnected, account, onDisconnected])

  // Handle errors
  useEffect(() => {
    if (error) {
      toast({
        title: "Connection Error",
        description: error,
        variant: "destructive",
        duration: 5000
      })
    }
  }, [error, toast])

  const handleConnect = async () => {
    try {
      await connect()
      
      // For desktop users, we might want to show a QR code
      // The client SDK will handle the flow automatically
      if (!isMobile()) {
        setShowQR(true)
        // Generate a simple placeholder QR data - in real implementation,
        // this would come from the SDK's authorization flow
        setQrData(`xumm://connect/${Date.now()}`)
      }
    } catch (err) {
      console.error('Connection failed:', err)
    }
  }

  const handleDisconnect = () => {
    disconnect()
    setShowQR(false)
    setQrData(null)
  }

  const copyAccountToClipboard = async () => {
    if (account) {
      try {
        await navigator.clipboard.writeText(account)
        toast({
          title: "Copied!",
          description: "Account address copied to clipboard",
          duration: 2000
        })
      } catch (err) {
        console.error('Failed to copy:', err)
      }
    }
  }

  const getNetworkBadgeVariant = (network: string | null) => {
    if (!network) return "secondary"
    return network.toLowerCase().includes('main') ? "default" : "secondary"
  }

  if (!sdkReady) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <div className="flex items-center space-x-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Loading Xumm SDK...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Wallet className="h-5 w-5" />
          <span>Xumm Wallet (Client-Side)</span>
        </CardTitle>
        <CardDescription>
          Connect your Xumm wallet directly from your browser
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {!isConnected ? (
          <>
            {/* Connection UI */}
            <div className="space-y-3">
              <Button 
                onClick={handleConnect} 
                disabled={isLoading}
                className="w-full"
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    {isMobile() ? (
                      <Smartphone className="mr-2 h-4 w-4" />
                    ) : (
                      <Monitor className="mr-2 h-4 w-4" />
                    )}
                    Connect Xumm Wallet
                  </>
                )}
              </Button>
              
              {/* Device-specific instructions */}
              <div className="text-sm text-muted-foreground text-center">
                {isMobile() ? (
                  <p>Tap to open Xumm app and connect your wallet</p>
                ) : (
                  <p>Scan QR code with your mobile Xumm app</p>
                )}
              </div>
            </div>

            {/* QR Code for desktop */}
            {showQR && qrData && !isMobile() && (
              <div className="flex flex-col items-center space-y-3 p-4 border rounded-lg bg-muted/50">
                <QRCode 
                  value={qrData} 
                  size={200}
                  level="M"
                  style={{ padding: 10 }}
                />
                <p className="text-sm text-center text-muted-foreground">
                  Scan this QR code with your Xumm mobile app
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setShowQR(false)}
                >
                  Hide QR Code
                </Button>
              </div>
            )}

            {/* Error display */}
            {error && (
              <div className="flex items-center space-x-2 p-3 border border-destructive/20 rounded-lg bg-destructive/5">
                <XCircle className="h-4 w-4 text-destructive" />
                <span className="text-sm text-destructive">{error}</span>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => window.location.reload()}
                >
                  <RefreshCw className="h-3 w-3" />
                </Button>
              </div>
            )}
          </>
        ) : (
          <>
            {/* Connected state */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2 p-3 border rounded-lg bg-green-50 dark:bg-green-950/20">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-900 dark:text-green-100">
                  Wallet Connected
                </span>
              </div>

              {/* Account info */}
              {account && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Account:</span>
                    <div className="flex items-center space-x-2">
                      <code className="text-xs bg-muted px-2 py-1 rounded">
                        {account.slice(0, 6)}...{account.slice(-4)}
                      </code>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={copyAccountToClipboard}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {networkType && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Network:</span>
                      <Badge variant={getNetworkBadgeVariant(networkType)}>
                        {networkType}
                      </Badge>
                    </div>
                  )}
                </div>
              )}

              {/* Disconnect button */}
              <Button 
                variant="outline" 
                onClick={handleDisconnect}
                className="w-full"
              >
                Disconnect Wallet
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}