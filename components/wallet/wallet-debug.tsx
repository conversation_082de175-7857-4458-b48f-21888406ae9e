'use client';

import { useXamanWallet } from '@/hooks/use-xaman-wallet';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { Eye, EyeOff, Bug } from 'lucide-react';

export function WalletDebug() {
  const { user, profile } = useAuth();
  const {
    isLoading: isConnecting,
    isConnected,
    walletAddress,
    xummPkce,
  } = useXamanWallet();
  
  const [showDetails, setShowDetails] = useState(false);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Card className="mt-8 bg-gray-900 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Bug className="h-5 w-5" />
          Wallet Debug Info
          <Badge variant="secondary" className="ml-2">
            DEV ONLY
          </Badge>
        </CardTitle>
        <CardDescription className="text-gray-400">
          Development debugging information for wallet connection
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-400">Show sensitive data:</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
            className="border-gray-600"
          >
            {showDetails ? (
              <>
                <EyeOff className="h-4 w-4 mr-2" />
                Hide
              </>
            ) : (
              <>
                <Eye className="h-4 w-4 mr-2" />
                Show
              </>
            )}
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Connection Status */}
          <div className="space-y-2">
            <h4 className="text-sm font-semibold text-white">Connection Status</h4>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-400">Is Connecting:</span>
                <Badge variant={isConnecting ? "default" : "secondary"}>
                  {isConnecting ? "Yes" : "No"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Is Connected:</span>
                <Badge variant={isConnected ? "default" : "destructive"}>
                  {isConnected ? "Yes" : "No"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Xaman SDK:</span>
                <Badge variant={xummPkce ? "default" : "destructive"}>
                  {xummPkce ? "Loaded" : "Not Loaded"}
                </Badge>
              </div>
            </div>
          </div>

          {/* User Info */}
          <div className="space-y-2">
            <h4 className="text-sm font-semibold text-white">User Info</h4>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-400">User ID:</span>
                <span className="text-white font-mono">
                  {showDetails ? (user?.id || 'None') : (user?.id ? '***' : 'None')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Email:</span>
                <span className="text-white">
                  {showDetails ? (user?.email || 'None') : (user?.email ? '***' : 'None')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Profile Wallet:</span>
                <span className="text-white font-mono text-xs">
                  {showDetails 
                    ? (profile?.xrp_wallet_address || 'None')
                    : (profile?.xrp_wallet_address ? '***' : 'None')
                  }
                </span>
              </div>
            </div>
          </div>

          {/* Wallet Addresses */}
          <div className="space-y-2">
            <h4 className="text-sm font-semibold text-white">Wallet Addresses</h4>
            <div className="space-y-1 text-xs">
              <div>
                <span className="text-gray-400">Wallet Address:</span>
                <div className="text-white font-mono break-all">
                  {showDetails ? (walletAddress || 'None') : (walletAddress ? '***' : 'None')}
                </div>
              </div>
              <div>
                <span className="text-gray-400">XRP Wallet Address:</span>
                <div className="text-white font-mono break-all">
                  {showDetails ? (walletAddress || 'None') : (walletAddress ? '***' : 'None')}
                </div>
              </div>
            </div>
          </div>

          {/* Account Info */}
          <div className="space-y-2">
            <h4 className="text-sm font-semibold text-white">Account Info</h4>
            <div className="space-y-1 text-xs">
              <span className="text-gray-400">Account info not available with OAuth2 PKCE SDK</span>
            </div>
          </div>
        </div>

        {/* Environment Variables */}
        <div className="space-y-2">
          <h4 className="text-sm font-semibold text-white">Environment</h4>
          <div className="space-y-1 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-400">NODE_ENV:</span>
              <Badge variant="outline">
                {process.env.NODE_ENV}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">XAMAN API Key:</span>
              <Badge variant={process.env.NEXT_PUBLIC_XAMAN_API_KEY ? "default" : "destructive"}>
                {process.env.NEXT_PUBLIC_XAMAN_API_KEY ? "Set" : "Missing"}
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
