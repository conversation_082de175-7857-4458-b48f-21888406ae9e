"use client"

import { useEffect, useState } from "react"
import { X } from "lucide-react"
import { isMobileDevice, type XamanConnectionResponse } from "@/lib/xaman-mock"

interface WalletQRModalProps {
  isOpen: boolean
  onClose: () => void
  connectionData: XamanConnectionResponse | null
}

export function WalletQRModal({ isOpen, onClose, connectionData }: WalletQRModalProps) {
  const [countdown, setCountdown] = useState(300) // 5 minutes in seconds

  useEffect(() => {
    if (!isOpen) return

    // Reset countdown when modal opens
    setCountdown(300)

    // Start countdown
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          onClose()
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [isOpen, onClose])

  if (!isOpen || !connectionData) return null

  const minutes = Math.floor(countdown / 60)
  const seconds = countdown % 60

  const handleOpenApp = () => {
    if (connectionData.deeplink.universal) {
      window.location.href = connectionData.deeplink.universal
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="relative w-full max-w-md p-6 bg-white rounded-lg shadow-xl">
        <button onClick={onClose} className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100">
          <X className="w-5 h-5" />
        </button>

        <div className="flex flex-col items-center">
          <h3 className="mb-4 text-xl font-bold text-center">Connect Your Xaman Wallet</h3>

          {isMobileDevice() ? (
            <div className="flex flex-col items-center">
              <p className="mb-4 text-center">Open the Xaman app to connect your wallet</p>
              <button
                onClick={handleOpenApp}
                className="px-4 py-2 mb-4 font-medium text-white bg-[#3A56FF] rounded-md hover:bg-blue-700"
              >
                Open Xaman App
              </button>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <p className="mb-4 text-center">Sign your Xaman app to connect your wallet</p>
              <div className="p-2 mb-4 border border-gray-300 rounded-lg">
                <img src={connectionData.qrUrl || "/placeholder.svg"} alt="QR Code" className="w-64 h-64" />
              </div>
            </div>
          )}

          <p className="text-sm text-gray-500">
            Connection expires in {minutes}:{seconds.toString().padStart(2, "0")}
          </p>
        </div>
      </div>
    </div>
  )
}
