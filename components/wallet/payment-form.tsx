'use client';

import { useState } from 'react';
import { useXamanWallet } from '@/hooks/use-xaman-wallet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

export function PaymentForm() {
  const { isConnected, createPayment } = useXamanWallet();
  const [destination, setDestination] = useState('');
  const [amount, setAmount] = useState('');
  const [memo, setMemo] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [paymentUrl, setPaymentUrl] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isConnected) {
      setError('Please connect your wallet first');
      return;
    }
    
    if (!destination || !amount) {
      setError('Destination address and amount are required');
      return;
    }
    
    setIsLoading(true);
    setError('');
    
    try {
      // Convert amount to drops (1 XRP = 1,000,000 drops)
      const amountInDrops = (parseFloat(amount) * 1000000).toString();
      
      const response = await createPayment(destination, amountInDrops, memo);
      
      // Set the payment URL
      setPaymentUrl(response.next.always);
      
      // Open the payment URL in a new window
      window.open(response.next.always, '_blank');
    } catch (error) {
      console.error('Error creating payment:', error);
      setError(error.message || 'Failed to create payment');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Send XRP Payment</CardTitle>
        <CardDescription>
          Send XRP to another wallet address using Xaman
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="destination">Destination Address</Label>
            <Input
              id="destination"
              placeholder="r..."
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="amount">Amount (XRP)</Label>
            <Input
              id="amount"
              type="number"
              step="0.000001"
              min="0.000001"
              placeholder="0.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="memo">Memo (Optional)</Label>
            <Textarea
              id="memo"
              placeholder="Add a note to this payment"
              value={memo}
              onChange={(e) => setMemo(e.target.value)}
            />
          </div>
          
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading || !isConnected}
          >
            {isLoading ? 'Creating Payment...' : 'Send Payment'}
          </Button>
        </form>
        
        {paymentUrl && (
          <div className="mt-4">
            <p className="text-sm text-gray-500 mb-2">Payment URL:</p>
            <a
              href={paymentUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:text-blue-700 text-sm break-all"
              id="launchXummBtn"
            >
              {paymentUrl}
            </a>
          </div>
        )}
      </CardContent>
    </Card>
  );
}