"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Wallet, ExternalLink, Smartphone, QrCode, CheckCircle, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { joeyWalletService, JoeyWalletSession, formatJoeyWalletAddress } from '@/lib/joey-wallet'

interface JoeyWalletConnectProps {
  onConnect?: (session: JoeyWalletSession) => void
  onDisconnect?: () => void
  onError?: (error: string) => void
  className?: string
}

export function JoeyWalletConnect({ 
  onConnect, 
  onDisconnect, 
  onError,
  className = '' 
}: JoeyWalletConnectProps) {
  const [isConnecting, setIsConnecting] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [session, setSession] = useState<JoeyWalletSession | null>(null)
  const [error, setError] = useState('')
  const [showInstructions, setShowInstructions] = useState(false)

  // Check for existing session on mount
  useEffect(() => {
    const existingSession = joeyWalletService.getCurrentSession()
    if (existingSession) {
      setSession(existingSession)
      setIsConnected(true)
    }
  }, [])

  // Handle Joey Wallet connection
  const handleConnect = async () => {
    try {
      setIsConnecting(true)
      setError('')
      setShowInstructions(true)

      // Initialize and connect to Joey Wallet
      const newSession = await joeyWalletService.connectJoeyWallet()
      
      setSession(newSession)
      setIsConnected(true)
      setShowInstructions(false)
      
      if (onConnect) {
        onConnect(newSession)
      }

      console.log('Successfully connected to Joey Wallet:', newSession)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to connect to Joey Wallet'
      setError(errorMessage)
      setShowInstructions(false)
      
      if (onError) {
        onError(errorMessage)
      }
      
      console.error('Joey Wallet connection failed:', err)
    } finally {
      setIsConnecting(false)
    }
  }

  // Handle disconnection
  const handleDisconnect = async () => {
    try {
      await joeyWalletService.disconnect()
      setSession(null)
      setIsConnected(false)
      setError('')
      
      if (onDisconnect) {
        onDisconnect()
      }
      
      console.log('Disconnected from Joey Wallet')
    } catch (err: any) {
      console.error('Failed to disconnect:', err)
      setError('Failed to disconnect from Joey Wallet')
    }
  }

  // Cancel connection attempt
  const handleCancel = () => {
    setIsConnecting(false)
    setShowInstructions(false)
    setError('')
  }

  // Open Joey Wallet app store page
  const openJoeyWalletStore = () => {
    const userAgent = navigator.userAgent
    if (/iPhone|iPad|iPod/i.test(userAgent)) {
      // iOS - App Store
      window.open('https://apps.apple.com/app/joey-wallet/id6502764448', '_blank')
    } else if (/Android/i.test(userAgent)) {
      // Android - Google Play Store
      window.open('https://play.google.com/store/apps/details?id=xyz.joeywallet', '_blank')
    } else {
      // Desktop - Joey Wallet website
      window.open('https://joeywallet.xyz/', '_blank')
    }
  }

  if (showInstructions && isConnecting) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`bg-gradient-to-br from-purple-900/80 to-blue-900/80 backdrop-blur-sm rounded-2xl p-8 border border-purple-500/30 ${className}`}
      >
        <div className="text-center space-y-6">
          <div className="mx-auto w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center">
            <Smartphone className="w-8 h-8 text-purple-400" />
          </div>
          
          <div>
            <h3 className="text-2xl font-bold text-white mb-2">Connect Joey Wallet</h3>
            <p className="text-gray-300">Connecting to your Joey Wallet app...</p>
          </div>

          <div className="bg-black/20 rounded-xl p-6 space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                <Smartphone className="w-4 h-4 text-white" />
              </div>
              <span className="text-white">Open Joey Wallet on your mobile device</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <QrCode className="w-4 h-4 text-white" />
              </div>
              <span className="text-white">Scan QR code or approve connection</span>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <CheckCircle className="w-4 h-4 text-white" />
              </div>
              <span className="text-white">Sign transaction in Joey Wallet</span>
            </div>
          </div>

          <div className="flex gap-4">
            <Button
              onClick={handleCancel}
              variant="outline"
              className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              Cancel
            </Button>
            
            <Button
              onClick={openJoeyWalletStore}
              className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Get Joey Wallet
            </Button>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-700 ${className}`}
    >
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-purple-500/20 rounded-lg">
          <Wallet className="w-6 h-6 text-purple-400" />
        </div>
        <h3 className="text-xl font-bold text-white">Joey Wallet</h3>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-900/20 border border-red-800 rounded-lg flex items-center gap-2">
          <AlertCircle className="w-5 h-5 text-red-400" />
          <span className="text-red-300 text-sm">{error}</span>
        </div>
      )}

      {isConnected && session ? (
        <div className="space-y-4">
          <div className="bg-green-900/20 border border-green-800 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-green-300 font-medium">Connected</span>
            </div>
            <p className="text-gray-300 text-sm">
              Address: <span className="font-mono">{formatJoeyWalletAddress(session.address)}</span>
            </p>
          </div>

          <Button
            onClick={handleDisconnect}
            variant="outline"
            className="w-full border-red-600 text-red-400 hover:bg-red-900/20"
          >
            Disconnect Joey Wallet
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          <p className="text-gray-300 text-sm">
            Connect your Joey Wallet to access XRPL features and make payments.
          </p>

          <Button
            onClick={handleConnect}
            disabled={isConnecting}
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
          >
            {isConnecting ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                Connecting...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Wallet className="w-4 h-4" />
                Connect Joey Wallet
              </div>
            )}
          </Button>

          <div className="text-center">
            <button
              onClick={openJoeyWalletStore}
              className="text-purple-400 hover:text-purple-300 text-sm underline flex items-center justify-center gap-1"
            >
              <ExternalLink className="w-3 h-3" />
              Don't have Joey Wallet? Get it here
            </button>
          </div>
        </div>
      )}
    </motion.div>
  )
}