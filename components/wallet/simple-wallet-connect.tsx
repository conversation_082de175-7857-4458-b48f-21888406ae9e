"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/auth-context'
import { createClient } from '@/utils/supabase/client'

declare global {
  interface Window {
    XummPkce: any;
  }
}

interface SimpleWalletConnectProps {
  onConnected?: (walletAddress: string) => void;
  showCard?: boolean;
}

export function SimpleWalletConnect({ 
  onConnected, 
  showCard = true 
}: SimpleWalletConnectProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');
  const [xummPkce, setXummPkce] = useState<any>(null);
  const [sdkReady, setSdkReady] = useState(false);

  // Load XUMM SDK
  useEffect(() => {
    const loadXummSDK = () => {
      if (window.XummPkce) {
        setSdkReady(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://xumm.app/assets/cdn/xumm.min.js';
      script.onload = () => {
        setSdkReady(true);
      };
      script.onerror = () => {
        console.error('Failed to load XUMM SDK');
      };
      document.head.appendChild(script);
    };

    loadXummSDK();
  }, []);

  // Initialize XUMM PKCE when SDK is ready
  useEffect(() => {
    if (!sdkReady || xummPkce) return;

    try {
      const apiKey = process.env.NEXT_PUBLIC_XAMAN_API_KEY;
      if (!apiKey) {
        console.error('Missing Xaman API key');
        return;
      }
      
      const redirectUrl = typeof window !== 'undefined'
        ? `${window.location.origin}/dashboard`
        : 'https://fuse.vip/dashboard';

      const pkce = new window.XummPkce(apiKey, {
        implicit: true,
        redirectUrl: redirectUrl
      });

      setXummPkce(pkce);

      // Check if already connected
      pkce.state().then((state: any) => {
        if (state?.me?.sub) {
          setWalletAddress(state.me.sub);
          setIsConnected(true);
          
          // Save to profile if user is logged in
          if (user?.id) {
            saveWalletToProfile(state.me.sub);
          }
          
          if (onConnected) {
            onConnected(state.me.sub);
          }
        }
      }).catch((error: any) => {
        console.error('Error checking wallet state:', error);
      });

      // Listen for authorization events
      pkce.on('success', () => {
        pkce.state().then((state: any) => {
          if (state?.me?.sub) {
            setWalletAddress(state.me.sub);
            setIsConnected(true);
            setIsLoading(false);
            
            // Save to profile if user is logged in
            if (user?.id) {
              saveWalletToProfile(state.me.sub);
            }
            
            if (onConnected) {
              onConnected(state.me.sub);
            }
          }
        });
      });

      pkce.on('error', (error: any) => {
        console.error('XUMM authorization error:', error);
        setIsLoading(false);
      });

    } catch (error) {
      console.error('Failed to initialize XUMM PKCE:', error);
    }
  }, [sdkReady, user?.id, onConnected]);

  const saveWalletToProfile = async (address: string, userToken?: string) => {
    if (!user?.id) return;

    try {
      // Use the new wallet connection API route instead of direct Supabase calls
      const response = await fetch('/api/wallet-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: user.id,
          walletAddress: address,
          userToken: userToken,
          action: 'connect'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error saving wallet to profile via API:', errorData.error);
        return;
      }

      const result = await response.json();
      console.log('✅ Wallet saved to profile successfully via API:', result);
    } catch (error) {
      console.error('Error saving wallet to profile via API:', error);
    }
  };

  const handleConnect = async () => {
    if (!xummPkce) {
      console.error('XUMM PKCE not initialized');
      return;
    }

    setIsLoading(true);
    
    try {
      await xummPkce.authorize();
    } catch (error) {
      console.error('Failed to connect wallet:', error);
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    if (!xummPkce) return;

    try {
      await xummPkce.logout();
      setWalletAddress('');
      setIsConnected(false);
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    }
  };

  const content = (
    <>
      {isConnected ? (
        <div className="space-y-4">
          <div className="p-4 bg-green-900/20 border border-green-800 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium text-green-400">Connected</span>
            </div>
            <div className="mt-2 text-xs text-gray-300 break-all">
              {walletAddress}
            </div>
          </div>

          <Button
            variant="outline"
            className="w-full"
            onClick={handleDisconnect}
          >
            Disconnect Wallet
          </Button>

          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Wallet disconnection will not affect your app login
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <Button
            className="w-full bg-blue-600 hover:bg-blue-700"
            onClick={handleConnect}
            disabled={isLoading || !xummPkce}
          >
            {isLoading ? 'Connecting...' : !xummPkce ? 'Loading...' : 'Connect Xaman Wallet'}
          </Button>

          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Connect your Xaman wallet to manage FUSE tokens and XRP payments
          </div>
        </div>
      )}
    </>
  );

  if (!showCard) return content;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>Xaman Wallet</span>
          {isConnected && (
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          )}
        </CardTitle>
        <CardDescription>
          Connect your Xaman wallet to manage FUSE tokens and XRP payments.
          Wallet connection is independent of your app login.
        </CardDescription>
      </CardHeader>
      <CardContent>{content}</CardContent>
    </Card>
  );
}
