'use client';

import { useEffect, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import { useAuth } from '@/contexts/auth-context';
import { walletService, UserWallet } from '@/lib/wallet-service';
import { authStateManager } from '@/lib/auth-state-manager';

// Types for Xaman OAuth2 PKCE
interface XummPkceState {
  me?: {
    sub: string;
    account: string;
    name?: string;
  };
  sdk?: any;
}

interface XummPkce {
  authorize: () => Promise<any>;
  logout: () => Promise<void>;
  state: () => Promise<XummPkceState>;
  on: (event: string, callback: (data?: any) => void) => void;
}

declare global {
  interface Window {
    XummPkce: new (apiKey: string, options?: {
      implicit?: boolean;
      redirectUrl?: string;
    }) => XummPkce;
  }
}

interface XamanWalletConnectProps {
  onConnected?: () => void;
  showCard?: boolean;
}

export function XamanWalletConnect({
  onConnected,
  showCard = true,
}: XamanWalletConnectProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');
  const [xummPkce, setXummPkce] = useState<XummPkce | null>(null);
  const [accountInfo, setAccountInfo] = useState<any>(null);
  const [userWallets, setUserWallets] = useState<UserWallet[]>([]);
  const [primaryWallet, setPrimaryWallet] = useState<UserWallet | null>(null);

  // Initialize Xaman OAuth2 PKCE SDK
  useEffect(() => {
    const loadXummPkce = () => {
      if (typeof window === 'undefined' || window.XummPkce) {
        initializeXummPkce();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://xumm.app/assets/cdn/xumm-oauth2-pkce.min.js?v=2.7.1';
      script.async = true;

      script.onload = () => {
        initializeXummPkce();
      };

      script.onerror = () => {
        console.error('Failed to load Xaman OAuth2 PKCE SDK');
      };

      document.head.appendChild(script);
    };

    loadXummPkce();
  }, []);

  const initializeXummPkce = useCallback(() => {
    if (!window.XummPkce) return;

    const apiKey = process.env.NEXT_PUBLIC_XAMAN_API_KEY;
    if (!apiKey) {
      console.error('Missing Xaman API key');
      return;
    }

    try {
      // Determine the correct redirect URL based on environment
      const getRedirectUrl = () => {
        if (typeof window === 'undefined') return 'https://fuse.vip/wallet-connected';

        // For production, always use the production domain
        if (window.location.hostname === 'fuse.vip' || window.location.hostname === 'www.fuse.vip') {
          return 'https://fuse.vip/wallet-connected';
        }

        // For development, use localhost
        return window.location.origin + '/wallet-connected';
      };

      const redirectUrl = getRedirectUrl();
      console.log('🔧 Xaman SDK - Using redirect URL:', redirectUrl);

      const xumm = new window.XummPkce(apiKey, {
        implicit: true, // Allows moving from social browser to stock browser
        redirectUrl: redirectUrl
      });

      setXummPkce(xumm);

      // Set up event listeners
      xumm.on('error', (error) => {
        console.error('Xaman error:', error);
        setIsLoading(false);
      });

      xumm.on('success', () => {
        checkWalletState(xumm);
      });

      xumm.on('retrieved', () => {
        checkWalletState(xumm);
      });

      // Check initial state
      checkWalletState(xumm);
    } catch (error) {
      console.error('Failed to initialize Xaman OAuth2 PKCE:', error);
    }
  }, []);

  const checkWalletState = useCallback(async (xumm: XummPkce) => {
    try {
      const state = await xumm.state();
      if (state?.me?.sub) {
        setWalletAddress(state.me.sub);
        setIsConnected(true);

        // Add wallet to user's wallet collection if user is logged in
        if (user?.id) {
          await addWalletToUser(state.me.sub);
          await loadUserWallets();
        }

        if (onConnected) {
          onConnected();
        }
      } else {
        setWalletAddress('');
        setIsConnected(false);
      }
    } catch (error) {
      console.error('Failed to check wallet state:', error);
      setWalletAddress('');
      setIsConnected(false);
    }
  }, [user?.id, onConnected]);

  const loadUserWallets = useCallback(async () => {
    if (!user?.id) return;

    try {
      const wallets = await walletService.getUserWallets(user.id, 'xrp');
      setUserWallets(wallets);

      const primary = await walletService.getPrimaryWallet(user.id, 'xrp');
      setPrimaryWallet(primary);

      // Update local state with primary wallet
      if (primary) {
        setWalletAddress(primary.wallet_address);
        setIsConnected(true);
      }
    } catch (error) {
      console.error('Failed to load user wallets:', error);
    }
  }, [user?.id]);

  const addWalletToUser = useCallback(async (address: string, userToken?: string) => {
    if (!user?.id) return;

    try {
      // Use the new wallet connection API route instead of wallet service
      const response = await fetch('/api/wallet-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: user.id,
          walletAddress: address,
          userToken: userToken,
          action: 'connect'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Failed to connect wallet via API:', errorData.error);
        return;
      }

      const result = await response.json();
      console.log('✅ Wallet connected successfully via API:', result);
    } catch (error) {
      console.error('Failed to add wallet to user via API:', error);
    }
  }, [user?.id]);

  // Load user wallets when user changes
  useEffect(() => {
    if (user?.id) {
      loadUserWallets();
    }
  }, [user?.id, loadUserWallets]);

  const handleConnect = async () => {
    if (!xummPkce) {
      console.error('Xaman SDK not initialized');
      return;
    }

    setIsLoading(true);
    try {
      // Store authentication state for wallet return (if user is logged in)
      let sessionId: string | undefined;
      if (user?.id) {
        try {
          sessionId = authStateManager.storeAuthState({
            user,
            targetRoute: window.location.pathname + window.location.search,
            walletContext: {
              transactionId: undefined, // No specific transaction for connection
            }
          });

          console.log('🔐 [Component] Stored auth state for wallet connection:', {
            sessionId,
            targetRoute: window.location.pathname + window.location.search
          });

          // Update the redirect URL with session ID for this connection
          const baseRedirectUrl = typeof window !== 'undefined' 
            ? (window.location.hostname === 'fuse.vip' || window.location.hostname === 'www.fuse.vip')
              ? 'https://fuse.vip/wallet-connected'
              : window.location.origin + '/wallet-connected'
            : 'https://fuse.vip/wallet-connected';

          const redirectUrl = authStateManager.createWalletReturnUrl(baseRedirectUrl, sessionId);
          
          // Update the redirect URL for this authorization
          if (xummPkce && typeof xummPkce === 'object' && 'redirectUrl' in xummPkce) {
            (xummPkce as any).redirectUrl = redirectUrl;
          }

          console.log('🔧 [Component] Updated Xaman redirect URL for this session:', redirectUrl);
        } catch (stateError) {
          console.warn('⚠️ [Component] Failed to store auth state, proceeding without it:', stateError);
        }
      } else {
        console.log('ℹ️ [Component] User not logged in, skipping auth state storage');
      }

      // Check if mobile device
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      if (isMobile) {
        console.log('Mobile device detected, creating deep link...');
        
        // For mobile, we need to handle the deep link flow differently
        // The authorize method should automatically handle mobile deep links
        const result = await xummPkce.authorize();
        
        if (result) {
          console.log('Authorization initiated for mobile');
          // On mobile, user will be redirected to XAMAN app
          // Keep loading state until they return or timeout
          setTimeout(() => {
            setIsLoading(false);
          }, 30000); // 30 second timeout
        }
      } else {
        console.log('Desktop device detected, using standard authorization...');
        await xummPkce.authorize();
        // Desktop auth is handled by the 'success' event
      }
    } catch (error) {
      console.error('Failed to connect wallet:', error);
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    if (!xummPkce) return;

    setIsLoading(true);
    try {
      await xummPkce.logout();
      setWalletAddress('');
      setIsConnected(false);
      setAccountInfo(null);
      setUserWallets([]);
      setPrimaryWallet(null);

      // Note: We don't remove wallets from the database on disconnect
      // This allows users to reconnect to their existing wallets
      // The wallet connection state is independent of the wallet storage
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetTrustline = async () => {
    if (!xummPkce || !isConnected) {
      console.error('Wallet not connected');
      return;
    }

    setIsLoading(true);
    try {
      const state = await xummPkce.state();
      if (!state?.sdk) {
        throw new Error('SDK not available');
      }

      // Create trustline payload for FUSE token
      const payload = await state.sdk.payload.create({
        TransactionType: 'TrustSet',
        LimitAmount: {
          currency: 'FUSE',
          issuer: 'rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU', // Treasury wallet from memories
          value: '1000000000'
        }
      });

      if (payload.pushed) {
        alert('Trustline payload pushed to your Xaman app.');
      } else {
        // Open the payload in browser/app
        if (payload.next?.always) {
          window.open(payload.next.always, '_blank');
        }
      }
    } catch (error) {
      console.error('Failed to create trustline:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createPayment = async (destination: string, amount: string, currency: string = 'XRP') => {
    if (!xummPkce || !isConnected) {
      throw new Error('Wallet not connected');
    }

    const state = await xummPkce.state();
    if (!state?.sdk) {
      throw new Error('SDK not available');
    }

    const payload = await state.sdk.payload.create({
      TransactionType: 'Payment',
      Destination: destination,
      Amount: currency === 'XRP' ? amount : {
        currency: currency,
        issuer: 'rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU', // Treasury wallet
        value: amount
      }
    });

    return payload;
  };

  const content = (
    <>
      {isConnected ? (
        <div className="space-y-4">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <p className="text-sm font-medium text-green-700 dark:text-green-300">
                Wallet Connected
              </p>
              {userWallets.length > 1 && (
                <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                  {userWallets.length} wallets
                </span>
              )}
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
              {primaryWallet ? `${primaryWallet.nickname || 'Primary Wallet'}` : 'Current Address'}
            </p>
            <p className="font-mono text-sm break-all text-gray-900 dark:text-gray-100">
              {walletAddress}
            </p>
            {primaryWallet && primaryWallet.wallet_address !== walletAddress && (
              <p className="text-xs text-amber-600 dark:text-amber-400 mt-1">
                ⚠️ Connected wallet differs from primary wallet
              </p>
            )}
          </div>

          {accountInfo && (
            <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-md">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Account Balance
              </p>
              <p className="text-xl font-bold">
                {(parseInt(accountInfo.Balance) / 1000000).toFixed(2)} XRP
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Button
              className="w-full bg-orange-600 hover:bg-orange-700"
              onClick={handleSetTrustline}
              disabled={isLoading}
            >
              {isLoading ? 'Creating Trustline...' : 'Set FUSE Trustline'}
            </Button>

            {userWallets.length > 1 && (
              <Button
                variant="secondary"
                className="w-full"
                onClick={() => {
                  // TODO: Open wallet management modal
                  console.log('Manage wallets:', userWallets);
                }}
                disabled={isLoading}
              >
                Manage Wallets ({userWallets.length})
              </Button>
            )}

            <div className="flex space-x-2">
              <Button
                variant="outline"
                className="flex-1"
                onClick={handleDisconnect}
                disabled={isLoading}
              >
                {isLoading ? 'Disconnecting...' : 'Disconnect Wallet'}
              </Button>
              
              {userWallets.length > 0 && (
                <Button
                  variant="destructive"
                  className="flex-1"
                  onClick={async () => {
                    if (user?.id && primaryWallet) {
                      setIsLoading(true);
                      try {
                        await walletService.removeWallet(user.id, primaryWallet.wallet_address);
                        await loadUserWallets();
                        
                        // If we removed the connected wallet, also disconnect
                        if (primaryWallet.wallet_address === walletAddress) {
                          await handleDisconnect();
                        }
                      } catch (error) {
                        console.error('Failed to remove wallet:', error);
                      } finally {
                        setIsLoading(false);
                      }
                    }
                  }}
                  disabled={isLoading}
                >
                  Remove Wallet
                </Button>
              )}
            </div>
          </div>

          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Wallet disconnection will not affect your app login
            {userWallets.length > 0 && (
              <><br />Your wallet addresses are saved for future connections</>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <Button
            className="w-full bg-blue-600 hover:bg-blue-700"
            onClick={handleConnect}
            disabled={isLoading || !xummPkce}
          >
            {isLoading ? 'Connecting...' : !xummPkce ? 'Loading...' : 'Connect Xaman Wallet'}
          </Button>

          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            Connect your Xaman wallet to manage FUSE tokens and XRP payments
          </div>
        </div>
      )}
    </>
  );

  if (!showCard) return content;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>Xaman Wallet</span>
          {isConnected && (
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          )}
        </CardTitle>
        <CardDescription>
          Connect your Xaman wallet to manage FUSE tokens and XRP payments.
          Wallet connection is independent of your app login.
        </CardDescription>
      </CardHeader>
      <CardContent>{content}</CardContent>
    </Card>
  );
}

// Export the createPayment function for external use
export { XamanWalletConnect as default };
