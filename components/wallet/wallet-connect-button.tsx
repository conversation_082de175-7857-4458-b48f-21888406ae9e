"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Wallet } from "lucide-react"
import { useSimpleWallet } from "@/hooks/use-simple-wallet"

interface WalletConnectButtonProps {
  compact?: boolean
  className?: string
}

export function WalletConnectButton({ compact = false, className = "" }: WalletConnectButtonProps) {
  const { isConnected, walletAddress, connect, disconnect, isLoading, sdkReady } = useSimpleWallet()
  const [isHovering, setIsHovering] = useState(false)
  const [accountAddress, setAccountAddress] = useState("...")

  // Update account address when wallet connects
  useEffect(() => {
    if (isConnected && walletAddress) {
      setAccountAddress(walletAddress)
    } else {
      setAccountAddress("...")
    }
  }, [isConnected, walletAddress])

  const handleClick = async () => {
    try {
      if (isConnected) {
        // If connected, disconnect the wallet
        await disconnect()
      } else {
        // If not connected, connect directly
        const result = await connect()
        if (!result) {
          // Connection failed, don't show additional error
          return
        }
      }
    } catch (error) {
      console.error('Wallet operation failed:', error)
    }
  }

  // Compact version for mobile header
  if (compact) {
    return (
      <motion.button
        className={`relative flex items-center justify-center ${
          isConnected ? "bg-green-500 hover:bg-green-600" : "bg-[#3A56FF] hover:bg-[#2A46EF]"
        } text-white p-2.5 rounded-lg shadow-lg ${
          isLoading ? "opacity-50 cursor-not-allowed" : ""
        } transition-colors duration-200 ${className}`}
        whileHover={{ scale: isLoading ? 1 : 1.05 }}
        whileTap={{ scale: isLoading ? 1 : 0.95 }}
        onClick={handleClick}
        disabled={isLoading || !sdkReady}
        aria-label={isConnected ? "Disconnect Wallet" : "Connect Xaman Wallet"}
        title={isConnected ? `Connected: ${accountAddress}` : sdkReady ? "Connect Xaman Wallet" : "Loading wallet service..."}
        style={{
          minWidth: '44px',
          minHeight: '44px',
          WebkitTapHighlightColor: 'transparent'
        }}
      >
        <Wallet className={`h-5 w-5 ${isLoading ? 'animate-pulse' : ''}`} />
        {isConnected && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-[#1A1A1A] shadow-sm" />
        )}
      </motion.button>
    )
  }

  // Full version for desktop
  return (
    <motion.button
      className={`flex items-center space-x-1 ${
        isConnected ? "bg-green-500" : "bg-[#3A56FF]"
      } text-white px-3 py-1.5 rounded-md text-sm font-medium ${
        isLoading ? "opacity-50 cursor-not-allowed" : ""
      } ${className}`}
      whileHover={{ scale: isLoading ? 1 : 1.05 }}
      whileTap={{ scale: isLoading ? 1 : 0.95 }}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onClick={handleClick}
      disabled={isLoading || !sdkReady}
    >
      <Wallet className="h-4 w-4 mr-1" />
      <span>
        {!sdkReady
          ? "Loading..."
          : isLoading
          ? "Connecting..."
          : isConnected
          ? isHovering
            ? "Disconnect"
            : accountAddress !== "..."
              ? `${accountAddress.substring(0, 4)}...${accountAddress.substring(accountAddress.length - 4)}`
              : `${walletAddress.substring(0, 4)}...${walletAddress.substring(walletAddress.length - 4)}`
          : "Connect Wallet"}
      </span>
    </motion.button>
  )
}
