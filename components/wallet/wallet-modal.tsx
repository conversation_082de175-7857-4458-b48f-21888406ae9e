"use client";

import { useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import { XamanWalletConnect } from "./xaman-wallet-connect";

interface WalletModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function WalletModal({ isOpen, onClose }: WalletModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className="relative bg-[#1A1A1A] rounded-lg shadow-xl max-w-md w-full"
            ref={modalRef}
          >
            <div className="flex justify-between items-center p-6 border-b border-gray-700">
              <h3 className="text-xl font-bold text-white">Connect Wallet</h3>
              <button onClick={onClose} className="text-gray-400 hover:text-white transition-colors duration-200">
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="p-6">
              <XamanWalletConnect onConnected={onClose} />
            </div>
            <div className="p-6 border-t border-gray-700">
              <p className="text-sm text-gray-400 text-center">
                By connecting your wallet, you agree to our Terms of Service and Privacy Policy. Read those on our resources page.
              </p>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
