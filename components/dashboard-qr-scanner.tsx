"use client";

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Camera, QrCode, X, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { parseQRData } from '@/lib/qr-code-generator';
import { getSupabaseClient } from '@/lib/supabase';
import { toast } from 'sonner';

interface DashboardQRScannerProps {
  onScanSuccess?: (result: { success: boolean; message: string }) => void;
}

export function DashboardQRScanner({ onScanSuccess }: DashboardQRScannerProps) {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cameraSupported, setCameraSupported] = useState(true);
  const scannerRef = useRef<any>(null);

  // Check camera support
  useEffect(() => {
    const checkCameraSupport = async () => {
      try {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          setCameraSupported(false);
          return;
        }
        
        // Quick test for camera availability
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        stream.getTracks().forEach(track => track.stop());
        setCameraSupported(true);
      } catch (err) {
        setCameraSupported(false);
      }
    };

    checkCameraSupport();
  }, []);

  // Initialize scanner when opened
  useEffect(() => {
    if (!isOpen || !cameraSupported) return;

    const initializeScanner = async () => {
      try {
        setIsScanning(true);
        setError(null);

        const { Html5QrcodeScanner } = await import('html5-qrcode');
        
        if (scannerRef.current) {
          await scannerRef.current.clear();
        }

        scannerRef.current = new Html5QrcodeScanner(
          'dashboard-qr-reader',
          { 
            fps: 10,
            qrbox: { width: 200, height: 200 },
            aspectRatio: 1.0,
            disableFlip: false,
            rememberLastUsedCamera: true,
            showTorchButtonIfSupported: true,
          },
          false
        );

        scannerRef.current.render(
          (decodedText: string) => {
            handleScanSuccess(decodedText);
          },
          (error: string) => {
            if (error.includes('Camera') || error.includes('permission') || error.includes('NotAllowed')) {
              setError('Camera access denied. Please allow camera permissions.');
            }
          }
        );
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize camera';
        setError(errorMessage);
      }
    };

    initializeScanner();

    return () => {
      if (scannerRef.current) {
        scannerRef.current.clear().catch(console.error);
      }
    };
  }, [isOpen, cameraSupported]);

  const handleScanSuccess = async (decodedText: string) => {
    if (isProcessing) return;

    setIsProcessing(true);

    try {
      const qrData = parseQRData(decodedText.trim());
      
      if (!qrData) {
        throw new Error('Invalid QR Code format');
      }

      if (qrData.userId === user?.id) {
        throw new Error("You cannot scan your own QR code");
      }

      let successMessage = "";

      if (qrData.type === 'business') {
        const supabase = getSupabaseClient();
        if (!supabase) {
          throw new Error('Database connection not available');
        }

        // Check if user is a cardholder
        const { data: profile } = await supabase
          .from('profiles')
          .select('is_card_holder')
          .eq('id', user?.id)
          .single();

        if (!profile?.is_card_holder) {
          throw new Error("Only VIP cardholders can earn FUSE rewards. Upgrade to VIP!");
        }

        // Check for cooldown
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const { data: existingInteraction } = await supabase
          .from('qr_interactions')
          .select('id')
          .eq('scanner_user_id', user?.id)
          .eq('scanned_business_id', qrData.userId)
          .eq('interaction_type', 'business_scan')
          .gte('created_at', today.toISOString())
          .lt('created_at', tomorrow.toISOString())
          .single();

        if (existingInteraction) {
          throw new Error("You've already scanned this business today!");
        }

        // Get or create business
        let { data: business } = await supabase
          .from('businesses')
          .select('id, name, category, is_active')
          .eq('id', qrData.userId)
          .single();

        if (!business) {
          // Auto-create FUSE Network business
          const { data: newBusiness } = await supabase
            .from('businesses')
            .insert({
              id: qrData.userId,
              name: `FUSE Network Business`,
              category: 'Business Services',
              is_active: true,
              user_id: null,
              website: 'https://fuse.vip',
              contact_email: '<EMAIL>',
            })
            .select('id, name, category, is_active')
            .single();

          business = newBusiness;
        }

        if (!business?.is_active) {
          throw new Error(`This business is not currently active for FUSE rewards.`);
        }

        // Log interaction
        await supabase.from("qr_interactions").insert({
          scanner_user_id: user?.id,
          scanned_business_id: qrData.userId,
          interaction_type: 'business_scan',
          qr_data: decodedText.trim(),
          interaction_metadata: {
            business_name: business.name,
            fuse_tokens_awarded: 100,
            scan_timestamp: new Date().toISOString()
          }
        });

        successMessage = `🎉 Earned 100 FUSE tokens at ${business.name}!`;
      } else {
        const supabase = getSupabaseClient();
        if (!supabase) {
          throw new Error('Database connection not available');
        }

        // User scan
        await supabase.from("qr_interactions").insert({
          scanner_user_id: user?.id,
          scanned_user_id: qrData.userId,
          interaction_type: 'user_scan',
          qr_data: decodedText.trim(),
        });

        successMessage = `✅ Successfully connected with user!`;
      }

      const result = { success: true, message: successMessage };
      onScanSuccess?.(result);
      toast.success(successMessage);
      setIsOpen(false);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to process QR code";
      const result = { success: false, message: errorMessage };
      onScanSuccess?.(result);
      toast.error(errorMessage);
      setIsOpen(false);
    } finally {
      setIsProcessing(false);
    }
  };

  const openScanner = () => {
    if (!cameraSupported) {
      toast.error('Camera not supported on this device');
      return;
    }
    setIsOpen(true);
    setError(null);
  };

  const closeScanner = () => {
    setIsOpen(false);
    setIsProcessing(false);
    setError(null);
    if (scannerRef.current) {
      scannerRef.current.clear().catch(console.error);
    }
  };

  if (isOpen) {
    return (
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Scan QR Code</h3>
              <Button
                onClick={closeScanner}
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {error ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={closeScanner} variant="outline">
                  Close
                </Button>
              </div>
            ) : (
              <div>
                <div 
                  id="dashboard-qr-reader" 
                  className="w-full rounded-lg overflow-hidden"
                  style={{ minHeight: '250px' }}
                />
                
                {isProcessing && (
                  <div className="mt-4 text-center">
                    <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-lg">
                      <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                      <span className="text-blue-600 text-sm">Processing scan...</span>
                    </div>
                  </div>
                )}

                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-600">
                    Position QR code within the frame to scan
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <Button
      onClick={openScanner}
      className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-3 rounded-xl w-full shadow-lg transform hover:scale-105 transition-all duration-200"
      disabled={!cameraSupported}
    >
      <Camera className="h-5 w-5 mr-2" />
      {cameraSupported ? 'Open Camera Scanner' : 'Camera Not Available'}
    </Button>
  );
}
