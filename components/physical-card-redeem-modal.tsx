"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { getSupabaseClient } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";
import { CreditCard, Gift } from "lucide-react";

interface PhysicalCardRedeemModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function PhysicalCardRedeemModal({ isOpen, onClose }: PhysicalCardRedeemModalProps) {
  const { user } = useAuth();
  const [cardCode, setCardCode] = useState("");
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [statusMsg, setStatusMsg] = useState("");

  const handleCardRedeem = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsRedeeming(true);
    setErrorMsg("");
    setStatusMsg("Redeeming physical card...");

    try {
      // Check if user is authenticated
      if (!user) {
        throw new Error("Please sign in first");
      }

      // First, check if the physical card exists and is valid
      const { data: cardData, error: cardError } = await supabase
        .from('physical_cards')
        .select('*')
        .eq('card_number', cardCode.trim())
        .eq('is_active', true)
        .is('redeemed_at', null)
        .single();

      if (cardError || !cardData) {
        throw new Error("Invalid physical card code or card has already been redeemed. Please check your code and try again.");
      }

      // Check if card is expired
      if (cardData.expiry_date && new Date(cardData.expiry_date) < new Date()) {
        throw new Error("This physical card has expired. Please contact support for assistance.");
      }

      // Get current user ID from auth context
      const userId = user.id;
      
      // Calculate dates
      const now = new Date();
      const oneYearFromNow = new Date(now);
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      
      // Update the physical card record
      const { error: cardUpdateError } = await supabase
        .from('physical_cards')
        .update({
          user_id: userId,
          redeemed_at: now.toISOString(),
          updated_at: now.toISOString()
        })
        .eq('id', cardData.id);
      
      if (cardUpdateError) {
        throw new Error("Failed to update physical card record");
      }

      // Update the user's profile
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_card_holder: true,
          card_tier: cardData.tier || 'Premium',
          membership_start_date: now.toISOString(),
          membership_end_date: oneYearFromNow.toISOString()
        })
        .eq('id', userId);
      
      if (profileError) {
        throw new Error("Failed to update membership status");
      }

      setCardCode("");
      setStatusMsg("Physical card redeemed successfully! Your VIP membership is now active. Redirecting...");
      
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 2000);

    } catch (error: any) {
      console.error("Error redeeming physical card:", error);
      setErrorMsg(error.message);
      setStatusMsg("");
    } finally {
      setIsRedeeming(false);
    }
  };

  const handleClose = () => {
    if (!isRedeeming) {
      setCardCode("");
      setErrorMsg("");
      setStatusMsg("");
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-blue-600" />
            Redeem Physical Card
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Gift className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">Convert Physical to Digital</h4>
                <p className="text-sm text-blue-700">
                  Enter your physical card code (e.g., FUSE-2025001-0131) to convert it to a digital VIP membership.
                </p>
              </div>
            </div>
          </div>

          <form onSubmit={handleCardRedeem} className="space-y-4">
            <div>
              <Input
                placeholder="Enter your physical card code (e.g., FUSE-2025001-0131)"
                value={cardCode}
                onChange={(e) => setCardCode(e.target.value)}
                disabled={isRedeeming}
                className="w-full"
              />
            </div>
            
            {errorMsg && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-700 text-sm">{errorMsg}</p>
              </div>
            )}
            
            {statusMsg && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-green-700 text-sm">{statusMsg}</p>
              </div>
            )}
            
            <div className="flex justify-end gap-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleClose} 
                disabled={isRedeeming}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={!cardCode.trim() || isRedeeming}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isRedeeming ? "Processing..." : "Redeem Physical Card"}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
