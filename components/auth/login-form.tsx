"use client"

import type React from "react"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { authStateManager } from "@/lib/auth-state-manager"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Eye, EyeOff } from "lucide-react"
import { ServiceStatusBanner } from "@/components/ui/service-status-banner"

export function LoginForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState("Signing in...")
  const [showPassword, setShowPassword] = useState(false)
  const { signIn } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!email || !password) {
      setError("Please enter both email and password")
      return
    }

    setIsLoading(true)
    setLoadingMessage("Signing in...")

    try {
      console.log("Attempting login for:", email)

      // Use auth context's signIn method
      const result = await signIn(email, password)

      if (result.error) {
        console.error("Login error:", result.error)
        // Provide more user-friendly error messages
        let userFriendlyError = result.error
        if (result.error.includes("Invalid login credentials")) {
          userFriendlyError = "Invalid email or password. Please check your credentials and try again."
        } else if (result.error.includes("Email not confirmed")) {
          userFriendlyError = "Please check your email and click the confirmation link before signing in."
        } else if (result.error.includes("Too many requests")) {
          userFriendlyError = "Too many login attempts. Please wait a moment before trying again."
        } else if (result.error.includes("temporarily unavailable") || result.error.includes("503")) {
          userFriendlyError = "The authentication service is temporarily unavailable. Please try again in a few minutes."
        } else if (result.error.includes("Network")) {
          userFriendlyError = "Network connection issue. Please check your internet connection and try again."
        }

        setError(userFriendlyError)
        setIsLoading(false)
        return
      }

      console.log("Login successful, loading profile...")

      // Store user info for session recovery
      if (result.data?.user) {
        localStorage.setItem('user_id', result.data.user.id)
        localStorage.setItem('user_email', result.data.user.email || email)
      }

      // Update loading message to show profile loading
      setLoadingMessage("Loading your profile...")
      setError(null)

      // Wait for auth context to update with profile data
      await new Promise(resolve => setTimeout(resolve, 500))

      // Check for wallet return parameters
      const walletReturn = searchParams.get("wallet_return") === "true"
      const sessionId = searchParams.get("session_id")
      
      if (walletReturn && sessionId) {
        console.log("Login: Wallet return detected, restoring auth state")
        setLoadingMessage("Restoring your session...")
        
        try {
          const restoration = authStateManager.restoreAuthState(sessionId)
          
          if (restoration.success && restoration.state) {
            console.log("Login: Auth state restored successfully, redirecting to target route")
            
            // Clean up auth state
            authStateManager.cleanupAuthState(sessionId)
            
            // Redirect to the original target route
            router.push(restoration.state.targetRoute)
            return
          } else {
            console.warn("Login: Failed to restore auth state:", restoration.error)
          }
        } catch (restoreError) {
          console.error("Login: Error during auth state restoration:", restoreError)
        }
      }

      // Standard redirect logic
      const redirectTo = searchParams.get("redirect") || "/dashboard"

      setLoadingMessage("Redirecting to dashboard...")
      console.log("Profile loaded, redirecting to:", redirectTo)

      // Use Next.js router for more reliable navigation
      router.push(redirectTo)

    } catch (err) {
      console.error("Login error:", err)
      setError("An unexpected error occurred. Please try again.")
      setIsLoading(false)
    }
  }

  const handleSignUpClick = () => {
    router.push("/register")
  }

  const handleResetPasswordClick = () => {
    router.push("/reset-password")
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-4">
      <ServiceStatusBanner />
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Sign In</CardTitle>
          <CardDescription>Enter your credentials to access your account</CardDescription>
        </CardHeader>
        <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="••••••••"
                required
                className="pr-10"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> {loadingMessage}
              </>
            ) : (
              "Sign In"
            )}
          </Button>
        </form>
      </CardContent>
      <CardFooter className="flex flex-col items-center space-y-4">
        <p className="text-sm text-muted-foreground">
          Don't have an account?{" "}
          <Button variant="link" className="p-0" onClick={handleSignUpClick}>
            Sign up
          </Button>
        </p>
        <p className="text-sm text-muted-foreground">
          Forgot your password?{" "}
          <Button variant="link" className="p-0" onClick={handleResetPasswordClick}>
            Reset Password
          </Button>
        </p>
      </CardFooter>
      </Card>
    </div>
  )
}
