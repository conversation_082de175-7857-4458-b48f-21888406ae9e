"use client"

import { Suspense } from "react"
import { LoginForm } from "./login-form"
import { Loader2 } from "lucide-react"

function LoginFormFallback() {
  return (
    <div className="w-full max-w-md mx-auto">
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3A56FF]" />
          <p className="text-gray-600">Loading login form...</p>
        </div>
      </div>
    </div>
  )
}

export function LoginFormWrapper() {
  return (
    <Suspense fallback={<LoginFormFallback />}>
      <LoginForm />
    </Suspense>
  )
}
