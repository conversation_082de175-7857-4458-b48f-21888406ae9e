"use client";

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import Link from "next/link"
import { motion } from "framer-motion"
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react"
import { getSupabaseClient } from "@/lib/supabase"
import { BusinessCombobox } from "@/components/business/business-combobox"
import { profileCache } from "@/lib/profile-cache"
import { qrCodeCache } from "@/lib/qr-code-cache"

export function RegisterForm() {
  const { refreshSession } = useAuth()
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [phone, setPhone] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [referringBusinessId, setReferringBusinessId] = useState("");
  const [referringBusinessName, setReferringBusinessName] = useState("");
  const router = useRouter();


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form
    if (!email || !password || !confirmPassword || !firstName || !lastName) {
      setError("All required fields must be filled")
      return
    }
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }
    if (password.length < 6) {
      setError("Password must be at least 6 characters");
      return;
    }


    setIsLoading(true);
    try {
      const supabase = getSupabaseClient();
      if (!supabase) {
        setError("Authentication service unavailable. Please try again.")
        setIsLoading(false)
        return
      }

      // Register the user directly with Supabase
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/dashboard`,
          data: {
            first_name: firstName,
            last_name: lastName,
            phone: phone,
            referring_business_id: referringBusinessId,
          }
        }
      });

      if (signUpError) {
        console.error("Signup error:", signUpError)
        setError(signUpError.message || "Failed to register. Please try again.")
        setIsLoading(false)
        return
      }

      const userId = data?.user?.id
      console.log("User registered successfully:", { userId, email })

      if (!userId) {
        setError("User registration successful but user ID not found. Please try logging in.")
        setIsLoading(false)
        return
      }

      // Sign in the user immediately after signup
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (signInError) {
        setError(signInError.message || "Account created but failed to sign in. Please try logging in.")
        setIsLoading(false)
        return
      }

      // Use optimized profile creation API with caching
      console.log("🔄 Creating profile with optimized flow...")
      
      try {
        const profileResponse = await fetch('/api/auth/create-profile', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            email,
            firstName,
            lastName,
            phone,
            referringBusinessId,
            isBusinessApplicant: false
          }),
        });

        const profileResult = await profileResponse.json();

        if (profileResult.success) {
          console.log("✅ Profile created successfully:", profileResult.message);
          
          // Cache the profile data immediately for faster auth context loading
          if (profileResult.cacheData) {
            profileCache.cacheProfile(userId, profileResult.cacheData.profile);
            profileCache.cacheUserSession(userId, email);
            console.log("📦 Profile and session cached for instant access");
            
            // Preload QR code for the new user (background operation)
            console.log("🔄 Preloading QR code for new user...");
            qrCodeCache.preloadQRCodes([userId]).catch(error => {
              console.warn("⚠️ QR code preloading failed (non-critical):", error);
            });
          }
        } else {
          console.warn("⚠️ Profile creation API failed:", profileResult.error);
          // Continue with registration - fallback mechanisms in place
        }
      } catch (profileError) {
        console.warn("⚠️ Profile creation API error:", profileError);
        // Continue with registration - fallback mechanisms in place
      }



      // Set success and redirect to dashboard for all users
      setSuccess(true)

      // Refresh the auth context to ensure proper synchronization
      try {
        // Use the auth context's refreshSession to sync the new user state
        await refreshSession()

        // Store user info for session recovery using cache service
        profileCache.cacheUserSession(userId, email)

        // Use Next.js router for consistent navigation
        setTimeout(() => {
          router.push("/dashboard")
        }, 1000)
      } catch (refreshError) {
        console.error("Error refreshing session after registration:", refreshError)
        // Even if refresh fails, still redirect - the middleware will handle auth
        setTimeout(() => {
          router.push("/dashboard")
        }, 1000)
      }

      setIsLoading(false)

    } catch (err) {
      console.error("Registration error:", err);
      setError("An unexpected error occurred. Please try again.");
      setIsLoading(false);
    }
  }

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-8">
        {success ? (
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to FUSE.VIP!</h2>
            <p className="text-gray-600 mb-4">
              Your account has been created successfully. You're being redirected to your dashboard where you can explore features and upgrade to VIP.
            </p>
            <div className="flex items-center justify-center">
              <Loader2 className="animate-spin h-5 w-5 mr-2 text-[#3A56FF]" />
              <span className="text-sm text-gray-500">Taking you to your dashboard...</span>
            </div>
            <div className="mt-2 flex justify-center">
              <Link
                href="/dashboard"
                className="inline-block bg-[#3A56FF] text-white px-4 py-2 rounded-md font-medium hover:bg-[#2A46EF] transition-colors"
              >
                Go to Dashboard
              </Link>
            </div>
          </motion.div>
        ) : (
          <>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Create an Account</h2>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-start"
              >
                <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                <span>{error}</span>
              </motion.div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="space-y-6">
                {/* Personal Information Section */}
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                        First Name *
                      </label>
                      <input
                        id="firstName"
                        type="text"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                        Last Name *
                      </label>
                      <input
                        id="lastName"
                        type="text"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address *
                    </label>
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      id="phone"
                      type="tel"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        Password *
                      </label>
                      <input
                        id="password"
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                        placeholder="••••••••"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password *
                      </label>
                      <input
                        id="confirmPassword"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent"
                        placeholder="••••••••"
                        required
                      />
                    </div>
                  </div>
                </div>
                <div className="border-t border-gray-200 pt-4">
                          <h4 className="text-md font-medium text-gray-700 mb-2">Referring Business</h4>
                          <div>
                            <label
                              htmlFor="referringBusinessId"
                              className="block text-sm font-medium text-gray-700 mb-1"
                            >
                              Select Referring Business (Optional)
                            </label>
                            <BusinessCombobox
                              value={referringBusinessId}
                              onChange={setReferringBusinessId}
                              onNameChange={setReferringBusinessName}
                              includeAllBusinesses={true}
                            />
                            <p className="mt-1 text-xs text-gray-500">
                              If you were referred by a business, please select it from the dropdown
                            </p>
                          </div>
                        </div>

              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full mt-6 bg-[#3A56FF] text-white py-2 px-4 rounded-md font-medium hover:bg-[#2A46EF] transition-colors disabled:opacity-70 disabled:cursor-not-allowed flex justify-center items-center"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="animate-spin h-5 w-5 mr-2" />
                    Creating Account...
                  </>
                ) : (
                  "Create Account"
                )}
              </button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{" "}
                <Link href="/login" className="text-[#3A56FF] font-medium hover:underline">
                  Sign in
                </Link>
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
