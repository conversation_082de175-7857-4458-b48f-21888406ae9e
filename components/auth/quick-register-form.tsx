"use client"

import React, { useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Eye, EyeOff, CheckCircle, User, Mail, Lock } from "lucide-react"
import { motion } from "framer-motion"

interface QuickRegisterFormProps {
  onSuccess?: () => void
  showTitle?: boolean
  redirectTo?: string
  className?: string
}

export function QuickRegisterForm({ 
  onSuccess, 
  showTitle = true, 
  redirectTo = "/dashboard",
  className = ""
}: QuickRegisterFormProps) {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [firstName, setFirstName] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [step, setStep] = useState(1) // 1: Email, 2: Name & Password, 3: Success
  
  const { signUp, refreshSession } = useAuth()
  const router = useRouter()

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePassword = (password: string) => {
    // Simplified validation for older users - just minimum length
    return password.length >= 6
  }

  const handleEmailStep = (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!email) {
      setError("Please enter your email address")
      return
    }

    if (!validateEmail(email)) {
      setError("Please enter a valid email address")
      return
    }

    setStep(2)
  }

  const handleFinalStep = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!firstName.trim()) {
      setError("Please enter your first name")
      return
    }

    if (!password) {
      setError("Please create a password")
      return
    }

    if (!validatePassword(password)) {
      setError("Password must be at least 6 characters long")
      return
    }

    setIsLoading(true)

    try {
      const result = await signUp(email, password, {
        first_name: firstName,
        profile_id: crypto.randomUUID(),
        confirmed_at: new Date().toISOString(),
      })

      if (result.error) {
        setError(result.error)
        setIsLoading(false)
        return
      }

      // Show success step
      setStep(3)
      
      // Refresh session and redirect after a brief delay
      setTimeout(async () => {
        try {
          await refreshSession()
          if (onSuccess) {
            onSuccess()
          } else {
            router.push(redirectTo)
          }
        } catch (refreshError) {
          console.error("Error refreshing session:", refreshError)
          router.push(redirectTo)
        }
      }, 2000)

    } catch (err) {
      console.error("Registration error:", err)
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleBack = () => {
    setStep(step - 1)
    setError(null)
  }

  return (
    <Card className={`w-full max-w-md mx-auto ${className}`}>
      <CardHeader className="text-center pb-4 px-6 pt-8">
        {showTitle && (
          <>
            <CardTitle className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
              {step === 1 && "Get Started"}
              {step === 2 && "Almost Done!"}
              {step === 3 && "Welcome!"}
            </CardTitle>
            <CardDescription className="text-lg md:text-xl text-gray-600">
              {step === 1 && "Enter your email to begin"}
              {step === 2 && "Just a few more details"}
              {step === 3 && "Your account is ready"}
            </CardDescription>
          </>
        )}
        
        {/* Progress Indicator */}
        <div className="flex justify-center mt-4">
          <div className="flex space-x-2">
            {[1, 2, 3].map((num) => (
              <div
                key={num}
                className={`w-3 h-3 rounded-full transition-colors ${
                  num <= step ? "bg-blue-600" : "bg-gray-300"
                }`}
              />
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent className="px-6 pb-8">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription className="text-base md:text-lg font-medium">{error}</AlertDescription>
          </Alert>
        )}

        {/* Step 1: Email */}
        {step === 1 && (
          <motion.form
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            onSubmit={handleEmailStep}
            className="space-y-6"
          >
            <div className="space-y-3">
              <Label htmlFor="email" className="text-lg font-medium flex items-center">
                <Mail className="h-5 w-5 mr-2 text-blue-600" />
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="h-14 text-lg px-4"
                autoFocus
                required
              />
            </div>
            <Button 
              type="submit" 
              className="w-full h-14 text-lg font-semibold bg-blue-600 hover:bg-blue-700"
            >
              Continue
            </Button>
          </motion.form>
        )}

        {/* Step 2: Name & Password */}
        {step === 2 && (
          <motion.form
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            onSubmit={handleFinalStep}
            className="space-y-6"
          >
            <div className="space-y-3">
              <Label htmlFor="firstName" className="text-lg font-medium flex items-center">
                <User className="h-5 w-5 mr-2 text-blue-600" />
                First Name
              </Label>
              <Input
                id="firstName"
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                placeholder="Your first name"
                className="h-14 text-lg px-4"
                autoFocus
                required
              />
            </div>
            
            <div className="space-y-3">
              <Label htmlFor="password" className="text-lg font-medium flex items-center">
                <Lock className="h-5 w-5 mr-2 text-blue-600" />
                Create Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="At least 6 characters"
                  className="h-14 text-lg px-4 pr-14"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? (
                    <EyeOff className="h-6 w-6" />
                  ) : (
                    <Eye className="h-6 w-6" />
                  )}
                </button>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleBack}
                className="flex-1 h-14 text-lg"
              >
                Back
              </Button>
              <Button 
                type="submit" 
                disabled={isLoading}
                className="flex-1 h-14 text-lg font-semibold bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Account"
                )}
              </Button>
            </div>
          </motion.form>
        )}

        {/* Step 3: Success */}
        {step === 3 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-8"
          >
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Account Created Successfully!
            </h3>
            <p className="text-gray-600 mb-6">
              Redirecting you to your dashboard...
            </p>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  )
}
