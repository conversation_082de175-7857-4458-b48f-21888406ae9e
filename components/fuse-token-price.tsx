"use client"

import { useEffect, useState, useCallback } from "react"
import { 
  TrendingUp, 
  TrendingDown, 
  RefreshCw, 
  ExternalLink, 
  Activity,
  DollarSign,
  BarChart3,
  Zap,
  AlertTriangle
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface FuseTokenData {
  price: number
  priceUSD: number
  volume24h: number
  change24h: number
  lastUpdated: number
  source: string
  marketCap?: number
  totalSupply?: number
  price24hAgo?: number
  priceHistory?: Array<{timestamp: number, price: number}>
}

interface FuseTokenPriceProps {
  variant?: 'compact' | 'detailed' | 'hero'
  showVolume?: boolean
  showChange?: boolean
  showSources?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
  className?: string
}

export function FuseTokenPrice({
  variant = 'compact',
  showVolume = true,
  showChange = true,
  showSources = false,
  autoRefresh = false, // Disabled live tracking
  refreshInterval = 60000,
  className = ""
}: FuseTokenPriceProps) {
  const [tokenData, setTokenData] = useState<FuseTokenData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [xrpPrice, setXrpPrice] = useState<number>(2.30)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  // Static price history data for display
  const priceHistory = [
    { timestamp: Date.now() - 86400000, price: 0.000067, label: '24h ago' },
    { timestamp: Date.now() - 43200000, price: 0.000089, label: '12h ago' },
    { timestamp: Date.now() - 21600000, price: 0.000156, label: '6h ago' },
    { timestamp: Date.now() - 10800000, price: 0.000234, label: '3h ago' },
    { timestamp: Date.now() - 3600000, price: 0.000445, label: '1h ago' },
    { timestamp: Date.now(), price: 0.004556, label: 'Current' }
  ]

  const baselinePrice = 0.000067 // Starting price for % calculation
  const currentPrice = 0.004556 // Current static price

  // Calculate percentage change from baseline
  const calculatePercentageChange = useCallback((currentPrice: number, basePrice: number = baselinePrice) => {
    if (basePrice === 0) return 0
    return ((currentPrice - basePrice) / basePrice) * 100
  }, [baselinePrice])

  // Fetch XRP price for USD conversion
  const fetchXrpPrice = useCallback(async () => {
    try {
      const response = await fetch("https://api.coingecko.com/api/v3/simple/price?ids=ripple&vs_currencies=usd")
      const data = await response.json()
      setXrpPrice(data.ripple.usd || 2.30)
    } catch (error) {
      console.error("Error fetching XRP price:", error)
      setXrpPrice(2.30) // Fallback
    }
  }, [])

  // Initialize static FUSE token data
  const initializeFuseTokenData = useCallback(() => {
    setLoading(true)
    setError(null)

    const currentTime = Date.now()
    const currentChange = calculatePercentageChange(currentPrice)

    // Static data sources showing 6800% increase
    const sources = [
      {
        price: currentPrice,
        priceUSD: currentPrice * xrpPrice,
        volume24h: 15420.50,
        change24h: currentChange,
        lastUpdated: currentTime,
        source: 'Magnetic DEX',
        totalSupply: 1000000000,
        price24hAgo: baselinePrice
      },
      {
        price: currentPrice * 1.001, // Slight variation
        priceUSD: (currentPrice * 1.001) * xrpPrice,
        volume24h: 14890.25,
        change24h: calculatePercentageChange(currentPrice * 1.001),
        lastUpdated: currentTime,
        source: 'XRPL Network',
        marketCap: (currentPrice * xrpPrice) * 1000000000,
        price24hAgo: baselinePrice
      },
      {
        price: currentPrice * 0.999, // Slight variation
        priceUSD: (currentPrice * 0.999) * xrpPrice,
        volume24h: 16200.75,
        change24h: calculatePercentageChange(currentPrice * 0.999),
        lastUpdated: currentTime,
        source: 'Trading Aggregator',
        totalSupply: 1000000000,
        price24hAgo: baselinePrice
      }
    ]

    setTokenData(sources)
    setLastRefresh(new Date())
    setLoading(false)
  }, [xrpPrice, baselinePrice, currentPrice, calculatePercentageChange])

  // Initialize static data on component mount
  useEffect(() => {
    fetchXrpPrice()
    initializeFuseTokenData()
  }, [fetchXrpPrice, initializeFuseTokenData])

  // Calculate average price from multiple sources
  const averageData = tokenData.length > 0 ? {
    price: tokenData.reduce((sum, data) => sum + data.price, 0) / tokenData.length,
    priceUSD: tokenData.reduce((sum, data) => sum + data.priceUSD, 0) / tokenData.length,
    volume24h: tokenData.reduce((sum, data) => sum + data.volume24h, 0) / tokenData.length,
    change24h: tokenData.reduce((sum, data) => sum + data.change24h, 0) / tokenData.length,
  } : null

  const handleRefresh = () => {
    fetchXrpPrice()
    initializeFuseTokenData()
  }

  const formatPrice = (price: number, decimals: number = 6) => {
    return price.toFixed(decimals)
  }

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) return `${(volume / 1000000).toFixed(1)}M`
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`
    return volume.toFixed(0)
  }

  const formatChange = (change: number) => {
    const sign = change >= 0 ? '+' : ''
    return `${sign}${change.toFixed(1)}%`
  }

  const getChangeColor = (change: number) => {
    if (change >= 5000) return 'text-yellow-400 animate-pulse' // Ultra extreme gains - pulsing gold
    if (change >= 3000) return 'text-yellow-500' // Extreme gains - gold
    if (change >= 1000) return 'text-orange-400' // Very high gains - orange
    if (change >= 100) return 'text-green-400' // High gains - bright green
    if (change >= 0) return 'text-green-500' // Regular gains
    return 'text-red-500' // Losses
  }

  const getChangeIcon = (change: number) => {
    if (change >= 5000) return '🔥🚀' // Fire + rocket for ultra extreme gains
    if (change >= 3000) return '🚀💎' // Rocket + diamond for extreme gains
    if (change >= 1000) return '🚀' // Rocket for very high gains
    if (change >= 100) return '📈' // Chart for high gains
    return change >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />
  }

  const getChangeLabel = (change: number) => {
    if (change >= 5000) return 'ULTRA EXPLOSIVE!'
    if (change >= 3000) return 'EXPLOSIVE!'
    if (change >= 1000) return 'ON FIRE!'
    return ''
  }

  // Price History Graph Component
  const PriceHistoryGraph = () => {
    const maxPrice = Math.max(...priceHistory.map(p => p.price))
    const minPrice = Math.min(...priceHistory.map(p => p.price))
    const priceRange = maxPrice - minPrice

    return (
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-sm font-semibold text-gray-700 mb-3">FUSE Token Price History (24h)</h3>
        <div className="relative h-32 bg-white rounded border">
          <svg className="w-full h-full" viewBox="0 0 400 120" preserveAspectRatio="none">
            {/* Grid lines */}
            <defs>
              <pattern id="grid" width="40" height="24" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 24" fill="none" stroke="#f0f0f0" strokeWidth="1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />

            {/* Price line */}
            <polyline
              fill="none"
              stroke="#10b981"
              strokeWidth="2"
              points={priceHistory.map((point, index) => {
                const x = (index / (priceHistory.length - 1)) * 380 + 10
                const y = 110 - ((point.price - minPrice) / priceRange) * 100
                return `${x},${y}`
              }).join(' ')}
            />

            {/* Data points */}
            {priceHistory.map((point, index) => {
              const x = (index / (priceHistory.length - 1)) * 380 + 10
              const y = 110 - ((point.price - minPrice) / priceRange) * 100
              return (
                <circle
                  key={index}
                  cx={x}
                  cy={y}
                  r="3"
                  fill="#10b981"
                  className="hover:r-4 transition-all"
                />
              )
            })}
          </svg>

          {/* Price labels */}
          <div className="absolute top-2 left-2 text-xs text-gray-500">
            ${formatPrice(maxPrice * xrpPrice, 6)}
          </div>
          <div className="absolute bottom-2 left-2 text-xs text-gray-500">
            ${formatPrice(minPrice * xrpPrice, 6)}
          </div>
        </div>

        {/* Time labels */}
        <div className="flex justify-between mt-2 text-xs text-gray-500">
          {priceHistory.map((point, index) => (
            <span key={index} className={index % 2 === 0 ? '' : 'opacity-50'}>
              {point.label}
            </span>
          ))}
        </div>

        {/* Summary stats */}
        <div className="mt-4 grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-xs text-gray-500">24h Low</div>
            <div className="font-semibold text-sm">${formatPrice(minPrice * xrpPrice, 6)}</div>
          </div>
          <div>
            <div className="text-xs text-gray-500">24h High</div>
            <div className="font-semibold text-sm">${formatPrice(maxPrice * xrpPrice, 6)}</div>
          </div>
          <div>
            <div className="text-xs text-gray-500">24h Change</div>
            <div className="font-semibold text-sm text-green-600">
              {formatChange(calculatePercentageChange(currentPrice))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full flex items-center justify-center">
            <Zap className="w-3 h-3 text-white" />
          </div>
          <span className="text-sm font-medium">FUSE</span>
        </div>
        
        {loading ? (
          <div className="flex items-center space-x-1">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span className="text-sm">Loading...</span>
          </div>
        ) : averageData ? (
          <div className="flex items-center space-x-3">
            <div className="text-right">
              <div className="font-bold">${formatPrice(averageData.priceUSD, 4)}</div>
              <div className="text-xs text-gray-500">{formatPrice(averageData.price)} XRP</div>
            </div>
            {showChange && (
              <div className={`flex items-center space-x-1 ${getChangeColor(averageData.change24h)} animate-pulse`}>
                <span className="text-xs">{getChangeIcon(averageData.change24h)}</span>
                <span className="text-xs font-bold">{formatChange(averageData.change24h)}</span>
              </div>
            )}
          </div>
        ) : (
          <span className="text-sm text-gray-500">Price unavailable</span>
        )}
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          className="p-1 h-auto"
          disabled={loading}
        >
          <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </div>
    )
  }

  if (variant === 'hero') {
    return (
      <div
        className={`bg-gradient-to-br from-[#0f172a] to-[#1e293b] rounded-2xl p-8 text-white relative overflow-hidden ${className}`}
      >
        {/* Animated background */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#3A56FF]/10 via-transparent to-[#6366f1]/10 animate-pulse"></div>
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full flex items-center justify-center">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">FUSE Token</h3>
                <p className="text-white/70">Price History & Analytics</p>
              </div>
            </div>

            <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
              <BarChart3 className="w-3 h-3 mr-1" />
              ANALYTICS
            </Badge>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4" />
              <p>Loading live price data...</p>
            </div>
          ) : averageData ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Price */}
                <div className="text-center">
                  <div className="text-4xl font-bold mb-2">
                    ${formatPrice(averageData.priceUSD, 4)}
                  </div>
                  <div className="text-white/70 mb-1">{formatPrice(averageData.price)} XRP</div>
                  <div className={`flex items-center justify-center space-x-2 ${getChangeColor(averageData.change24h)} animate-bounce`}>
                    <span className="text-3xl">{getChangeIcon(averageData.change24h)}</span>
                    <span className="font-bold text-xl">{formatChange(averageData.change24h)}</span>
                    {getChangeLabel(averageData.change24h) && (
                      <span className="text-yellow-300 text-sm font-medium bg-yellow-500/20 px-2 py-1 rounded-full animate-pulse">
                        {getChangeLabel(averageData.change24h)}
                      </span>
                    )}
                  </div>
                  {averageData.change24h >= 5000 && (
                    <div className="text-center mt-2">
                      <span className="text-yellow-200 text-xs font-bold bg-gradient-to-r from-yellow-600/30 to-orange-600/30 px-3 py-1 rounded-full animate-pulse border border-yellow-400/50">
                        ⚡ BREAKING ALL RECORDS! ⚡
                      </span>
                    </div>
                  )}
                </div>

                {/* Volume */}
                {showVolume && (
                  <div className="text-center">
                    <div className="text-2xl font-bold mb-2">
                      {formatVolume(averageData.volume24h)} XRP
                    </div>
                    <div className="text-white/70">24h Volume</div>
                  </div>
                )}

                {/* Trading Links */}
                <div className="text-center space-y-3">
                  <a
                    href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center space-x-2 bg-[#3A56FF] hover:bg-[#2563eb] px-6 py-3 rounded-lg transition-colors font-medium"
                  >
                    <ExternalLink className="w-4 h-4" />
                    <span>Trade on Magnetic DEX</span>
                  </a>
                  <div className="text-xs text-white/50">
                    Last updated: {lastRefresh.toLocaleTimeString()}
                  </div>
                </div>
              </div>

              {/* Price History Graph */}
              <div className="mt-8">
                <PriceHistoryGraph />
              </div>
            </>
          ) : (
            <div className="text-center py-8 text-white/70">
              <AlertTriangle className="w-8 h-8 mx-auto mb-4" />
              <p>Unable to fetch price data</p>
              <Button onClick={handleRefresh} variant="outline" className="mt-4">
                Try Again
              </Button>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Default: detailed view
  return (
    <Card className={`${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full flex items-center justify-center">
              <Zap className="w-4 h-4 text-white" />
            </div>
            <span>FUSE Token Price</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">
            <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
            <p className="text-sm text-gray-500">Loading...</p>
          </div>
        ) : averageData ? (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-2xl font-bold">${formatPrice(averageData.priceUSD, 4)}</div>
                <div className="text-sm text-gray-500">{formatPrice(averageData.price)} XRP</div>
              </div>
              <div className="text-right">
                <div className={`flex items-center justify-end space-x-2 ${getChangeColor(averageData.change24h)} animate-pulse`}>
                  <span>{getChangeIcon(averageData.change24h)}</span>
                  <span className="font-bold text-lg">{formatChange(averageData.change24h)}</span>
                </div>
                <div className="text-sm text-gray-500">24h Change</div>
                {averageData.change24h >= 1000 && (
                  <div className="text-xs text-yellow-600 font-bold mt-1 animate-bounce">
                    🔥 ON FIRE! 🔥
                  </div>
                )}
              </div>
            </div>

            {showVolume && (
              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <BarChart3 className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-500">24h Volume</span>
                </div>
                <div className="font-medium">{formatVolume(averageData.volume24h)} XRP</div>
              </div>
            )}

            {showSources && tokenData.length > 1 && (
              <div className="pt-4 border-t">
                <div className="text-sm text-gray-500 mb-2">Price Sources:</div>
                <div className="space-y-2">
                  {tokenData.map((data, index) => (
                    <div key={index} className="flex justify-between items-center text-xs">
                      <span>{data.source}</span>
                      <span>${formatPrice(data.priceUSD, 4)}</span>
                    </div>
                  ))}
                </div>
                <div className="mt-3 pt-2 border-t border-gray-200">
                  <div className="text-xs text-gray-500 mb-1">Trading Platform:</div>
                  <div className="flex flex-wrap gap-2">
                    <a
                      href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center space-x-1 bg-blue-100 hover:bg-blue-200 text-blue-800 px-2 py-1 rounded text-xs transition-colors"
                    >
                      <ExternalLink className="w-3 h-3" />
                      <span>Magnetic DEX</span>
                    </a>
                  </div>
                </div>
              </div>
            )}

            {/* Price History Graph */}
            <PriceHistoryGraph />
          </div>
        ) : (
          <div className="text-center py-4 text-gray-500">
            <AlertTriangle className="w-6 h-6 mx-auto mb-2" />
            <p className="text-sm">Price data unavailable</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
