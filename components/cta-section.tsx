"use client"
import { motion } from "framer-motion"
import { AnimatedSection } from "./animated-section"
import Image from "next/image"
import { GoogleCalendarButton } from "./google-calendar-button"

export function CtaSection() {
  return (
    <section className="bg-[#1A1A1A] py-12 my-12">
      <div className="container mx-auto px-4 text-center">
        <AnimatedSection>
          <h2 className="text-white text-3xl md:text-4xl font-bold mb-4">
            Ready to discover
            <motion.span
              className="inline-flex items-center ml-2"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <div className="mr-1">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png"
                  width={24}
                  height={24}
                  alt="Fuse.vip Logo"
                  className="w-6 h-6"
                />
              </div>
              <span>Fuse.Vip</span>
            </motion.span>
            ?
          </h2>
        </AnimatedSection>

        <AnimatedSection delay={0.2}>
          <p className="text-white/70 mb-8 max-w-md mx-auto">
            Loyalty reimagined. Commerce reconnected. Join the future of customer engagement.
          </p>
        </AnimatedSection>

        <AnimatedSection delay={0.4}>
          <div className="flex justify-center">
            <GoogleCalendarButton label="Book a call now" color="#3A56FF" className="inline-block" />
          </div>
        </AnimatedSection>
      </div>
    </section>
  )
}
