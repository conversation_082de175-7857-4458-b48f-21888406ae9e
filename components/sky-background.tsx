"use client"

import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import DaySky from "./backgrounds/day-sky"
import NightSky from "./backgrounds/night-sky"

interface SkyBackgroundProps {
  className?: string
}

export function SkyBackground({ className = "" }: SkyBackgroundProps) {
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <div className={`fixed inset-0 -z-10 ${className}`}>
      {theme === "dark" ? <NightSky /> : <DaySky />}
    </div>
  )
}