"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface CubeProps {
  title: string
  content: React.ReactNode
  color: string
  delay: number
  className?: string
}

const Cube = ({ title, content, color, delay, className }: CubeProps) => {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      className={cn(
        "relative p-6 rounded-xl overflow-hidden transition-all duration-500",
        "shadow-[0_0_15px_rgba(0,0,0,0.1)] backdrop-blur-sm",
        "transform perspective-1000",
        className,
      )}
      style={{
        background: `linear-gradient(135deg, ${color}55, ${color}77)`,
        boxShadow: isHovered ? `0 0 25px ${color}88` : `0 0 15px ${color}44`,
        border: `1px solid ${color}33`,
      }}
      initial={{ opacity: 0, y: 50, rotateX: -10 }}
      animate={{ opacity: 1, y: 0, rotateX: 0 }}
      transition={{
        duration: 0.8,
        delay: delay * 0.2,
        ease: "easeOut",
      }}
      whileHover={{
        scale: 1.03,
        rotateY: 5,
        boxShadow: `0 0 30px ${color}aa`,
        transition: { duration: 0.3 },
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <motion.div
        className="absolute inset-0 opacity-20"
        style={{
          background: `radial-gradient(circle at ${isHovered ? "60%" : "40%"} ${isHovered ? "40%" : "60%"}, ${color}88, transparent 70%)`,
        }}
        animate={{
          opacity: isHovered ? 0.4 : 0.2,
        }}
        transition={{ duration: 1 }}
      />

      <div className="relative z-10">
        <h3 className="text-xl font-bold mb-3 text-white drop-shadow-md">{title}</h3>
        <div className="text-white/90 text-sm leading-relaxed">{content}</div>
      </div>

      <motion.div
        className="absolute bottom-0 right-0 w-24 h-24 rounded-full opacity-20"
        style={{ background: color }}
        animate={{
          scale: isHovered ? 1.2 : 1,
          x: isHovered ? -5 : 10,
          y: isHovered ? -5 : 10,
        }}
        transition={{ duration: 1 }}
      />
    </motion.div>
  )
}

export function AnimatedCubes() {
  const cubes = [
    {
      title: "The One Tool",
      color: "#3A56FF",
      content: (
        <>
          <p className="mb-2">
            You've seen the charts with 100+ AI tools for productivity, marketing, writing, design, video, and chat.
          </p>
          <p className="mb-2">But what if small businesses didn't have to chase them all?</p>
          <p className="mb-2">What if it was just… one tool? One network?</p>
          <p className="font-bold">Welcome to FUSE.vip.</p>
        </>
      ),
    },
    {
      title: "Small Business Relief",
      color: "#FF3A2F",
      content: (
        <>
          <p className="mb-2">Small businesses are overwhelmed.</p>
          <p className="mb-2">Too many tech tools.</p>
          <p className="mb-2">Too much overhead.</p>
          <p className="mb-2">Not enough time.</p>
          <p className="mb-2">
            They don't need <em>more software</em>.
          </p>
          <p className="font-bold">
            They need one platform that <em>just works</em>.
          </p>
          <p>That's what FUSE delivers.</p>
        </>
      ),
    },
    {
      title: "All-In-One Solution",
      color: "#FFD700",
      content: (
        <>
          <p className="mb-2">Imagine running your local business with:</p>
          <p className="mb-2">Personalized marketing on autopilot</p>
          <p className="mb-2">Instant customer follow-ups</p>
          <p className="mb-2">Loyalty tracking + AI analytics</p>
          <p className="mb-2">One-click content for emails & promos</p>
          <p className="mb-2">Real-time sales insights</p>
          <p>No subscription stacking. No learning curve.</p>
        </>
      ),
    },
    {
      title: "Enterprise Power, Main Street Simple",
      color: "#00C2A8",
      content: (
        <>
          <p className="mb-2">Big brands use massive stacks of AI tools:</p>
          <p className="mb-2">Amazon, Meta, Starbucks—they have teams of engineers behind every campaign.</p>
          <p className="font-bold">
            FUSE levels the playing field with the same kind of firepower—<strong>packaged simply</strong> for Main
            Street.
          </p>
        </>
      ),
    },
    {
      title: "One Login. One Dashboard.",
      color: "#9747FF",
      content: (
        <>
          <p className="mb-2">One login. One dashboard.</p>
          <p className="mb-2">All the tools you need to:</p>
          <p className="mb-2">Grow customer loyalty</p>
          <p className="mb-2">Attract new foot traffic</p>
          <p className="mb-2">Market smarter</p>
          <p className="mb-2">Reward spending</p>
          <p>
            All tied into a secure rewards network that pays <em>you</em> back.
          </p>
        </>
      ),
    },
  ]

  return (
    <div className="py-16 relative overflow-hidden">
      {/* Background glow effects */}
      <div className="absolute top-0 left-1/4 w-1/2 h-1/2 bg-[#3A56FF] opacity-10 blur-[100px] rounded-full" />
      <div className="absolute bottom-0 right-1/4 w-1/2 h-1/2 bg-[#FF3A2F] opacity-10 blur-[100px] rounded-full" />

      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#3A56FF] to-[#FF3A2F]">
            The FUSE Advantage
          </span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* First row */}
          <Cube
            title={cubes[0].title}
            content={cubes[0].content}
            color={cubes[0].color}
            delay={0}
            className="lg:col-span-2"
          />
          <Cube
            title={cubes[1].title}
            content={cubes[1].content}
            color={cubes[1].color}
            delay={1}
            className="lg:col-span-1"
          />

          {/* Second row */}
          <Cube
            title={cubes[2].title}
            content={cubes[2].content}
            color={cubes[2].color}
            delay={2}
            className="lg:col-span-1"
          />
          <Cube
            title={cubes[3].title}
            content={cubes[3].content}
            color={cubes[3].color}
            delay={3}
            className="lg:col-span-2"
          />

          {/* Third row - centered */}
          <div className="md:col-span-2 lg:col-span-3 flex justify-center">
            <div className="w-full md:w-2/3 lg:w-1/2">
              <Cube title={cubes[4].title} content={cubes[4].content} color={cubes[4].color} delay={4} className="" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
