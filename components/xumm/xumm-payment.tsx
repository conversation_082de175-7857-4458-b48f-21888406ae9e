'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { QRCodeSVG } from 'qrcode.react';
import { Copy, ExternalLink, CheckCircle, XCircle, Clock, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
// Note: This component uses server-side xummService which requires XUMM_API_SECRET
// Consider switching to browser-based Xumm integration like in hooks/use-wallet.ts
import { xummService, XummPaymentRequest, formatXRPAmount } from '@/lib/xumm-utils';
import { useAuth } from '@/contexts/auth-context';

interface XummPaymentProps {
  destination: string;
  amount: string;
  memo?: string;
  description?: string;
  onSuccess?: (txHash: string) => void;
  onError?: (error: Error) => void;
  onCancel?: () => void;
}

export function XummPayment({
  destination,
  amount,
  memo,
  description = 'Complete your payment',
  onSuccess,
  onError,
  onCancel
}: XummPaymentProps) {
  const { user } = useAuth();
  const [paymentState, setPaymentState] = useState<'idle' | 'creating' | 'waiting' | 'success' | 'error' | 'cancelled'>('idle');
  const [paymentUuid, setPaymentUuid] = useState<string | null>(null);
  const [qrUrl, setQrUrl] = useState<string | null>(null);
  const [deepLink, setDeepLink] = useState<string | null>(null);
  const [txHash, setTxHash] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeXumm = async () => {
      const apiKey = process.env.NEXT_PUBLIC_XUMM_API_KEY;
      if (!apiKey) {
        setError('XUMM API key not configured');
        return;
      }

      try {
        await xummService.initialize(apiKey);
      } catch (err) {
        setError('Failed to initialize XUMM SDK');
        console.error('XUMM initialization error:', err);
      }
    };

    initializeXumm();
  }, []);

  const createPayment = async () => {
    if (!xummService.isReady()) {
      setError('XUMM SDK not ready');
      return;
    }

    setPaymentState('creating');
    setError(null);

    try {
      const request: XummPaymentRequest = {
        destination,
        amount,
        memo,
        returnUrl: `${window.location.origin}/payment-success`,
        instruction: description
      };

      const response = await xummService.createPayment(request);
      
      setPaymentUuid(response.uuid);
      setQrUrl(response.refs.qr_png);
      setDeepLink(xummService.generateDeepLink(response.uuid));
      setPaymentState('waiting');

      // Start monitoring payment
      monitorPayment(response.uuid);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create payment';
      setError(errorMessage);
      setPaymentState('error');
      onError?.(err instanceof Error ? err : new Error(errorMessage));
    }
  };

  const monitorPayment = async (uuid: string) => {
    try {
      const result = await xummService.waitForPayment(uuid);
      
      if (result.meta.signed === true) {
        setTxHash(result.response.txid);
        setPaymentState('success');
        onSuccess?.(result.response.txid);
        toast.success('Payment successful!');
      } else {
        setPaymentState('cancelled');
        onCancel?.();
        toast.error('Payment was cancelled');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Payment failed';
      setError(errorMessage);
      setPaymentState('error');
      onError?.(err instanceof Error ? err : new Error(errorMessage));
      toast.error(errorMessage);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const openXummApp = () => {
    if (deepLink) {
      window.open(deepLink, '_blank');
    }
  };

  const resetPayment = () => {
    setPaymentState('idle');
    setPaymentUuid(null);
    setQrUrl(null);
    setDeepLink(null);
    setTxHash(null);
    setError(null);
  };

  const getStatusIcon = () => {
    switch (paymentState) {
      case 'creating':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'waiting':
        return <Clock className="h-4 w-4" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (paymentState) {
      case 'creating':
        return 'Creating payment...';
      case 'waiting':
        return 'Waiting for payment...';
      case 'success':
        return 'Payment successful!';
      case 'error':
        return 'Payment failed';
      case 'cancelled':
        return 'Payment cancelled';
      default:
        return 'Ready to pay';
    }
  };

  const getStatusColor = () => {
    switch (paymentState) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'waiting':
        return 'bg-blue-100 text-blue-800';
      case 'creating':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          XUMM Payment
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">Amount:</span>
            <span className="font-mono">{formatXRPAmount(amount)} XRP</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">To:</span>
            <span className="font-mono text-xs">{destination.slice(0, 8)}...{destination.slice(-8)}</span>
          </div>
          {memo && (
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">Memo:</span>
              <span className="text-sm">{memo}</span>
            </div>
          )}
        </div>

        <div className="flex justify-center">
          <Badge className={getStatusColor()}>
            {getStatusText()}
          </Badge>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {paymentState === 'idle' && (
          <Button 
            onClick={createPayment} 
            className="w-full"
            disabled={!user}
          >
            {!user ? 'Please sign in to pay' : 'Create Payment'}
          </Button>
        )}

        {paymentState === 'waiting' && qrUrl && (
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="p-2 bg-white rounded-lg shadow-sm">
                <QRCodeSVG value={qrUrl} size={200} />
              </div>
            </div>
            
            <div className="space-y-2">
              <Button
                onClick={openXummApp}
                className="w-full"
                variant="outline"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Open in XUMM App
              </Button>
              
              <Button
                onClick={() => copyToClipboard(qrUrl)}
                variant="ghost"
                size="sm"
                className="w-full"
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy QR Code URL
              </Button>
            </div>
          </div>
        )}

        {paymentState === 'success' && txHash && (
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-700">
                Transaction Hash: 
                <span className="font-mono block break-all">{txHash}</span>
              </p>
            </div>
            <Button onClick={resetPayment} variant="outline" className="w-full">
              Make Another Payment
            </Button>
          </div>
        )}

        {(paymentState === 'error' || paymentState === 'cancelled') && (
          <div className="space-y-2">
            <Button onClick={resetPayment} variant="outline" className="w-full">
              Try Again
            </Button>
            <Button onClick={onCancel} variant="ghost" className="w-full">
              Cancel
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}