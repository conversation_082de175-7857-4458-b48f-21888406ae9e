"use client";

import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Camera, Type, RefreshCw, AlertCircle } from 'lucide-react';

interface ImprovedQRScannerProps {
  onScanSuccess: (decodedText: string) => void;
  onScanError?: (error: string) => void;
}

export default function ImprovedQRScanner({ 
  onScanSuccess, 
  onScanError 
}: ImprovedQRScannerProps) {
  const [scanMode, setScanMode] = useState<'camera' | 'manual'>('camera');
  const [manualInput, setManualInput] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cameraSupported, setCameraSupported] = useState(true);
  const scannerRef = useRef<any>(null);
  const readerRef = useRef<HTMLDivElement>(null);

  // Check camera support
  useEffect(() => {
    const checkCameraSupport = async () => {
      try {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          setCameraSupported(false);
          setScanMode('manual');
          return;
        }
        
        // Test camera access
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        stream.getTracks().forEach(track => track.stop());
        setCameraSupported(true);
      } catch (err) {
        console.warn('Camera not supported:', err);
        setCameraSupported(false);
        setScanMode('manual');
      }
    };

    checkCameraSupport();
  }, []);

  // Initialize camera scanner
  useEffect(() => {
    if (scanMode !== 'camera' || !cameraSupported) return;

    const initializeScanner = async () => {
      try {
        setIsScanning(true);
        setError(null);

        // Dynamically import to avoid SSR issues
        const { Html5QrcodeScanner } = await import('html5-qrcode');
        
        if (scannerRef.current) {
          await scannerRef.current.clear();
        }

        scannerRef.current = new Html5QrcodeScanner(
          'qr-reader',
          { 
            fps: 10,
            qrbox: { width: 250, height: 250 },
            aspectRatio: 1.0,
            disableFlip: false,
            rememberLastUsedCamera: true,
            showTorchButtonIfSupported: true,
          },
          false // verbose logging
        );

        scannerRef.current.render(
          (decodedText: string) => {
            console.log('QR Code scanned:', decodedText);
            onScanSuccess(decodedText);
          },
          (error: string) => {
            // Only show critical errors
            if (error.includes('Camera') || error.includes('permission') || error.includes('NotAllowed')) {
              setError('Camera access denied. Please allow camera permissions or use manual input.');
              if (onScanError) onScanError(error);
            }
          }
        );
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize camera';
        setError(errorMessage);
        if (onScanError) onScanError(errorMessage);
      }
    };

    initializeScanner();

    // Cleanup
    return () => {
      if (scannerRef.current) {
        scannerRef.current.clear().catch(console.error);
      }
    };
  }, [scanMode, cameraSupported, onScanSuccess, onScanError]);

  const handleManualSubmit = () => {
    if (!manualInput.trim()) {
      setError('Please enter QR code data');
      return;
    }
    
    console.log('Manual QR input:', manualInput);
    onScanSuccess(manualInput.trim());
    setManualInput('');
  };

  const handleRetryCamera = () => {
    setError(null);
    setScanMode('camera');
  };

  const switchToManual = () => {
    setScanMode('manual');
    setError(null);
  };

  const switchToCamera = () => {
    if (cameraSupported) {
      setScanMode('camera');
      setError(null);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Mode Toggle */}
      <div className="flex mb-4 bg-gray-100 rounded-lg p-1">
        <button
          onClick={switchToCamera}
          disabled={!cameraSupported}
          className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            scanMode === 'camera' && cameraSupported
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          } ${!cameraSupported ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Camera className="h-4 w-4" />
          Camera
        </button>
        <button
          onClick={switchToManual}
          className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            scanMode === 'manual'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          <Type className="h-4 w-4" />
          Manual
        </button>
      </div>

      {/* Scanner Content */}
      {scanMode === 'camera' ? (
        <div className="space-y-4">
          {error ? (
            <div className="flex flex-col items-center justify-center p-8 text-center bg-red-50 rounded-lg">
              <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold text-red-900 mb-2">Camera Error</h3>
              <p className="text-red-700 mb-4 text-sm">{error}</p>
              <div className="flex gap-2">
                <Button onClick={handleRetryCamera} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry Camera
                </Button>
                <Button onClick={switchToManual} size="sm">
                  Use Manual Input
                </Button>
              </div>
            </div>
          ) : (
            <div>
              <div 
                id="qr-reader" 
                ref={readerRef} 
                className="w-full rounded-lg overflow-hidden"
                style={{ minHeight: '300px' }}
              />
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600 mb-2">
                  Position the QR code within the frame to scan
                </p>
                <Button onClick={switchToManual} variant="outline" size="sm">
                  Having trouble? Use manual input
                </Button>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Manual QR Input</h3>
            <p className="text-blue-700 text-sm mb-4">
              Paste or type the QR code data below. This can be a business ID or JSON data.
            </p>
            
            <div className="space-y-3">
              <Input
                value={manualInput}
                onChange={(e) => setManualInput(e.target.value)}
                placeholder="Paste QR code data here..."
                className="w-full"
                onKeyPress={(e) => e.key === 'Enter' && handleManualSubmit()}
              />
              
              <div className="flex gap-2">
                <Button onClick={handleManualSubmit} className="flex-1">
                  Process QR Data
                </Button>
                {cameraSupported && (
                  <Button onClick={switchToCamera} variant="outline">
                    <Camera className="h-4 w-4 mr-2" />
                    Use Camera
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Example formats */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-semibold text-gray-900 mb-2">Expected formats:</h4>
            <div className="text-xs text-gray-600 space-y-1">
              <div><strong>Business QR:</strong> {"{"}"userId":"business-id","type":"business"{"}"}</div>
              <div><strong>Legacy:</strong> 550e8400-e29b-41d4-a716-446655440000</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
