"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { SignUpCard } from "./sign-up-card"

interface CardData {
  id: number
  title: string
  subtitle: string
  imageUrl: string
  colors: {
    primary: string
    secondary: string
    text: string
    shadow: string
  }
}

const storyCards: CardData[] = [
  {
    id: 1,
    title: "Your Epic Journey Starts Now",
    subtitle: "Step into a new world of growth.",
    imageUrl: "/images/journey-start.jpeg",
    colors: {
      primary: "#0a192f",
      secondary: "#172a45",
      text: "#ffffff",
      shadow: "rgba(2, 12, 27, 0.7)",
    },
  },
  {
    id: 2,
    title: "Unlock the Path to Community",
    subtitle: "Connect with partners and customers instantly.",
    imageUrl: "/images/path-to-community.jpeg",
    colors: {
      primary: "#022c22",
      secondary: "#044a3a",
      text: "#ffffff",
      shadow: "rgba(2, 44, 34, 0.7)",
    },
  },
  {
    id: 3,
    title: "Automate Your Social Buzz",
    subtitle: "We handle the posts, you handle the business.",
    imageUrl: "/images/amplify-voice.jpeg",
    colors: {
      primary: "#592f1d",
      secondary: "#7a452d",
      text: "#ffffff",
      shadow: "rgba(89, 47, 29, 0.7)",
    },
  },
  {
    id: 4,
    title: "Claim Your Future",
    subtitle: "The power of Fuse, in your hands.",
    imageUrl: "/images/business-vision.jpeg",
    colors: {
      primary: "#2a1d4a",
      secondary: "#3e2c6b",
      text: "#ffffff",
      shadow: "rgba(42, 29, 74, 0.7)",
    },
  },
  {
    id: 5,
    title: "This Is Your Future",
    subtitle: "Focus on what matters. We'll handle the rest.",
    imageUrl: "/images/lifestyle-reward.png",
    colors: {
      primary: "#3d2c21",
      secondary: "#5d4037",
      text: "#ffffff",
      shadow: "rgba(61, 44, 33, 0.7)",
    },
  },
]

export function CardStack() {
  const [cards, setCards] = useState<CardData[]>(storyCards)
  const [showSignUp, setShowSignUp] = useState(false)

  const removeCard = (id: number) => {
    setCards((prevCards) => {
      const newCards = prevCards.filter((card) => card.id !== id)
      if (newCards.length === 0) {
        setTimeout(() => setShowSignUp(true), 250)
      }
      return newCards
    })
  }

  return (
    <div className="relative h-[600px] w-full">
      <AnimatePresence>
        {cards.map((card, index) => (
          <Card key={card.id} card={card} index={index} removeCard={removeCard} totalCards={cards.length} />
        ))}
      </AnimatePresence>
      {showSignUp && <SignUpCard />}
    </div>
  )
}

interface CardProps {
  card: CardData
  index: number
  removeCard: (id: number) => void
  totalCards: number
}

function Card({ card, index, removeCard, totalCards }: CardProps) {
  const zIndex = totalCards - index
  const yOffset = index * 20

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 100 }}
      animate={{
        opacity: 1,
        y: yOffset,
        scale: 1 - index * 0.05,
        rotateZ: index * -2,
      }}
      exit={{
        opacity: 0,
        x: 300,
        rotateZ: 20,
        transition: { duration: 0.3 },
      }}
      transition={{ type: "spring", stiffness: 400, damping: 40, mass: 1 }}
      style={{
        zIndex,
        boxShadow: `0 ${10 + index * 5}px ${30 + index * 10}px ${card.colors.shadow}`,
        backgroundColor: card.colors.primary,
      }}
      className="absolute left-0 top-0 h-full w-full cursor-grab overflow-hidden rounded-2xl active:cursor-grabbing"
      drag={index === 0}
      dragConstraints={{ top: 0, bottom: 0, left: 0, right: 0 }}
      dragElastic={0.6}
      onDragEnd={(_, info) => {
        if (index === 0 && (info.offset.x > 150 || info.offset.y > 150)) {
          removeCard(card.id)
        }
      }}
      whileDrag={{
        scale: 1.05,
        rotateZ: 0,
        boxShadow: `0 15px 40px ${card.colors.shadow}`,
      }}
    >
      <div
        className="relative flex h-full flex-col justify-end overflow-hidden rounded-2xl bg-cover bg-center p-6 text-white"
        style={{
          backgroundImage: `linear-gradient(to top, ${card.colors.primary} 20%, transparent 60%), url(${card.imageUrl})`,
        }}
      >
        <div className="relative z-10 text-center">
          <h2 className="text-3xl font-bold drop-shadow-lg">{card.title}</h2>
          <h3 className="mt-1 text-lg font-medium opacity-90 drop-shadow-md">{card.subtitle}</h3>
          <div className="mt-4 border-t border-white/20 pt-3 text-xs">
            <p className="font-semibold">Free to Join. No Fees Ever.</p>
            <p className="opacity-80">Risk-free crypto rewards for your business.</p>
          </div>
        </div>

        {index === 0 && (
          <div className="pointer-events-none absolute bottom-4 left-1/2 flex -translate-x-1/2 flex-col items-center opacity-70">
            <motion.div
              className="mt-1 h-1 w-10 rounded-full bg-white/40"
              animate={{ x: [0, 10, 0] }}
              transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1.5 }}
            />
          </div>
        )}
      </div>
    </motion.div>
  )
}