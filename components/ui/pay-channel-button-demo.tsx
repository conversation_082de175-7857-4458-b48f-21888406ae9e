"use client"

import React from 'react'
import { PayChannelButton, PayChannel } from './pay-channel-button'
import { VIP_CARD_PRICES } from '@/lib/xrpl'

/**
 * Demo component showing how to use PayChannelButton across different use cases
 * This demonstrates the 5 different card types with both XRP and FUSE currencies
 */
export function PayChannelButtonDemo() {
  const handlePaymentSuccess = (txHash: string, payChannel: PayChannel) => {
    console.log('PayChannel payment successful:', {
      transactionHash: txHash,
      channelDetails: payChannel
    })
    
    // In a real app, you might:
    // - Update user's card holder status
    // - Send confirmation email
    // - Redirect to success page
    // - Update database records
  }

  const handlePaymentError = (error: string) => {
    console.error('PayChannel payment failed:', error)
    
    // In a real app, you might:
    // - Show user-friendly error message
    // - Log error for debugging
    // - Offer alternative payment methods
    // - Contact support integration
  }

  // Get all available card types
  const cardTypes = Object.keys(VIP_CARD_PRICES) as Array<keyof typeof VIP_CARD_PRICES>

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          PayChannel Button Demo
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Demonstrating XRPL PayChannel integration for VIP card purchases
        </p>
      </div>

      {/* All 5 Card Types with FUSE */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
          FUSE Token Payments
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cardTypes.slice(0, 5).map((cardType) => (
            <div
              key={`fuse-${cardType}`}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {cardType}
              </h3>
              <PayChannelButton
                cardType={cardType}
                currency="FUSE"
                variant="gradient"
                size="md"
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentError={handlePaymentError}
              />
            </div>
          ))}
        </div>
      </section>

      {/* All 5 Card Types with XRP */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
          XRP Payments
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cardTypes.slice(0, 5).map((cardType) => (
            <div
              key={`xrp-${cardType}`}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {cardType}
              </h3>
              <PayChannelButton
                cardType={cardType}
                currency="XRP"
                variant="default"
                size="md"
                onPaymentSuccess={handlePaymentSuccess}
                onPaymentError={handlePaymentError}
              />
            </div>
          ))}
        </div>
      </section>

      {/* Custom Amounts and Variants */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
          Custom Configurations
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Custom amount with fast settlement */}
          <div className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Custom Amount (Fast Settlement)
            </h3>
            <PayChannelButton
              cardType="Premium Card"
              currency="FUSE"
              customAmount={50}
              settleDelay={3600} // 1 hour
              variant="outline"
              size="sm"
              onPaymentSuccess={handlePaymentSuccess}
              onPaymentError={handlePaymentError}
            />
          </div>

          {/* Large button variant */}
          <div className="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Premium Experience
            </h3>
            <PayChannelButton
              cardType="Obsidian Card"
              currency="XRP"
              variant="gradient"
              size="lg"
              onPaymentSuccess={handlePaymentSuccess}
              onPaymentError={handlePaymentError}
            />
          </div>

          {/* Minimal pricing display */}
          <div className="bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Minimal Display
            </h3>
            <PayChannelButton
              cardType="Monthly VIP Card"
              currency="FUSE"
              showPricing={false}
              variant="default"
              size="md"
              className="w-full"
              onPaymentSuccess={handlePaymentSuccess}
              onPaymentError={handlePaymentError}
            />
          </div>
        </div>
      </section>

      {/* Integration Examples */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
          Integration Examples
        </h2>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Usage in Your Components
          </h3>
          <pre className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
{`// In your /upgrade or /fuse page:
import { PayChannelButton } from '@/components/ui/pay-channel-button'

// Basic usage
<PayChannelButton
  cardType="Premium Card"
  currency="FUSE"
  onPaymentSuccess={(txHash, payChannel) => {
    // Handle successful payment
    console.log('Success:', { txHash, payChannel })
  }}
  onPaymentError={(error) => {
    // Handle payment error
    console.error('Error:', error)
  }}
/>

// Advanced usage with custom settings
<PayChannelButton
  cardType="Obsidian Card"
  currency="XRP"
  customAmount={750}           // Custom USD amount
  settleDelay={7200}          // 2 hours settlement
  variant="gradient"          // Button style
  size="lg"                  // Button size
  showPricing={true}         // Show price info
  destinationAddress="rXXX..." // Custom destination
/>`}
          </pre>
        </div>
      </section>

      {/* PayChannel Benefits */}
      <section className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
          PayChannel Benefits
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-300 mb-3">
              ⚡ Fast Transactions
            </h3>
            <p className="text-blue-800 dark:text-blue-400 text-sm">
              Off-ledger payments enable instant transfers without waiting for ledger confirmation.
            </p>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800">
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-300 mb-3">
              💰 Low Fees
            </h3>
            <p className="text-green-800 dark:text-green-400 text-sm">
              Minimal transaction costs since payments happen off-ledger until settlement.
            </p>
          </div>
          
          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800">
            <h3 className="text-lg font-semibold text-purple-900 dark:text-purple-300 mb-3">
              🔒 Secure
            </h3>
            <p className="text-purple-800 dark:text-purple-400 text-sm">
              Cryptographically secured channels with built-in dispute resolution.
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}