"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import { DotLottieReact } from "@lottiefiles/dotlottie-react"
import { Button } from "@/components/ui/button"
import Image from "next/image"

export function SignUpCard() {
  const [isClicked, setIsClicked] = useState(false)
  const router = useRouter()

  const handleClick = () => {
    setIsClicked(true)
    // Use Next.js router to navigate to register page
    setTimeout(() => {
      router.push('/register')
    }, 1500)
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 50 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="absolute inset-0 z-50 flex flex-col items-center justify-center rounded-2xl bg-white p-6 shadow-2xl"
    >
      <div className="pointer-events-none absolute inset-0">
        <DotLottieReact
          src="/animations/spark.json"
          loop={false}
          autoplay={true}
          style={{ width: "100%", height: "100%" }}
        />
      </div>

      {!isClicked ? (
        <>
          <motion.div
            initial={{ scale: 0, y: -50 }}
            animate={{ scale: 1, y: 0 }}
            transition={{ type: "spring", stiffness: 400, damping: 20, delay: 0.2 }}
            className="absolute -top-12 left-1/2 z-20 h-24 w-24 -translate-x-1/2"
          >
            <Image
              src="/images/fuse-mascot.png"
              alt="Fuse Mascot"
              width={96}
              height={96}
              className="rounded-full border-4 border-white object-cover shadow-lg"
            />
          </motion.div>

          <div className="relative z-10 flex w-full flex-col items-center pt-12 text-center">
            <h2 className="text-2xl font-bold text-slate-900">Your 1-Minute Onboarding</h2>
            <p className="mt-1 text-center text-sm text-slate-600">Join Fuse in two quick steps.</p>

            <div className="my-4 grid w-full max-w-xs grid-cols-2 gap-2 rounded-lg border border-blue-200 bg-blue-50 p-3 text-center text-sm text-blue-900">
              <div>
                <p className="font-bold">Step 1</p>
                <p className="text-xs">Quick Profile</p>
              </div>
              <div>
                <p className="font-bold">Step 2</p>
                <p className="text-xs">1-Min Business App</p>
              </div>
            </div>

            <Button
              onClick={handleClick}
              className="mt-2 w-full max-w-sm bg-blue-600 text-lg hover:bg-blue-700"
            >
              Sign Up
            </Button>
          </div>
        </>
      ) : (
        <div className="relative z-10 flex w-full flex-col items-center text-center">
          <h2 className="text-2xl font-bold text-slate-900">Awesome! Let's Go.</h2>
          <p className="mt-1 text-center text-sm text-slate-600">Redirecting you to create your profile now...</p>
        </div>
      )}
    </motion.div>
  )
}