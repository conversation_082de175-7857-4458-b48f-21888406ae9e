"use client"

import { useState, useEffect } from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react'
import { performHealthCheck, type HealthCheckResult } from '@/utils/supabase/health-check'

interface ServiceStatusBannerProps {
  showWhenHealthy?: boolean
  autoCheck?: boolean
  checkInterval?: number
}

export function ServiceStatusBanner({
  showWhenHealthy = false,
  autoCheck = true,
  checkInterval = 120000 // 2 minutes - reduced frequency to avoid excessive requests
}: ServiceStatusBannerProps) {
  const [healthStatus, setHealthStatus] = useState<{
    auth: HealthCheckResult
    database: HealthCheckResult
    overall: boolean
  } | null>(null)
  const [isChecking, setIsChecking] = useState(false)
  const [lastChecked, setLastChecked] = useState<Date | null>(null)

  const checkHealth = async () => {
    setIsChecking(true)
    try {
      const health = await performHealthCheck()
      setHealthStatus(health)
      setLastChecked(new Date())
    } catch (error) {
      console.error('Health check failed:', error)
    } finally {
      setIsChecking(false)
    }
  }

  useEffect(() => {
    if (autoCheck) {
      checkHealth()
      
      const interval = setInterval(checkHealth, checkInterval)
      return () => clearInterval(interval)
    }
  }, [autoCheck, checkInterval])

  // Don't show anything if we haven't checked yet
  if (!healthStatus) {
    return null
  }

  // Don't show banner if services are healthy and showWhenHealthy is false
  if (healthStatus.overall && !showWhenHealthy) {
    return null
  }

  const getStatusColor = () => {
    if (healthStatus.overall) return 'default'
    return 'destructive'
  }

  const getStatusIcon = () => {
    if (isChecking) return <RefreshCw className="h-4 w-4 animate-spin" />
    if (healthStatus.overall) return <CheckCircle className="h-4 w-4" />
    return <AlertTriangle className="h-4 w-4" />
  }

  const getStatusMessage = () => {
    if (healthStatus.overall) {
      return 'All services are operational'
    }

    const issues = []
    if (!healthStatus.auth.isHealthy) {
      issues.push(`Authentication: ${healthStatus.auth.error}`)
    }
    if (!healthStatus.database.isHealthy) {
      issues.push(`Database: ${healthStatus.database.error}`)
    }

    return `Service issues detected: ${issues.join(', ')}`
  }

  return (
    <Alert variant={getStatusColor()} className="mb-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <AlertDescription>
            {getStatusMessage()}
            {lastChecked && (
              <span className="text-xs text-muted-foreground ml-2">
                Last checked: {lastChecked.toLocaleTimeString()}
              </span>
            )}
          </AlertDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={checkHealth}
          disabled={isChecking}
          className="ml-4"
        >
          {isChecking ? (
            <>
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
              Checking...
            </>
          ) : (
            <>
              <RefreshCw className="h-3 w-3 mr-1" />
              Refresh
            </>
          )}
        </Button>
      </div>
      
      {!healthStatus.overall && (
        <div className="mt-2 text-sm">
          <p>If you're experiencing login issues, please:</p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Wait a few minutes and try again</li>
            <li>Check your internet connection</li>
            <li>Clear your browser cache and cookies</li>
            <li>Try using a different browser or incognito mode</li>
          </ul>
        </div>
      )}
    </Alert>
  )
}
