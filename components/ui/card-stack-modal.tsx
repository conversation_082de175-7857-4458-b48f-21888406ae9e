"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X } from "lucide-react"
import { CardStack } from "./card-stack"
import { Button } from "@/components/ui/button"

interface CardStackModalProps {
  isOpen: boolean
  onClose: () => void
}

export function CardStackModal({ isOpen, onClose }: CardStackModalProps) {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 50 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="relative w-full max-w-lg mx-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="absolute -top-12 -right-2 z-60 bg-white/10 hover:bg-white/20 text-white border border-white/20 rounded-full backdrop-blur-sm"
            >
              <X className="h-5 w-5" />
            </Button>

            {/* Card Stack Container */}
            <div className="relative">
              <CardStack />
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

interface CardStackTriggerProps {
  children: React.ReactNode
  className?: string
}

export function CardStackTrigger({ children, className }: CardStackTriggerProps) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <div
        onClick={() => setIsOpen(true)}
        className={className}
      >
        {children}
      </div>
      <CardStackModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  )
}