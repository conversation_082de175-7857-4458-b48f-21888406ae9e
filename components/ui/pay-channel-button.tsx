"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Wallet, Zap, AlertCircle, CheckCircle, ExternalLink, Sparkles } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { XRPL_CONFIG, VIP_CARD_PRICES } from '@/lib/xrpl'
import { pricingService, formatCurrency } from '@/lib/pricing-service'

// PayChannel interface from XRPL documentation
export interface PayChannel {
  Account: string;
  Amount: string;
  Balance: string;
  CancelAfter?: number;
  Destination: string;
  DestinationNode?: string;
  DestinationTag?: number;
  Expiration?: number;
  Flags: number;
  index: string;
  LedgerEntryType: "PayChannel";
  OwnerNode: string;
  PreviousTxnID: string;
  PreviousTxnLgrSeq: number;
  PublicKey: string;
  SettleDelay: number;
  SourceTag?: number;
}

export interface PayChannelButtonProps {
  /** Card type from VIP_CARD_PRICES */
  cardType: keyof typeof VIP_CARD_PRICES;
  /** Payment currency - XRP or FUSE */
  currency: 'XRP' | 'FUSE';
  /** Optional custom amount in USD - defaults to card price */
  customAmount?: number;
  /** Callback when payment is successful */
  onPaymentSuccess?: (txHash: string, payChannel: PayChannel) => void;
  /** Callback when payment fails */
  onPaymentError?: (error: string) => void;
  /** Optional custom destination address */
  destinationAddress?: string;
  /** Optional settlement delay in seconds (default: 24 hours) */
  settleDelay?: number;
  /** Button variant */
  variant?: 'default' | 'gradient' | 'outline';
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Custom class name */
  className?: string;
  /** Show pricing info */
  showPricing?: boolean;
  /** Disabled state */
  disabled?: boolean;
}

export function PayChannelButton({
  cardType,
  currency,
  customAmount,
  onPaymentSuccess,
  onPaymentError,
  destinationAddress = XRPL_CONFIG.DESTINATION_WALLET,
  settleDelay = 86400, // 24 hours default
  variant = 'default',
  size = 'md',
  className = '',
  showPricing = true,
  disabled = false
}: PayChannelButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [cryptoPrices, setCryptoPrices] = useState<any>(null)
  const [currentAmount, setCurrentAmount] = useState<string>('')
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState(false)

  // Calculate USD amount
  const usdAmount = customAmount || VIP_CARD_PRICES[cardType]?.usd || 0

  // Load current pricing
  useEffect(() => {
    const loadPricing = async () => {
      try {
        setIsLoading(true)
        const prices = await pricingService.getCurrentPrices()
        setCryptoPrices(prices)
        
        // Calculate current amount based on currency
        if (currency === 'XRP') {
          const xrpAmount = await pricingService.calculateXRPAmount(usdAmount.toString())
          setCurrentAmount(xrpAmount)
        } else {
          const fuseAmount = await pricingService.calculateFUSEAmount(usdAmount.toString())
          setCurrentAmount(fuseAmount)
        }
      } catch (err) {
        console.error('Failed to load pricing:', err)
        setError('Failed to load current pricing')
        
        // Fallback pricing
        if (currency === 'XRP') {
          setCurrentAmount((usdAmount / 2.4).toFixed(6))
        } else {
          setCurrentAmount(Math.round(usdAmount / 0.00172).toString())
        }
      } finally {
        setIsLoading(false)
      }
    }

    loadPricing()
    
    // Refresh pricing every 5 minutes
    const interval = setInterval(loadPricing, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [cardType, currency, usdAmount])

  // Create PayChannel transaction
  const createPayChannel = async (): Promise<PayChannel> => {
    const channelAmount = currency === 'XRP' 
      ? (parseFloat(currentAmount) * 1000000).toString() // Convert to drops
      : currentAmount

    // Generate a unique channel index (in practice, this would be handled by XRPL)
    const channelIndex = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const payChannel: PayChannel = {
      Account: '', // Will be set when user connects wallet
      Amount: channelAmount,
      Balance: '0', // Initial balance
      Destination: destinationAddress,
      DestinationTag: Math.floor(Math.random() * 1000000), // Random destination tag
      Expiration: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30 days from now
      Flags: 0,
      index: channelIndex,
      LedgerEntryType: "PayChannel",
      OwnerNode: '',
      PreviousTxnID: '',
      PreviousTxnLgrSeq: 0,
      PublicKey: '', // Will be set from wallet
      SettleDelay: settleDelay,
      SourceTag: Math.floor(Math.random() * 1000000), // Random source tag
    }

    return payChannel
  }

  // Handle payment channel creation
  const handlePayChannelPayment = async () => {
    try {
      setIsLoading(true)
      setError('')
      setSuccess(false)

      // Check if required amount is calculated
      if (!currentAmount || currentAmount === '0') {
        throw new Error('Unable to calculate payment amount')
      }

      // Create payment channel configuration
      const payChannel = await createPayChannel()

      // In a real implementation, this would:
      // 1. Connect to user's wallet (Xaman, Joey, etc.)
      // 2. Create the PayChannel transaction
      // 3. Submit to XRPL network
      // 4. Monitor for confirmation
      
      // For now, we'll simulate the process and redirect to existing payment flow
      // You would replace this with actual PayChannel creation logic
      
      // Simulate PayChannel creation delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock successful transaction hash
      const mockTxHash = `PAY_CHANNEL_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      setSuccess(true)
      onPaymentSuccess?.(mockTxHash, payChannel)

    } catch (err: any) {
      const errorMessage = err.message || 'PayChannel creation failed'
      setError(errorMessage)
      onPaymentError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // Button styling based on variant
  const getButtonStyles = () => {
    const baseStyles = "font-bold transition-all duration-300 flex items-center justify-center gap-2"
    
    const sizeStyles = {
      sm: "px-4 py-2 text-sm",
      md: "px-6 py-3 text-base",
      lg: "px-8 py-4 text-lg"
    }

    const variantStyles = {
      default: "bg-[#316bff] hover:bg-[#2557d6] text-white",
      gradient: "bg-gradient-to-r from-[#316bff] to-purple-600 hover:from-[#2557d6] hover:to-purple-700 text-white",
      outline: "border-2 border-[#316bff] text-[#316bff] hover:bg-[#316bff] hover:text-white"
    }

    return `${baseStyles} ${sizeStyles[size]} ${variantStyles[variant]}`
  }

  // Format display amount
  const getDisplayAmount = () => {
    if (isLoading) return 'Loading...'
    if (!currentAmount) return 'N/A'
    
    return currency === 'XRP' 
      ? formatCurrency.xrp(currentAmount)
      : formatCurrency.fuse(currentAmount)
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Pricing Display */}
      {showPricing && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-800"
        >
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-2">
              {currency === 'XRP' ? (
                <Zap className="h-4 w-4 text-blue-500" />
              ) : (
                <Sparkles className="h-4 w-4 text-orange-500" />
              )}
              <Badge variant="outline" className="text-xs">
                PayChannel • {currency}
              </Badge>
            </div>
            <div className="text-xl font-bold text-[#316bff] dark:text-blue-400">
              {getDisplayAmount()}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              ${usdAmount} USD • Live pricing
            </div>
            {settleDelay !== 86400 && (
              <div className="text-xs text-gray-500">
                Channel settles in {Math.floor(settleDelay / 3600)}h
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3"
        >
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">{error}</span>
          </div>
        </motion.div>
      )}

      {/* Success Display */}
      {success && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3"
        >
          <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">PayChannel created successfully!</span>
          </div>
        </motion.div>
      )}

      {/* Pay Button */}
      <Button
        onClick={handlePayChannelPayment}
        disabled={disabled || isLoading || success || !currentAmount}
        className={`w-full rounded-xl ${getButtonStyles()}`}
      >
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
            Creating PayChannel...
          </>
        ) : success ? (
          <>
            <CheckCircle className="h-4 w-4" />
            PayChannel Created
          </>
        ) : (
          <>
            <Wallet className="h-4 w-4" />
            Create PayChannel ({getDisplayAmount()})
          </>
        )}
      </Button>

      {/* Info Text */}
      <div className="text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          PayChannel enables fast, off-ledger payments with {currency}
        </p>
        {currency === 'FUSE' && (
          <a
            href="https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-1 text-xs text-[#316bff] hover:text-[#2557d6] mt-1"
          >
            Trade FUSE <ExternalLink className="h-3 w-3" />
          </a>
        )}
      </div>
    </div>
  )
}

// Export useful types for consumers
export type { PayChannelButtonProps, PayChannel }

// Example usage component for documentation
export function PayChannelButtonExample() {
  const handleSuccess = (txHash: string, payChannel: PayChannel) => {
    console.log('PayChannel created:', { txHash, payChannel })
  }

  const handleError = (error: string) => {
    console.error('PayChannel error:', error)
  }

  return (
    <div className="space-y-6 p-6">
      <h3 className="text-lg font-bold">PayChannel Button Examples</h3>
      
      {/* Premium Card with FUSE */}
      <PayChannelButton
        cardType="Premium Card"
        currency="FUSE"
        variant="gradient"
        size="lg"
        onPaymentSuccess={handleSuccess}
        onPaymentError={handleError}
      />

      {/* Monthly VIP with XRP */}
      <PayChannelButton
        cardType="Monthly VIP Card"
        currency="XRP"
        variant="default"
        size="md"
        onPaymentSuccess={handleSuccess}
        onPaymentError={handleError}
      />

      {/* Custom amount */}
      <PayChannelButton
        cardType="Premium Card"
        currency="FUSE"
        customAmount={50}
        variant="outline"
        size="sm"
        settleDelay={3600} // 1 hour
        onPaymentSuccess={handleSuccess}
        onPaymentError={handleError}
      />
    </div>
  )
}