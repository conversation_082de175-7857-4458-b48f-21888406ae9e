"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CreditCard, Star, ArrowRight, Gift, Zap } from "lucide-react"
import { PurchaseModal } from "@/components/purchase/purchase-modal"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface QuickPurchaseButtonProps {
  variant?: "primary" | "secondary" | "floating" | "banner" | "minimal"
  size?: "sm" | "md" | "lg" | "xl"
  className?: string
  children?: React.ReactNode
  showPrice?: boolean
  animated?: boolean
}

export function QuickPurchaseButton({ 
  variant = "primary",
  size = "md",
  className = "",
  children,
  showPrice = true,
  animated = true
}: QuickPurchaseButtonProps) {
  const [showModal, setShowModal] = useState(false)

  const getButtonContent = () => {
    if (children) return children

    switch (variant) {
      case "primary":
        return (
          <>
            <Star className="h-5 w-5 mr-2" />
            Get VIP Card {showPrice && "- $100"}
            <ArrowRight className="h-5 w-5 ml-2" />
          </>
        )
      case "secondary":
        return (
          <>
            <CreditCard className="h-4 w-4 mr-2" />
            Become VIP Member
          </>
        )
      case "floating":
        return (
          <>
            <Gift className="h-5 w-5 mr-2" />
            VIP Card
          </>
        )
      case "banner":
        return (
          <>
            <Zap className="h-6 w-6 mr-2" />
            Get Instant Discounts
            <ArrowRight className="h-6 w-6 ml-2" />
          </>
        )
      case "minimal":
        return "Get VIP Card"
      default:
        return "Get VIP Card"
    }
  }

  const getButtonClasses = () => {
    const baseClasses = "font-semibold transition-all duration-300"
    
    const variantClasses = {
      primary: "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl",
      secondary: "bg-white text-blue-600 border-2 border-blue-600 hover:bg-blue-50",
      floating: "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-2xl",
      banner: "bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white shadow-lg",
      minimal: "bg-blue-600 hover:bg-blue-700 text-white"
    }

    const sizeClasses = {
      sm: "px-4 py-2 text-sm h-10",
      md: "px-6 py-3 text-base h-12",
      lg: "px-8 py-4 text-lg h-14",
      xl: "px-10 py-5 text-xl h-16"
    }

    return cn(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      animated && "hover:scale-105 active:scale-95",
      className
    )
  }

  const ButtonComponent = animated ? motion.button : "button"
  const motionProps = animated ? {
    whileHover: { scale: 1.05 },
    whileTap: { scale: 0.95 },
    transition: { duration: 0.2 }
  } : {}

  return (
    <>
      <Button
        onClick={() => setShowModal(true)}
        className={getButtonClasses()}
        asChild
      >
        <ButtonComponent {...motionProps}>
          {getButtonContent()}
        </ButtonComponent>
      </Button>

      <PurchaseModal 
        isOpen={showModal} 
        onClose={() => setShowModal(false)} 
      />
    </>
  )
}

// Floating Action Button variant for mobile
export function FloatingPurchaseButton({ className = "" }: { className?: string }) {
  return (
    <div className={cn("fixed bottom-6 right-6 z-50", className)}>
      <QuickPurchaseButton
        variant="floating"
        size="lg"
        animated={true}
        className="rounded-full shadow-2xl"
      >
        <div className="flex flex-col items-center">
          <Gift className="h-6 w-6 mb-1" />
          <span className="text-xs font-bold">VIP</span>
        </div>
      </QuickPurchaseButton>
    </div>
  )
}

// Banner CTA variant
export function BannerPurchaseButton({ className = "" }: { className?: string }) {
  return (
    <div className={cn("w-full", className)}>
      <QuickPurchaseButton
        variant="banner"
        size="xl"
        className="w-full rounded-xl"
        animated={true}
      />
    </div>
  )
}

// Header CTA variant
export function HeaderPurchaseButton({ className = "" }: { className?: string }) {
  return (
    <QuickPurchaseButton
      variant="primary"
      size="md"
      className={cn("rounded-lg", className)}
      animated={true}
    />
  )
}
