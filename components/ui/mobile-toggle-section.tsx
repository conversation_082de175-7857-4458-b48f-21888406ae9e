"use client"

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useIsMobile } from '@/hooks/use-mobile'

interface MobileToggleSectionProps {
  title: string
  subtitle?: string
  icon?: React.ReactNode
  children: React.ReactNode
  defaultOpen?: boolean
  className?: string
  headerClassName?: string
  contentClassName?: string
  persistKey?: string // For localStorage persistence
  onToggle?: (isOpen: boolean) => void
}

export function MobileToggleSection({
  title,
  subtitle,
  icon,
  children,
  defaultOpen = false,
  className,
  headerClassName,
  contentClassName,
  persistKey,
  onToggle
}: MobileToggleSectionProps) {
  const isMobile = useIsMobile()
  const [isOpen, setIsOpen] = useState(() => {
    if (typeof window !== 'undefined' && persistKey) {
      const saved = localStorage.getItem(`toggle-${persistKey}`)
      return saved ? JSON.parse(saved) : defaultOpen
    }
    return defaultOpen
  })
  
  const contentRef = useRef<HTMLDivElement>(null)
  const [contentHeight, setContentHeight] = useState<number>(0)
  const touchStartY = useRef<number>(0)
  const touchEndY = useRef<number>(0)

  // Measure content height for smooth animations
  useEffect(() => {
    if (contentRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setContentHeight(entry.contentRect.height)
        }
      })
      
      resizeObserver.observe(contentRef.current)
      return () => resizeObserver.disconnect()
    }
  }, [children])

  // Persist toggle state
  useEffect(() => {
    if (persistKey && typeof window !== 'undefined') {
      localStorage.setItem(`toggle-${persistKey}`, JSON.stringify(isOpen))
    }
  }, [isOpen, persistKey])

  const handleToggle = () => {
    const newState = !isOpen
    setIsOpen(newState)
    onToggle?.(newState)
    
    // Haptic feedback on supported devices
    if (navigator.vibrate && isMobile) {
      navigator.vibrate(25)
    }
  }

  // Enhanced touch handling for mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartY.current = e.touches[0].clientY
  }

  const handleTouchEnd = (e: React.TouchEvent) => {
    touchEndY.current = e.changedTouches[0].clientY
    
    // Prevent accidental toggles from scrolling
    const touchDiff = Math.abs(touchStartY.current - touchEndY.current)
    if (touchDiff < 10) {
      handleToggle()
    }
  }

  const headerVariants = {
    initial: { backgroundColor: 'rgba(0, 0, 0, 0)' },
    hover: { backgroundColor: 'rgba(255, 255, 255, 0.05)' },
    tap: { backgroundColor: 'rgba(255, 255, 255, 0.1)', scale: 0.98 }
  }

  const contentVariants = {
    closed: {
      height: 0,
      opacity: 0,
      transition: {
        height: { duration: 0.3, ease: [0.4, 0, 0.2, 1] },
        opacity: { duration: 0.2, ease: 'easeOut' }
      }
    },
    open: {
      height: 'auto',
      opacity: 1,
      transition: {
        height: { duration: 0.3, ease: [0.4, 0, 0.2, 1] },
        opacity: { duration: 0.3, delay: 0.1, ease: 'easeIn' }
      }
    }
  }

  const iconVariants = {
    closed: { rotate: 0 },
    open: { rotate: 180 }
  }

  return (
    <div className={cn(
      'w-full border border-gray-700/30 rounded-lg overflow-hidden',
      'bg-gradient-to-br from-gray-900/40 to-gray-800/40 backdrop-blur-sm',
      className
    )}>
      <motion.div
        className={cn(
          'flex items-center justify-between p-4 cursor-pointer select-none',
          'transition-colors duration-200',
          isMobile && 'touch-manipulation',
          headerClassName
        )}
        variants={headerVariants}
        initial="initial"
        whileHover={!isMobile ? "hover" : undefined}
        whileTap="tap"
        onClick={!isMobile ? handleToggle : undefined}
        onTouchStart={isMobile ? handleTouchStart : undefined}
        onTouchEnd={isMobile ? handleTouchEnd : undefined}
        style={{
          WebkitTouchCallout: 'none',
          WebkitUserSelect: 'none',
          WebkitTapHighlightColor: 'transparent',
          userSelect: 'none'
        }}
      >
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {icon && (
            <div className="flex-shrink-0 text-blue-400">
              {icon}
            </div>
          )}
          <div className="min-w-0 flex-1">
            <h3 className="text-lg font-semibold text-white truncate">
              {title}
            </h3>
            {subtitle && (
              <p className="text-sm text-white/70 truncate">
                {subtitle}
              </p>
            )}
          </div>
        </div>
        
        <motion.div
          variants={iconVariants}
          animate={isOpen ? "open" : "closed"}
          transition={{ duration: 0.2 }}
          className="flex-shrink-0 ml-2"
        >
          <ChevronDown className="h-5 w-5 text-white/70" />
        </motion.div>
      </motion.div>

      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            variants={contentVariants}
            initial="closed"
            animate="open"
            exit="closed"
            className="overflow-hidden"
          >
            <div
              ref={contentRef}
              className={cn(
                'p-4 pt-0 border-t border-gray-700/20',
                contentClassName
              )}
            >
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
