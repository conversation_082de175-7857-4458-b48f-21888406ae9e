"use client";

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuth } from '@/contexts/auth-context';

// Types
interface BusinessData {
  id: uuid;
  name: text;
  logo_url?: string;
  website?: string;
  category?: string;
  premium_discount?: string;
  is_active: boolean;
  user_id: string;
  business_address?: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  latitude?: number;
  longitude?: number;
  business_referral?: string;
  referring_business_id?: string;
  business_spotlight?: boolean;
  loyalty_reward_frequency?: string;
  logo_optimized_url?: string;
  logo_alt_text?: string;
  logo_width?: number;
  logo_height?: number;
  logo_file_size?: number;
  logo_mime_type?: string;
  created_at: string;
  updated_at?: string;
  display_order?: number;
  contact_info?: any;
  interaction_count?: number;
  referral_count?: number;
}

interface QRInteraction {
  id: string;
  scanner_user_id: string;
  scanned_user_id: string | null;
  scanned_business_id: string | null;
  interaction_type: 'user_scan' | 'business_scan';
  created_at: string;
  scanner_profile?: {
    first_name: string;
    last_name: string;
    user_email: string;
    is_card_holder: boolean;
  };
  scanned_profile?: {
    first_name: string;
    last_name: string;
    user_email: string;
    is_card_holder: boolean;
  };
  business?: {
    name: string;
    category: string;
    logo_url: string;
  };
}

interface DashboardData {
  userBusiness: BusinessData | null;
  hasUserBusiness: boolean;
  recentInteractions: QRInteraction[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  fetchInteractions: () => Promise<void>;
  interactionsLoading: boolean;
}

// Context
const DashboardDataContext = createContext<DashboardData | null>(null);

// Hook to use the context
export function useDashboardData() {
  const context = useContext(DashboardDataContext);
  if (!context) {
    throw new Error('useDashboardData must be used within a DashboardDataProvider');
  }
  return context;
}

// Provider component
interface DashboardDataProviderProps {
  children: ReactNode;
}

export function DashboardDataProvider({ children }: DashboardDataProviderProps) {
  const { user } = useAuth();
  const [userBusiness, setUserBusiness] = useState<BusinessData | null>(null);
  const [hasUserBusiness, setHasUserBusiness] = useState(false);
  const [recentInteractions, setRecentInteractions] = useState<QRInteraction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [interactionsLoading, setInteractionsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cache management
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  const cache = new Map<string, { data: any; timestamp: number }>();

  const getCachedData = (key: string) => {
    const cached = cache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }
    return null;
  };

  const setCachedData = (key: string, data: any) => {
    cache.set(key, { data, timestamp: Date.now() });
  };

  // Fetch business data only
  const fetchDashboardData = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      // Check cache first
      const cacheKey = `business_data_${user.id}`;
      const cachedData = getCachedData(cacheKey);
      if (cachedData) {
        setUserBusiness(cachedData.userBusiness);
        setHasUserBusiness(cachedData.hasUserBusiness);
        setIsLoading(false);
        return;
      }

      // Test authentication first
      console.log('🔍 Testing authentication before business fetch...');
      const testAuthResponse = await fetch('/api/test-auth', {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (testAuthResponse.ok) {
        const testAuthResult = await testAuthResponse.json();
        console.log('✅ Auth Test Result:', testAuthResult);
      } else {
        const testAuthError = await testAuthResponse.text();
        console.error('❌ Auth Test Failed:', {
          status: testAuthResponse.status,
          error: testAuthError
        });
      }

      // Fetch business data only - use authenticated session (no userId parameter needed)
      const businessResponse = await fetch('/api/dashboard/business', {
        credentials: 'include', // Include authentication cookies
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      console.log('🔍 Business API Response Status:', businessResponse.status, businessResponse.ok);

      // Handle business data
      let businessData = null;
      let hasBusiness = false;
      if (businessResponse.ok) {
        const businessResult = await businessResponse.json();
        console.log('✅ Business API Result:', businessResult);
        businessData = businessResult.business;
        hasBusiness = !!businessData;
        console.log('📊 Final business state:', { businessData: !!businessData, hasBusiness });
      } else {
        // Log error details when API call fails
        const errorText = await businessResponse.text();
        console.error('❌ Business API Error:', {
          status: businessResponse.status,
          statusText: businessResponse.statusText,
          error: errorText
        });
        setError(`Failed to fetch business data: ${businessResponse.status} ${businessResponse.statusText}`);
      }

      // Update state
      setUserBusiness(businessData);
      setHasUserBusiness(hasBusiness);

      // Cache the results
      setCachedData(cacheKey, {
        userBusiness: businessData,
        hasUserBusiness: hasBusiness
      });

    } catch (err: any) {
      console.error('Error fetching business data:', err);
      setError(err.message || 'Failed to load business data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch interactions data separately
  const fetchInteractions = async () => {
    if (!user?.id) return;

    setInteractionsLoading(true);

    try {
      // Check cache first
      const cacheKey = `interactions_data_${user.id}`;
      const cachedData = getCachedData(cacheKey);
      if (cachedData) {
        setRecentInteractions(cachedData.recentInteractions);
        setInteractionsLoading(false);
        return;
      }

      // Fetch interactions data
      const interactionsResponse = await fetch(`/api/dashboard/interactions?userId=${user.id}&limit=3`);

      // Handle interactions data
      let interactionsData: QRInteraction[] = [];
      if (interactionsResponse.ok) {
        const interactionsResult = await interactionsResponse.json();
        interactionsData = interactionsResult.interactions || [];
      }

      // Update state
      setRecentInteractions(interactionsData);

      // Cache the results
      setCachedData(cacheKey, {
        recentInteractions: interactionsData
      });

    } catch (err: any) {
      console.error('Error fetching interactions data:', err);
      setError(err.message || 'Failed to load interactions data');
    } finally {
      setInteractionsLoading(false);
    }
  };

  // Fetch business data when user changes (but not interactions)
  useEffect(() => {
    if (user?.id) {
      fetchDashboardData();
    } else {
      // Reset state when user logs out
      setUserBusiness(null);
      setHasUserBusiness(false);
      setRecentInteractions([]);
      setError(null);
    }
  }, [user?.id]);

  const contextValue: DashboardData = {
    userBusiness,
    hasUserBusiness,
    recentInteractions,
    isLoading,
    error,
    refetch: fetchDashboardData,
    fetchInteractions,
    interactionsLoading
  };

  return (
    <DashboardDataContext.Provider value={contextValue}>
      {children}
    </DashboardDataContext.Provider>
  );
}
