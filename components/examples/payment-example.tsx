// Example usage of useXRPPayment hook for /fuse and /upgrade pages
'use client';

import { useState } from 'react';
import { useXRPPayment } from '@/hooks/use-xrp-payment';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Wallet, CreditCard, Coins } from 'lucide-react';

export function PaymentExample() {
  const {
    createXRPPayment,
    createFUSEPayment,
    createPayment,
    checkPaymentStatus,
    isProcessing,
    lastPayment,
    isConnected,
    walletAddress,
    xrpToDrops,
    dropsToXRP,
  } = useXRPPayment();

  const [paymentResult, setPaymentResult] = useState<any>(null);
  const [statusResult, setStatusResult] = useState<any>(null);

  // Example: VIP Card Upgrade Payment (XRP)
  const handleVIPUpgradePayment = async () => {
    const destination = 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx'; // Your business wallet
    const amount = '100'; // 100 XRP for VIP upgrade
    
    const result = await createXRPPayment(destination, amount, {
      memo: 'VIP_CARD_UPGRADE',
      instruction: 'Pay 100 XRP to upgrade to VIP card',
      identifier: `vip_upgrade_${Date.now()}`,
      returnUrl: '/upgrade-success',
    });

    setPaymentResult(result);
    
    if (result.success && result.deepLink) {
      // On mobile, this will open Xaman app
      window.location.href = result.deepLink;
    }
  };

  // Example: FUSE Token Purchase/Reward Payment
  const handleFUSERewardPayment = async () => {
    const destination = 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo'; // FUSE token issuer
    const amount = '50'; // 50 FUSE tokens
    
    const result = await createFUSEPayment(destination, amount, {
      memo: 'FUSE_REWARD_PURCHASE',
      instruction: 'Send 50 FUSE tokens for rewards',
      identifier: `fuse_reward_${Date.now()}`,
      returnUrl: '/fuse-success',
    });

    setPaymentResult(result);
    
    if (result.success && result.deepLink) {
      // On mobile, this will open Xaman app
      window.location.href = result.deepLink;
    }
  };

  // Example: Generic Payment Handler
  const handleGenericPayment = async (currency: 'XRP' | 'FUSE') => {
    const destination = currency === 'XRP' 
      ? 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx' 
      : 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo';
    const amount = currency === 'XRP' ? '25' : '100';
    
    const result = await createPayment(destination, amount, currency, {
      memo: `${currency}_PAYMENT`,
      instruction: `Send ${amount} ${currency} for purchase`,
    });

    setPaymentResult(result);
    
    if (result.success && result.deepLink) {
      window.location.href = result.deepLink;
    }
  };

  // Example: Check Payment Status
  const handleCheckStatus = async () => {
    if (lastPayment?.uuid) {
      const status = await checkPaymentStatus(lastPayment.uuid);
      setStatusResult(status);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            XRP Payment Hook Demo
          </CardTitle>
          <CardDescription>
            Examples of how to use the useXRPPayment hook for /fuse and /upgrade pages
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Wallet Status */}
          <div className="flex items-center gap-3">
            <Badge variant={isConnected ? 'default' : 'secondary'}>
              {isConnected ? 'Wallet Connected' : 'Wallet Disconnected'}
            </Badge>
            {walletAddress && (
              <span className="text-sm text-gray-600 font-mono">
                {walletAddress.slice(0, 8)}...{walletAddress.slice(-8)}
              </span>
            )}
          </div>

          {/* Payment Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              onClick={handleVIPUpgradePayment}
              disabled={!isConnected || isProcessing}
              className="flex items-center gap-2"
            >
              {isProcessing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <CreditCard className="h-4 w-4" />
              )}
              VIP Upgrade (100 XRP)
            </Button>

            <Button
              onClick={handleFUSERewardPayment}
              disabled={!isConnected || isProcessing}
              className="flex items-center gap-2"
              variant="outline"
            >
              {isProcessing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Coins className="h-4 w-4" />
              )}
              FUSE Reward (50 FUSE)
            </Button>

            <Button
              onClick={() => handleGenericPayment('XRP')}
              disabled={!isConnected || isProcessing}
              variant="secondary"
            >
              Generic XRP Payment (25 XRP)
            </Button>

            <Button
              onClick={() => handleGenericPayment('FUSE')}
              disabled={!isConnected || isProcessing}
              variant="secondary"
            >
              Generic FUSE Payment (100 FUSE)
            </Button>
          </div>

          {/* Payment Status */}
          {lastPayment && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <h4 className="font-semibold">Last Payment</h4>
              <div className="text-sm space-y-1">
                <div>Amount: {lastPayment.amount} {lastPayment.currency}</div>
                <div>UUID: {lastPayment.uuid}</div>
                <div>Destination: {lastPayment.destination.slice(0, 8)}...{lastPayment.destination.slice(-8)}</div>
                <div>Time: {new Date(lastPayment.timestamp).toLocaleString()}</div>
              </div>
              <Button
                onClick={handleCheckStatus}
                size="sm"
                variant="outline"
              >
                Check Status
              </Button>
            </div>
          )}

          {/* Payment Result */}
          {paymentResult && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Payment Result</h4>
              <pre className="text-xs bg-white p-2 rounded overflow-auto">
                {JSON.stringify(paymentResult, null, 2)}
              </pre>
            </div>
          )}

          {/* Status Result */}
          {statusResult && (
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Status Result</h4>
              <pre className="text-xs bg-white p-2 rounded overflow-auto">
                {JSON.stringify(statusResult, null, 2)}
              </pre>
            </div>
          )}

          {/* Utility Examples */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Utility Functions</h4>
            <div className="text-sm space-y-1">
              <div>1 XRP = {xrpToDrops('1')} drops</div>
              <div>1,000,000 drops = {dropsToXRP('1000000')} XRP</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Example integration for /upgrade page
export function UpgradePageExample() {
  const { createXRPPayment, isConnected, isProcessing } = useXRPPayment();

  const handleUpgradePayment = async (tier: 'premium' | 'platinum' | 'black') => {
    const amounts = {
      premium: '50',
      platinum: '100', 
      black: '200'
    };

    const result = await createXRPPayment(
      'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx',
      amounts[tier],
      {
        memo: `VIP_UPGRADE_${tier.toUpperCase()}`,
        instruction: `Upgrade to ${tier} VIP card`,
        identifier: `upgrade_${tier}_${Date.now()}`,
      }
    );

    if (result.success && result.deepLink) {
      window.location.href = result.deepLink;
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Premium VIP</CardTitle>
            <CardDescription>50 XRP</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => handleUpgradePayment('premium')}
              disabled={!isConnected || isProcessing}
              className="w-full"
            >
              {isProcessing ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Upgrade'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Platinum VIP</CardTitle>
            <CardDescription>100 XRP</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => handleUpgradePayment('platinum')}
              disabled={!isConnected || isProcessing}
              className="w-full"
            >
              {isProcessing ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Upgrade'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Black VIP</CardTitle>
            <CardDescription>200 XRP</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => handleUpgradePayment('black')}
              disabled={!isConnected || isProcessing}
              className="w-full"
            >
              {isProcessing ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Upgrade'}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Example integration for /fuse page
export function FusePageExample() {
  const { createFUSEPayment, isConnected, isProcessing } = useXRPPayment();

  const handleFUSEPurchase = async (amount: string) => {
    const result = await createFUSEPayment(
      'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo',
      amount,
      {
        memo: 'FUSE_PURCHASE',
        instruction: `Purchase ${amount} FUSE tokens`,
        identifier: `fuse_purchase_${Date.now()}`,
      }
    );

    if (result.success && result.deepLink) {
      window.location.href = result.deepLink;
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {['10', '25', '50', '100'].map(amount => (
          <Button
            key={amount}
            onClick={() => handleFUSEPurchase(amount)}
            disabled={!isConnected || isProcessing}
            variant="outline"
            className="flex flex-col items-center gap-2 h-20"
          >
            <Coins className="h-5 w-5" />
            <span>{amount} FUSE</span>
          </Button>
        ))}
      </div>
    </div>
  );
}