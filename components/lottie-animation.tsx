"use client"

import { LottieReactAnimation } from "./lottie-react-animation"

interface LottieAnimationProps {
  src?: string
  width?: string | number
  height?: string | number
  className?: string
}

export function LottieAnimation({
  src = "https://lottie.host/2de3886b-81e9-40c9-a364-dce493cfc31a/APDopJg77F.lottie",
  width = 300,
  height = 300,
  className = "",
}: LottieAnimationProps) {
  return (
    <div className={`flex justify-center items-center py-4 ${className}`}>
      <LottieReactAnimation
        src={src}
        width={width}
        height={height}
        loop={true}
        autoplay={true}
        speed={1}
      />
    </div>
  )
}
