"use client";

import { useEffect, useState, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
// Removed deprecated auth-client import
import { Loader2 } from "lucide-react";
import { authStateManager } from "@/lib/auth-state-manager";

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string;
  businessOnly?: boolean;
}

export function ProtectedRoute({ children, requiredRole, businessOnly = false }: ProtectedRouteProps) {
  const { user, profile, isLoading, isAdmin, isBusinessOwner, roles, portalRoles } = useAuth();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    // Wait for auth context to finish loading
    if (isLoading) return;

    console.log("ProtectedRoute: Checking auth", {
      user: !!user,
      profile: !!profile,
      pathname: window.location.pathname
    });

    // Check if user is authenticated
    if (!user) {
      // Before redirecting to login, check if this is a wallet return
      const walletReturn = authStateManager.isWalletReturn();
      
      if (walletReturn.isReturn && walletReturn.sessionId) {
        console.log("ProtectedRoute: Wallet return detected, checking stored auth state");
        
        const restoration = authStateManager.restoreAuthState(walletReturn.sessionId);
        
        if (restoration.success && restoration.state) {
          console.log("ProtectedRoute: Auth state found, redirecting to login for restoration");
          // Redirect to login with wallet return parameters for auth restoration
          const loginUrl = `/login?wallet_return=true&session_id=${walletReturn.sessionId}&redirect=${encodeURIComponent(restoration.state.targetRoute)}`;
          setTimeout(() => {
            router.push(loginUrl);
          }, 100);
          return;
        } else {
          console.warn("ProtectedRoute: Wallet return detected but no valid auth state found");
          // Continue with normal login redirect
        }
      }

      console.log("ProtectedRoute: No user, redirecting to login");
      const currentPath = window.location.pathname + window.location.search;
      // Add a small delay to prevent race conditions
      setTimeout(() => {
        router.push(`/login?redirect=${encodeURIComponent(currentPath)}`);
      }, 100);
      return;
    }

    // Check if user has profile - if not, redirect to onboarding
    if (user && !profile) {
      console.log("ProtectedRoute: User authenticated but no profile, redirecting to onboarding");
      // Add a small delay to prevent race conditions
      setTimeout(() => {
        router.push('/onboarding?reason=missing_profile');
      }, 100);
      return;
    }

    // Check role requirements
    if (requiredRole) {
      const hasRole = roles.includes(requiredRole) || portalRoles.includes(requiredRole);
      const isAdminAccess = requiredRole === "admin" && isAdmin;

      if (!hasRole && !isAdminAccess) {
        console.log("ProtectedRoute: User lacks required role, redirecting to dashboard");
        setTimeout(() => {
          router.push("/dashboard");
        }, 100);
        return;
      }
    }

    // Check business owner requirement
    if (businessOnly && !isBusinessOwner) {
      console.log("ProtectedRoute: Business access required but user is not business owner");
      setTimeout(() => {
        router.push("/dashboard");
      }, 100);
      return;
    }

    // All checks passed
    console.log("ProtectedRoute: All checks passed, allowing access");
    setIsAuthorized(true);
  }, [user, profile, isLoading, isAdmin, isBusinessOwner, roles, portalRoles, router, requiredRole, businessOnly]);

  // Show loading while auth context is loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3A56FF]" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show loading while checking authorization
  if (!isAuthorized && user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3A56FF]" />
          <p className="text-gray-600">Setting up your dashboard...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if not authorized (redirect is happening)
  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}
