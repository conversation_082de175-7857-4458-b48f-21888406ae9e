"use client";

import { useEffect, useState } from 'react';
import { getSupabaseClient } from '@/lib/supabase';

// Example component showing the exact subscription pattern you requested
export function RealtimeExample() {
  const [data, setData] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const supabase = getSupabaseClient();
    if (!supabase) return;

    // This is the exact pattern you requested
    const subscription = supabase
      .channel('custom-all-channel')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'businesses' // You can change this to any table
      }, payload => {
        console.log('Change received!', payload);
        
        // Handle the real-time update
        if (payload.eventType === 'INSERT') {
          setData(prev => [payload.new, ...prev]);
        } else if (payload.eventType === 'UPDATE') {
          setData(prev => 
            prev.map(item => 
              item.id === payload.new.id ? payload.new : item
            )
          );
        } else if (payload.eventType === 'DELETE') {
          setData(prev => 
            prev.filter(item => item.id !== payload.old.id)
          );
        }
      })
      .subscribe((status) => {
        console.log('Subscription status:', status);
        setIsConnected(status === 'SUBSCRIBED');
      });

    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(subscription);
    };
  }, []);

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-2">Real-time Subscription Example</h3>
      <div className="mb-2">
        <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
          isConnected ? 'bg-green-500' : 'bg-red-500'
        }`}></span>
        {isConnected ? 'Connected' : 'Disconnected'}
      </div>
      <div className="text-sm text-gray-600">
        <p>This component subscribes to all changes on the 'businesses' table.</p>
        <p>Check the console for real-time updates!</p>
      </div>
    </div>
  );
}

// Example for multiple table subscriptions
export function MultiTableSubscription() {
  const [businessData, setBusinessData] = useState<any[]>([]);
  const [qrData, setQrData] = useState<any[]>([]);
  const [connections, setConnections] = useState({
    businesses: false,
    qr_interactions: false
  });

  useEffect(() => {
    const supabase = getSupabaseClient();
    if (!supabase) return;

    // Subscribe to businesses table
    const businessSubscription = supabase
      .channel('businesses-channel')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'businesses' 
      }, payload => {
        console.log('Business change received!', payload);
        // Handle business updates...
      })
      .subscribe((status) => {
        setConnections(prev => ({ ...prev, businesses: status === 'SUBSCRIBED' }));
      });

    // Subscribe to QR interactions table
    const qrSubscription = supabase
      .channel('qr-interactions-channel')
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'qr_interactions' 
      }, payload => {
        console.log('QR interaction change received!', payload);
        // Handle QR interaction updates...
      })
      .subscribe((status) => {
        setConnections(prev => ({ ...prev, qr_interactions: status === 'SUBSCRIBED' }));
      });

    // Cleanup subscriptions on unmount
    return () => {
      supabase.removeChannel(businessSubscription);
      supabase.removeChannel(qrSubscription);
    };
  }, []);

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-2">Multi-Table Subscription Example</h3>
      <div className="space-y-1">
        <div>
          <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
            connections.businesses ? 'bg-green-500' : 'bg-red-500'
          }`}></span>
          Businesses: {connections.businesses ? 'Connected' : 'Disconnected'}
        </div>
        <div>
          <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
            connections.qr_interactions ? 'bg-green-500' : 'bg-red-500'
          }`}></span>
          QR Interactions: {connections.qr_interactions ? 'Connected' : 'Disconnected'}
        </div>
      </div>
    </div>
  );
}

// Example with filtered subscription (user-specific)
export function FilteredSubscription({ userId }: { userId?: string }) {
  const [userInteractions, setUserInteractions] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!userId) return;

    const supabase = getSupabaseClient();
    if (!supabase) return;

    // Subscribe with filter for specific user
    const subscription = supabase
      .channel(`user-interactions-${userId}`)
      .on('postgres_changes', { 
        event: '*', 
        schema: 'public', 
        table: 'qr_interactions',
        filter: `scanner_user_id=eq.${userId}`
      }, payload => {
        console.log('User-specific change received!', payload);
        
        if (payload.eventType === 'INSERT') {
          setUserInteractions(prev => [payload.new, ...prev]);
        } else if (payload.eventType === 'UPDATE') {
          setUserInteractions(prev => 
            prev.map(item => 
              item.id === payload.new.id ? payload.new : item
            )
          );
        } else if (payload.eventType === 'DELETE') {
          setUserInteractions(prev => 
            prev.filter(item => item.id !== payload.old.id)
          );
        }
      })
      .subscribe((status) => {
        setIsConnected(status === 'SUBSCRIBED');
      });

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [userId]);

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-2">Filtered Subscription Example</h3>
      <div className="mb-2">
        <span className={`inline-block w-3 h-3 rounded-full mr-2 ${
          isConnected ? 'bg-green-500' : 'bg-red-500'
        }`}></span>
        User-specific QR interactions: {isConnected ? 'Connected' : 'Disconnected'}
      </div>
      <div className="text-sm text-gray-600">
        <p>This subscription only receives changes for user ID: {userId}</p>
        <p>Total interactions: {userInteractions.length}</p>
      </div>
    </div>
  );
}
