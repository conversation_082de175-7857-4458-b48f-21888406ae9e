"use client";

import { useState } from 'react';
import { ChatWidget } from './chat-widget';
import { ChatButton } from './chat-button';

export function ChatProvider() {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggleChat = () => {
    setIsOpen(!isOpen);
  };

  const handleCloseChat = () => {
    setIsOpen(false);
  };

  return (
    <>
      <ChatButton 
        onClick={handleToggleChat} 
        isOpen={isOpen}
      />
      <ChatWidget 
        isOpen={isOpen} 
        onClose={handleCloseChat}
      />
    </>
  );
}
