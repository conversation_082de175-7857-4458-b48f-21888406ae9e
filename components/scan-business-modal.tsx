"use client";

import { Dialog } from "@headlessui/react";
import { useEffect, useState } from "react";
import { getSupabaseClient } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";
import { CheckCircle } from "lucide-react";

export function ScanBusinessModal({ isOpen, onClose }) {
  const { user } = useAuth();
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (!isOpen || typeof window === "undefined" || success) return;

    const initScanner = async () => {
      const { Html5QrcodeScanner } = await import("html5-qrcode");

      const scanner = new Html5QrcodeScanner("reader", {
        fps: 10,
        qrbox: { width: 250, height: 250 },
      });

      scanner.render(
        async (decodedText) => {
          try {
            const businessId = decodedText.trim(); // Expect just the UUID

            if (!businessId || businessId.length < 36) throw new Error("Invalid QR Code");

            const supabase = getSupabaseClient();
            if (!supabase) throw new Error("Database connection not available");

            const { error } = await supabase.from("business_visits").insert({
              user_id: user?.id,
              business_id: businessId,
              scanned_at: new Date().toISOString(),
            });

            if (error) throw error;

            setSuccess(true);
            setTimeout(() => {
              setSuccess(false);
              onClose();
            }, 2500);
          } catch (error) {
            console.error("QR Error:", error);
            alert("Failed to log visit.");
            onClose();
          }
        },
        (err) => console.warn("QR Scan failed:", err)
      );

      return () => {
        scanner.clear().catch(console.error);
      };
    };

    initScanner();
  }, [isOpen, success]);

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black bg-opacity-50" />
      <div className="fixed inset-0 flex items-center justify-center">
        <div className="bg-white rounded-xl p-6 w-full max-w-md text-center shadow-lg">
          {success ? (
            <div className="flex flex-col items-center space-y-4">
              <CheckCircle className="text-green-500 w-12 h-12" />
              <h2 className="text-xl font-bold text-black">Visit Logged!</h2>
              <p className="text-gray-600">
                You’re one step closer to earning rewards with FUSE.
              </p>
              <p className="text-sm text-gray-400 italic">This window will close automatically.</p>
            </div>
          ) : (
            <>
              <Dialog.Title className="text-xl font-semibold mb-4 text-black">
                Scan Business QR Code
              </Dialog.Title>
              <div id="reader" className="w-full" />
            </>
          )}
        </div>
      </div>
    </Dialog>
  );
}