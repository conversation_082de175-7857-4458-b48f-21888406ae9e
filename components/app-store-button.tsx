"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"

interface AppStoreButtonProps {
  className?: string
}

export function AppStoreButton({ className }: AppStoreButtonProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [isIOS, setIsIOS] = useState(false)

  useEffect(() => {
    const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera

    // Check if mobile
    const isMobileDevice = /android|iphone|ipad|ipod|blackberry|windows phone/i.test(userAgent.toLowerCase())
    setIsMobile(isMobileDevice)

    // Check if iOS
    const isIOSDevice = /iphone|ipad|ipod/i.test(userAgent.toLowerCase())
    setIsIOS(isIOSDevice)
  }, [])

  const handleClick = () => {
    // iOS App Store link
    const iosAppStoreLink = "https://apps.apple.com/app/xumm-xrpl-wallet/id1492302343"

    // Google Play Store link
    const androidPlayStoreLink = "https://play.google.com/store/apps/details?id=com.xrpllabs.xumm"

    // Default to website if not on mobile
    const xamanWebsite = "https://xumm.app/"

    if (isMobile) {
      if (isIOS) {
        window.location.href = iosAppStoreLink
      } else {
        window.location.href = androidPlayStoreLink
      }
    } else {
      // On desktop, open a new tab with the website
      window.open(xamanWebsite, "_blank")
    }
  }

  return (
    <Button
      onClick={handleClick}
      className={`bg-[#3A56FF] text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center hover:bg-[#2A46EF] transition-colors ${className}`}
    >
      <Image
        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/xaman-logo-J6kQDGmHNAw1OC3KOJz5MFo9DNOUFO.png"
        width={24}
        height={24}
        alt="Xaman Logo"
        className="mr-2"
      />
      Download Xaman App
    </Button>
  )
}
