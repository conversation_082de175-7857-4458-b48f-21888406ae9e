"use client"

import React, { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Gift, Star, ArrowRight, Percent, MapPin } from "lucide-react"
import { PurchaseModal } from "@/components/purchase/purchase-modal"
import { getSupabaseClient } from "@/lib/supabase"
import Image from "next/image"

interface Business {
  id: string
  name: string
  premium_discount: string
  logo_url?: string
  category?: string
  business_address?: string
}

interface DiscountPreviewBannerProps {
  className?: string
  showTitle?: boolean
  maxBusinesses?: number
}

export function DiscountPreviewBanner({ 
  className = "", 
  showTitle = true,
  maxBusinesses = 6 
}: DiscountPreviewBannerProps) {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)

  useEffect(() => {
    fetchFeaturedBusinesses()
  }, [])

  useEffect(() => {
    if (businesses.length > 0) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % businesses.length)
      }, 4000) // Change every 4 seconds

      return () => clearInterval(interval)
    }
  }, [businesses.length])

  const fetchFeaturedBusinesses = async () => {
    try {
      setIsLoading(true)

      const supabase = getSupabaseClient()
      if (!supabase) {
        console.error("Supabase client not initialized")
        return
      }

      const { data, error } = await supabase
        .from("businesses")
        .select("id, name, premium_discount, logo_url, category, business_address")
        .eq("is_active", true)
        .limit(maxBusinesses)
        .order("name")

      if (error) {
        console.error("Error fetching businesses:", error)
      } else if (data) {
        setBusinesses(data)
      }
    } catch (error) {
      console.error("Error fetching businesses:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const extractState = (address: string) => {
    if (!address) return ""
    const parts = address.split(",")
    if (parts.length >= 3) {
      return parts[2].trim()
    }
    return ""
  }

  if (isLoading) {
    return (
      <div className={`bg-gradient-to-r from-blue-600 to-purple-600 text-white py-8 ${className}`}>
        <div className="container mx-auto px-4 text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-white/20 rounded w-64 mx-auto mb-4"></div>
            <div className="h-4 bg-white/20 rounded w-48 mx-auto"></div>
          </div>
        </div>
      </div>
    )
  }

  if (businesses.length === 0) {
    return null
  }

  const currentBusiness = businesses[currentIndex]

  return (
    <>
      <div className={`bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white py-12 relative overflow-hidden ${className}`}>
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-4 left-4 w-16 h-16 bg-white rounded-full animate-pulse"></div>
          <div className="absolute top-8 right-8 w-12 h-12 bg-white rounded-full animate-bounce"></div>
          <div className="absolute bottom-4 left-1/4 w-8 h-8 bg-white rounded-full animate-ping"></div>
          <div className="absolute bottom-8 right-1/3 w-10 h-10 bg-white rounded-full animate-pulse"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {showTitle && (
            <div className="text-center mb-8">
              <Badge className="bg-white/20 text-white mb-4 text-lg px-6 py-2 border-white/30">
                <Gift className="h-5 w-5 mr-2" />
                Exclusive VIP Discounts
              </Badge>
              <h2 className="text-3xl md:text-5xl font-bold mb-4">
                Save Money at Local Businesses
              </h2>
              <p className="text-xl text-white/90 max-w-2xl mx-auto">
                Get instant access to exclusive discounts at 73+ verified businesses
              </p>
            </div>
          )}

          {/* Featured Business Carousel */}
          <div className="max-w-4xl mx-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20"
              >
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                  {/* Business Info */}
                  <div className="text-center lg:text-left">
                    <div className="flex items-center justify-center lg:justify-start mb-4">
                      {currentBusiness.logo_url && currentBusiness.logo_url !== "/placeholder-logo.png" ? (
                        <div className="w-16 h-16 rounded-lg overflow-hidden mr-4 bg-white/20">
                          <Image
                            src={currentBusiness.logo_url}
                            alt={currentBusiness.name}
                            width={64}
                            height={64}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                          <Gift className="h-8 w-8 text-white" />
                        </div>
                      )}
                      <div>
                        <h3 className="text-2xl font-bold text-white">{currentBusiness.name}</h3>
                        {currentBusiness.category && (
                          <p className="text-white/80">{currentBusiness.category}</p>
                        )}
                        {currentBusiness.business_address && (
                          <div className="flex items-center text-white/70 mt-1">
                            <MapPin className="h-4 w-4 mr-1" />
                            {extractState(currentBusiness.business_address)}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Discount Badge */}
                    <div className="mb-6">
                      <div className="inline-flex items-center bg-gradient-to-r from-green-400 to-emerald-500 text-white px-6 py-3 rounded-full text-2xl font-bold shadow-lg">
                        <Percent className="h-6 w-6 mr-2" />
                        {currentBusiness.premium_discount}
                      </div>
                    </div>

                    <p className="text-white/90 text-lg mb-6">
                      VIP members save with exclusive discounts at this location and 72+ other businesses
                    </p>
                  </div>

                  {/* CTA Section */}
                  <div className="text-center">
                    <div className="bg-white/10 rounded-xl p-6 mb-6">
                      <div className="flex items-center justify-center mb-4">
                        <Star className="h-6 w-6 text-yellow-400 mr-2" />
                        <span className="text-xl font-semibold">VIP Card Benefits</span>
                      </div>
                      <ul className="text-left space-y-2 text-white/90">
                        <li className="flex items-center">
                          <ArrowRight className="h-4 w-4 mr-2 text-green-400" />
                          Instant access to all discounts
                        </li>
                        <li className="flex items-center">
                          <ArrowRight className="h-4 w-4 mr-2 text-green-400" />
                          Digital card on your phone
                        </li>
                        <li className="flex items-center">
                          <ArrowRight className="h-4 w-4 mr-2 text-green-400" />
                          One-time payment, no monthly fees
                        </li>
                      </ul>
                    </div>

                    <div className="space-y-4">
                      <div className="text-center">
                        <span className="text-4xl font-bold">$100</span>
                        <span className="text-lg text-white/80 ml-2">one-time</span>
                      </div>
                      
                      <Button
                        onClick={() => setShowPurchaseModal(true)}
                        size="lg"
                        className="w-full bg-white text-blue-600 hover:bg-gray-100 font-bold text-lg py-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                      >
                        Get Your VIP Card Now
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Button>
                      
                      <p className="text-sm text-white/70">
                        Join {businesses.length * 12}+ happy VIP members
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Business Indicators */}
            {businesses.length > 1 && (
              <div className="flex justify-center mt-6 space-x-2">
                {businesses.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentIndex 
                        ? "bg-white scale-125" 
                        : "bg-white/40 hover:bg-white/60"
                    }`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <PurchaseModal 
        isOpen={showPurchaseModal} 
        onClose={() => setShowPurchaseModal(false)} 
      />
    </>
  )
}
