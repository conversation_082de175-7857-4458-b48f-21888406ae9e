"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { User, Database, Wallet, CheckCircle, AlertCircle, ArrowRight, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/contexts/auth-context"
import { createClient } from "@/utils/supabase/client"
import { WalletConnectButton } from "@/components/wallet/wallet-connect-button"
import { useSimpleWallet } from "@/hooks/use-simple-wallet"

interface FlowStep {
  id: string
  name: string
  status: 'pending' | 'loading' | 'success' | 'error'
  data?: any
  error?: string
}

export function AuthWalletFlowTest() {
  const { user, signUp, signIn, signOut } = useAuth()
  const { isConnected, walletAddress, connect, disconnect } = useSimpleWallet()
  const [flowSteps, setFlowSteps] = useState<FlowStep[]>([
    { id: 'auth', name: 'User Authentication', status: 'pending' },
    { id: 'profile', name: 'Profile Creation/Fetch', status: 'pending' },
    { id: 'wallet', name: 'Wallet Connection', status: 'pending' },
    { id: 'sync', name: 'Profile-Wallet Sync', status: 'pending' }
  ])
  const [testEmail] = useState(`test${Date.now()}@ex.com`)
  const [testPassword] = useState('ComplexTestPassword2024!@#')
  const [profileData, setProfileData] = useState<any>(null)
  const [isRunningTest, setIsRunningTest] = useState(false)

  const updateStep = (stepId: string, status: FlowStep['status'], data?: any, error?: string) => {
    setFlowSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, status, data, error } : step
    ))
  }

  const logStep = (stepName: string, data: any) => {
    console.log(`🔍 [AUTH-WALLET-FLOW] ${stepName}:`, data)
  }

  const runCompleteFlow = async () => {
    setIsRunningTest(true)
    
    try {
      // Step 1: Authentication
      logStep('Starting Authentication Flow', { email: testEmail })
      updateStep('auth', 'loading')
      
      // Try to sign up first, then sign in if user exists
      try {
        const metadata = {
          first_name: 'Test',
          last_name: 'User',
          phone: '555-0123'
        }
        const signUpResult = await signUp(testEmail, testPassword, metadata)
        logStep('Sign Up Result', signUpResult)

        if (signUpResult.error) {
          throw signUpResult.error
        }

        updateStep('auth', 'success', { action: 'signup', result: signUpResult })
      } catch (signUpError: any) {
        logStep('Sign Up Failed, Trying Sign In', signUpError)
        try {
          const signInResult = await signIn(testEmail, testPassword)
          logStep('Sign In Result', signInResult)

          if (signInResult.error) {
            throw signInResult.error
          }

          updateStep('auth', 'success', { action: 'signin', result: signInResult })
        } catch (signInError: any) {
          logStep('Sign In Failed', signInError)
          updateStep('auth', 'error', null, signInError.message)
          return
        }
      }

      // Wait for auth context to update
      await new Promise(resolve => setTimeout(resolve, 2000))

    } catch (error: any) {
      logStep('Authentication Error', error)
      updateStep('auth', 'error', null, error.message)
    } finally {
      setIsRunningTest(false)
    }
  }

  // Monitor auth state changes
  useEffect(() => {
    if (user && flowSteps.find(s => s.id === 'auth')?.status === 'success') {
      fetchProfileData()
    }
  }, [user])

  const fetchProfileData = async () => {
    if (!user) return

    logStep('Fetching Profile Data', { userId: user.id })
    updateStep('profile', 'loading')

    try {
      const supabase = createClient()
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error) {
        logStep('Profile Fetch Error', error)
        updateStep('profile', 'error', null, error.message)
        return
      }

      logStep('Profile Data Retrieved', profile)
      setProfileData(profile)
      updateStep('profile', 'success', profile)

    } catch (error: any) {
      logStep('Profile Fetch Exception', error)
      updateStep('profile', 'error', null, error.message)
    }
  }

  const testWalletConnection = async () => {
    logStep('Testing Wallet Connection', { userAuthenticated: !!user })
    updateStep('wallet', 'loading')

    try {
      const result = await connect()
      logStep('Wallet Connect Result', result)
      
      if (result) {
        updateStep('wallet', 'success', { connected: true, address: walletAddress })
        // Test sync after connection
        setTimeout(testProfileWalletSync, 2000)
      } else {
        updateStep('wallet', 'error', null, 'Wallet connection failed')
      }
    } catch (error: any) {
      logStep('Wallet Connection Error', error)
      updateStep('wallet', 'error', null, error.message)
    }
  }

  const testProfileWalletSync = async () => {
    if (!user) return

    logStep('Testing Profile-Wallet Sync', { userId: user.id, walletAddress })
    updateStep('sync', 'loading')

    try {
      const supabase = createClient()
      const { data: updatedProfile, error } = await supabase
        .from('profiles')
        .select('xrp_wallet_address')
        .eq('id', user.id)
        .single()

      if (error) {
        logStep('Profile Sync Check Error', error)
        updateStep('sync', 'error', null, error.message)
        return
      }

      logStep('Profile Sync Check Result', updatedProfile)
      
      if (updatedProfile.xrp_wallet_address === walletAddress) {
        updateStep('sync', 'success', updatedProfile)
      } else {
        updateStep('sync', 'error', null, 'Wallet address not synced to profile')
      }

    } catch (error: any) {
      logStep('Profile Sync Exception', error)
      updateStep('sync', 'error', null, error.message)
    }
  }

  const resetTest = async () => {
    if (user) {
      await signOut()
    }
    setFlowSteps(prev => prev.map(step => ({ ...step, status: 'pending', data: undefined, error: undefined })))
    setProfileData(null)
  }

  const getStepIcon = (step: FlowStep) => {
    switch (step.status) {
      case 'loading': return <Loader2 className="h-4 w-4 animate-spin text-blue-400" />
      case 'success': return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'error': return <AlertCircle className="h-4 w-4 text-red-400" />
      default: return <div className="h-4 w-4 rounded-full border-2 border-gray-400" />
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto bg-[#1A1A1A] border border-white/10 text-white">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5 text-[#3A56FF]" />
          Auth → Wallet Flow Test
        </CardTitle>
        <CardDescription className="text-gray-400">
          Test complete user flow: Authentication → Profile → Wallet → Sync
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Current State */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white/5 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <User className="h-4 w-4 text-blue-400" />
              <span className="text-sm font-medium">Auth Status</span>
            </div>
            <Badge className={user ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}>
              {user ? 'Authenticated' : 'Not Authenticated'}
            </Badge>
            {user && (
              <p className="text-xs text-gray-400 mt-1">{user.email}</p>
            )}
          </div>

          <div className="bg-white/5 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <Database className="h-4 w-4 text-purple-400" />
              <span className="text-sm font-medium">Profile</span>
            </div>
            <Badge className={profileData ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}>
              {profileData ? 'Loaded' : 'Not Loaded'}
            </Badge>
            {profileData?.xrp_wallet_address && (
              <p className="text-xs text-green-400 mt-1 font-mono break-all">
                {profileData.xrp_wallet_address.substring(0, 8)}...
              </p>
            )}
          </div>

          <div className="bg-white/5 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <Wallet className="h-4 w-4 text-orange-400" />
              <span className="text-sm font-medium">Wallet</span>
            </div>
            <Badge className={isConnected ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}>
              {isConnected ? 'Connected' : 'Not Connected'}
            </Badge>
            {walletAddress && (
              <p className="text-xs text-green-400 mt-1 font-mono break-all">
                {walletAddress.substring(0, 8)}...
              </p>
            )}
          </div>
        </div>

        {/* Flow Steps */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Flow Steps</h3>
          {flowSteps.map((step, index) => (
            <motion.div
              key={step.id}
              className="flex items-center gap-3 p-3 bg-white/5 rounded-lg"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {getStepIcon(step)}
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{step.name}</span>
                  <Badge className={`text-xs ${
                    step.status === 'success' ? 'bg-green-500/20 text-green-400' :
                    step.status === 'error' ? 'bg-red-500/20 text-red-400' :
                    step.status === 'loading' ? 'bg-blue-500/20 text-blue-400' :
                    'bg-gray-500/20 text-gray-400'
                  }`}>
                    {step.status}
                  </Badge>
                </div>
                {step.error && (
                  <p className="text-xs text-red-400 mt-1">{step.error}</p>
                )}
                {step.data && (
                  <p className="text-xs text-gray-400 mt-1">
                    {JSON.stringify(step.data, null, 2).substring(0, 100)}...
                  </p>
                )}
              </div>
              {index < flowSteps.length - 1 && (
                <ArrowRight className="h-4 w-4 text-gray-400" />
              )}
            </motion.div>
          ))}
        </div>

        {/* Test Controls */}
        <div className="flex gap-3 pt-4 border-t border-white/10">
          <Button
            onClick={runCompleteFlow}
            disabled={isRunningTest}
            className="bg-[#3A56FF] hover:bg-[#2A46EF]"
          >
            {isRunningTest ? 'Running Test...' : 'Run Complete Flow'}
          </Button>
          
          {user && (
            <Button
              onClick={testWalletConnection}
              disabled={isRunningTest}
              variant="outline"
            >
              Test Wallet Connection
            </Button>
          )}
          
          <Button
            onClick={resetTest}
            variant="outline"
            className="ml-auto"
          >
            Reset Test
          </Button>
        </div>

        {/* Wallet Component Test */}
        {user && (
          <div className="pt-4 border-t border-white/10">
            <h3 className="text-lg font-semibold mb-3">Live Wallet Component</h3>
            <div className="bg-white/5 rounded-lg p-4">
              <WalletConnectButton />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
