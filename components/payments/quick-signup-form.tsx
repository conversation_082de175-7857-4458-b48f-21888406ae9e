"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Loader2, AlertCircle, Mail, Lock, User, Phone } from 'lucide-react'
import { getSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { VIPCardTier, VIP_CARD_TIERS } from '@/hooks/use-vip-card-payment'

interface QuickSignupFormProps {
  tier: VIPCardTier
  onSuccess: () => void
  onError: (error: string) => void
  showCloseButton?: boolean
}

export function QuickSignupForm({ 
  tier, 
  onSuccess, 
  onError,
  showCloseButton = true 
}: QuickSignupFormProps) {
  const { refreshSession } = useAuth()
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const cardConfig = VIP_CARD_TIERS[tier]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (error) setError(null) // Clear error when user starts typing
  }

  const validateForm = () => {
    if (!formData.email || !formData.password || !formData.firstName || !formData.lastName) {
      return 'Please fill in all required fields'
    }
    if (formData.password !== formData.confirmPassword) {
      return 'Passwords do not match'
    }
    if (formData.password.length < 6) {
      return 'Password must be at least 6 characters'
    }
    if (!formData.email.includes('@')) {
      return 'Please enter a valid email address'
    }
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      return
    }

    setIsLoading(true)

    try {
      const supabase = getSupabaseClient()
      if (!supabase) {
        throw new Error('Authentication service unavailable')
      }

      // Register the user
      const { data, error: signUpError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          emailRedirectTo: `${window.location.origin}/dashboard`,
          data: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            phone: formData.phone,
          }
        }
      })

      if (signUpError) {
        throw new Error(signUpError.message)
      }

      const userId = data?.user?.id
      if (!userId) {
        throw new Error('Registration failed - please try again')
      }

      // Sign in immediately after signup
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password
      })

      if (signInError) {
        throw new Error('Account created but sign-in failed - please try logging in')
      }

      // Create profile
      try {
        const profileResponse = await fetch('/api/auth/create-profile', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            email: formData.email,
            firstName: formData.firstName,
            lastName: formData.lastName,
            phone: formData.phone,
            referringBusinessId: '',
            isBusinessApplicant: false
          }),
        })

        if (profileResponse.ok) {
          const profileResult = await profileResponse.json()
          console.log('Profile created successfully:', profileResult.message)
        }
      } catch (profileError) {
        console.warn('Profile creation warning:', profileError)
        // Continue even if profile creation fails - will be handled by fallback mechanisms
      }

      // Refresh the auth context
      await refreshSession()

      // Success - move to payment step
      onSuccess()

    } catch (err: any) {
      console.error('Quick signup error:', err)
      setError(err.message || 'Registration failed - please try again')
      onError(err.message || 'Registration failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-md mx-auto">
      {/* Card Benefits Header */}
      <div className="bg-gradient-to-r from-[#3A56FF]/10 to-purple-600/10 rounded-lg p-4 mb-6 border border-[#3A56FF]/20">
        <h4 className="font-semibold text-white mb-2">You're getting {cardConfig.name}</h4>
        <div className="flex justify-between text-sm">
          <span className="text-gray-300">Price:</span>
          <span className="font-bold text-[#3A56FF]">${cardConfig.price}</span>
        </div>
        {cardConfig.duration && (
          <div className="flex justify-between text-sm mt-1">
            <span className="text-gray-300">Duration:</span>
            <span className="text-green-400">
              {cardConfig.duration === 30 ? '1 Month' : 
               cardConfig.duration === 365 ? '1 Year' : 'Lifetime'}
            </span>
          </div>
        )}
      </div>

      {/* Quick Benefits */}
      <div className="mb-6">
        <p className="text-sm text-gray-400 mb-3">Create your account to unlock:</p>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center space-x-1">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Exclusive discounts</span>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Cashback rewards</span>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">VIP access</span>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-green-400">✓</span>
            <span className="text-gray-300">Priority support</span>
          </div>
        </div>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-4 p-3 bg-red-500/10 border border-red-500/20 text-red-400 rounded-lg flex items-start text-sm"
        >
          <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
          <span>{error}</span>
        </motion.div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Name Fields */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              First Name *
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                className="w-full pl-10 pr-3 py-2 bg-[#2A2A2A] border border-gray-600 rounded-lg focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent text-white"
                required
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Last Name *
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                className="w-full pl-10 pr-3 py-2 bg-[#2A2A2A] border border-gray-600 rounded-lg focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent text-white"
                required
              />
            </div>
          </div>
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Email Address *
          </label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="w-full pl-10 pr-3 py-2 bg-[#2A2A2A] border border-gray-600 rounded-lg focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent text-white"
              placeholder="<EMAIL>"
              required
            />
          </div>
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Phone Number
          </label>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full pl-10 pr-3 py-2 bg-[#2A2A2A] border border-gray-600 rounded-lg focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent text-white"
              placeholder="(*************"
            />
          </div>
        </div>

        {/* Password Fields */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Password *
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="w-full pl-10 pr-3 py-2 bg-[#2A2A2A] border border-gray-600 rounded-lg focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent text-white"
                placeholder="••••••••"
                required
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Confirm *
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                className="w-full pl-10 pr-3 py-2 bg-[#2A2A2A] border border-gray-600 rounded-lg focus:ring-2 focus:ring-[#3A56FF] focus:border-transparent text-white"
                placeholder="••••••••"
                required
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <motion.button
          type="submit"
          disabled={isLoading}
          className="w-full py-3 bg-gradient-to-r from-[#3A56FF] to-purple-600 text-white rounded-lg font-semibold hover:from-[#2A46EF] hover:to-purple-700 transition-all disabled:opacity-70 disabled:cursor-not-allowed flex justify-center items-center"
          whileHover={!isLoading ? { scale: 1.02 } : {}}
          whileTap={!isLoading ? { scale: 0.98 } : {}}
        >
          {isLoading ? (
            <>
              <Loader2 className="animate-spin h-4 w-4 mr-2" />
              Creating Account...
            </>
          ) : (
            'Create Account & Continue'
          )}
        </motion.button>
      </form>

      {/* Terms */}
      <p className="text-xs text-gray-500 text-center mt-4">
        By creating an account, you agree to our Terms of Service and Privacy Policy
      </p>
    </div>
  )
}