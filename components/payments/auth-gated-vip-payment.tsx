"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, CreditCard, Wallet, Coins, X, CheckCircle } from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import { VIPCardPayment } from './vip-card-payment'
import { VIPCardTier } from '@/hooks/use-vip-card-payment'
import { QuickSignupForm } from './quick-signup-form'

interface AuthGatedVIPPaymentProps {
  tier: VIPCardTier
  onSuccess?: () => void
  onError?: (error: string) => void
  onClose?: () => void
  className?: string
  stripeButtonId?: string
  xamanPaymentUrl?: string
  xamanPaymentUrls?: {
    xrp: string
    fuse: string
    amounts: {
      xrp: number
      fuse: number
      usd: number
    }
  }
}

type PaymentStep = 'auth-check' | 'signup' | 'payment' | 'success'

export function AuthGatedVIPPayment({ 
  tier, 
  onSuccess, 
  onError, 
  onClose,
  className = '',
  stripeButtonId,
  xamanPaymentUrl,
  xamanPaymentUrls
}: AuthGatedVIPPaymentProps) {
  const { user, isLoading } = useAuth()
  const [currentStep, setCurrentStep] = useState<PaymentStep>('auth-check')
  const [showBackButton, setShowBackButton] = useState(false)

  // Check authentication status when component mounts
  useEffect(() => {
    if (!isLoading) {
      if (user) {
        setCurrentStep('payment')
      } else {
        setCurrentStep('signup')
        setShowBackButton(false)
      }
    }
  }, [user, isLoading])

  const handleSignupSuccess = () => {
    setCurrentStep('payment')
  }

  const handleSignupError = (error: string) => {
    onError?.(error)
  }

  const handlePaymentSuccess = () => {
    setCurrentStep('success')
    setTimeout(() => {
      onSuccess?.()
    }, 2000)
  }

  const handleBackToSignup = () => {
    setCurrentStep('signup')
    setShowBackButton(false)
  }

  if (isLoading) {
    return (
      <div className={`bg-[#1A1A1A] rounded-xl p-6 text-white ${className}`}>
        <div className="flex items-center justify-center h-48">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-[#1A1A1A] rounded-xl text-white overflow-hidden ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b border-gray-800">
        <div className="flex items-center space-x-3">
          {showBackButton && currentStep === 'payment' && (
            <button
              onClick={handleBackToSignup}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
          )}
          <div>
            <h3 className="text-xl font-bold">
              {currentStep === 'signup' ? 'Create Account' : 
               currentStep === 'payment' ? 'Choose Payment Method' :
               currentStep === 'success' ? 'Purchase Complete!' : 'Getting Ready...'}
            </h3>
            <p className="text-gray-400">
              {currentStep === 'signup' ? 'Quick signup to secure your VIP membership' : 
               currentStep === 'payment' ? 'Select how you\'d like to pay' :
               currentStep === 'success' ? 'Welcome to Fuse VIP!' : 'Please wait...'}
            </p>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Progress Indicator */}
      <div className="px-6 py-4 bg-[#2A2A2A] border-b border-gray-800">
        <div className="flex items-center space-x-4">
          <div className={`flex items-center space-x-2 ${
            currentStep === 'signup' ? 'text-blue-400' : 
            currentStep === 'payment' || currentStep === 'success' ? 'text-green-400' : 'text-gray-400'
          }`}>
            <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
              currentStep === 'payment' || currentStep === 'success' ? 'bg-green-500 border-green-500' :
              currentStep === 'signup' ? 'border-blue-400' : 'border-gray-600'
            }`}>
              {(currentStep === 'payment' || currentStep === 'success') ? (
                <CheckCircle className="h-3 w-3 text-white" />
              ) : (
                <span className="text-xs font-bold">1</span>
              )}
            </div>
            <span className="text-sm font-medium">Account</span>
          </div>
          
          <div className={`h-0.5 flex-1 ${
            currentStep === 'payment' || currentStep === 'success' ? 'bg-green-500' : 'bg-gray-600'
          }`}></div>
          
          <div className={`flex items-center space-x-2 ${
            currentStep === 'payment' ? 'text-blue-400' : 
            currentStep === 'success' ? 'text-green-400' : 'text-gray-400'
          }`}>
            <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
              currentStep === 'success' ? 'bg-green-500 border-green-500' :
              currentStep === 'payment' ? 'border-blue-400' : 'border-gray-600'
            }`}>
              {currentStep === 'success' ? (
                <CheckCircle className="h-3 w-3 text-white" />
              ) : (
                <span className="text-xs font-bold">2</span>
              )}
            </div>
            <span className="text-sm font-medium">Payment</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <AnimatePresence mode="wait">
        {currentStep === 'signup' && (
          <motion.div
            key="signup"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="p-6"
          >
            <QuickSignupForm
              tier={tier}
              onSuccess={handleSignupSuccess}
              onError={handleSignupError}
              showCloseButton={false}
            />
          </motion.div>
        )}

        {currentStep === 'payment' && (
          <motion.div
            key="payment"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {stripeButtonId ? (
              // Show direct Stripe button with custom success handling
              <div className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-white mb-2">Complete Your Purchase</h3>
                  <p className="text-gray-400">Click below to securely complete your VIP membership purchase</p>
                </div>
                <div 
                  className="w-full"
                  dangerouslySetInnerHTML={{
                    __html: `<stripe-buy-button 
                      buy-button-id="${stripeButtonId}" 
                      publishable-key="pk_live_51QwvpfE4IOnXedOCMTsYaU72T9XduMcJ4aJNM3DEIlq3DNjlEkb72yCYAUUtmLRDpnXtMpXgQ1xakPb5lcmijab30043s5auJo"
                    ></stripe-buy-button>`
                  }} 
                />
                <div className="mt-4 text-center text-sm text-gray-500">
                  After payment, you'll be redirected to complete your VIP setup
                </div>
              </div>
            ) : xamanPaymentUrls ? (
              // Show crypto payment options (XRP and FUSE)
              <div className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-white mb-2">Choose Your Crypto</h3>
                  <p className="text-gray-400">Select XRP or FUSE to complete your payment via Xaman</p>
                </div>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-[#2A2A2A] rounded-lg p-4">
                      <div className="text-center mb-3">
                        <div className="text-lg font-bold text-blue-400">{xamanPaymentUrls.amounts.xrp.toFixed(6)} XRP</div>
                        <div className="text-sm text-gray-500">≈ ${xamanPaymentUrls.amounts.usd}</div>
                      </div>
                      <a
                        href={xamanPaymentUrls.xrp}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                      >
                        <span>Pay XRP</span>
                      </a>
                    </div>
                    <div className="bg-[#2A2A2A] rounded-lg p-4">
                      <div className="text-center mb-3">
                        <div className="text-lg font-bold text-purple-400">{xamanPaymentUrls.amounts.fuse.toLocaleString()} FUSE</div>
                        <div className="text-sm text-gray-500">≈ ${xamanPaymentUrls.amounts.usd}</div>
                      </div>
                      <a
                        href={xamanPaymentUrls.fuse}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full py-3 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-center gap-2"
                      >
                        <span>Pay FUSE</span>
                      </a>
                    </div>
                  </div>
                  <div className="text-center text-sm text-gray-500">
                    <p>Opens Xaman wallet for secure crypto payment</p>
                    <p className="mt-1">After payment, return here to complete your VIP setup</p>
                  </div>
                </div>
              </div>
            ) : xamanPaymentUrl ? (
              // Show single Xaman payment button (fallback)
              <div className="p-6">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-white mb-2">Pay with Xaman</h3>
                  <p className="text-gray-400">Click below to complete your crypto payment via Xaman</p>
                </div>
                <div className="space-y-4">
                  <a
                    href={xamanPaymentUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full py-4 bg-gradient-to-r from-[#316bff] to-purple-600 text-white rounded-lg font-medium hover:from-[#2557d6] hover:to-purple-700 transition-all flex items-center justify-center gap-3"
                  >
                    <Wallet className="h-5 w-5" />
                    <span>Pay with Xaman</span>
                  </a>
                  <div className="text-center text-sm text-gray-500">
                    <p>Opens Xaman wallet for secure crypto payment</p>
                    <p className="mt-1">After payment, return here to complete your VIP setup</p>
                  </div>
                </div>
              </div>
            ) : (
              // Show regular payment options
              <VIPCardPayment
                tier={tier}
                onSuccess={handlePaymentSuccess}
                onError={onError}
                onClose={onClose}
                className="bg-transparent rounded-none"
              />
            )}
          </motion.div>
        )}

        {currentStep === 'success' && (
          <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="p-6 text-center"
          >
            <div className="bg-green-500/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            <h3 className="text-xl font-bold text-green-400 mb-2">Welcome to Fuse VIP!</h3>
            <p className="text-gray-400 mb-4">
              Your VIP membership is now active. You'll be redirected to your dashboard shortly.
            </p>
            <div className="bg-[#2A2A2A] rounded-lg p-4">
              <div className="flex items-center justify-center space-x-4 text-sm">
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-4 w-4 text-green-400" />
                  <span>VIP Access</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Wallet className="h-4 w-4 text-blue-400" />
                  <span>Exclusive Discounts</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Coins className="h-4 w-4 text-purple-400" />
                  <span>Rewards Program</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}