"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CreditCard, Wallet, Coins, Loader2, AlertCircle, CheckCircle, X } from 'lucide-react'
import { useVIPCardPayment, VIPCardTier, PaymentMethod } from '@/hooks/use-vip-card-payment'
import { useXamanWallet } from '@/hooks/use-xaman-wallet'

interface VIPCardPaymentProps {
  tier: VIPCardTier
  onSuccess?: () => void
  onError?: (error: string) => void
  onClose?: () => void
  className?: string
}

export function VIPCardPayment({ 
  tier, 
  onSuccess, 
  onError, 
  onClose,
  className = '' 
}: VIPCardPaymentProps) {
  const {
    purchaseVIPCard,
    getPricingPreview,
    isProcessing,
    currentStep,
    error,
    VIP_CARD_TIERS
  } = useVIPCardPayment()

  const { isConnected, connect } = useXamanWallet()
  
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('stripe')
  const [pricingData, setPricingData] = useState<any>(null)
  const [showPaymentMethods, setShowPaymentMethods] = useState(false)

  const cardConfig = VIP_CARD_TIERS[tier]

  // Load pricing data when payment method changes
  useEffect(() => {
    const loadPricing = async () => {
      const pricing = await getPricingPreview(tier, selectedPaymentMethod)
      setPricingData(pricing)
    }
    loadPricing()
  }, [tier, selectedPaymentMethod, getPricingPreview])

  // Handle payment method selection
  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setSelectedPaymentMethod(method)
    if (method !== 'stripe') {
      setShowPaymentMethods(true)
    }
  }

  // Handle purchase
  const handlePurchase = async () => {
    try {
      // For crypto payments, ensure wallet is connected
      if (selectedPaymentMethod !== 'stripe' && !isConnected) {
        await connect()
        return
      }

      const result = await purchaseVIPCard(tier, selectedPaymentMethod)
      
      if (result.success) {
        onSuccess?.()
      } else {
        onError?.(result.error || 'Payment failed')
      }
    } catch (error: any) {
      onError?.(error.message || 'Payment failed')
    }
  }

  // Payment method options
  const paymentMethods = [
    {
      id: 'stripe' as PaymentMethod,
      name: 'Credit Card',
      icon: CreditCard,
      description: 'Pay with credit/debit card',
      available: !!cardConfig.stripeButtonId,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'XRP' as PaymentMethod,
      name: 'XRP',
      icon: Wallet,
      description: 'Pay with XRP cryptocurrency',
      available: true,
      color: 'from-[#3A56FF] to-[#5A7BFF]'
    },
    {
      id: 'FUSE' as PaymentMethod,
      name: 'FUSE',
      icon: Coins,
      description: 'Pay with FUSE tokens',
      available: true,
      color: 'from-purple-500 to-purple-600'
    }
  ]

  return (
    <div className={`bg-[#1A1A1A] rounded-xl p-6 text-white ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-xl font-bold">{cardConfig.name}</h3>
          <p className="text-gray-400">Choose your payment method</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Pricing Display */}
      {pricingData && (
        <div className="bg-[#2A2A2A] rounded-lg p-4 mb-6">
          <div className="flex justify-between items-center">
            <span className="text-gray-300">Price:</span>
            <div className="text-right">
              <div className="text-lg font-bold">${pricingData.usdPrice}</div>
              {pricingData.cryptoAmount && (
                <div className="text-sm text-gray-400">
                  ≈ {pricingData.cryptoAmount} {pricingData.currency}
                </div>
              )}
            </div>
          </div>
          {cardConfig.duration && (
            <div className="flex justify-between items-center mt-2 pt-2 border-t border-gray-600">
              <span className="text-gray-300">Duration:</span>
              <span className="text-white">
                {cardConfig.duration === 30 ? '1 Month' : 
                 cardConfig.duration === 365 ? '1 Year' : 'Lifetime'}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Payment Method Selection */}
      <div className="space-y-3 mb-6">
        <h4 className="font-semibold text-gray-300">Payment Method</h4>
        {paymentMethods.map((method) => (
          <motion.button
            key={method.id}
            onClick={() => handlePaymentMethodSelect(method.id)}
            disabled={!method.available || isProcessing}
            className={`w-full p-4 rounded-lg border-2 transition-all ${
              selectedPaymentMethod === method.id
                ? 'border-blue-500 bg-blue-500/10'
                : 'border-gray-600 hover:border-gray-500'
            } ${!method.available ? 'opacity-50 cursor-not-allowed' : ''}`}
            whileHover={method.available ? { scale: 1.02 } : {}}
            whileTap={method.available ? { scale: 0.98 } : {}}
          >
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${method.color} flex items-center justify-center`}>
                <method.icon className="h-5 w-5 text-white" />
              </div>
              <div className="text-left flex-1">
                <div className="font-medium">{method.name}</div>
                <div className="text-sm text-gray-400">{method.description}</div>
                {!method.available && (
                  <div className="text-xs text-red-400">Not available for this tier</div>
                )}
              </div>
              {selectedPaymentMethod === method.id && (
                <CheckCircle className="h-5 w-5 text-blue-500" />
              )}
            </div>
          </motion.button>
        ))}
      </div>

      {/* Wallet Connection Status for Crypto Payments */}
      {selectedPaymentMethod !== 'stripe' && (
        <div className="mb-6">
          {isConnected ? (
            <div className="flex items-center space-x-2 text-green-400 bg-green-400/10 rounded-lg p-3">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">Wallet connected</span>
            </div>
          ) : (
            <div className="flex items-center space-x-2 text-yellow-400 bg-yellow-400/10 rounded-lg p-3">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">Wallet connection required for crypto payments</span>
            </div>
          )}
        </div>
      )}

      {/* Status Display */}
      <AnimatePresence>
        {(isProcessing || error) && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mb-6"
          >
            {isProcessing && (
              <div className="flex items-center space-x-3 text-blue-400 bg-blue-400/10 rounded-lg p-4">
                <Loader2 className="h-5 w-5 animate-spin" />
                <div>
                  <div className="font-medium">Processing Payment</div>
                  {currentStep && (
                    <div className="text-sm text-gray-400">{currentStep}</div>
                  )}
                </div>
              </div>
            )}
            
            {error && (
              <div className="flex items-center space-x-3 text-red-400 bg-red-400/10 rounded-lg p-4">
                <AlertCircle className="h-5 w-5" />
                <div>
                  <div className="font-medium">Payment Error</div>
                  <div className="text-sm text-gray-400">{error}</div>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Purchase Button */}
      <motion.button
        onClick={handlePurchase}
        disabled={isProcessing || (selectedPaymentMethod !== 'stripe' && !isConnected)}
        className={`w-full py-4 rounded-lg font-semibold transition-all ${
          isProcessing || (selectedPaymentMethod !== 'stripe' && !isConnected)
            ? 'bg-gray-600 cursor-not-allowed'
            : 'bg-gradient-to-r from-[#3A56FF] to-purple-600 hover:from-[#2A46EF] hover:to-purple-700'
        }`}
        whileHover={!isProcessing ? { scale: 1.02 } : {}}
        whileTap={!isProcessing ? { scale: 0.98 } : {}}
      >
        {isProcessing ? (
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Processing...</span>
          </div>
        ) : selectedPaymentMethod !== 'stripe' && !isConnected ? (
          'Connect Wallet First'
        ) : (
          `Purchase ${cardConfig.name}`
        )}
      </motion.button>

      {/* Additional Info */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        {selectedPaymentMethod === 'stripe' ? (
          'Secure payment processed by Stripe'
        ) : (
          'Crypto payments are processed on the XRPL network'
        )}
      </div>
    </div>
  )
}
