"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CreditCard, Wallet, Coins, X } from 'lucide-react'
import { AuthGatedVIPPayment } from './auth-gated-vip-payment'
import { VIPCardTier } from '@/hooks/use-vip-card-payment'

interface VIPCardButtonProps {
  tier: VIPCardTier
  className?: string
  children?: React.ReactNode
  variant?: 'default' | 'stripe-only' | 'crypto-only' | 'stripe-direct' | 'crypto-direct'
  stripeButtonId?: string
  xamanPaymentUrl?: string
  xamanPaymentUrls?: {
    xrp: string
    fuse: string
    amounts: {
      xrp: number
      fuse: number
      usd: number
    }
  }
}

export function VIPCardButton({ 
  tier, 
  className = '', 
  children,
  variant = 'default',
  stripeButtonId,
  xamanPaymentUrl,
  xamanPaymentUrls
}: VIPCardButtonProps) {
  const [showPaymentModal, setShowPaymentModal] = useState(false)

  const handleSuccess = () => {
    setShowPaymentModal(false)
    // Success is handled by the payment component (redirect to success page)
  }

  const handleError = (error: string) => {
    console.error('Payment error:', error)
    // Error is displayed in the payment component
  }

  // For stripe-only variant, render the existing Stripe button
  if (variant === 'stripe-only') {
    const stripeButtonIds = {
      monthly: 'buy_btn_1RbT1cE4IOnXedOCDlLSY0gA',
      annual: 'buy_btn_1ReD59E4IOnXedOCfvsWehxl',
      lifetime: null
    }

    const buttonId = stripeButtonIds[tier]
    
    if (!buttonId) {
      return (
        <button 
          className={`w-full py-3 bg-gray-400 text-white rounded-lg font-medium cursor-not-allowed ${className}`}
          disabled
        >
          Not Available
        </button>
      )
    }

    return (
      <div 
        className={className}
        dangerouslySetInnerHTML={{
          __html: `<stripe-buy-button buy-button-id="${buttonId}" publishable-key="pk_live_51QwvpfE4IOnXedOCMTsYaU72T9XduMcJ4aJNM3DEIlq3DNjlEkb72yCYAUUtmLRDpnXtMpXgQ1xakPb5lcmijab30043s5auJo"></stripe-buy-button>`
        }} 
      />
    )
  }

  // For stripe-direct variant, show profile creation flow first, then direct Stripe button
  if (variant === 'stripe-direct') {
    if (!stripeButtonId) {
      return (
        <button 
          className={`w-full py-3 bg-gray-400 text-white rounded-lg font-medium cursor-not-allowed ${className}`}
          disabled
        >
          Not Available
        </button>
      )
    }

    return (
      <>
        {/* Trigger Button */}
        <motion.button
          onClick={() => setShowPaymentModal(true)}
          className={`w-full py-3 bg-[#3A56FF] text-white rounded-lg font-medium hover:bg-[#2A46EF] transition-colors ${className}`}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {children || 'Get VIP Access'}
        </motion.button>

        {/* Payment Modal with Profile Creation */}
        <AnimatePresence>
          {showPaymentModal && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={(e) => {
                if (e.target === e.currentTarget) {
                  setShowPaymentModal(false)
                }
              }}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="w-full max-w-md"
                onClick={(e) => e.stopPropagation()}
              >
                <AuthGatedVIPPayment
                  tier={tier}
                  onSuccess={handleSuccess}
                  onError={handleError}
                  onClose={() => setShowPaymentModal(false)}
                  stripeButtonId={stripeButtonId}
                />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </>
    )
  }

  // For crypto-direct variant, show profile creation flow first, then direct Xaman link
  if (variant === 'crypto-direct') {
    if (!xamanPaymentUrls && !xamanPaymentUrl) {
      return (
        <button 
          className={`w-full py-3 bg-gray-400 text-white rounded-lg font-medium cursor-not-allowed ${className}`}
          disabled
        >
          Not Available
        </button>
      )
    }

    return (
      <>
        {/* Trigger Button */}
        <motion.button
          onClick={() => setShowPaymentModal(true)}
          className={`w-full py-3 bg-gradient-to-r from-[#316bff] to-purple-600 text-white rounded-lg font-medium hover:from-[#2557d6] hover:to-purple-700 transition-colors ${className}`}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {children || 'Pay with Crypto'}
        </motion.button>

        {/* Payment Modal with Profile Creation */}
        <AnimatePresence>
          {showPaymentModal && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={(e) => {
                if (e.target === e.currentTarget) {
                  setShowPaymentModal(false)
                }
              }}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="w-full max-w-md"
                onClick={(e) => e.stopPropagation()}
              >
                <AuthGatedVIPPayment
                  tier={tier}
                  onSuccess={handleSuccess}
                  onError={handleError}
                  onClose={() => setShowPaymentModal(false)}
                  xamanPaymentUrl={xamanPaymentUrl}
                  xamanPaymentUrls={xamanPaymentUrls}
                />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </>
    )
  }

  return (
    <>
      {/* Trigger Button */}
      <motion.button
        onClick={() => setShowPaymentModal(true)}
        className={`w-full py-3 bg-gradient-to-r from-[#3A56FF] to-purple-600 text-white rounded-lg font-medium hover:from-[#2A46EF] hover:to-purple-700 transition-all flex items-center justify-center gap-2 ${className}`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {children || (
          <>
            <div className="flex items-center gap-1">
              <CreditCard className="h-4 w-4" />
              <Wallet className="h-4 w-4" />
              <Coins className="h-4 w-4" />
            </div>
            <span>Choose Payment Method</span>
          </>
        )}
      </motion.button>

      {/* Payment Modal */}
      <AnimatePresence>
        {showPaymentModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowPaymentModal(false)
              }
            }}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
            >
              <AuthGatedVIPPayment
                tier={tier}
                onSuccess={handleSuccess}
                onError={handleError}
                onClose={() => setShowPaymentModal(false)}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

// Convenience components for specific tiers
export function MonthlyVIPButton({ className = '', children }: { className?: string, children?: React.ReactNode }) {
  return (
    <VIPCardButton tier="monthly" className={className}>
      {children || 'Get Monthly VIP - $9.99'}
    </VIPCardButton>
  )
}

export function AnnualVIPButton({ className = '', children }: { className?: string, children?: React.ReactNode }) {
  return (
    <VIPCardButton tier="annual" className={className}>
      {children || 'Get Annual VIP - $100'}
    </VIPCardButton>
  )
}

export function LifetimeVIPButton({ className = '', children }: { className?: string, children?: React.ReactNode }) {
  return (
    <VIPCardButton tier="lifetime" className={className}>
      {children || 'Get Lifetime VIP - $1500'}
    </VIPCardButton>
  )
}

// Stripe-only variants (for backward compatibility)
export function MonthlyVIPStripeButton({ className = '' }: { className?: string }) {
  return <VIPCardButton tier="monthly" variant="stripe-only" className={className} />
}

export function AnnualVIPStripeButton({ className = '' }: { className?: string }) {
  return <VIPCardButton tier="annual" variant="stripe-only" className={className} />
}
