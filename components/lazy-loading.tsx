"use client";

import React, { Suspense, lazy, useState, useEffect, useRef } from "react";
import { CyclingBusinesses } from "@/components/business/cycling-businesses";

// Lazy load the CyclingBusinesses component
const LazyCyclingBusinessesComponent = lazy(() => 
  import("@/components/business/cycling-businesses").then(module => ({
    default: module.CyclingBusinesses
  }))
);

// Business List Skeleton Component
export function BusinessListSkeleton() {
  return (
    <section className="py-16 bg-gradient-to-r from-[#316bff]/5 via-purple-500/5 to-pink-500/5">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="h-8 bg-gray-200 rounded-lg w-64 mx-auto mb-4 animate-pulse"></div>
          <div className="h-12 bg-gray-200 rounded-lg w-96 mx-auto mb-4 animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded-lg w-80 mx-auto animate-pulse"></div>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100">
            <div className="grid grid-cols-1 lg:grid-cols-2 min-h-[300px]">
              {/* Logo Section Skeleton */}
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-8">
                <div className="w-48 h-48 bg-gray-200 rounded-xl animate-pulse"></div>
              </div>
              
              {/* Info Section Skeleton */}
              <div className="p-8 flex flex-col justify-center">
                <div className="h-8 bg-gray-200 rounded-lg w-3/4 mb-4 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded-lg w-1/2 mb-6 animate-pulse"></div>
                
                <div className="bg-gray-100 rounded-xl p-6 border-2 border-dashed border-gray-200 mb-4">
                  <div className="text-center">
                    <div className="h-4 bg-gray-200 rounded w-32 mx-auto mb-2 animate-pulse"></div>
                    <div className="h-12 bg-gray-200 rounded w-24 mx-auto mb-2 animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded w-40 mx-auto animate-pulse"></div>
                  </div>
                </div>
                
                <div className="h-12 bg-gray-200 rounded-xl w-full animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Lazy Wrapper Component
export function LazyWrapper({ 
  children, 
  fallback = <BusinessListSkeleton /> 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  );
}

// Intersection Observer Hook for Lazy Loading on Viewport
function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, options]);

  return isIntersecting;
}

// Lazy On Viewport Component
export function LazyOnViewport({ 
  children, 
  fallback = <BusinessListSkeleton />,
  className = ""
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const isIntersecting = useIntersectionObserver(ref);
  const [hasLoaded, setHasLoaded] = useState(false);

  useEffect(() => {
    if (isIntersecting && !hasLoaded) {
      setHasLoaded(true);
    }
  }, [isIntersecting, hasLoaded]);

  return (
    <div ref={ref} className={className}>
      {hasLoaded ? children : fallback}
    </div>
  );
}

// Lazy Cycling Businesses Component
export function LazyCyclingBusinesses() {
  return (
    <Suspense fallback={<BusinessListSkeleton />}>
      <LazyCyclingBusinessesComponent />
    </Suspense>
  );
}

// Export all components
export {
  LazyCyclingBusinessesComponent as LazyCyclingBusinessesRaw
};
