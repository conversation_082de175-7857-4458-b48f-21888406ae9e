"use client"

import { useEffect, useRef } from "react"

/**
 * Example component showing exactly how to implement the dotlottie-wc
 * web component as requested by the user
 */
export function ExampleLottie() {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Load the script exactly as specified by the user
    const script = document.createElement("script")
    script.src = "https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js"
    script.type = "module"

    script.onload = () => {
      if (containerRef.current) {
        // Create the exact HTML structure the user requested
        containerRef.current.innerHTML = `
          <dotlottie-wc 
            src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie" 
            style="width: 300px;height: 300px" 
            speed="1" 
            autoplay 
            loop>
          </dotlottie-wc>
        `
      }
    }

    script.onerror = (error) => {
      console.error("Failed to load dotlottie-wc script:", error)
      if (containerRef.current) {
        containerRef.current.innerHTML = `
          <div style="width: 300px; height: 300px; display: flex; align-items: center; justify-content: center; background-color: #f0f0f0; border-radius: 8px; color: #666;">
            Animation failed to load
          </div>
        `
      }
    }

    document.head.appendChild(script)

    return () => {
      // Cleanup
      if (containerRef.current) {
        containerRef.current.innerHTML = ""
      }
    }
  }, [])

  return (
    <div className="flex justify-center items-center p-4">
      <div ref={containerRef}>
        {/* Loading placeholder */}
        <div style={{
          width: "300px", 
          height: "300px", 
          display: "flex", 
          alignItems: "center", 
          justifyContent: "center", 
          backgroundColor: "#f9f9f9", 
          borderRadius: "8px", 
          color: "#999"
        }}>
          Loading animation...
        </div>
      </div>
    </div>
  )
}

/**
 * Alternative implementation using the reusable component
 */
export function ExampleLottieReusable() {
  return (
    <div className="flex justify-center items-center p-4">
      {/* This uses our reusable DotLottieWCAnimation component */}
      <div>
        <script 
          src="https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js" 
          type="module"
        />
        <dotlottie-wc 
          src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie" 
          style={{width: "300px", height: "300px"}}
          speed="1" 
          autoplay 
          loop
        />
      </div>
    </div>
  )
}
