"use client"

import { motion } from "framer-motion"

interface PageHeaderProps {
  title: string
  subtitle?: string
  description?: string
}

export function PageHeader({ title, subtitle, description }: PageHeaderProps) {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  return (
    <div className="bg-[#f8f9fa] py-16 md:py-24">
      <motion.div className="container mx-auto px-4 text-center" variants={container} initial="hidden" animate="show">
        {subtitle && (
          <motion.div variants={item} className="text-[#316bff] font-medium mb-2">
            {subtitle}
          </motion.div>
        )}
        <motion.h1 variants={item} className="text-3xl md:text-5xl font-bold mb-4">
          {title}
        </motion.h1>
        {description && (
          <motion.p variants={item} className="text-[#4a4a4a] max-w-2xl mx-auto">
            {description}
          </motion.p>
        )}
      </motion.div>
    </div>
  )
}
