"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  X, 
  <PERSON>R<PERSON>,
  Spark<PERSON>,
  TrendingUp
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface FuseTokenAnnouncementProps {
  showOnPages?: string[];
  autoHide?: boolean;
  autoHideDelay?: number;
}

export function FuseTokenAnnouncement({ 
  showOnPages = ['/', '/yield-pool'],
  autoHide = false,
  autoHideDelay = 10000 
}: FuseTokenAnnouncementProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasBeenDismissed, setHasBeenDismissed] = useState(false);

  useEffect(() => {
    // Check if user has already dismissed this announcement
    const dismissed = localStorage.getItem('fuse-token-announcement-dismissed');
    if (dismissed) {
      setHasBeenDismissed(true);
      return;
    }

    // Check if we should show on current page
    const currentPath = window.location.pathname;
    const shouldShow = showOnPages.some(page => 
      page === currentPath || (page === '/' && currentPath === '/')
    );

    if (shouldShow) {
      // Delay showing the announcement
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [showOnPages]);

  useEffect(() => {
    if (isVisible && autoHide) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [isVisible, autoHide, autoHideDelay]);

  const handleDismiss = () => {
    setIsVisible(false);
    setHasBeenDismissed(true);
    localStorage.setItem('fuse-token-announcement-dismissed', 'true');
  };

  const handleLearnMore = () => {
    // Scroll to FUSE token section or navigate to yield pool
    if (window.location.pathname === '/yield-pool') {
      // Scroll to the FUSE token multiplier section
      const fuseSection = document.querySelector('[data-fuse-multiplier]');
      if (fuseSection) {
        fuseSection.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      // Navigate to yield pool page
      window.location.href = '/yield-pool#fuse-multiplier';
    }
    handleDismiss();
  };

  if (hasBeenDismissed || !isVisible) {
    return null;
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            onClick={handleDismiss}
          />
          
          {/* Announcement Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-md mx-4"
          >
            <div className="bg-gradient-to-br from-purple-900/95 to-pink-900/95 backdrop-blur-xl border border-purple-500/50 rounded-2xl p-6 shadow-2xl relative overflow-hidden">
              {/* Animated Background */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-purple-500/10 animate-pulse"></div>
              
              {/* Close Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="absolute top-4 right-4 text-white/70 hover:text-white z-10"
              >
                <X className="h-4 w-4" />
              </Button>

              {/* Content */}
              <div className="relative space-y-4">
                {/* Header */}
                <div className="text-center space-y-2">
                  <motion.div
                    animate={{ 
                      rotate: [0, 10, -10, 0],
                      scale: [1, 1.1, 1]
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                    className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto"
                  >
                    <Flame className="h-8 w-8 text-white" />
                  </motion.div>
                  
                  <div className="space-y-1">
                    <h2 className="text-2xl font-bold text-white">
                      FUSE Token Burning
                    </h2>
                    <div className="flex items-center justify-center gap-2">
                      <Badge className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0">
                        Coming Soon
                      </Badge>
                      <Sparkles className="h-4 w-4 text-yellow-400 animate-pulse" />
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div className="text-center space-y-3">
                  <p className="text-white/90 text-lg font-medium">
                    Supercharge Your Yields!
                  </p>
                  <p className="text-white/70 text-sm leading-relaxed">
                    Burn FUSE tokens to unlock powerful yield multipliers up to <span className="text-green-400 font-bold">3x</span> your current earnings. 
                    The more you burn, the higher your rewards!
                  </p>
                </div>

                {/* Features Preview */}
                <div className="space-y-2">
                  <div className="flex items-center gap-3 text-sm text-gray-200">
                    <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full"></div>
                    <span>Yield multipliers up to 3x</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm text-gray-200">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full"></div>
                    <span>Premium grid bot strategies</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm text-gray-200">
                    <div className="w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full"></div>
                    <span>Exclusive VIP features</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-2">
                  <Button
                    onClick={handleLearnMore}
                    className="flex-1 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white border-0 font-medium"
                  >
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Learn More
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                  <Button
                    onClick={handleDismiss}
                    variant="outline"
                    className="border-white/20 text-white/70 hover:text-white hover:bg-white/10"
                  >
                    Later
                  </Button>
                </div>

                {/* Footer */}
                <div className="text-center pt-2">
                  <p className="text-white/50 text-xs">
                    Start accumulating FUSE tokens now to be ready for launch!
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
