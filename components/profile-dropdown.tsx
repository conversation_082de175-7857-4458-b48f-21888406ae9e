"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { User, LogOut, ChevronDown, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { ProfileDisplay } from "@/components/profile/profile-display"
// Removed deprecated auth-client import

export function ProfileDropdown() {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const { user, profile, signOut } = useAuth()
  const router = useRouter()

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleLogout = async () => {
    setIsLoggingOut(true)
    setIsOpen(false)
    
    try {
      // Sign out using auth context (includes clearing cache)
      await signOut()
      
      // Force page reload to ensure clean state
      window.location.reload()
    } catch (error) {
      console.error('Logout error:', error)
      // Force navigation even if signOut fails
      window.location.href = '/'
    } finally {
      setIsLoggingOut(false)
    }
  }

  const handleDashboard = () => {
    setIsOpen(false)
    router.push('/dashboard')
  }



  // Get user display name
  const displayName = profile?.first_name 
    ? `${profile.first_name} ${profile.last_name || ''}`.trim()
    : user?.email?.split('@')[0] || 'User'

  const userInitials = profile?.first_name 
    ? `${profile.first_name[0]}${profile.last_name?.[0] || ''}`.toUpperCase()
    : user?.email?.[0]?.toUpperCase() || 'U'

  if (isLoggingOut) {
    return (
      <div className="flex items-center space-x-2 text-white">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm">Signing out...</span>
      </div>
    )
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 bg-[#2A2A2A] hover:bg-[#3A3A3A] text-white px-3 py-2 rounded-md text-sm transition-colors"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Profile Avatar */}
        <div className="w-6 h-6 bg-[#3A56FF] rounded-full flex items-center justify-center text-xs font-medium text-white">
          {userInitials}
        </div>
        <span className="hidden sm:block max-w-24 truncate">{displayName}</span>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50"
          >
            {/* User Info */}
            <div className="px-4 py-2 border-b border-gray-100">
              <ProfileDisplay variant="header" />
            </div>

            {/* Menu Items */}
            <button
              onClick={handleDashboard}
              className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <User className="h-4 w-4 mr-3" />
              Dashboard
            </button>

            <div className="border-t border-gray-100 my-1"></div>

            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
            >
              <LogOut className="h-4 w-4 mr-3" />
              Sign Out
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
