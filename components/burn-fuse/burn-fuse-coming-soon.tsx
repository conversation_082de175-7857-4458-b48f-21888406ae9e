'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Flame,
  Zap,
  TrendingUp,
  Star,
  Spark<PERSON>,
  Clock,
  ArrowRight,
  Gift,
  Target,
  Crown,
  Rocket,
  Trophy,
  DollarSign
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface MultiplierTier {
  id: string
  name: string
  fuseRequired: number
  multiplier: number
  color: string
  icon: React.ReactNode
  benefits: string[]
  description: string
}

const MULTIPLIER_TIERS: MultiplierTier[] = [
  {
    id: 'bronze',
    name: 'Bronze Boost',
    fuseRequired: 1000,
    multiplier: 1.25,
    color: 'from-orange-600 to-orange-400',
    icon: <Flame className="h-5 w-5" />,
    description: 'Entry-level multiplier for new users',
    benefits: [
      '25% reward multiplier',
      'Priority transaction processing',
      'Basic analytics dashboard',
      'Community access'
    ]
  },
  {
    id: 'silver',
    name: 'Silver Surge',
    fuseRequired: 5000,
    multiplier: 1.5,
    color: 'from-gray-400 to-gray-200',
    icon: <Zap className="h-5 w-5" />,
    description: 'Enhanced benefits for committed users',
    benefits: [
      '50% reward multiplier',
      'Advanced features access',
      'Weekly performance reports',
      'Early access to new features',
      'VIP customer support'
    ]
  },
  {
    id: 'gold',
    name: 'Gold Rush',
    fuseRequired: 15000,
    multiplier: 2.0,
    color: 'from-yellow-500 to-yellow-300',
    icon: <Star className="h-5 w-5" />,
    description: 'Premium tier with maximum benefits',
    benefits: [
      '100% reward multiplier',
      'Premium algorithm access',
      'Daily performance insights',
      'Exclusive strategy access',
      'Personal account manager',
      'Beta feature testing'
    ]
  },
  {
    id: 'platinum',
    name: 'Platinum Power',
    fuseRequired: 50000,
    multiplier: 3.0,
    color: 'from-purple-500 to-purple-300',
    icon: <Crown className="h-5 w-5" />,
    description: 'Ultimate tier for power users',
    benefits: [
      '200% reward multiplier',
      'AI-powered optimization',
      'Real-time strategy adjustments',
      'Governance voting rights',
      'Exclusive events access',
      'Premium support priority'
    ]
  }
]

export function BurnFuseComingSoon() {
  const [selectedTier, setSelectedTier] = useState<string | null>(null)

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center space-y-6"
      >
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-600/20 blur-3xl rounded-full"></div>
          <div className="relative bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-2xl p-8 border border-purple-500/20 backdrop-blur-sm">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full">
                <Flame className="h-8 w-8 text-white animate-pulse" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Burn $FUSE
              </h1>
              <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white border-0 animate-bounce shadow-lg text-lg px-4 py-2">
                Coming Soon
              </Badge>
            </div>
            
            <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
              Burn FUSE tokens to unlock powerful multipliers and exclusive features. 
              The more you burn, the higher your rewards across the entire Fuse ecosystem!
            </p>
            
            <div className="flex items-center justify-center gap-6 mt-6">
              <div className="flex items-center gap-2 text-blue-300">
                <Rocket className="h-5 w-5" />
                <span className="font-medium">Multiplier Powers</span>
              </div>
              <div className="flex items-center gap-2 text-green-300">
                <Trophy className="h-5 w-5" />
                <span className="font-medium">Exclusive Access</span>
              </div>
              <div className="flex items-center gap-2 text-yellow-300">
                <DollarSign className="h-5 w-5" />
                <span className="font-medium">Higher Rewards</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Multiplier Tiers */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="space-y-6"
      >
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-2">Multiplier Tiers</h2>
          <p className="text-white/70">Choose your power level and unlock exclusive benefits</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {MULTIPLIER_TIERS.map((tier, index) => (
            <motion.div
              key={tier.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
              className="relative"
            >
              <Card 
                className={`bg-gradient-to-br from-gray-900/90 to-gray-800/90 border-gray-700/50 backdrop-blur-sm hover:border-purple-500/50 transition-all duration-300 cursor-pointer transform hover:scale-105 ${
                  selectedTier === tier.id ? 'ring-2 ring-purple-500' : ''
                }`}
                onClick={() => setSelectedTier(selectedTier === tier.id ? null : tier.id)}
              >
                <CardHeader className="text-center pb-4">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${tier.color} flex items-center justify-center`}>
                    {tier.icon}
                  </div>
                  <CardTitle className="text-white text-xl">{tier.name}</CardTitle>
                  <CardDescription className="text-white/70">{tier.description}</CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className={`text-3xl font-bold bg-gradient-to-r ${tier.color} bg-clip-text text-transparent`}>
                      {tier.multiplier}x
                    </div>
                    <div className="text-white/70 text-sm">Multiplier Power</div>
                  </div>
                  
                  <div className="bg-white/5 rounded-lg p-3 text-center">
                    <div className="text-sm text-white/70 mb-1">FUSE Required</div>
                    <div className="text-lg font-bold text-white">
                      {tier.fuseRequired.toLocaleString()}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-white/80">Benefits:</div>
                    <ul className="space-y-1">
                      {tier.benefits.slice(0, 3).map((benefit, idx) => (
                        <li key={idx} className="text-xs text-gray-200 flex items-center gap-2">
                          <div className="w-1 h-1 bg-blue-400 rounded-full"></div>
                          {benefit}
                        </li>
                      ))}
                      {tier.benefits.length > 3 && (
                        <li className="text-xs text-blue-400 font-medium">
                          +{tier.benefits.length - 3} more benefits
                        </li>
                      )}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Coming Soon Alert */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Alert className="bg-gradient-to-r from-blue-900/30 to-indigo-900/30 border-blue-400/60 backdrop-blur-sm">
          <Clock className="h-5 w-5 text-blue-400" />
          <AlertTitle className="text-white text-lg font-semibold">Development in Progress</AlertTitle>
          <AlertDescription className="text-gray-100 mt-2">
            The FUSE token burning mechanism is currently under development. This feature will allow you to:
            <ul className="mt-3 space-y-1 ml-4">
              <li>• Burn FUSE tokens to unlock permanent multipliers</li>
              <li>• Access exclusive features and premium content</li>
              <li>• Participate in platform governance decisions</li>
              <li>• Get priority access to new platform features</li>
            </ul>
            <div className="mt-4 p-3 bg-white/10 rounded-lg border border-white/20">
              <strong className="text-blue-300">Stay tuned!</strong> Follow our updates for the latest news on when this feature will be available.
            </div>
          </AlertDescription>
        </Alert>
      </motion.div>
    </div>
  )
}
