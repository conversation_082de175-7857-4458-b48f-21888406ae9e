"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/auth-context";
import { Search, Building2, Send, CheckCircle } from "lucide-react";
import { notificationService } from "@/lib/notifications";

interface BusinessAccessRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Business {
  id: string;
  name: string;
  business_address?: string;
  contact_name?: string;
  contact_email?: string;
}

export function BusinessAccessRequestModal({ isOpen, onClose }: BusinessAccessRequestModalProps) {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);
  const [message, setMessage] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  // Search for businesses
  const searchBusinesses = async (query: string) => {
    if (!query.trim()) return;

    setIsSearching(true);
    setError("");

    try {
      // Use the custom API endpoint instead of direct Supabase
      const response = await fetch(`/api/referring-businesses?search=${encodeURIComponent(query)}`);

      if (!response.ok) {
        throw new Error('Failed to search businesses');
      }

      const result = await response.json();
      setBusinesses(result.data || []);
    } catch (err) {
      console.error('Business search error:', err);
      setError('Failed to search businesses. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.length >= 2) {
        searchBusinesses(searchQuery);
      } else {
        setBusinesses([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const handleSubmitRequest = async () => {
    if (!user || !selectedBusiness) {
      setError("Please select a business and ensure you are logged in");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      // Submit the access request via API
      const response = await fetch('/api/business-access-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          business_id: selectedBusiness.id,
          message: message.trim() || null
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit request');
      }

      // Send notification to admin
      try {
        const userName = `${user.user_metadata?.first_name || ''} ${user.user_metadata?.last_name || ''}`.trim() || user.email || 'Unknown User';
        await notificationService.notifyBusinessAccessRequest(
          userName,
          user.email || '',
          selectedBusiness.name
        );
      } catch (notificationError) {
        console.error('Failed to send notification:', notificationError);
        // Don't fail the request if notification fails
      }

      setSuccess(true);
      setTimeout(() => {
        onClose();
        resetForm();
      }, 2000);

    } catch (err) {
      console.error('Request submission error:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit request');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setSearchQuery("");
    setBusinesses([]);
    setSelectedBusiness(null);
    setMessage("");
    setError("");
    setSuccess(false);
    setIsSearching(false);
    setIsSubmitting(false);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      resetForm();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            Request Business Portal Access
          </DialogTitle>
        </DialogHeader>

        {success ? (
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              Request Submitted Successfully!
            </h3>
            <p className="text-green-600">
              Your access request has been sent to the business owner and admin. 
              You'll be notified when it's reviewed.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-700">
                Is your business already listed on Fuse? Search for it below and request access to manage its portal.
              </p>
            </div>

            {/* Search for businesses */}
            <div className="space-y-2">
              <Label htmlFor="search">Search for your business</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Enter business name or city..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  disabled={isSubmitting}
                />
              </div>
              
              {isSearching && (
                <p className="text-sm text-gray-500">Searching...</p>
              )}
            </div>

            {/* Search results */}
            {businesses.length > 0 && (
              <div className="space-y-2">
                <Label>Select your business</Label>
                <div className="max-h-40 overflow-y-auto space-y-1">
                  {businesses.map((business) => (
                    <button
                      key={business.id}
                      onClick={() => setSelectedBusiness(business)}
                      className={`w-full text-left p-3 rounded-lg border transition-colors ${
                        selectedBusiness?.id === business.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      disabled={isSubmitting}
                    >
                      <div className="font-medium">{business.name}</div>
                      {business.business_address && (
                        <div className="text-sm text-gray-500">
                          {business.business_address}
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Message field */}
            {selectedBusiness && (
              <div className="space-y-2">
                <Label htmlFor="message">
                  Message to business owner (optional)
                </Label>
                <Textarea
                  id="message"
                  placeholder="Briefly explain your role at this business..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={3}
                  disabled={isSubmitting}
                />
              </div>
            )}

            {/* Error message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            )}

            {/* Action buttons */}
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitRequest}
                disabled={!selectedBusiness || isSubmitting}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Request
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}