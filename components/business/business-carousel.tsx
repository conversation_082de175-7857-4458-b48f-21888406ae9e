"use client"

import { useState, useEffect, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ExternalLink, ChevronLeft, ChevronRight, X } from "lucide-react"
import { BusinessLogo } from "@/components/business-logo"

interface Business {
  id: string
  name: string
  premium_discount: string | number
  website?: string
  logo_url?: string
  category?: string
  business_address?: string
  description?: string
  contact_info?: string
}

interface BusinessCarouselProps {
  businesses: Business[]
  batchSize?: number
  autoRotate?: boolean
  showControls?: boolean
}

export function BusinessCarousel({ 
  businesses, 
  batchSize = 5, 
  autoRotate = true,
  showControls = true 
}: BusinessCarouselProps) {
  const [currentBatch, setCurrentBatch] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const [expandedDiscount, setExpandedDiscount] = useState<string | null>(null)
  
  // Group businesses into batches
  const batches = useMemo(() => {
    const chunks = []
    for (let i = 0; i < businesses.length; i += batchSize) {
      chunks.push(businesses.slice(i, i + batchSize))
    }
    return chunks
  }, [businesses, batchSize])

  // Auto-cycle through batches
  useEffect(() => {
    if (!autoRotate || batches.length <= 1 || isPaused) return

    const interval = setInterval(() => {
      setCurrentBatch((prev) => (prev + 1) % batches.length)
    }, 4000)

    return () => clearInterval(interval)
  }, [batches.length, isPaused, autoRotate])

  const handleVisitWebsite = (business: Business) => {
    if (business.website) {
      const url = business.website.startsWith('http') ? business.website : `https://${business.website}`
      window.open(url, '_blank', 'noopener,noreferrer')
    } else if (business.contact_info) {
      alert(`Contact Info for ${business.name}:\n${business.contact_info}`)
    }
  }

  if (batches.length === 0) {
    return (
      <div className="text-center py-12">
        {/* Show nothing - businesses are loading in background */}
      </div>
    )
  }

  const currentBusinesses = batches[currentBatch] || []
  const totalBatches = batches.length
  const totalBusinesses = businesses.length

  return (
    <div 
      className="relative"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      {/* Header */}
      {showControls && (
        <div className="bg-gradient-to-r from-gray-900/95 to-black/95 backdrop-blur-lg rounded-t-xl p-4 border-b border-gray-700/50">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white font-mono text-sm tracking-wide">FEATURED BUSINESSES</span>
            </div>
            <div className="text-[#00d4ff] font-mono text-sm">
              BATCH {currentBatch + 1}/{totalBatches} • {totalBusinesses} TOTAL
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-3 bg-gray-800 rounded-full h-1">
            <motion.div
              className="bg-gradient-to-r from-[#316bff] to-[#00d4ff] h-full rounded-full"
              initial={{ width: "0%" }}
              animate={{ width: `${((currentBatch + 1) / totalBatches) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>
      )}
      
      {/* Business List */}
      <div className="bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-blur-lg rounded-b-xl border border-gray-700/50 min-h-[400px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentBatch}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            className="p-6"
          >
            <div className="space-y-3">
              {currentBusinesses.map((business, index) => (
                <motion.div
                  key={business.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="group relative bg-gradient-to-r from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-lg p-4 border border-gray-700/50 hover:border-[#316bff]/50 transition-all duration-300 cursor-pointer hover:bg-gradient-to-r hover:from-[#316bff]/10 hover:to-[#00d4ff]/10"
                  onClick={() => {
                    if (business.website || business.contact_info) {
                      handleVisitWebsite(business)
                    }
                  }}
                >
                  <div className="flex items-start gap-4">
                    {/* Business Logo */}
                    <BusinessLogo
                      src={business.logo_url}
                      alt={`${business.name} logo`}
                      businessName={business.name}
                      businessId={business.id}
                      size="medium"
                      futuristic={true}
                      glowEffect={true}
                    />
                    
                    {/* Business Info */}
                    <div className="flex-1 min-w-0">
                      {/* Business Name as Main Headline */}
                      <h3 className="text-white font-bold text-xl mb-2 group-hover:text-[#00d4ff] transition-colors">
                        {business.name}
                      </h3>

                      {/* Premium Discount - Clickable */}
                      <div
                        className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg font-bold text-base shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer hover:scale-105 inline-block mb-3"
                        onClick={(e) => {
                          e.stopPropagation()
                          setExpandedDiscount(business.id)
                        }}
                      >
                        {business.premium_discount}{typeof business.premium_discount === 'number' ? '% OFF' : ''} - Click for Details
                      </div>

                      {/* Business Details */}
                      <div className="space-y-1">
                        {business.category && (
                          <p className="text-gray-400 text-sm capitalize">
                            {business.category.replace('_', ' ')}
                          </p>
                        )}
                        {business.business_address && (
                          <p className="text-gray-300 text-sm">
                            📍 {business.business_address}
                          </p>
                        )}
                        {business.website && (
                          <div className="flex items-center gap-2 text-[#316bff] text-sm hover:text-[#00d4ff] transition-colors">
                            <ExternalLink className="h-4 w-4" />
                            <span className="truncate">{business.website}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </AnimatePresence>
        
        {/* Navigation Controls */}
        {showControls && totalBatches > 1 && (
          <div className="flex items-center justify-between p-4 border-t border-gray-700/50">
            {/* Previous Button */}
            <button
              onClick={() => setCurrentBatch((prev) => (prev - 1 + totalBatches) % totalBatches)}
              className="flex items-center gap-2 bg-gray-800/50 hover:bg-gray-700/50 text-white px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105"
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="font-mono text-sm">PREV</span>
            </button>
            
            {/* Batch Indicator */}
            <div className="text-center">
              <div className="text-[#00d4ff] font-mono text-sm font-bold">
                {currentBatch + 1} / {totalBatches}
              </div>
              <div className="text-xs text-gray-500 font-mono">
                {totalBusinesses} BUSINESSES
              </div>
            </div>

            {/* Next Button */}
            <button
              onClick={() => setCurrentBatch((prev) => (prev + 1) % totalBatches)}
              className="flex items-center gap-2 bg-gray-800/50 hover:bg-gray-700/50 text-white px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105"
            >
              <span className="font-mono text-sm">NEXT</span>
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>

      {/* Expanded Discount Modal */}
      <AnimatePresence>
        {expandedDiscount && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setExpandedDiscount(null)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-blur-lg rounded-xl p-6 max-w-lg w-full border border-[#316bff]/30 shadow-xl shadow-[#316bff]/20"
              onClick={(e) => e.stopPropagation()}
            >
              {(() => {
                const business = businesses.find(b => b.id === expandedDiscount)
                if (!business) return null
                
                return (
                  <>
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <BusinessLogo
                          src={business.logo_url}
                          alt={`${business.name} logo`}
                          businessName={business.name}
                          businessId={business.id}
                          size="small"
                          futuristic={true}
                        />
                        <div>
                          <h3 className="text-white font-bold text-lg">{business.name}</h3>
                          {business.category && (
                            <p className="text-gray-400 text-sm capitalize">
                              {business.category.replace('_', ' ')}
                            </p>
                          )}
                          {business.business_address && (
                            <p className="text-gray-300 text-xs mt-1">
                              📍 {business.business_address}
                            </p>
                          )}
                        </div>
                      </div>
                      <button
                        onClick={() => setExpandedDiscount(null)}
                        className="text-gray-400 hover:text-white transition-colors"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                    
                    <div className="text-center py-6">
                      <div className="text-4xl font-black bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent mb-2">
                        {business.premium_discount}{typeof business.premium_discount === 'number' ? '%' : ''}
                      </div>
                      <div className="text-white text-xl font-bold mb-4">EXCLUSIVE DISCOUNT</div>
                      <p className="text-gray-300 text-sm mb-6">
                        Show your Fuse.Vip membership to receive this exclusive discount at {business.name}.
                      </p>
                      
                      {(business.website || business.contact_info) && (
                        <button
                          onClick={() => handleVisitWebsite(business)}
                          className="bg-gradient-to-r from-[#316bff] to-[#00d4ff] hover:from-[#2557d6] hover:to-[#00b8e6] text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 flex items-center gap-2 mx-auto"
                        >
                          {business.website ? 'Visit Website' : 'Contact Info'}
                          <ExternalLink className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </>
                )
              })()}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
