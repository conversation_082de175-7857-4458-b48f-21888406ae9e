"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, Building2, Save, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

interface BusinessApplication {
  id: string;
  businessName: string;
  category: string;
  website: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  businessAddress: string;
  proposedDiscount: string;
  loyaltyRewardFrequency: 'monthly' | 'quarterly' | 'annually';
}

interface MultipleApplicationsFormProps {
  onSubmit?: (applications: BusinessApplication[]) => void;
  onCancel?: () => void;
  maxApplications?: number;
}

const businessCategories = [
  'Restaurant',
  'Retail',
  'Health & Wellness',
  'Beauty & Spa',
  'Automotive',
  'Entertainment',
  'Fitness & Sports',
  'Technology',
  'Education',
  'Professional Services',
  'Real Estate',
  'Financial Services',
  'Home & Garden',
  'Travel & Tourism',
  'Arts & Crafts'
];

export function MultipleApplicationsForm({ 
  onSubmit, 
  onCancel, 
  maxApplications = 5 
}: MultipleApplicationsFormProps) {
  const { user } = useAuth();
  const [applications, setApplications] = useState<BusinessApplication[]>([
    {
      id: '1',
      businessName: '',
      category: '',
      website: '',
      contactName: '',
      contactEmail: user?.email || '',
      contactPhone: '',
      businessAddress: '',
      proposedDiscount: '',
      loyaltyRewardFrequency: 'monthly'
    }
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const addApplication = () => {
    if (applications.length < maxApplications) {
      const newApplication: BusinessApplication = {
        id: Date.now().toString(),
        businessName: '',
        category: '',
        website: '',
        contactName: '',
        contactEmail: user?.email || '',
        contactPhone: '',
        businessAddress: '',
        proposedDiscount: '',
        loyaltyRewardFrequency: 'monthly'
      };
      setApplications([...applications, newApplication]);
    }
  };

  const removeApplication = (id: string) => {
    if (applications.length > 1) {
      setApplications(applications.filter(app => app.id !== id));
    }
  };

  const updateApplication = (id: string, field: keyof BusinessApplication, value: string) => {
    setApplications(applications.map(app => 
      app.id === id ? { ...app, [field]: value } : app
    ));
    // Clear error for this field
    setErrors(prev => ({ ...prev, [`${id}_${field}`]: '' }));
  };

  const validateApplications = () => {
    const newErrors: { [key: string]: string } = {};
    
    applications.forEach((app, index) => {
      if (!app.businessName.trim()) {
        newErrors[`${app.id}_businessName`] = 'Business name is required';
      }
      if (!app.category) {
        newErrors[`${app.id}_category`] = 'Category is required';
      }
      if (!app.contactName.trim()) {
        newErrors[`${app.id}_contactName`] = 'Contact name is required';
      }
      if (!app.contactEmail.trim()) {
        newErrors[`${app.id}_contactEmail`] = 'Contact email is required';
      }
      if (!app.proposedDiscount.trim()) {
        newErrors[`${app.id}_proposedDiscount`] = 'Proposed discount is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateApplications()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Submit all applications
      const response = await fetch('/api/network-applications/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user?.id,
          applications: applications
        }),
      });

      if (response.ok) {
        onSubmit?.(applications);
      } else {
        const error = await response.json();
        console.error('Failed to submit applications:', error);
      }
    } catch (error) {
      console.error('Error submitting applications:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Submit Multiple Business Applications
        </h2>
        <p className="text-gray-600">
          Apply for multiple businesses at once. Each application will be reviewed separately.
        </p>
      </div>

      {applications.map((application, index) => (
        <Card key={application.id} className="relative">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="flex items-center">
              <Building2 className="h-5 w-5 mr-2" />
              Business Application #{index + 1}
            </CardTitle>
            {applications.length > 1 && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => removeApplication(application.id)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor={`businessName_${application.id}`}>Business Name *</Label>
                <Input
                  id={`businessName_${application.id}`}
                  value={application.businessName}
                  onChange={(e) => updateApplication(application.id, 'businessName', e.target.value)}
                  placeholder="Enter business name"
                  className={errors[`${application.id}_businessName`] ? 'border-red-500' : ''}
                />
                {errors[`${application.id}_businessName`] && (
                  <p className="text-red-500 text-sm mt-1">{errors[`${application.id}_businessName`]}</p>
                )}
              </div>

              <div>
                <Label htmlFor={`category_${application.id}`}>Category *</Label>
                <Select
                  value={application.category}
                  onValueChange={(value) => updateApplication(application.id, 'category', value)}
                >
                  <SelectTrigger className={errors[`${application.id}_category`] ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {businessCategories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors[`${application.id}_category`] && (
                  <p className="text-red-500 text-sm mt-1">{errors[`${application.id}_category`]}</p>
                )}
              </div>

              <div>
                <Label htmlFor={`website_${application.id}`}>Website</Label>
                <Input
                  id={`website_${application.id}`}
                  value={application.website}
                  onChange={(e) => updateApplication(application.id, 'website', e.target.value)}
                  placeholder="https://example.com"
                />
              </div>

              <div>
                <Label htmlFor={`proposedDiscount_${application.id}`}>Proposed Discount *</Label>
                <Input
                  id={`proposedDiscount_${application.id}`}
                  value={application.proposedDiscount}
                  onChange={(e) => updateApplication(application.id, 'proposedDiscount', e.target.value)}
                  placeholder="e.g., 10% off first purchase"
                  className={errors[`${application.id}_proposedDiscount`] ? 'border-red-500' : ''}
                />
                {errors[`${application.id}_proposedDiscount`] && (
                  <p className="text-red-500 text-sm mt-1">{errors[`${application.id}_proposedDiscount`]}</p>
                )}
              </div>

              <div>
                <Label htmlFor={`contactName_${application.id}`}>Contact Name *</Label>
                <Input
                  id={`contactName_${application.id}`}
                  value={application.contactName}
                  onChange={(e) => updateApplication(application.id, 'contactName', e.target.value)}
                  placeholder="Your name"
                  className={errors[`${application.id}_contactName`] ? 'border-red-500' : ''}
                />
                {errors[`${application.id}_contactName`] && (
                  <p className="text-red-500 text-sm mt-1">{errors[`${application.id}_contactName`]}</p>
                )}
              </div>

              <div>
                <Label htmlFor={`contactEmail_${application.id}`}>Contact Email *</Label>
                <Input
                  id={`contactEmail_${application.id}`}
                  type="email"
                  value={application.contactEmail}
                  onChange={(e) => updateApplication(application.id, 'contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                  className={errors[`${application.id}_contactEmail`] ? 'border-red-500' : ''}
                />
                {errors[`${application.id}_contactEmail`] && (
                  <p className="text-red-500 text-sm mt-1">{errors[`${application.id}_contactEmail`]}</p>
                )}
              </div>

              <div>
                <Label htmlFor={`contactPhone_${application.id}`}>Contact Phone</Label>
                <Input
                  id={`contactPhone_${application.id}`}
                  value={application.contactPhone}
                  onChange={(e) => updateApplication(application.id, 'contactPhone', e.target.value)}
                  placeholder="(*************"
                />
              </div>

              <div>
                <Label htmlFor={`loyaltyFrequency_${application.id}`}>Loyalty Reward Frequency</Label>
                <Select
                  value={application.loyaltyRewardFrequency}
                  onValueChange={(value: 'monthly' | 'quarterly' | 'annually') => 
                    updateApplication(application.id, 'loyaltyRewardFrequency', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="annually">Annually</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor={`businessAddress_${application.id}`}>Business Address</Label>
              <Textarea
                id={`businessAddress_${application.id}`}
                value={application.businessAddress}
                onChange={(e) => updateApplication(application.id, 'businessAddress', e.target.value)}
                placeholder="Full business address"
                rows={2}
              />
            </div>
          </CardContent>
        </Card>
      ))}

      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={addApplication}
          disabled={applications.length >= maxApplications}
          className="flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Another Business ({applications.length}/{maxApplications})
        </Button>

        <div className="flex space-x-2">
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button 
            onClick={handleSubmit} 
            disabled={isSubmitting}
            className="bg-[#3A56FF] hover:bg-[#3A56FF]/90"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Submit All Applications
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
