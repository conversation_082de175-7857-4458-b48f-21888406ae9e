"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { AnimatedCard } from "@/components/animated-card"
import { AnimatedSection } from "@/components/animated-section"
import { Save, X, Upload, FileImage } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { BusinessCombobox } from "./business-combobox"
import { getCategoryOptions } from "@/lib/business-categories"

interface BusinessFormProps {
  initialData?: any
  onSubmit: (data: any) => void
  onCancel: () => void
}

export function BusinessForm({ initialData, onSubmit, onCancel }: BusinessFormProps) {
  const { user } = useAuth()
  
  // Check if we're editing an existing business (has id) or creating a new application
  const isEditingBusiness = initialData?.id && !initialData?.business_name
  
  // Map field names based on whether we're editing a business or creating an application
  const [formData, setFormData] = useState({
    business_name: isEditingBusiness ? initialData?.name || "" : initialData?.business_name || "",
    website: initialData?.website || "",
    category: initialData?.category || "",
    contact_name: initialData?.contact_name || "",
    contact_email: initialData?.contact_email || "",
    contact_phone: initialData?.contact_phone || "",
    proposed_discount: isEditingBusiness ? initialData?.premium_discount || "" : initialData?.proposed_discount || "",
    status: initialData?.status || "pending",
    created_at: initialData?.created_at || new Date().toISOString(),
    updated_at: initialData?.updated_at || new Date().toISOString(),
    loyalty_reward_frequency: initialData?.loyalty_reward_frequency || "monthly",
    user_id: initialData?.user_id || user?.id || "",
    business_address: initialData?.business_address || "",
    referring_business_id: initialData?.referring_business_id || "",
    business_spotlight: initialData?.business_spotlight || false,
  })

  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml']
    if (!allowedTypes.includes(file.type)) {
      alert('Please select a valid image file (JPEG, PNG, WebP, or SVG)')
      return
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB')
      return
    }

    setLogoFile(file)
    console.log("Logo file selected:", file.name)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    const checked = (e.target as HTMLInputElement).checked
    setFormData((prev) => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : value 
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user?.id) {
      alert("You must be logged in to submit a business application")
      return
    }

    setIsSubmitting(true)

    try {
      if (initialData) {
        // Update existing business - map field names to businesses table schema
        const businessUpdateData = {
          ...formData,
          // Map network_applications field names to businesses table field names
          name: formData.business_name,
          premium_discount: formData.proposed_discount,
          user_id: user.id,
          updated_at: new Date().toISOString()
        }
        
        // Remove network_applications field names when updating business
        delete businessUpdateData.business_name
        delete businessUpdateData.proposed_discount
        delete businessUpdateData.status
        delete businessUpdateData.created_at
        
        onSubmit(businessUpdateData)
      } else {
        // New business - submit to network_applications with logo file
        const formDataToSubmit = new FormData()

        // Add all form fields
        Object.entries(formData).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            formDataToSubmit.append(key, value.toString())
          }
        })

        // Add user ID
        formDataToSubmit.append('user_id', user.id)

        // Add logo file if selected
        if (logoFile) {
          formDataToSubmit.append('logo_file', logoFile)
        }

        const response = await fetch('/api/network-applications', {
          method: 'POST',
          body: formDataToSubmit, // Use FormData instead of JSON
        })

        const result = await response.json()

        if (!response.ok || !result.success) {
          console.error("Error submitting business application:", result.error || result.details)
          alert(`Error submitting business application: ${result.error || 'Please try again.'}`)
          return
        }

        alert("Congratulations! Your business application has been submitted successfully. Please allow our team 24 hours to review it.")
        onSubmit(result.data || formData)
      }
    } catch (err) {
      console.error("Error in form submission:", err)
      alert("An unexpected error occurred. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Use standardized categories
  const categories = getCategoryOptions().map(cat => cat.label)

  return (
    <AnimatedSection>
      <AnimatedCard className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-6">{initialData ? "Edit Business" : "Register Your Business"}</h2>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="business_name" className="block text-sm font-medium text-gray-700 mb-1">
                Business Name *
              </label>
              <input
                type="text"
                id="business_name"
                name="business_name"
                value={formData.business_name}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              >
                <option value="">Select Category</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="mb-6">
            <label htmlFor="referring_business_id" className="block text-sm font-medium text-gray-700 mb-1">
              Referring Business
            </label>
            <BusinessCombobox
              value={formData.referring_business_id}
              onChange={(value) => setFormData(prev => ({ ...prev, referring_business_id: value }))}
            />
            <p className="text-xs text-gray-500 mt-1">
              If you were referred by a business, please select it from the dropdown
            </p>
          </div>

          <div className="mb-6">
            <label htmlFor="business_address" className="block text-sm font-medium text-gray-700 mb-1">
              Business Address *
            </label>
            <input
              type="text"
              id="business_address"
              name="business_address"
              value={formData.business_address}
              onChange={handleChange}
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <label htmlFor="contact_name" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Name *
              </label>
              <input
                type="text"
                id="contact_name"
                name="contact_name"
                value={formData.contact_name}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
            <div>
              <label htmlFor="contact_phone" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Phone *
              </label>
              <input
                type="tel"
                id="contact_phone"
                name="contact_phone"
                value={formData.contact_phone}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
            <div>
              <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Email *
              </label>
              <input
                type="email"
                id="contact_email"
                name="contact_email"
                value={formData.contact_email}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                Website
              </label>
              <input
                type="url"
                id="website"
                name="website"
                value={formData.website}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Business Logo
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                <input
                  type="file"
                  id="logo-upload"
                  accept="image/jpeg,image/jpg,image/png,image/webp,image/svg+xml"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      handleFileSelect(file)
                    }
                  }}
                  className="hidden"
                />
                <label htmlFor="logo-upload" className="cursor-pointer">
                  <div className="flex flex-col items-center">
                    {logoFile ? (
                      <>
                        <FileImage className="h-12 w-12 text-green-500 mb-2" />
                        <p className="text-sm font-medium text-green-600">
                          {logoFile.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {(logoFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </>
                    ) : (
                      <>
                        <Upload className="h-12 w-12 text-gray-400 mb-2" />
                        <p className="text-sm font-medium text-gray-600">
                          Click to upload business logo
                        </p>
                        <p className="text-xs text-gray-500">
                          JPEG, PNG, WebP, or SVG (max 5MB)
                        </p>
                      </>
                    )}
                  </div>
                </label>
              </div>
              {logoFile && (
                <p className="text-xs text-gray-600 mt-2">
                  ✓ Logo will be processed during application review
                </p>
              )}
            </div>
            <div>
              <label htmlFor="proposed_discount" className="block text-sm font-medium text-gray-700 mb-1">
                Discount/Offer Details
              </label>
              <input
                type="text"
                id="proposed_discount"
                name="proposed_discount"
                value={formData.proposed_discount}
                onChange={handleChange}
                placeholder="e.g., 20% off, $50 off services, Free consultation"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter your discount as a percentage (e.g., 20%) or describe your offer (e.g., $50 off services)
              </p>
            </div>
          </div>

          <div className="mb-6">
            <label htmlFor="loyalty_reward_frequency" className="block text-sm font-medium text-gray-700 mb-1">
              Loyalty Reward Frequency
            </label>
            <select
              id="loyalty_reward_frequency"
              name="loyalty_reward_frequency"
              value={formData.loyalty_reward_frequency}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3A56FF]"
            >
              <option value="monthly">Monthly - Rewards distributed every month</option>
              <option value="quarterly">Quarterly - Rewards distributed every 3 months</option>
              <option value="annually">Annually - Rewards distributed once per year</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              Choose how often you want to distribute loyalty rewards to your customers
            </p>
          </div>

          {/* Business Spotlight - Only show for existing businesses */}
          {initialData && (
            <div className="mb-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="business_spotlight"
                  name="business_spotlight"
                  checked={formData.business_spotlight}
                  onChange={handleChange}
                  className="h-4 w-4 text-[#3A56FF] focus:ring-[#3A56FF] border-gray-300 rounded"
                />
                <label htmlFor="business_spotlight" className="ml-2 block text-sm font-medium text-gray-700">
                  Featured Business Spotlight
                </label>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Enable to feature your business in the spotlight section
              </p>
            </div>
          )}

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 flex items-center"
            >
              <X className="h-4 w-4 mr-2" /> Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-[#3A56FF] text-white rounded-md flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? "Submitting..." : (initialData ? "Update" : "Register")}
            </button>
          </div>
        </form>
      </AnimatedCard>
    </AnimatedSection>
  )
}
