"use client";

import { useState } from "react";
import { Edit, Save, X, Clock } from "lucide-react";

interface LoyaltyFrequencyEditorProps {
  currentFrequency: 'monthly' | 'quarterly' | 'annually';
  onUpdate: (frequency: 'monthly' | 'quarterly' | 'annually') => void;
  loading?: boolean;
}

export function LoyaltyFrequencyEditor({ 
  currentFrequency, 
  onUpdate, 
  loading = false 
}: LoyaltyFrequencyEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedFrequency, setSelectedFrequency] = useState<'monthly' | 'quarterly' | 'annually'>(currentFrequency);

  const frequencyOptions = [
    { value: 'monthly' as const, label: 'Monthly', description: 'Rewards distributed every month' },
    { value: 'quarterly' as const, label: 'Quarterly', description: 'Rewards distributed every 3 months' },
    { value: 'annually' as const, label: 'Annually', description: 'Rewards distributed once per year' }
  ];

  const handleSave = () => {
    onUpdate(selectedFrequency);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setSelectedFrequency(currentFrequency);
    setIsEditing(false);
  };

  const getFrequencyLabel = (frequency: string) => {
    const option = frequencyOptions.find(opt => opt.value === frequency);
    return option ? option.label : frequency;
  };

  const getFrequencyDescription = (frequency: string) => {
    const option = frequencyOptions.find(opt => opt.value === frequency);
    return option ? option.description : '';
  };

  return (
    <div className="bg-[#2A2A2A] p-4 rounded-lg">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Clock className="h-5 w-5 text-[#3A56FF]" />
          <h4 className="font-medium text-white">Loyalty Reward Frequency</h4>
        </div>
        {!isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            disabled={loading}
            className="bg-[#3A56FF] text-white px-3 py-1 rounded-md text-sm flex items-center hover:bg-[#2A46FF] transition-colors disabled:opacity-50"
          >
            <Edit className="h-3 w-3 mr-1" /> Edit
          </button>
        )}
      </div>

      {!isEditing ? (
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-300">Current frequency</span>
            <span className="font-bold text-white">{getFrequencyLabel(currentFrequency)}</span>
          </div>
          <p className="text-sm text-gray-400">{getFrequencyDescription(currentFrequency)}</p>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="space-y-3">
            {frequencyOptions.map((option) => (
              <label
                key={option.value}
                className="flex items-start space-x-3 cursor-pointer p-3 rounded-lg border border-gray-600 hover:border-[#3A56FF] transition-colors"
              >
                <input
                  type="radio"
                  name="loyalty-frequency"
                  value={option.value}
                  checked={selectedFrequency === option.value}
                  onChange={(e) => setSelectedFrequency(e.target.value as 'monthly' | 'quarterly' | 'annually')}
                  className="mt-1 text-[#3A56FF] focus:ring-[#3A56FF] focus:ring-2"
                />
                <div className="flex-1">
                  <div className="font-medium text-white">{option.label}</div>
                  <div className="text-sm text-gray-400">{option.description}</div>
                </div>
              </label>
            ))}
          </div>

          <div className="flex justify-end space-x-2 pt-2">
            <button
              onClick={handleCancel}
              disabled={loading}
              className="px-3 py-2 border border-gray-600 rounded-md text-gray-300 text-sm flex items-center hover:bg-gray-700 transition-colors disabled:opacity-50"
            >
              <X className="h-3 w-3 mr-1" /> Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={loading}
              className="px-3 py-2 bg-[#3A56FF] text-white rounded-md text-sm flex items-center hover:bg-[#2A46FF] transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-3 w-3 border-t-2 border-b-2 border-white mr-1"></div>
              ) : (
                <Save className="h-3 w-3 mr-1" />
              )}
              Save
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
