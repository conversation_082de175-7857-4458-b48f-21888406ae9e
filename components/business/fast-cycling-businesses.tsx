"use client"

import { useState, useEffect } from "react"
import { BusinessCarousel } from "./business-carousel"

// Simple cache to avoid repeated API calls
let cachedBusinesses: Business[] | null = null
let cacheTimestamp: number = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

interface Business {
  id: string
  name: string
  premium_discount: string | number
  website?: string
  logo_url?: string
  category?: string
  business_address?: string
  description?: string
  contact_info?: string
}

export function FastCyclingBusinesses() {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBusinesses()
  }, [])

  const fetchBusinesses = async () => {
    try {
      // Check cache first for instant loading
      const now = Date.now()
      if (cachedBusinesses && (now - cacheTimestamp) < CACHE_DURATION) {
        console.log('🏢 FastCyclingBusinesses: Using cached businesses')
        setBusinesses(cachedBusinesses)
        setLoading(false)
        return
      }

      // Use the consolidated API with minimal fields for carousel
      console.log('🏢 FastCyclingBusinesses: Fetching businesses via consolidated API')
      const response = await fetch('/api/businesses?fields=minimal&limit=50')

      if (response.ok) {
        const result = await response.json()
        if (result.data && result.data.length > 0) {
          console.log(`✅ FastCyclingBusinesses: Loaded ${result.data.length} businesses (${result.source})`)

          // Transform data to match expected interface
          const transformedBusinesses = result.data.map((business: any) => ({
            id: business.id,
            name: business.name,
            premium_discount: business.premium_discount,
            website: business.website,
            logo_url: business.logo_optimized_url || business.logo_url,
            category: business.category,
            business_address: business.business_address
          }))

          setBusinesses(transformedBusinesses)
          setError(null)
          setLoading(false)

          // Cache the results
          cachedBusinesses = transformedBusinesses
          cacheTimestamp = now
        } else {
          console.log('⚠️ FastCyclingBusinesses: No businesses found in response')
          setBusinesses([])
          setLoading(false)
        }
      } else {
        console.error('❌ FastCyclingBusinesses: Fast API response not ok:', response.status)
        // Fallback to the old simple API
        await fetchBusinessesFallback()
      }
    } catch (error) {
      console.error('❌ FastCyclingBusinesses: Error fetching businesses:', error)
      // Try fallback before giving up
      await fetchBusinessesFallback()
    }
  }

  const fetchBusinessesFallback = async () => {
    try {
      console.log('🏢 FastCyclingBusinesses: Using static data fallback...')

      // Use consolidated API with public access as fallback
      let response = await fetch('/api/businesses?public=true&fields=minimal')

      if (response.ok) {
        const result = await response.json()
        if (result.data && result.data.length > 0) {
          console.log(`✅ FastCyclingBusinesses: Fallback loaded ${result.data.length} businesses`)

          // Transform data to match expected interface
          const transformedBusinesses = result.data.map((business: any) => ({
            id: business.id,
            name: business.name,
            premium_discount: business.premium_discount,
            website: business.website,
            logo_url: business.logo_url,
            category: business.category,
            business_address: business.business_address
          }))

          setBusinesses(transformedBusinesses)
          setError(null)
          setLoading(false)

          // Cache fallback results
          const now = Date.now()
          cachedBusinesses = transformedBusinesses
          cacheTimestamp = now
        } else {
          console.log('⚠️ FastCyclingBusinesses: No businesses in fallback response')
          setBusinesses([])
          setLoading(false)
        }
      } else {
        console.error('❌ FastCyclingBusinesses: Fallback API failed:', response.status)
        setBusinesses([])
        setLoading(false)
      }
    } catch (error) {
      console.error('❌ FastCyclingBusinesses: Fallback failed:', error)
      // Silently fail - don't scare users with error messages
      setBusinesses([])
      setLoading(false)
    }
  }

  // Always show the section - no loading states or error messages to scare users

  return (
    <section className="py-16 bg-gradient-to-r from-[#316bff]/5 via-purple-500/5 to-pink-500/5 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 bg-[#316bff] rounded-full animate-pulse"></div>
        <div className="absolute top-32 right-20 w-16 h-16 bg-purple-500 rounded-full animate-bounce"></div>
        <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-500 rounded-full animate-ping"></div>
        <div className="absolute bottom-32 right-1/3 w-14 h-14 bg-yellow-400 rounded-full animate-pulse"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-[#316bff] to-purple-600 bg-clip-text text-transparent">
            Featured Businesses
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Discover exclusive discounts and rewards from our partner businesses
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <BusinessCarousel 
            businesses={businesses} 
            batchSize={4}
            autoRotate={true}
            showControls={true}
          />
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6 text-lg">
            Want access to all these exclusive discounts?
          </p>
          <a href="/register">
            <button className="bg-gradient-to-r from-[#316bff] to-purple-600 hover:from-[#2151d3] hover:to-purple-700 text-white font-semibold text-lg px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
              Get Your VIP Card Now! 🎯
            </button>
          </a>
        </div>
      </div>
    </section>
  )
}
