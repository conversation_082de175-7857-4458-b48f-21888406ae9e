"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { BusinessLogo } from "@/components/business-logo"
import { 
  User, 
  MapPin, 
  Globe, 
  Mail, 
  Phone, 
  Edit3, 
  Save, 
  X, 
  Upload,
  Star,
  Users,
  TrendingUp,
  Calendar,
  MessageSquare,
  Share2,
  Heart,
  Eye,
  Zap,
  Sparkles
} from "lucide-react"

interface BusinessProfile {
  id: string
  name: string
  logo_url?: string
  website?: string
  category?: string
  business_address?: string
  contact_email?: string
  contact_phone?: string
  contact_name?: string
  premium_discount?: string
  description?: string
  business_spotlight?: boolean
  display_order?: number
  created_at?: string
}

interface BusinessStats {
  totalVisits: number
  qrScans: number
  referrals: number
  revenue: number
}

export function InteractiveBusinessDashboard() {
  const { user } = useAuth()
  const [business, setBusiness] = useState<BusinessProfile | null>(null)
  const [stats, setStats] = useState<BusinessStats>({
    totalVisits: 0,
    qrScans: 0,
    referrals: 0,
    revenue: 0
  })
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState<Partial<BusinessProfile>>({})
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'analytics' | 'customization'>('overview')

  useEffect(() => {
    if (user) {
      fetchBusinessData()
    }
  }, [user])

  const fetchBusinessData = async () => {
    try {
      setLoading(true)

      // Use authenticated API endpoint for business data
      const businessResponse = await fetch('/api/dashboard/business', {
        credentials: 'include', // Include authentication cookies
        headers: {
          'Cache-Control': 'no-cache',
        },
      })
      if (businessResponse.ok) {
        const businessResult = await businessResponse.json()
        if (businessResult.business) {
          setBusiness(businessResult.business)
          setEditForm(businessResult.business)

          // Fetch business stats (mock data for now)
          setStats({
            totalVisits: Math.floor(Math.random() * 1000) + 100,
            qrScans: Math.floor(Math.random() * 500) + 50,
            referrals: Math.floor(Math.random() * 50) + 5,
            revenue: Math.floor(Math.random() * 10000) + 1000
          })
        } else {
          console.log('No business found for user:', user?.id)
        }
      } else {
        console.error('Failed to fetch business data:', businessResponse.status)
      }
    } catch (error) {
      console.error('Error fetching business data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = () => {
    setIsEditing(true)
    setEditForm(business || {})
  }

  const handleSave = async () => {
    try {
      // Save business changes
      const response = await fetch(`/api/businesses/${business?.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editForm)
      })

      if (response.ok) {
        const updatedBusiness = await response.json()
        setBusiness(updatedBusiness)
        setIsEditing(false)
      }
    } catch (error) {
      console.error('Error saving business:', error)
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditForm(business || {})
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#316bff]/30 border-t-[#316bff] rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading your business dashboard...</p>
        </div>
      </div>
    )
  }

  if (!business) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <Zap className="w-16 h-16 text-[#316bff] mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-4">No Business Found</h2>
          <p className="text-gray-400 mb-6">You don't have a business registered yet. Apply to join the FUSE network!</p>
          <button className="bg-gradient-to-r from-[#316bff] to-[#00d4ff] text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg hover:shadow-[#316bff]/20 transition-all duration-300">
            Apply for Business Network
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header Section - Facebook-style cover */}
      <div className="relative">
        {/* Cover Image */}
        <div className="h-64 bg-gradient-to-r from-[#316bff] via-[#00d4ff] to-[#316bff] relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20"></div>
          <div className="absolute inset-0 bg-[url('/images/circuit-pattern.svg')] opacity-10"></div>
          
          {/* Floating particles effect */}
          <div className="absolute inset-0">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-white/20 rounded-full animate-pulse"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`,
                  animationDuration: `${2 + Math.random() * 2}s`
                }}
              ></div>
            ))}
          </div>
        </div>

        {/* Business Profile Section */}
        <div className="relative -mt-20 mx-auto max-w-6xl px-6">
          <div className="bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-blur-sm border border-[#316bff]/30 rounded-2xl shadow-2xl shadow-[#316bff]/10 p-8">
            <div className="flex flex-col md:flex-row items-center md:items-end gap-6">
              {/* Business Logo */}
              <div className="relative">
                <BusinessLogo
                  src={business.logo_url}
                  alt={`${business.name} logo`}
                  businessName={business.name}
                  businessId={business.id}
                  className="w-32 h-32 rounded-2xl border-4 border-[#316bff]/50 shadow-xl shadow-[#316bff]/20"
                  futuristic={true}
                  glowEffect={true}
                />
                {isEditing && (
                  <button className="absolute bottom-2 right-2 bg-[#316bff] text-white p-2 rounded-full hover:bg-[#2557d6] transition-colors">
                    <Upload className="w-4 h-4" />
                  </button>
                )}
              </div>

              {/* Business Info */}
              <div className="flex-1 text-center md:text-left">
                {isEditing ? (
                  <input
                    type="text"
                    value={editForm.name || ''}
                    onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                    className="text-3xl font-bold bg-transparent border-b-2 border-[#316bff] text-white focus:outline-none focus:border-[#00d4ff] mb-2"
                  />
                ) : (
                  <h1 className="text-3xl font-bold text-white mb-2 group-hover:text-[#00d4ff] transition-colors">
                    {business.name}
                  </h1>
                )}
                
                <div className="flex flex-wrap gap-2 mb-4 justify-center md:justify-start">
                  <span className="inline-flex items-center gap-1 bg-gradient-to-r from-[#316bff] to-[#00d4ff] text-white text-sm px-3 py-1 rounded-full">
                    <Star className="w-4 h-4" />
                    {business.category || 'Business'}
                  </span>
                  {business.business_spotlight && (
                    <span className="inline-flex items-center gap-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-sm px-3 py-1 rounded-full">
                      <Sparkles className="w-4 h-4" />
                      Featured
                    </span>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 justify-center md:justify-start">
                  {!isEditing ? (
                    <>
                      <button
                        onClick={handleEdit}
                        className="flex items-center gap-2 bg-[#316bff] text-white px-4 py-2 rounded-xl hover:bg-[#2557d6] transition-all duration-300 hover:shadow-lg hover:shadow-[#316bff]/20"
                      >
                        <Edit3 className="w-4 h-4" />
                        Edit Profile
                      </button>
                      <button className="flex items-center gap-2 bg-gray-700 text-white px-4 py-2 rounded-xl hover:bg-gray-600 transition-colors">
                        <Share2 className="w-4 h-4" />
                        Share
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        onClick={handleSave}
                        className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-xl hover:bg-green-700 transition-colors"
                      >
                        <Save className="w-4 h-4" />
                        Save
                      </button>
                      <button
                        onClick={handleCancel}
                        className="flex items-center gap-2 bg-gray-600 text-white px-4 py-2 rounded-xl hover:bg-gray-700 transition-colors"
                      >
                        <X className="w-4 h-4" />
                        Cancel
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="max-w-6xl mx-auto px-6 mt-8">
        <div className="flex gap-1 bg-gray-800/50 rounded-xl p-1 backdrop-blur-sm border border-gray-700/50">
          {[
            { id: 'overview', label: 'Overview', icon: Eye },
            { id: 'analytics', label: 'Analytics', icon: TrendingUp },
            { id: 'customization', label: 'Customize', icon: Edit3 }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`
                flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300
                ${activeTab === id 
                  ? 'bg-gradient-to-r from-[#316bff] to-[#00d4ff] text-white shadow-lg shadow-[#316bff]/20' 
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                }
              `}
            >
              <Icon className="w-4 h-4" />
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-6xl mx-auto px-6 py-8">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Stats Cards */}
            {[
              { label: 'Total Visits', value: stats.totalVisits, icon: Users, color: 'from-blue-500 to-blue-600' },
              { label: 'QR Scans', value: stats.qrScans, icon: Eye, color: 'from-green-500 to-green-600' },
              { label: 'Referrals', value: stats.referrals, icon: Share2, color: 'from-purple-500 to-purple-600' },
              { label: 'Revenue', value: `$${stats.revenue}`, icon: TrendingUp, color: 'from-orange-500 to-orange-600' }
            ].map(({ label, value, icon: Icon, color }) => (
              <div key={label} className="bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 hover:border-[#316bff]/50 transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${color} flex items-center justify-center`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-2xl font-bold text-white">{value}</span>
                </div>
                <p className="text-gray-400 text-sm">{label}</p>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            <div className="bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
              <h3 className="text-xl font-bold text-white mb-4">Business Analytics</h3>
              <p className="text-gray-400 mb-4">Detailed analytics coming soon...</p>
              <div className="h-64 bg-gray-800/50 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-16 h-16 text-gray-600" />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'customization' && (
          <div className="space-y-6">
            <div className="bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
              <h3 className="text-xl font-bold text-white mb-6">Customize Your Business Profile</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Business Description</label>
                  <textarea
                    value={editForm.description || ''}
                    onChange={(e) => setEditForm({...editForm, description: e.target.value})}
                    className="w-full bg-gray-800/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-[#316bff] resize-none"
                    rows={4}
                    placeholder="Tell customers about your business..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Premium Discount</label>
                  <input
                    type="text"
                    value={editForm.premium_discount || ''}
                    onChange={(e) => setEditForm({...editForm, premium_discount: e.target.value})}
                    className="w-full bg-gray-800/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-[#316bff]"
                    placeholder="e.g., 20% off all services"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Website</label>
                  <input
                    type="url"
                    value={editForm.website || ''}
                    onChange={(e) => setEditForm({...editForm, website: e.target.value})}
                    className="w-full bg-gray-800/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-[#316bff]"
                    placeholder="https://yourbusiness.com"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Contact Email</label>
                  <input
                    type="email"
                    value={editForm.contact_email || ''}
                    onChange={(e) => setEditForm({...editForm, contact_email: e.target.value})}
                    className="w-full bg-gray-800/50 border border-gray-600 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-[#316bff]"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={handleSave}
                  className="bg-gradient-to-r from-[#316bff] to-[#00d4ff] text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg hover:shadow-[#316bff]/20 transition-all duration-300"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}