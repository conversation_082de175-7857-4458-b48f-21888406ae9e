"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>it<PERSON> } from "@/components/ui/dialog"
import { BusinessForm } from "./business-form"
import { useAuth } from "@/contexts/auth-context"
import { useState } from "react"

interface AddMoreBusinessModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function AddMoreBusinessModal({ isOpen, onClose, onSuccess }: AddMoreBusinessModalProps) {
  const { user } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (formData: any) => {
    if (!user?.id) {
      console.error('User not authenticated')
      return
    }

    setIsSubmitting(true)

    try {
      console.log('Submitting additional business application:', {
        userId: user.id,
        businessName: formData.business_name,
        category: formData.category
      })

      // Submit as network application - allowing multiple applications per user
      const response = await fetch('/api/network-applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          business_name: formData.business_name,
          website: formData.website,
          category: formData.category,
          contact_name: formData.contact_name,
          contact_email: formData.contact_email,
          contact_phone: formData.contact_phone,
          proposed_discount: formData.proposed_discount,
          business_address: formData.business_address,
          loyalty_reward_frequency: formData.loyalty_reward_frequency || 'monthly',
          status: 'pending'
        }),
      })

      const result = await response.json()

      if (!response.ok || result.error) {
        throw new Error(result.error || 'Failed to submit application')
      }

      console.log('✅ Additional business application submitted successfully:', result.application?.id)

      // Show success message
      const successDiv = document.createElement('div')
      successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse'
      successDiv.textContent = '✅ Business application submitted successfully!'
      document.body.appendChild(successDiv)
      setTimeout(() => successDiv.remove(), 3000)

      // Close modal and notify parent
      onClose()
      if (onSuccess) {
        onSuccess()
      }

    } catch (error) {
      console.error('❌ Error submitting additional business application:', error)
      
      // Show error message  
      const errorDiv = document.createElement('div')
      errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50'
      errorDiv.textContent = '❌ Failed to submit application. Please try again.'
      document.body.appendChild(errorDiv)
      setTimeout(() => errorDiv.remove(), 5000)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            🏢 Apply for Additional Business
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <p className="text-gray-600 mb-6">
            Submit an application for another business you own or manage. Each business will be reviewed separately.
          </p>
          
          <BusinessForm
            initialData={{
              user_id: user?.id,
              status: 'pending',
              loyalty_reward_frequency: 'monthly'
            }}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}