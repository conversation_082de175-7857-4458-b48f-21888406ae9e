"use client"

import React, { useState, useRef, useCallback } from 'react'
import { Upload, X, Check, AlertCircle, Image as ImageIcon, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LogoUploadProps {
  onUpload: (file: File) => Promise<void>
  currentLogoUrl?: string
  isUploading?: boolean
  className?: string
  maxFileSize?: number // in bytes
  acceptedFormats?: string[]
  showMetadata?: boolean
}

interface UploadState {
  isDragOver: boolean
  isValidating: boolean
  validationError: string | null
  uploadProgress: number
}

export function EnhancedLogoUpload({
  onUpload,
  currentLogoUrl,
  isUploading = false,
  className,
  maxFileSize = 5 * 1024 * 1024, // 5MB default
  acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'],
  showMetadata = true
}: LogoUploadProps) {
  const [uploadState, setUploadState] = useState<UploadState>({
    isDragOver: false,
    isValidating: false,
    validationError: null,
    uploadProgress: 0
  })
  
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [fileMetadata, setFileMetadata] = useState<{
    size: string
    dimensions?: string
    type: string
  } | null>(null)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const dropZoneRef = useRef<HTMLDivElement>(null)

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const validateFile = useCallback((file: File): Promise<{ isValid: boolean; error?: string }> => {
    return new Promise((resolve) => {
      // Check file size
      if (file.size > maxFileSize) {
        resolve({
          isValid: false,
          error: `File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxFileSize)})`
        })
        return
      }

      // Check file type
      if (!acceptedFormats.includes(file.type)) {
        resolve({
          isValid: false,
          error: `File type ${file.type} is not supported. Accepted formats: ${acceptedFormats.join(', ')}`
        })
        return
      }

      // For images, check dimensions
      if (file.type.startsWith('image/')) {
        const img = new Image()
        const url = URL.createObjectURL(file)
        
        img.onload = () => {
          URL.revokeObjectURL(url)
          
          // Set metadata
          setFileMetadata({
            size: formatFileSize(file.size),
            dimensions: `${img.width} × ${img.height}`,
            type: file.type
          })
          
          resolve({ isValid: true })
        }
        
        img.onerror = () => {
          URL.revokeObjectURL(url)
          resolve({
            isValid: false,
            error: 'Invalid image file'
          })
        }
        
        img.src = url
      } else {
        // For non-image files (like SVG)
        setFileMetadata({
          size: formatFileSize(file.size),
          type: file.type
        })
        resolve({ isValid: true })
      }
    })
  }, [maxFileSize, acceptedFormats])

  const handleFileSelection = useCallback(async (file: File) => {
    setUploadState(prev => ({ ...prev, isValidating: true, validationError: null }))
    
    try {
      const validation = await validateFile(file)
      
      if (!validation.isValid) {
        setUploadState(prev => ({ 
          ...prev, 
          isValidating: false, 
          validationError: validation.error || 'Invalid file' 
        }))
        return
      }

      setSelectedFile(file)
      setUploadState(prev => ({ ...prev, isValidating: false, validationError: null }))
      
      // Create preview URL
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file)
        setPreviewUrl(url)
      }

      // Trigger upload
      await onUpload(file)
      
    } catch (error) {
      setUploadState(prev => ({ 
        ...prev, 
        isValidating: false, 
        validationError: error instanceof Error ? error.message : 'Upload failed' 
      }))
    }
  }, [validateFile, onUpload])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setUploadState(prev => ({ ...prev, isDragOver: true }))
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    if (!dropZoneRef.current?.contains(e.relatedTarget as Node)) {
      setUploadState(prev => ({ ...prev, isDragOver: false }))
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setUploadState(prev => ({ ...prev, isDragOver: false }))
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelection(files[0])
    }
  }, [handleFileSelection])

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelection(files[0])
    }
  }, [handleFileSelection])

  const clearSelection = useCallback(() => {
    setSelectedFile(null)
    setPreviewUrl(null)
    setFileMetadata(null)
    setUploadState(prev => ({ ...prev, validationError: null }))
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  const displayUrl = previewUrl || currentLogoUrl

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Zone */}
      <div
        ref={dropZoneRef}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={cn(
          "relative border-2 border-dashed rounded-lg p-6 transition-all duration-200 cursor-pointer",
          "hover:border-[#3A56FF] hover:bg-blue-50/50",
          uploadState.isDragOver 
            ? "border-[#3A56FF] bg-blue-50 scale-[1.02]" 
            : "border-gray-300",
          uploadState.validationError 
            ? "border-red-300 bg-red-50" 
            : "",
          isUploading || uploadState.isValidating 
            ? "pointer-events-none opacity-75" 
            : ""
        )}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedFormats.join(',')}
          onChange={handleFileInputChange}
          className="hidden"
        />
        
        <div className="flex flex-col items-center justify-center space-y-3">
          {isUploading || uploadState.isValidating ? (
            <Loader2 className="w-8 h-8 text-[#3A56FF] animate-spin" />
          ) : uploadState.validationError ? (
            <AlertCircle className="w-8 h-8 text-red-500" />
          ) : selectedFile ? (
            <Check className="w-8 h-8 text-green-500" />
          ) : (
            <Upload className="w-8 h-8 text-gray-400" />
          )}
          
          <div className="text-center">
            <p className="text-sm font-medium text-gray-900">
              {isUploading ? 'Uploading...' :
               uploadState.isValidating ? 'Validating...' :
               uploadState.validationError ? 'Upload Error' :
               selectedFile ? 'File Selected' :
               'Drop your logo here or click to browse'}
            </p>
            
            {!isUploading && !uploadState.isValidating && !uploadState.validationError && (
              <p className="text-xs text-gray-500 mt-1">
                Supports: JPG, PNG, WebP, SVG • Max size: {formatFileSize(maxFileSize)}
              </p>
            )}
            
            {uploadState.validationError && (
              <p className="text-xs text-red-600 mt-1">
                {uploadState.validationError}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Preview and Metadata */}
      {(displayUrl || selectedFile) && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
          <div className="flex items-start justify-between">
            <h4 className="text-sm font-medium text-gray-900 flex items-center">
              <ImageIcon className="w-4 h-4 mr-2" />
              Logo Preview
            </h4>
            
            {selectedFile && (
              <button
                onClick={clearSelection}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
          
          {displayUrl && (
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <img
                  src={displayUrl}
                  alt="Logo preview"
                  className="w-16 h-16 object-contain rounded-lg border border-gray-200 bg-white p-1"
                />
              </div>
              
              {showMetadata && fileMetadata && (
                <div className="flex-1 space-y-1">
                  <div className="text-xs text-gray-600">
                    <span className="font-medium">Size:</span> {fileMetadata.size}
                  </div>
                  {fileMetadata.dimensions && (
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Dimensions:</span> {fileMetadata.dimensions}
                    </div>
                  )}
                  <div className="text-xs text-gray-600">
                    <span className="font-medium">Type:</span> {fileMetadata.type}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {selectedFile && (
            <div className="text-xs text-gray-600">
              <span className="font-medium">File:</span> {selectedFile.name}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
