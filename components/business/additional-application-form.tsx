"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Building2, Save, Loader2, X, CheckCircle } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

interface AdditionalApplicationFormProps {
  onSubmit?: (success: boolean) => void;
  onCancel?: () => void;
}

const businessCategories = [
  'Restaurant',
  'Retail',
  'Health & Wellness',
  'Beauty & Spa',
  'Automotive',
  'Entertainment',
  'Fitness & Sports',
  'Technology',
  'Education',
  'Professional Services',
  'Real Estate',
  'Financial Services',
  'Home & Garden',
  'Travel & Tourism',
  'Arts & Crafts'
];

export function AdditionalApplicationForm({ 
  onSubmit, 
  onCancel 
}: AdditionalApplicationFormProps) {
  const { user, profile } = useAuth();
  const [formData, setFormData] = useState({
    businessName: '',
    category: '',
    website: '',
    contactName: profile?.first_name && profile?.last_name 
      ? `${profile.first_name} ${profile.last_name}` 
      : '',
    contactEmail: user?.email || '',
    contactPhone: '',
    businessAddress: '',
    proposedDiscount: '',
    loyaltyRewardFrequency: 'monthly' as 'monthly' | 'quarterly' | 'annually'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [success, setSuccess] = useState(false);

  const updateField = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    
    if (!formData.businessName.trim()) {
      newErrors.businessName = 'Business name is required';
    }
    if (!formData.category) {
      newErrors.category = 'Category is required';
    }
    if (!formData.contactName.trim()) {
      newErrors.contactName = 'Contact name is required';
    }
    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = 'Contact email is required';
    }
    if (!formData.proposedDiscount.trim()) {
      newErrors.proposedDiscount = 'Proposed discount is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Submit the application
      const response = await fetch('/api/network-applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user?.id,
          ...formData
        }),
      });

      if (response.ok) {
        setSuccess(true);
        setTimeout(() => {
          onSubmit?.(true);
        }, 2000);
      } else {
        const error = await response.json();
        console.error('Failed to submit application:', error);
        setErrors({ submit: 'Failed to submit application. Please try again.' });
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      setErrors({ submit: 'An error occurred. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            Application Submitted Successfully!
          </h3>
          <p className="text-gray-600 mb-6">
            Your additional business application has been submitted for review. 
            We'll contact you within 2-3 business days with an update.
          </p>
          <Button onClick={() => onSubmit?.(true)} className="bg-[#3A56FF] hover:bg-[#3A56FF]/90">
            Continue
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Building2 className="h-6 w-6 mr-2" />
            Submit Additional Business Application
          </div>
          {onCancel && (
            <Button variant="ghost" size="sm" onClick={onCancel}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
        <p className="text-gray-600">
          Already have a business in our network? Submit an application for another business you own or manage.
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-600 text-sm">{errors.submit}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="businessName">Business Name *</Label>
              <Input
                id="businessName"
                value={formData.businessName}
                onChange={(e) => updateField('businessName', e.target.value)}
                placeholder="Enter business name"
                className={errors.businessName ? 'border-red-500' : ''}
              />
              {errors.businessName && (
                <p className="text-red-500 text-sm mt-1">{errors.businessName}</p>
              )}
            </div>

            <div>
              <Label htmlFor="category">Category *</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => updateField('category', value)}
              >
                <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {businessCategories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-red-500 text-sm mt-1">{errors.category}</p>
              )}
            </div>

            <div>
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={formData.website}
                onChange={(e) => updateField('website', e.target.value)}
                placeholder="https://example.com"
              />
            </div>

            <div>
              <Label htmlFor="proposedDiscount">Proposed Discount *</Label>
              <Input
                id="proposedDiscount"
                value={formData.proposedDiscount}
                onChange={(e) => updateField('proposedDiscount', e.target.value)}
                placeholder="e.g., 10% off first purchase"
                className={errors.proposedDiscount ? 'border-red-500' : ''}
              />
              {errors.proposedDiscount && (
                <p className="text-red-500 text-sm mt-1">{errors.proposedDiscount}</p>
              )}
            </div>

            <div>
              <Label htmlFor="contactName">Contact Name *</Label>
              <Input
                id="contactName"
                value={formData.contactName}
                onChange={(e) => updateField('contactName', e.target.value)}
                placeholder="Your name"
                className={errors.contactName ? 'border-red-500' : ''}
              />
              {errors.contactName && (
                <p className="text-red-500 text-sm mt-1">{errors.contactName}</p>
              )}
            </div>

            <div>
              <Label htmlFor="contactEmail">Contact Email *</Label>
              <Input
                id="contactEmail"
                type="email"
                value={formData.contactEmail}
                onChange={(e) => updateField('contactEmail', e.target.value)}
                placeholder="<EMAIL>"
                className={errors.contactEmail ? 'border-red-500' : ''}
              />
              {errors.contactEmail && (
                <p className="text-red-500 text-sm mt-1">{errors.contactEmail}</p>
              )}
            </div>

            <div>
              <Label htmlFor="contactPhone">Contact Phone</Label>
              <Input
                id="contactPhone"
                value={formData.contactPhone}
                onChange={(e) => updateField('contactPhone', e.target.value)}
                placeholder="(*************"
              />
            </div>

            <div>
              <Label htmlFor="loyaltyFrequency">Loyalty Reward Frequency</Label>
              <Select
                value={formData.loyaltyRewardFrequency}
                onValueChange={(value: 'monthly' | 'quarterly' | 'annually') => 
                  updateField('loyaltyRewardFrequency', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="annually">Annually</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="businessAddress">Business Address</Label>
            <Textarea
              id="businessAddress"
              value={formData.businessAddress}
              onChange={(e) => updateField('businessAddress', e.target.value)}
              placeholder="Full business address"
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-3">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="bg-[#3A56FF] hover:bg-[#3A56FF]/90"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Submit Application
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
