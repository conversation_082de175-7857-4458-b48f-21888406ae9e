"use client";

import React, { useEffect } from "react";
import { AnimatedSection } from "@/components/animated-section";
import { AnimatedCard } from "@/components/animated-card";
import { Users, BarChart3, Building2, PlusCircle, Edit, Trash2, Share2, <PERSON>Cart, Heart, Settings, Eye } from "lucide-react";
import Image from "next/image";
import { BusinessForm } from "@/components/business/business-form";
import { LoyaltyFrequencyEditor } from "@/components/business/loyalty-frequency-editor";

interface BusinessDashboardContentProps {
  verifiedBusiness: any;
  showForm: boolean;
  setShowForm: (show: boolean) => void;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  loyaltyFrequency: 'monthly' | 'quarterly' | 'annually';
  setLoyaltyFrequency: (frequency: 'monthly' | 'quarterly' | 'annually') => void;
  formLoading: boolean;
  handleFormSubmit: (formData: any, existingBusiness?: any) => Promise<void>;
  handleLoyaltyFrequencyUpdate: (newFrequency: 'monthly' | 'quarterly' | 'annually', businessId: string) => Promise<void>;
  profile: any;
}

export function BusinessDashboardContent({
  verifiedBusiness,
  showForm,
  setShowForm,
  activeTab,
  setActiveTab,
  loyaltyFrequency,
  setLoyaltyFrequency,
  formLoading,
  handleFormSubmit,
  handleLoyaltyFrequencyUpdate,
  profile
}: BusinessDashboardContentProps) {
  
  // Initialize business data when verified business is available
  useEffect(() => {
    if (verifiedBusiness?.id) {
      setLoyaltyFrequency(verifiedBusiness.loyalty_reward_frequency || 'monthly');
    }
  }, [verifiedBusiness?.id, setLoyaltyFrequency]);

  // Define stats with verified business data
  const stats = [
    { label: "QR Interactions", value: verifiedBusiness?.interaction_count || 0, icon: <Users className="h-5 w-5" /> },
    { label: "Referrals", value: verifiedBusiness?.referral_count || 0, icon: <Share2 className="h-5 w-5" /> },
    { label: "Premium Discount", value: `${verifiedBusiness?.premium_discount || 0}%`, icon: <BarChart3 className="h-5 w-5" /> },
  ];

  const tabs = [
    { id: "overview", label: "Overview", icon: <Eye className="h-4 w-4" />, category: "main" },
    { id: "customers", label: "Customers", icon: <Users className="h-4 w-4" />, category: "main" },
    { id: "referrals", label: "Referrals", icon: <Share2 className="h-4 w-4" />, category: "main" },
    { id: "purchases", label: "Purchases", icon: <ShoppingCart className="h-4 w-4" />, category: "main" },
    { id: "loyalty", label: "Loyalty", icon: <Heart className="h-4 w-4" />, category: "config" },
    { id: "settings", label: "Settings", icon: <Settings className="h-4 w-4" />, category: "config" },
  ];

  if (showForm) {
    return (
      <BusinessForm
        initialData={verifiedBusiness}
        onSubmit={(formData) => handleFormSubmit(formData, verifiedBusiness)}
        onCancel={() => setShowForm(false)}
      />
    );
  }

  return (
    <>
      {/* Business Dashboard Content */}
      <div className="flex flex-col md:flex-row justify-between items-start mb-8">
        <div className="flex items-center mb-4 md:mb-0">
          {verifiedBusiness.logo_url ? (
            <Image
              src={verifiedBusiness.logo_url || "/placeholder.svg"}
              width={80}
              height={80}
              alt={verifiedBusiness.name}
              className="rounded-lg mr-4 object-contain bg-white p-1"
              unoptimized={verifiedBusiness.logo_url?.endsWith('.svg')}
            />
          ) : (
            <div className="w-20 h-20 bg-[#3A56FF]/20 rounded-lg flex items-center justify-center mr-4">
              <Building2 className="h-8 w-8 text-[#3A56FF]" />
            </div>
          )}
          <div>
            <h2 className="text-2xl font-bold">{verifiedBusiness.name}</h2>
            <p className="text-[#4a4a4a]">
              {verifiedBusiness.premium_discount
                ? `${verifiedBusiness.premium_discount}% Premium Discount`
                : "No premium discount"}
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowForm(true)}
            className="bg-[#3A56FF] text-white px-4 py-2 rounded-md text-sm flex items-center"
          >
            <Edit className="h-4 w-4 mr-2" /> Edit Business
          </button>
        </div>
      </div>

      {/* Tabs and Content */}
      <div className="mb-8">
        {/* Mobile: Horizontal scroll */}
        <div className="block sm:hidden">
          <div className="flex border-b border-gray-200 overflow-x-auto scrollbar-hide">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`py-3 px-4 font-medium whitespace-nowrap flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? "text-[#3A56FF] border-b-2 border-[#3A56FF]"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                {tab.icon}
                <span className="text-sm">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Desktop: Compartmentalized grid */}
        <div className="hidden sm:block">
          <div className="space-y-4">
            {/* Main Business Operations */}
            <div>
              <h4 className="text-sm font-medium text-gray-400 mb-2 px-1">Business Operations</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {tabs.filter(tab => tab.category === "main").map((tab) => (
                  <button
                    key={tab.id}
                    className={`p-3 rounded-lg font-medium flex items-center justify-center space-x-2 transition-all ${
                      activeTab === tab.id
                        ? "bg-[#3A56FF] text-white shadow-lg"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800"
                    }`}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    {tab.icon}
                    <span className="text-sm font-medium">{tab.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Configuration & Settings */}
            <div>
              <h4 className="text-sm font-medium text-gray-400 mb-2 px-1">Configuration</h4>
              <div className="grid grid-cols-2 gap-2">
                {tabs.filter(tab => tab.category === "config").map((tab) => (
                  <button
                    key={tab.id}
                    className={`p-3 rounded-lg font-medium flex items-center justify-center space-x-2 transition-all ${
                      activeTab === tab.id
                        ? "bg-[#3A56FF] text-white shadow-lg"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800"
                    }`}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    {tab.icon}
                    <span className="text-sm font-medium">{tab.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Render Tab Content */}
      {activeTab === "overview" && (
        <div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {stats.map((stat, index) => (
              <AnimatedSection key={stat.label} delay={index * 0.1}>
                <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white">
                  <div className="flex items-center mb-4">
                    <div className="bg-[#3A56FF]/20 p-3 rounded-full mr-4">{stat.icon}</div>
                    <div>
                      <p className="text-sm text-gray-300">{stat.label}</p>
                      <p className="text-2xl font-bold">{stat.value}</p>
                    </div>
                  </div>
                </AnimatedCard>
              </AnimatedSection>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <AnimatedSection>
              <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white">
                <h3 className="font-bold text-xl mb-4">Business Information</h3>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-400">Category</p>
                    <p>{verifiedBusiness.category || "Not specified"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Address</p>
                    <p>{verifiedBusiness.business_address || "Not provided"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Contact Phone</p>
                    <p>{verifiedBusiness.contact_phone || "Not provided"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Contact Email</p>
                    <p>{verifiedBusiness.contact_email || "Not provided"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Website</p>
                    <p>{verifiedBusiness.website || "Not provided"}</p>
                  </div>
                </div>
              </AnimatedCard>
            </AnimatedSection>

            <AnimatedSection delay={0.1}>
              <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white">
                <h3 className="font-bold text-xl mb-4">Business Status</h3>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-400">Status</p>
                    <p className="text-green-400">{verifiedBusiness.is_active ? "Active" : "Inactive"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Premium Discount</p>
                    <p>{verifiedBusiness.premium_discount || 0}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Loyalty Frequency</p>
                    <p className="capitalize">{verifiedBusiness.loyalty_reward_frequency || "Monthly"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Business Spotlight</p>
                    <p>{verifiedBusiness.business_spotlight ? "Enabled" : "Disabled"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Created</p>
                    <p>{verifiedBusiness.created_at ? new Date(verifiedBusiness.created_at).toLocaleDateString() : "Unknown"}</p>
                  </div>
                </div>
              </AnimatedCard>
            </AnimatedSection>
          </div>
        </div>
      )}

      {activeTab === "customers" && (
        <div>
          <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white mb-8">
            <h3 className="font-bold text-xl mb-4">Customer Management</h3>
            <p className="text-gray-300 mb-6">
              View and manage your customers, track their loyalty points, and send targeted promotions.
            </p>
            <div className="bg-[#2A2A2A] p-6 rounded-lg text-center">
              <p className="text-gray-300 mb-4">
                No customers found. Start adding customers to your loyalty program.
              </p>
              <button className="bg-[#3A56FF] text-white px-4 py-2 rounded-md text-sm flex items-center mx-auto">
                <PlusCircle className="h-4 w-4 mr-2" /> Add Customer
              </button>
            </div>
          </AnimatedCard>
        </div>
      )}

      {activeTab === "referrals" && (
        <div>
          <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white mb-8">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-bold text-xl">Referrals</h3>
              <div className="bg-[#3A56FF]/20 px-3 py-1 rounded-full">
                <span className="text-[#3A56FF] font-bold">{verifiedBusiness.referral_count || 0} Total</span>
              </div>
            </div>
            <p className="text-gray-300 mb-6">
              Track referrals to your business and reward customers for spreading the word.
            </p>

            <div className="bg-[#2A2A2A] p-6 rounded-lg text-center">
              <p className="text-gray-300 mb-4">
                You have {verifiedBusiness.referral_count || 0} users who selected your business as their referrer.
              </p>
              <p className="text-gray-400 text-sm">
                Detailed referral tracking coming soon.
              </p>
            </div>
          </AnimatedCard>
        </div>
      )}

      {activeTab === "purchases" && (
        <div>
          <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white mb-8">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-bold text-xl">Purchases</h3>
              <div className="bg-[#3A56FF]/20 px-3 py-1 rounded-full">
                <span className="text-[#3A56FF] font-bold">0 Total</span>
              </div>
            </div>
            <p className="text-gray-300 mb-6">
              Track all purchases made at your business and monitor customer spending patterns.
            </p>

            <div className="bg-[#2A2A2A] p-6 rounded-lg text-center">
              <p className="text-gray-300 mb-4">
                Purchase tracking system coming soon.
              </p>
              <p className="text-gray-400 text-sm">
                Connect your POS system to track customer purchases and loyalty points.
              </p>
            </div>
          </AnimatedCard>
        </div>
      )}

      {activeTab === "loyalty" && (
        <div>
          <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white mb-8">
            <h3 className="font-bold text-xl mb-4">Loyalty Program Settings</h3>
            <p className="text-gray-300 mb-6">
              Configure your loyalty program settings and check your card holder status to unlock rewards.
            </p>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">Reward Points</h4>
                <div className="bg-[#2A2A2A] p-4 rounded-lg">
                  <div className="flex justify-between mb-2">
                    <span>Points per dollar spent</span>
                    <span className="font-bold">1</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Points expiration (days)</span>
                    <span className="font-bold">365</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Loyalty Reward Schedule</h4>
                <LoyaltyFrequencyEditor
                  currentFrequency={loyaltyFrequency}
                  onUpdate={(frequency) => handleLoyaltyFrequencyUpdate(frequency, verifiedBusiness.id)}
                  loading={formLoading}
                />
              </div>

              <div>
                <h4 className="font-medium mb-2">Premium Discount</h4>
                <div className="bg-[#2A2A2A] p-4 rounded-lg">
                  <div className="flex justify-between mb-2">
                    <span>Current discount percentage</span>
                    <span className="font-bold">{verifiedBusiness.premium_discount || 0}%</span>
                  </div>
                  <button
                    onClick={() => setShowForm(true)}
                    className="bg-[#3A56FF] text-white px-4 py-2 rounded-md text-sm flex items-center mt-2"
                  >
                    <Edit className="h-4 w-4 mr-2" /> Update Discount
                  </button>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Card Holder Status</h4>
                <div className="bg-[#2A2A2A] p-4 rounded-lg">
                  {profile?.is_card_holder ? (
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <div>
                        <p className="text-green-400 font-medium">Card Holder Verified</p>
                        <p className="text-gray-300 text-sm">Your rewards are unlocked and active</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div>
                          <p className="text-yellow-400 font-medium">Card Required</p>
                          <p className="text-gray-300 text-sm">You need to purchase a Fuse VIP card to unlock your business rewards</p>
                        </div>
                      </div>
                      <button
                        onClick={() => window.open('/upgrade', '_blank')}
                        className="bg-[#3A56FF] text-white px-4 py-2 rounded-md text-sm flex items-center"
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" /> Get Your Fuse VIP Card
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </AnimatedCard>
        </div>
      )}

      {activeTab === "settings" && (
        <div>
          <AnimatedCard className="bg-[#1A1A1A] p-6 rounded-lg text-white mb-8">
            <h3 className="font-bold text-xl mb-4">Business Settings</h3>
            <p className="text-gray-300 mb-6">
              Manage your business settings, notification preferences, and account information.
            </p>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">Notification Settings</h4>
                <div className="bg-[#2A2A2A] p-4 rounded-lg space-y-2">
                  <div className="flex items-center justify-between">
                    <span>Email notifications</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:bg-[#3A56FF] peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
                    </label>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>SMS notifications</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:bg-[#3A56FF] peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
                    </label>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Push notifications</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:bg-[#3A56FF] peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
                    </label>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Danger Zone</h4>
                <div className="bg-[#2A2A2A] p-4 rounded-lg">
                  <p className="text-gray-300 mb-4">
                    Permanently delete your business and all associated data.
                  </p>
                  <button className="bg-red-600 text-white px-4 py-2 rounded-md text-sm flex items-center">
                    <Trash2 className="h-4 w-4 mr-2" /> Delete Business
                  </button>
                </div>
              </div>
            </div>
          </AnimatedCard>
        </div>
      )}
    </>
  );
}
