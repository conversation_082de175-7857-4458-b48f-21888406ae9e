"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { Building2, Loader2 } from "lucide-react";

interface BusinessOwnershipGuardProps {
  children: (business: any) => React.ReactNode;
  fallback?: React.ReactNode;
  onBusinessNotFound?: () => void;
}

interface BusinessData {
  id: string;
  name: string;
  logo_url?: string;
  website?: string;
  category?: string;
  premium_discount?: string;
  is_active: boolean;
  user_id: string;
  interaction_count?: number;
  referral_count?: number;
  [key: string]: any;
}

export function BusinessOwnershipGuard({
  children,
  fallback,
  onBusinessNotFound
}: BusinessOwnershipGuardProps) {
  const { user } = useAuth();
  const [business, setBusiness] = useState<BusinessData | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchBusinessData = async () => {
    if (!user?.id) {
      setLoading(false);
      return;
    }

    try {
      console.log('Fetching business data for user:', user.id);

      // Mirror the successful pattern from main dashboard
      // Use authenticated session (no userId parameter needed)
      const response = await fetch('/api/dashboard/business', {
        credentials: 'include', // Include authentication cookies
        headers: {
          'Cache-Control': 'no-cache',
        },
      });
      const result = await response.json();

      if (response.ok && result.business) {
        setBusiness(result.business);
        console.log('✅ Business data loaded successfully:', result.business.name);
      } else {
        setBusiness(null);
        onBusinessNotFound?.();
        console.log('No business found for user - this is normal for users without businesses');
      }
    } catch (error) {
      console.error("❌ Error fetching business data:", error);
      setBusiness(null);
      onBusinessNotFound?.();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBusinessData();
  }, [user?.id]);

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-5 w-5 animate-spin text-[#3A56FF]" />
          <span className="text-gray-600">Loading your business...</span>
        </div>
      </div>
    );
  }

  // Not authenticated
  if (!user) {
    return (
      <div className="text-center py-16">
        <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">Please log in to access your business dashboard.</p>
      </div>
    );
  }

  // No business found - show fallback
  if (!business) {
    return fallback || (
      <div className="text-center py-16">
        <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">No business found for your account.</p>
        <p className="text-gray-400 text-sm mt-2">
          Register your business to access the dashboard.
        </p>
      </div>
    );
  }

  // Business found - render children with business data
  return <>{children(business)}</>;
}
