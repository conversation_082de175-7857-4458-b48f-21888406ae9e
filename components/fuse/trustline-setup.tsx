"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ExternalLink, Wallet, CheckCircle, AlertCircle, Copy, Smartphone, Monitor, QrCode, Gift, CreditCard, Coins } from 'lucide-react'
import { toast } from 'sonner'
import { useXummClient } from '@/hooks/use-xumm-client'
import { QRCodeSVG as QRCode } from 'qrcode.react'
import { useAuth } from '@/contexts/auth-context'
import { useRouter } from 'next/navigation'
import { useXamanUserInfoCache } from '@/lib/xaman-userinfo-cache'

interface TrustlineSetupProps {
  className?: string
}

export function TrustlineSetup({ className }: TrustlineSetupProps) {
  const [copied, setCopied] = useState(false)
  const [showClientMethod, setShowClientMethod] = useState(false)
  const [clientQRData, setClientQRData] = useState<string | null>(null)
  const [isCreatingPayload, setIsCreatingPayload] = useState(false)
  const [trustlineComplete, setTrustlineComplete] = useState(false)
  const [userAccountInfo, setUserAccountInfo] = useState<any>(null)
  const [showSuccessActions, setShowSuccessActions] = useState(false)
  const [showWelcomeBack, setShowWelcomeBack] = useState(false)
  const [showTransactionVerification, setShowTransactionVerification] = useState(false)
  const [transactionHash, setTransactionHash] = useState('')
  const [isVerifyingTransaction, setIsVerifyingTransaction] = useState(false)
  const [showManualWalletInput, setShowManualWalletInput] = useState(false)
  const [manualWalletAddress, setManualWalletAddress] = useState('')

  const { user } = useAuth()
  const router = useRouter()

  const {
    isConnected,
    account,
    sdkReady,
    createPayload,
    isMobile
  } = useXummClient()

  const {
    fetchAndCache,
    getCachedByUserId
  } = useXamanUserInfoCache(user?.id)

  // FUSE token trustline configuration
  const trustlineConfig = {
    currency: '4655534500000000000000000000000000000000', // 'FUSE' padded to 20 bytes hex
    issuer: 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo',
    limit: '********.********'
  }

  // XRPL Services link for manual setup
  const xrplServicesLink = `https://xrpl.services/?issuer=${trustlineConfig.issuer}&currency=${trustlineConfig.currency}&limit=${trustlineConfig.limit}`

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    toast.success(`${label} copied to clipboard!`)
    setTimeout(() => setCopied(false), 2000)
  }

  // Cache user account info using OAuth2 userinfo endpoint
  const cacheUserAccountInfo = async () => {
    try {
      const userInfo = await fetchAndCache()

      if (userInfo) {
        setUserAccountInfo(userInfo)
        console.log('✅ User account info cached successfully')
        return userInfo
      } else {
        console.warn('⚠️ Could not fetch userinfo, proceeding without cache')
        return null
      }
    } catch (error) {
      console.error('❌ Error caching user account info:', error)
      return null
    }
  }

  // Client-side trustline creation using Xumm SDK with userinfo caching
  const handleClientSideTrustline = async () => {
    if (!sdkReady) {
      toast.error('Xumm SDK not ready. Please try again.')
      return
    }

    try {
      setIsCreatingPayload(true)

      // First, try to cache user account info
      const accountInfo = await cacheUserAccountInfo()

      const payload = {
        TransactionType: 'TrustSet',
        LimitAmount: {
          currency: trustlineConfig.currency,
          issuer: trustlineConfig.issuer,
          value: trustlineConfig.limit,
        }
      }

      // For desktop users, we'd typically get a QR code
      // For mobile users, it would deeplink directly
      if (!isMobile()) {
        // Generate QR data for desktop scanning
        const qrData = `xumm://payload/${JSON.stringify(payload)}`
        setClientQRData(qrData)
        toast.success('QR code generated! Scan with your Xumm mobile app.')
      }

      // Create the payload using client SDK
      const result = await createPayload(payload)

      if (result) {
        console.log('✅ Trustline setup completed successfully!')

        // Extract wallet address from the result or account info
        const walletAddress = (result as any)?.account || accountInfo?.account || account

        if (walletAddress) {
          // Store wallet address in localStorage for future use
          localStorage.setItem('fuse_wallet_address', walletAddress)
          localStorage.setItem('fuse_wallet_connected', 'true')

          // Update local state
          setUserAccountInfo({ account: walletAddress })

          // If user is logged in, update their profile
          if (user?.id) {
            try {
              await updateUserWalletProfile(user.id, walletAddress)
              console.log('✅ User wallet profile updated after trustline setup')
            } catch (updateError) {
              console.warn('⚠️ Could not update user profile, but trustline was successful:', updateError)
            }
          }
        }

        setTrustlineComplete(true)
        setShowSuccessActions(true)
        setShowClientMethod(false)
        setClientQRData(null)

        if (user?.id) {
          toast.success('🎉 FUSE Trustline setup completed! Your wallet is connected to your account!')
        } else {
          toast.success('🎉 FUSE Trustline setup completed! Your wallet is ready for FUSE tokens!')
          toast.info('Sign in or register to connect this wallet to your account')
        }
      }

    } catch (error) {
      console.error('Client-side trustline creation failed:', error)
      toast.error(`Failed to create trustline: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsCreatingPayload(false)
    }
  }

  // Helper function to update user profile with wallet address
  const updateUserWalletProfile = async (userId: string, walletAddress: string) => {
    const response = await fetch('/api/wallet-connection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        walletAddress,
        walletType: 'xrp',
        action: 'connect'
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || 'Failed to update wallet profile')
    }

    return response.json()
  }

  // Verify transaction and extract wallet address
  const verifyTransactionAndExtractWallet = async (txHash: string) => {
    // No login required - we want to allow any visitor to verify their transaction

    if (!txHash || txHash.length !== 64) {
      toast.error('Please enter a valid transaction hash (64 characters)')
      return
    }

    try {
      setIsVerifyingTransaction(true)

      // Call our transaction verification API
      const response = await fetch('/api/xrpl/verify-trustline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionHash: txHash,
          // Only include userId if user is logged in
          ...(user?.id && { userId: user.id }),
          expectedCurrency: trustlineConfig.currency,
          expectedIssuer: trustlineConfig.issuer
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Transaction verification failed')
      }

      const result = await response.json()

      if (result.success && result.walletAddress) {
        // Update local state with the wallet address
        setUserAccountInfo({ account: result.walletAddress })
        setTrustlineComplete(true)
        setShowSuccessActions(true)
        setShowTransactionVerification(false)
        setTransactionHash('')

        // Store wallet address in localStorage for future use
        localStorage.setItem('fuse_wallet_address', result.walletAddress)
        localStorage.setItem('fuse_wallet_connected', 'true')

        // If user is logged in, update their profile
        if (user?.id) {
          try {
            await updateUserWalletProfile(user.id, result.walletAddress)
            toast.success('🎉 Transaction verified! Your FUSE trustline is now active and connected to your account!')
          } catch (error) {
            console.error('Error updating user profile:', error)
            toast.success('🎉 Transaction verified! Your FUSE trustline is now active!')
            toast.info('Sign in to connect this wallet to your account')
          }
        } else {
          // User is not logged in - show success message with sign-in prompt
          toast.success('🎉 Transaction verified! Your FUSE trustline is now active!')
          toast.info('Sign in or register to connect this wallet to your account')
        }
      } else if (result.needsManualWalletAddress) {
        // Transaction found but wallet address couldn't be extracted automatically
        setShowManualWalletInput(true)
        toast.info('Transaction found! Please enter your wallet address to complete the verification.')
      } else {
        throw new Error('Transaction verification failed or wallet address not found')
      }

    } catch (error) {
      console.error('Transaction verification error:', error)
      toast.error(`Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsVerifyingTransaction(false)
    }
  }

  // Handle manual wallet address verification
  const handleManualWalletVerification = async () => {
    if (!manualWalletAddress || !manualWalletAddress.match(/^r[0-9A-Fa-f]{24,34}$/)) {
      toast.error('Please enter a valid XRP wallet address (starts with "r")')
      return
    }

    try {
      setIsVerifyingTransaction(true)

      // Store wallet address in localStorage for future use
      localStorage.setItem('fuse_wallet_address', manualWalletAddress)
      localStorage.setItem('fuse_wallet_connected', 'true')

      // Update local state
      setUserAccountInfo({ account: manualWalletAddress })
      setTrustlineComplete(true)
      setShowSuccessActions(true)
      setShowTransactionVerification(false)
      setShowManualWalletInput(false)
      setTransactionHash('')
      setManualWalletAddress('')

      // If user is logged in, update their profile
      if (user?.id) {
        try {
          await updateUserWalletProfile(user.id, manualWalletAddress)
          toast.success('🎉 Wallet address verified! Your FUSE trustline is now active and connected to your account!')
        } catch (error) {
          console.error('Error updating user profile:', error)
          toast.success('🎉 Wallet address verified! Your FUSE trustline is now active!')
          toast.info('There was an issue connecting this wallet to your account')
        }
      } else {
        // User is not logged in - show success message with sign-in prompt
        toast.success('🎉 Wallet address verified! Your FUSE trustline is now active!')
        toast.info('Sign in or register to connect this wallet to your account')
      }

    } catch (error) {
      console.error('Manual wallet verification error:', error)
      toast.error(`Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsVerifyingTransaction(false)
    }
  }

  // Check for cached user info on mount and detect returning users
  useEffect(() => {
    if (user?.id) {
      const cachedInfo = getCachedByUserId()
      if (cachedInfo) {
        console.log('📋 Found cached user info for user:', user.id)
        setUserAccountInfo(cachedInfo)

        // If user already has trustline set up, show welcome back message
        if (cachedInfo.account) {
          console.log('✅ User appears to have completed trustline setup previously')
          setShowWelcomeBack(true)
          setShowSuccessActions(true)

          // Show welcome back toast
          toast.success('🎉 Welcome back! Your FUSE trustline is active and ready!')

          // Auto-hide welcome back message after 5 seconds
          setTimeout(() => {
            setShowWelcomeBack(false)
          }, 5000)
        }
      }
    }
  }, [user?.id, getCachedByUserId])

  // Success actions component
  const renderSuccessActions = () => (
    <div className="space-y-4 mt-6">
      <div className="bg-green-50 dark:bg-green-950/30 rounded-lg p-4 border border-green-200 dark:border-green-800">
        <div className="flex items-start gap-3">
          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">
              🎉 FUSE Trustline Active!
            </h4>
            <p className="text-sm text-green-800 dark:text-green-200 mb-3">
              Your wallet is now connected and ready to receive FUSE tokens.
              {userAccountInfo?.account && (
                <>
                  <br />
                  <span className="font-mono text-xs">
                    {userAccountInfo.account.substring(0, 8)}...{userAccountInfo.account.substring(userAccountInfo.account.length - 6)}
                  </span>
                </>
              )}
              <br />Time to FUSE the revolution!
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Button
                onClick={() => router.push('/upgrade')}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Get VIP Card
              </Button>

              <Button
                onClick={() => window.open('https://xmagnetic.org', '_blank')}
                variant="outline"
                className="border-green-200 dark:border-green-700 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-950"
              >
                <Coins className="w-4 h-4 mr-2" />
                Get FUSE Tokens
              </Button>
            </div>

            {/* Show sign-in/register button for non-logged-in users */}
            {!user?.id && (
              <div className="mt-3 pt-3 border-t border-green-200 dark:border-green-700">
                <p className="text-xs text-green-700 dark:text-green-300 mb-2">
                  💡 <strong>Connect your wallet to an account</strong> to access exclusive features and manage your FUSE tokens.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <Button
                    onClick={() => router.push('/login')}
                    variant="outline"
                    className="border-blue-200 dark:border-blue-700 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950"
                  >
                    Sign In to Connect Wallet
                  </Button>
                  <Button
                    onClick={() => router.push('/register')}
                    variant="outline"
                    className="border-indigo-200 dark:border-indigo-700 text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-950"
                  >
                    Register New Account
                  </Button>
                </div>
              </div>
            )}

            <div className="mt-3 pt-3 border-t border-green-200 dark:border-green-700">
              <p className="text-xs text-green-700 dark:text-green-300">
                💡 <strong>Next Steps:</strong> Get a VIP Card for exclusive discounts at {' '}
                <button
                  onClick={() => router.push('/industry')}
                  className="underline hover:no-underline"
                >
                  74+ businesses
                </button>
                {' '} or grab some FUSE tokens to help the revolution!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  // Welcome back notification for returning users
  const renderWelcomeBack = () => (
    <div className="mb-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/30 dark:to-blue-950/30 rounded-lg p-4 border border-green-200 dark:border-green-700">
      <div className="flex items-start gap-3">
        <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
        <div className="flex-1">
          <h4 className="font-semibold text-green-900 dark:text-green-100 mb-1">
            🎉 Welcome Back, FUSE Revolutionary!
          </h4>
          <p className="text-sm text-green-800 dark:text-green-200 mb-3">
            Your wallet is connected and your FUSE trustline is active. You're all set to receive FUSE tokens and join the revolution!
          </p>
          <div className="flex items-center gap-2 text-xs text-green-700 dark:text-green-300">
            <span className="font-mono bg-green-100 dark:bg-green-900/50 px-2 py-1 rounded">
              {userAccountInfo?.account?.substring(0, 8)}...{userAccountInfo?.account?.substring(userAccountInfo.account.length - 6)}
            </span>
            <span>• Trustline Active</span>
          </div>
        </div>
        <button
          onClick={() => setShowWelcomeBack(false)}
          className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200"
        >
          ✕
        </button>
      </div>
    </div>
  )

  return (
    <Card className={`bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 border-blue-200 dark:border-blue-800 ${className}`}>
      <CardHeader className="text-center">
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mb-4">
          <Wallet className="w-8 h-8 text-white" />
        </div>
        <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
          Set FUSE Trustline
        </CardTitle>
        <CardDescription className="text-gray-600 dark:text-gray-300">
          Enable your XRP wallet to hold $FUSE tokens by setting up a trustline
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Welcome Back Notification */}
        {showWelcomeBack && renderWelcomeBack()}

        {/* What is a Trustline */}
        <div className="bg-blue-100/50 dark:bg-blue-900/30 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">
                What is a Trustline?
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                A trustline tells your XRP wallet that you trust the FUSE token issuer and want to hold FUSE tokens. 
                This is a one-time setup required before you can receive or trade FUSE tokens.
              </p>
            </div>
          </div>
        </div>

        {/* Token Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Token</label>
            <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <span className="font-mono text-lg font-bold text-blue-600 dark:text-blue-400">$FUSE</span>
            </div>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Trust Limit</label>
            <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <span className="font-mono text-sm">{trustlineConfig.limit}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(trustlineConfig.limit, 'Trust limit')}
                className="h-6 w-6 p-0"
              >
                <Copy className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Issuer Address */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Issuer Address</label>
          <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <span className="font-mono text-sm flex-1 break-all">{trustlineConfig.issuer}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(trustlineConfig.issuer, 'Issuer address')}
              className="h-6 w-6 p-0 flex-shrink-0"
            >
              <Copy className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {/* Setup Methods */}
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-900 dark:text-white">Choose Your Setup Method:</h4>
          
          {/* Method 0: Client-Side SDK (New) */}
          {sdkReady && (
            <div className="border-2 border-green-200 dark:border-green-700 rounded-lg p-4 bg-green-50/30 dark:bg-green-950/20">
              <div className="flex items-center gap-3 mb-3">
                <QrCode className="w-5 h-5 text-green-600" />
                <h5 className="font-semibold text-gray-900 dark:text-white">Client-Side SDK: Direct Browser Connection</h5>
                <span className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">
                  Latest
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                Direct wallet interaction with updated mobile routing - creates trustline via browser connection
                without server dependency. Works seamlessly on both mobile and desktop devices.
              </p>
              <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-950/30 p-2 rounded border border-blue-200 dark:border-blue-800 mb-4">
                💡 This method handles transactions client-side, protecting our backend while giving you direct wallet control
              </div>
              
              {!showClientMethod ? (
                <Button
                  onClick={() => setShowClientMethod(true)}
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
                >
                  {isMobile() ? (
                    <Smartphone className="w-4 h-4 mr-2" />
                  ) : (
                    <Monitor className="w-4 h-4 mr-2" />
                  )}
                  Use Browser Connection
                </Button>
              ) : (
                <div className="space-y-4">
                  {/* QR Code for browser connection - works for both desktop and mobile */}
                  {clientQRData && (
                    <div className="flex flex-col items-center space-y-3 p-4 border rounded-lg bg-white dark:bg-gray-900">
                      <QRCode
                        value={clientQRData}
                        size={200}
                        level="M"
                        style={{ padding: 10 }}
                      />
                      <p className="text-sm text-center text-gray-600 dark:text-gray-400">
                        {isMobile()
                          ? "QR code generated for browser connection - routing updated for mobile Xaman users"
                          : "Scan this QR code with your Xumm mobile app"
                        }
                      </p>
                    </div>
                  )}
                  
                  <div className="flex gap-2">
                    <Button
                      onClick={handleClientSideTrustline}
                      disabled={isCreatingPayload}
                      className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
                    >
                      {isCreatingPayload ? (
                        <>
                          <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Wallet className="w-4 h-4 mr-2" />
                          Create Trustline via Browser
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowClientMethod(false)
                        setClientQRData(null)
                      }}
                      disabled={isCreatingPayload}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

      

          {/* Method 2: XRPL Services */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <h5 className="font-semibold text-gray-900 dark:text-white">External: XRPL Services</h5>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              Use XRPL Services web interface to set up the trustline manually. Good for desktop users.
            </p>
            <div className="space-y-3">
              <Button
                onClick={() => window.open(xrplServicesLink, '_blank')}
                variant="outline"
                className="w-full border-blue-200 dark:border-blue-700 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Open XRPL Services
              </Button>

              {!showTransactionVerification ? (
                <Button
                  onClick={() => setShowTransactionVerification(true)}
                  variant="outline"
                  className="w-full border-green-200 dark:border-green-700 text-green-600 dark:text-green-400 hover:bg-green-50 dark:hover:bg-green-950"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  I've Completed My Trustline - Verify Transaction
                </Button>
              ) : (
                !showManualWalletInput ? (
                  <div className="space-y-3 p-3 border border-green-200 dark:border-green-700 rounded-lg bg-green-50/30 dark:bg-green-950/20">
                    <p className="text-xs text-gray-600 dark:text-gray-300">
                      Enter your transaction hash from Bithomp or XRPSCAN to verify and connect your wallet:
                    </p>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={transactionHash}
                        onChange={(e) => setTransactionHash(e.target.value)}
                        placeholder="Enter transaction hash (e.g., F01B00D710FE8CF27E565D007D2E2131520B1BF8FF30DDB9BE7C7746516CB1F4)"
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-xs font-mono"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => verifyTransactionAndExtractWallet(transactionHash)}
                        disabled={isVerifyingTransaction || !transactionHash}
                        className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
                      >
                        {isVerifyingTransaction ? (
                          <>
                            <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            Verifying...
                          </>
                        ) : (
                          <>
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Verify Transaction
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowTransactionVerification(false)
                          setTransactionHash('')
                        }}
                        disabled={isVerifyingTransaction}
                      >
                        Cancel
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 italic">
                      You can find your transaction hash on <a href="https://bithomp.com" target="_blank" rel="noopener noreferrer" className="underline">Bithomp</a> or <a href="https://xrpscan.com" target="_blank" rel="noopener noreferrer" className="underline">XRPSCAN</a> after completing your trustline setup.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3 p-3 border border-blue-200 dark:border-blue-700 rounded-lg bg-blue-50/30 dark:bg-blue-950/20">
                    <p className="text-xs text-gray-600 dark:text-gray-300">
                      Transaction found! Please enter your XRP wallet address to complete the verification:
                    </p>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={manualWalletAddress}
                        onChange={(e) => setManualWalletAddress(e.target.value)}
                        placeholder="Enter your XRP wallet address (e.g., rHuGNhqTG32mfmAvWA8hUyWRLV3tCSwKQt)"
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-xs font-mono"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={handleManualWalletVerification}
                        disabled={isVerifyingTransaction || !manualWalletAddress}
                        className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                      >
                        {isVerifyingTransaction ? (
                          <>
                            <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            Verifying...
                          </>
                        ) : (
                          <>
                            <Wallet className="w-4 h-4 mr-2" />
                            Verify Wallet Address
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowManualWalletInput(false)
                          setShowTransactionVerification(false)
                          setTransactionHash('')
                          setManualWalletAddress('')
                        }}
                        disabled={isVerifyingTransaction}
                      >
                        Cancel
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 italic">
                      Your wallet address is the address that initiated the trustline transaction. You can find it on <a href="https://bithomp.com" target="_blank" rel="noopener noreferrer" className="underline">Bithomp</a> under "Initiated by".
                    </p>
                  </div>
                )
              )}
            </div>
          </div>
        </div>

        {/* Success Actions - Show after trustline completion */}
        {showSuccessActions && renderSuccessActions()}

        {/* Quick Links for Advanced Users */}
        <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Quick Links (Advanced Users):</h4>
          <div className="space-y-2">
          
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[80px]">XRPL:</span>
              <code className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded flex-1 break-all">
                {xrplServicesLink}
              </code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(xrplServicesLink, 'XRPL Services link')}
                className="h-6 w-6 p-0 flex-shrink-0"
              >
                <Copy className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-amber-50 dark:bg-amber-950/30 rounded-lg p-4 border border-amber-200 dark:border-amber-800">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-amber-900 dark:text-amber-100 mb-2">Important Notes:</h4>
              <ul className="text-sm text-amber-800 dark:text-amber-200 space-y-1">
                <li>• You need at least 2 XRP in your wallet to set up a trustline</li>
                <li>• Setting a trustline is a one-time process per token</li>
                <li>• Once set, you can receive and trade FUSE tokens</li>
                <li>• The trustline can be removed later if needed</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
