"use client"

import React, { useState } from 'react'
import Image from 'next/image'
import { motion, AnimatePresence } from 'framer-motion'
import { CreditCard, Wallet, ArrowRight, Sparkles, Zap, X } from 'lucide-react'
import { VIPCardButton } from '@/components/payments/vip-card-button'

// VIP Cards data (same as upgrade page)
const cards = [
  { 
    name: "Monthly VIP Card", 
    description: "Just a taste of fuse?", 
    price: 9.99, 
    type: "monthly", 
    rewards: { referral: "15%", affiliate: "0.5%" },
    cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png",
    features: ["Access to 73+ businesses", "15% referral rewards", "Monthly billing", "Cancel anytime"]
  },
  { 
    name: "Premium Card", 
    price: 100, 
    type: "annual", 
    rewards: { referral: "20%", affiliate: "1%" },
    cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png",
    features: ["Access to 73+ businesses", "20% referral rewards", "Annual savings", "Priority support"]
  },
  { 
    name: "Obsidian Card", 
    description: "Ultimate lifetime access", 
    price: 1500, 
    type: "lifetime", 
    rewards: { referral: "40%", affiliate: "5%" },
    cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/obsidian-card-sZV0uG2g9pJ0BiQRLvn14MJIFGvzDn.png",
    features: ["Lifetime access", "40% referral rewards", "VIP support", "Exclusive perks"]
  },
  { 
    name: "Gold Card", 
    rewards: { referral: "25%", affiliate: "2%" }, 
    type: "coming-soon", 
    price: 250,
    cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/gold-card-bflkHLVolhMSi3L5RexoL9fh7wsqBq.png",
    features: ["Coming soon", "25% referral rewards", "Enhanced benefits", "Gold tier access"]
  },
  { 
    name: "Platinum Card", 
    rewards: { referral: "30%", affiliate: "3%" }, 
    type: "coming-soon", 
    price: 500,
    cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/platinum-card-IQekSEB3CMuXes5qUTxZc55ZeKUVOy.png",
    features: ["Coming soon", "30% referral rewards", "Premium benefits", "Platinum tier access"]
  },
  { 
    name: "Diamond Card", 
    rewards: { referral: "35%", affiliate: "4%" }, 
    type: "coming-soon", 
    price: 1000,
    cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/diamond-card-I9uaKzc6KKTRSgl9mrz4SP2Y2myuiF.png",
    features: ["Coming soon", "35% referral rewards", "Elite benefits", "Diamond tier access"]
  }
]

interface FuseVIPCardSectionProps {
  className?: string
}

export function FuseVIPCardSection({ className = '' }: FuseVIPCardSectionProps) {
  const [showAllCards, setShowAllCards] = useState(false)

  // Featured card (Monthly VIP Card)
  const featuredCard = cards[0]

  return (
    <>
      {/* Main VIP Card Section */}
      <section className={`py-20 bg-gradient-to-b from-white to-gray-50 ${className}`}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#316bff] to-[#4f46e5] text-white px-6 py-2 rounded-full mb-6">
              <Sparkles className="w-4 h-4" />
              <span className="font-bold text-sm uppercase tracking-wider">VIP Access</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Get Your <span className="text-[#316bff]">VIP Card</span>
            </h2>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
              Pay with $FUSE tokens or XRP. Instant access to exclusive discounts at 73+ businesses.
            </p>
          </div>

          {/* Featured Card Display */}
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Card Image */}
              <div className="relative">
                <div className="bg-gradient-to-br from-[#316bff]/10 to-[#4f46e5]/10 rounded-2xl p-8">
                  <Image
                    src={featuredCard.cardImage}
                    width={400}
                    height={250}
                    alt={featuredCard.name}
                    className="w-full aspect-[8/5] object-contain rounded-lg"
                  />
                </div>
              </div>

              {/* Card Details */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">{featuredCard.name}</h3>
                  <p className="text-gray-600 text-lg">{featuredCard.description}</p>
                </div>

                <div className="space-y-3">
                  {featuredCard.features?.map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-[#316bff] rounded-full"></div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-4xl font-bold text-gray-900">${featuredCard.price}</span>
                    <span className="text-gray-600">per month</span>
                  </div>
                  
                  <div className="space-y-3">
                    <VIPCardButton
                      tier="monthly"
                      className="w-full py-4 bg-gradient-to-r from-[#316bff] to-[#4f46e5] text-white rounded-xl font-bold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                    >
                      <div className="flex items-center justify-center gap-2">
                        <Wallet className="w-5 h-5" />
                        Get VIP Access
                        <ArrowRight className="w-5 h-5" />
                      </div>
                    </VIPCardButton>

                    <button
                      onClick={() => setShowAllCards(true)}
                      className="w-full py-4 bg-white border-2 border-[#316bff] text-[#316bff] rounded-xl font-bold text-lg hover:bg-[#316bff] hover:text-white transition-all duration-300 flex items-center justify-center gap-2"
                    >
                      <CreditCard className="w-5 h-5" />
                      View All VIP Cards
                      <ArrowRight className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* All Cards Modal */}
      <AnimatePresence>
        {showAllCards && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowAllCards(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="max-w-7xl w-full max-h-[90vh] overflow-y-auto bg-white rounded-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-8">
                {/* Modal Header */}
                <div className="flex items-center justify-between mb-8">
                  <div>
                    <h2 className="text-3xl font-bold text-gray-900">Choose Your VIP Card</h2>
                    <p className="text-gray-600 mt-2">Pay with $FUSE tokens or XRP</p>
                  </div>
                  <button
                    onClick={() => setShowAllCards(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X className="w-6 h-6 text-gray-500" />
                  </button>
                </div>

                {/* Cards Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {cards.map((card) => (
                    <motion.div
                      key={card.name}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: 0.1 }}
                      className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-all duration-300"
                    >
                      <div className="p-3">
                        <Image
                          src={card.cardImage}
                          width={400}
                          height={250}
                          alt={card.name}
                          className="w-full aspect-[8/5] object-contain rounded-lg"
                        />
                      </div>
                      <div className="p-6">
                        <div className="mb-4">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">{card.name}</h3>
                          {card.description && (
                            <p className="text-gray-600 text-sm">{card.description}</p>
                          )}
                        </div>

                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-2xl font-bold text-gray-900">${card.price}</span>
                            <span className="text-sm text-gray-600">
                              {card.type === 'monthly' ? '/month' :
                               card.type === 'annual' ? '/year' :
                               card.type === 'lifetime' ? 'lifetime' : ''}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600">
                            Referral: {card.rewards.referral} • Affiliate: {card.rewards.affiliate}
                          </div>
                        </div>

                        {card.type === "coming-soon" ? (
                          <button className="w-full py-3 bg-gray-400 text-white rounded-lg font-medium" disabled>
                            Coming Soon
                          </button>
                        ) : (
                          <VIPCardButton
                            tier={card.type === 'monthly' ? 'monthly' : card.type === 'annual' ? 'annual' : 'lifetime'}
                            className="w-full py-3 bg-gradient-to-r from-[#316bff] to-purple-600 text-white rounded-lg font-medium hover:from-[#2557d6] hover:to-purple-700 transition-all"
                          >
                            <div className="flex items-center justify-center gap-2">
                              <Wallet className="h-4 w-4" />
                              Get VIP Access
                            </div>
                          </VIPCardButton>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>


    </>
  )
}
