"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Wallet, ExternalLink, CheckCircle, ArrowRight } from 'lucide-react'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'
import { useXummClient } from '@/hooks/use-xumm-client'

interface WalletConnectModalProps {
  children: React.ReactNode
  className?: string
}

export function WalletConnectModal({ children, className }: WalletConnectModalProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const router = useRouter()

  const {
    isConnected,
    account,
    sdkReady,
    connect,
    isMobile
  } = useXummClient()

  const handleConnectWallet = async () => {
    if (!sdkReady) {
      toast.error('Wallet SDK not ready. Please try again.')
      return
    }

    try {
      setIsConnecting(true)
      await connect()
      
      // Wait a moment for connection to establish
      setTimeout(() => {
        if (account) {
          // Store wallet connection state
          localStorage.setItem('fuse_wallet_address', account)
          localStorage.setItem('fuse_wallet_connected', 'true')
          
          toast.success('🎉 Wallet connected successfully!')
          setIsOpen(false)
          
          // Redirect to trustline setup
          router.push('/fuse?tab=trustline')
        }
      }, 1000)
      
    } catch (error) {
      console.error('Wallet connection error:', error)
      toast.error(`Failed to connect wallet: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsConnecting(false)
    }
  }

  const handleSetupTrustline = () => {
    setIsOpen(false)
    router.push('/fuse?tab=trustline')
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild className={className}>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wallet className="w-5 h-5" />
            Connect Your Wallet
          </DialogTitle>
          <DialogDescription>
            Connect your XRP wallet to set up a FUSE trustline and join the revolution!
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Connection Status */}
          {isConnected && account ? (
            <div className="bg-green-50 dark:bg-green-950/30 rounded-lg p-4 border border-green-200 dark:border-green-800">
              <div className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                <div>
                  <p className="font-semibold text-green-900 dark:text-green-100">
                    Wallet Connected!
                  </p>
                  <p className="text-sm text-green-800 dark:text-green-200">
                    <span className="font-mono">
                      {account.substring(0, 8)}...{account.substring(account.length - 6)}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-blue-50 dark:bg-blue-950/30 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
              <div className="flex items-start gap-3">
                <Wallet className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                <div>
                  <p className="font-semibold text-blue-900 dark:text-blue-100 mb-1">
                    Why Connect Your Wallet?
                  </p>
                  <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <li>• Set up FUSE token trustline</li>
                    <li>• Receive FUSE tokens and rewards</li>
                    <li>• Access exclusive VIP features</li>
                    <li>• Join the decentralized revolution</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            {!isConnected ? (
              <Button
                onClick={handleConnectWallet}
                disabled={isConnecting || !sdkReady}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
              >
                {isConnecting ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <Wallet className="w-4 h-4 mr-2" />
                    Connect with Xaman {isMobile() ? '(Mobile)' : '(Desktop)'}
                  </>
                )}
              </Button>
            ) : (
              <Button
                onClick={handleSetupTrustline}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
              >
                <ArrowRight className="w-4 h-4 mr-2" />
                Set Up FUSE Trustline
              </Button>
            )}

            {/* Alternative: Direct to trustline setup */}
            <Button
              onClick={handleSetupTrustline}
              variant="outline"
              className="w-full border-gray-200 dark:border-gray-700"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Skip to Trustline Setup
            </Button>
          </div>

          {/* Info */}
          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            <p>
              No account required! You can set up a trustline as a visitor and connect your wallet to an account later.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
