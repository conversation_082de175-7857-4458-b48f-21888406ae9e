"use client"

interface LottieIframeProps {
  src: string
  width?: number | string
  height?: number | string
  className?: string
  title?: string
}

/**
 * Reliable Lottie animation component using iframe approach
 * This bypasses CSP script-src restrictions and is more reliable
 */
export function LottieIframe({
  src,
  width = 300,
  height = 300,
  className = "",
  title = "Lottie Animation"
}: LottieIframeProps) {
  // Convert lottie.host URL to embed URL if needed
  const getEmbedUrl = (url: string): string => {
    if (url.includes('/embed/')) {
      return url
    }
    
    // Extract the ID from lottie.host URL
    // Format: https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie
    const match = url.match(/lottie\.host\/([^\/]+)\/([^\/]+)\.lottie/)
    if (match) {
      const [, id, filename] = match
      return `https://lottie.host/embed/${id}/${filename}.html`
    }
    
    return url
  }

  const embedUrl = getEmbedUrl(src)

  return (
    <div className={`flex justify-center ${className}`}>
      <iframe
        src={embedUrl}
        width={width}
        height={height}
        className="border-0 overflow-hidden"
        title={title}
        allowFullScreen
        loading="lazy"
      />
    </div>
  )
}

/**
 * Your specific animation as requested
 */
export function YourLottieAnimation() {
  return (
    <LottieIframe
      src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
      width={300}
      height={300}
      title="Your Lottie Animation"
    />
  )
}
