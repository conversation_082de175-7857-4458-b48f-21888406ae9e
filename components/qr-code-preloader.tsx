"use client";

import { useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';

interface QRCodePreloaderProps {
  additionalUserIds?: string[];
}

export function QRCodePreloader({ additionalUserIds = [] }: QRCodePreloaderProps) {
  const { user } = useAuth();

  useEffect(() => {
    const preloadQRCodes = async () => {
      // Collect user IDs to preload
      const userIdsToPreload = [];
      
      if (user?.id) {
        userIdsToPreload.push(user.id);
      }
      
      // Add any additional user IDs passed as props
      userIdsToPreload.push(...additionalUserIds);

      if (userIdsToPreload.length === 0) {
        return;
      }

      try {
        console.log(`🔄 Preloading QR codes for ${userIdsToPreload.length} users`);
        
        const response = await fetch('/api/qr-codes/cache', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'preload',
            userIds: userIdsToPreload
          }),
        });

        const result = await response.json();
        
        if (result.success) {
          console.log('✅ QR codes preloaded successfully:', result.stats);
        } else {
          console.warn('⚠️ QR code preloading failed:', result.error);
        }
      } catch (error) {
        console.warn('⚠️ QR code preloading error:', error);
        // Don't show error to user - this is background optimization
      }
    };

    // Delay preloading slightly to not block initial page load
    const timeoutId = setTimeout(preloadQRCodes, 500);
    
    return () => clearTimeout(timeoutId);
  }, [user?.id, additionalUserIds]);

  // This component doesn't render anything
  return null;
}