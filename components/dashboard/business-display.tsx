"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Building2, BarChart3, Users, Plus } from 'lucide-react';
import { useDashboardData } from '@/components/containers/dashboard-data-container';
import Link from 'next/link';
import { AnimatedSection } from '@/components/animated-section';
import { BusinessAccessRequestModal } from '@/components/business/business-access-request-modal';

interface BusinessDisplayProps {
  variant?: 'full' | 'compact';
  className?: string;
}

export function BusinessDisplay({
  variant = 'full',
  className = ""
}: BusinessDisplayProps) {
  const { userBusiness, hasUserBusiness, isLoading } = useDashboardData();
  const [showAccessRequestModal, setShowAccessRequestModal] = useState(false);

  // Helper function to get business category colors and info
  const getBusinessCategoryInfo = (category: string) => {
    const categoryMap: { [key: string]: { color: string; icon: string; description: string } } = {
      'restaurant': {
        color: 'from-orange-600 via-red-600 to-pink-700',
        icon: '🍽️',
        description: 'Food & Dining'
      },
      'retail': {
        color: 'from-purple-600 via-blue-600 to-indigo-700',
        icon: '🛍️',
        description: 'Retail & Shopping'
      },
      'health': {
        color: 'from-green-600 via-emerald-600 to-teal-700',
        icon: '🏥',
        description: 'Health & Wellness'
      },
      'beauty': {
        color: 'from-pink-600 via-rose-600 to-red-700',
        icon: '💄',
        description: 'Beauty & Spa'
      },
      'automotive': {
        color: 'from-gray-600 via-slate-600 to-zinc-700',
        icon: '🚗',
        description: 'Automotive'
      },
      'entertainment': {
        color: 'from-yellow-600 via-orange-600 to-red-700',
        icon: '🎭',
        description: 'Entertainment'
      },
      'fitness': {
        color: 'from-blue-600 via-cyan-600 to-teal-700',
        icon: '💪',
        description: 'Fitness & Sports'
      },
      'technology': {
        color: 'from-indigo-600 via-purple-600 to-pink-700',
        icon: '💻',
        description: 'Technology'
      },
      'education': {
        color: 'from-emerald-600 via-green-600 to-lime-700',
        icon: '📚',
        description: 'Education'
      },
      'professional': {
        color: 'from-slate-600 via-gray-600 to-stone-700',
        icon: '💼',
        description: 'Professional Services'
      }
    };

    const categoryKey = category?.toLowerCase() || 'professional';
    return categoryMap[categoryKey] || categoryMap['professional'];
  };

  if (isLoading) {
    return (
      <div className={`${className} animate-pulse`}>
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-300 to-gray-400 rounded-2xl opacity-90"></div>
          <div className="relative bg-black/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <div className="text-center space-y-4">
              <div className="w-20 h-6 bg-gray-300 rounded mx-auto"></div>
              <div className="w-32 h-8 bg-gray-300 rounded mx-auto"></div>
              <div className="w-20 h-20 bg-gray-300 rounded-lg mx-auto"></div>
              <div className="space-y-2">
                <div className="w-full h-10 bg-gray-300 rounded"></div>
                <div className="grid grid-cols-2 gap-2">
                  <div className="h-8 bg-gray-300 rounded"></div>
                  <div className="h-8 bg-gray-300 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!isLoading && userBusiness) {
    // Show user's business
    return (
      <AnimatedSection delay={0} className={className}>
        <div className="relative group">
          <div className={`absolute inset-0 bg-gradient-to-br ${getBusinessCategoryInfo(userBusiness.category || '')?.color || 'from-blue-600 via-purple-600 to-indigo-700'} rounded-2xl opacity-90 group-hover:opacity-100 transition-opacity duration-300`}></div>

          <div className="relative bg-black/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <div className="text-center text-white space-y-4">
              <div className="inline-flex items-center px-3 py-1 bg-green-500 text-white rounded-full text-xs font-semibold">
                ✅ Business Owner
              </div>

              <h3 className="text-2xl font-bold text-yellow-400">
                {userBusiness.name}
              </h3>

              <div className="flex justify-center">
                <div className="w-20 h-20 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-3xl">{getBusinessCategoryInfo(userBusiness.category || '')?.icon || '🏢'}</span>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="grid grid-cols-2 gap-3 text-center text-sm">
                  <div>
                    <div className="font-bold text-yellow-400">{getBusinessCategoryInfo(userBusiness.category || '')?.description || 'Business'}</div>
                    <div className="text-white/70 text-xs">Category</div>
                  </div>
                  {userBusiness.premium_discount && (
                    <div>
                      <div className="font-bold text-green-400">{userBusiness.premium_discount}</div>
                      <div className="text-white/70 text-xs">Discount</div>
                    </div>
                  )}
                </div>
                
                {/* Business stats */}
                {(userBusiness.interaction_count !== undefined || userBusiness.referral_count !== undefined) && (
                  <div className="grid grid-cols-2 gap-3 text-center text-sm mt-3 pt-3 border-t border-white/20">
                    <div>
                      <div className="font-bold text-blue-400">{userBusiness.interaction_count || 0}</div>
                      <div className="text-white/70 text-xs">QR Scans</div>
                    </div>
                    <div>
                      <div className="font-bold text-purple-400">{userBusiness.referral_count || 0}</div>
                      <div className="text-white/70 text-xs">Referrals</div>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Link href="/dashboard/business">
                  <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-6 py-2 rounded-lg w-full border text-sm">
                    Manage Business
                  </Button>
                </Link>
                <div className="grid grid-cols-3 gap-2">
                  <Link href="/dashboard/business-qr">
                    <Button className="bg-yellow-500 hover:bg-yellow-400 text-black font-bold px-2 py-2 rounded-lg w-full text-xs">
                      Analytics
                    </Button>
                  </Link>
                  <Link href="/industry">
                    <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 px-2 py-2 rounded-lg border text-xs">
                      Directory
                    </Button>
                  </Link>
                  <Link href="/dashboard/business?additional=true">
                    <Button className="bg-green-500 hover:bg-green-400 text-white px-2 py-2 rounded-lg w-full text-xs">
                      <Plus className="h-3 w-3 mr-1" />
                      Add More
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AnimatedSection>
    );
  }

  // Show business registration CTA
  return (
    <>
      <AnimatedSection delay={0} className={className}>
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl opacity-90 group-hover:opacity-100 transition-opacity duration-300"></div>

          <div className="relative bg-black/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <div className="text-center text-white space-y-4">
              <div className="inline-flex items-center px-3 py-1 bg-yellow-500 text-black rounded-full text-xs font-semibold">
                🏢 For Business Owners
              </div>

              <h3 className="text-2xl font-bold">
                Grow Your <span className="text-yellow-400">Business</span>
              </h3>

              <p className="text-sm text-white/90">
                Join our network and attract more customers with loyalty rewards.
              </p>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center">
                    <span className="text-green-400 mr-1">✓</span>
                    <span>40% retention boost</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-400 mr-1">✓</span>
                    <span>Directory listing</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-400 mr-1">✓</span>
                    <span>Analytics access</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-400 mr-1">✓</span>
                    <span>Free setup</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Link href="/register-business">
                  <Button className="bg-yellow-500 hover:bg-yellow-400 text-black font-bold px-6 py-2 rounded-lg w-full text-sm">
                    🚀 Register Business
                  </Button>
                </Link>
                <Button
                  onClick={() => setShowAccessRequestModal(true)}
                  className="bg-blue-500 hover:bg-blue-400 text-white font-semibold px-6 py-2 rounded-lg w-full text-sm"
                >
                  🏢 Request Business Portal Access
                </Button>
                <div className="grid grid-cols-2 gap-2">
                  <Link href="/dashboard/business">
                    <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 px-4 py-2 rounded-lg w-full border text-xs">
                      View Dashboard
                    </Button>
                  </Link>
                  <Link href="/register-business?multiple=true">
                    <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 px-4 py-2 rounded-lg w-full border text-xs">
                      <Plus className="h-3 w-3 mr-1" />
                      Multiple Apps
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AnimatedSection>

      {/* Business Access Request Modal */}
      <BusinessAccessRequestModal
        isOpen={showAccessRequestModal}
        onClose={() => setShowAccessRequestModal(false)}
      />
    </>
  );
}
