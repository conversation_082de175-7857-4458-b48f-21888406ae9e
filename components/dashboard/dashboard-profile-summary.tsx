"use client"

import { useDashboardProfile, getDisplayName, getMembershipStatus } from "@/hooks/use-dashboard-profile"
import { useProfileRealtime } from "@/hooks/use-profile-realtime"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { User, Crown, Loader2, Wallet, Copy, CheckCircle, Edit, RefreshCw, Wifi, WifiOff } from "lucide-react"
import { AnimatedCard } from "@/components/animated-card"
import { ProfileEditModal } from "@/components/dashboard/profile-edit-modal"
import { useState } from "react"

interface DashboardProfileSummaryProps {
  className?: string
}

export function DashboardProfileSummary({ className = "" }: DashboardProfileSummaryProps) {
  // Use real-time profile hook with fallback to dashboard profile
  const { profile: realtimeProfile, isLoading: realtimeLoading, syncStatus, handleRefresh } = useProfileRealtime()
  const { profile: dashboardProfile, isLoading: dashboardLoading, error } = useDashboardProfile()
  
  // Use real-time profile if available, otherwise fallback to dashboard profile
  const profile = realtimeProfile || dashboardProfile
  const isLoading = realtimeLoading || dashboardLoading
  
  const [copied, setCopied] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)

  const handleCopyAddress = async (address: string) => {
    try {
      await navigator.clipboard.writeText(address)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy address:', error)
    }
  }

  if (isLoading) {
    return (
      <AnimatedCard className={`bg-[#1A1A1A] border border-white/10 rounded-lg p-4 text-white ${className}`}>
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin text-[#3A56FF]" />
        </div>
      </AnimatedCard>
    )
  }

  if (error || !profile) {
    return (
      <AnimatedCard className={`bg-[#1A1A1A] border border-white/10 rounded-lg p-4 text-white ${className}`}>
        <div className="text-center text-gray-400">
          <User className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">{error || 'Profile not available'}</p>
        </div>
      </AnimatedCard>
    )
  }

  const displayName = getDisplayName(profile)
  const membershipInfo = getMembershipStatus(profile)

  return (
    <AnimatedCard className={`bg-[#1A1A1A] border border-white/10 rounded-lg p-4 text-white ${className}`}>
      <div className="flex items-center gap-3">
        <div className="w-12 h-12 bg-[#3A56FF]/20 rounded-full flex items-center justify-center">
          <User className="h-6 w-6 text-[#3A56FF]" />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-white truncate">{displayName}</h3>
          <p className="text-sm text-gray-400 truncate">{profile.user_email}</p>
          <div className="flex items-center gap-2 mt-1">
            <Badge className={`text-xs ${membershipInfo.color}`}>
              {membershipInfo.label}
            </Badge>
            {profile.is_card_holder && (
              <Crown className="h-4 w-4 text-yellow-500" />
            )}
          </div>
        </div>
        <div className="flex-shrink-0 flex items-center gap-2">
          {/* Sync Status Indicator */}
          <div className="flex items-center gap-1">
            {!syncStatus.isOnline ? (
              <WifiOff className="h-3 w-3 text-red-400" />
            ) : (
              <Wifi className="h-3 w-3 text-green-400" />
            )}
            <div className={`w-2 h-2 rounded-full ${
              syncStatus.status === 'idle' ? 'bg-green-500' :
              syncStatus.status === 'syncing' ? 'bg-yellow-500 animate-pulse' :
              'bg-red-500'
            }`}></div>
          </div>
          
          {/* Refresh Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={syncStatus.status === 'syncing'}
            className="h-8 w-8 p-0 hover:bg-white/10"
            title="Refresh profile data"
          >
            <RefreshCw className={`h-3 w-3 text-gray-400 ${syncStatus.status === 'syncing' ? 'animate-spin' : ''}`} />
          </Button>
          
          {/* Edit Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditModalOpen(true)}
            className="border-white/20 text-white hover:bg-white/10"
          >
            <Edit className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Profile Completeness Indicator */}
      {!profile.is_profile_complete && (
        <div className="mt-3 pt-3 border-t border-white/10">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-400">Profile completion</span>
            <span className="text-xs text-yellow-400">Incomplete</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-1.5 mt-1">
            <div
              className="bg-yellow-400 h-1.5 rounded-full transition-all duration-300"
              style={{
                width: `${getProfileCompletionPercentage(profile)}%`
              }}
            />
          </div>
        </div>
      )}

      {/* Wallet Address Display */}
      {profile.xrp_wallet_address && (
        <div className="mt-3 pt-3 border-t border-white/10">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Wallet className="h-4 w-4 text-green-400" />
              <span className="text-xs text-gray-400">XRP Wallet Address</span>
            </div>
            <button
              onClick={() => profile.xrp_wallet_address && handleCopyAddress(profile.xrp_wallet_address)}
              className="p-1 hover:bg-white/10 rounded transition-colors"
            >
              {copied ? (
                <CheckCircle className="h-3 w-3 text-green-400" />
              ) : (
                <Copy className="h-3 w-3 text-gray-400 hover:text-white" />
              )}
            </button>
          </div>
          <div className="bg-white/5 rounded-lg p-2 border border-white/10">
            <p className="text-xs font-mono text-green-400 break-all">
              {profile.xrp_wallet_address}
            </p>
          </div>
        </div>
      )}

      {/* Sync Status Footer */}
      <div className="mt-3 pt-3 border-t border-white/10">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              syncStatus.status === 'idle' ? 'bg-green-500' :
              syncStatus.status === 'syncing' ? 'bg-yellow-500 animate-pulse' :
              'bg-red-500'
            }`}></div>
            <span className="text-gray-400">
              {syncStatus.status === 'idle' ? 'Synced' :
               syncStatus.status === 'syncing' ? 'Syncing...' :
               'Sync Error'}
            </span>
          </div>
          {syncStatus.lastSync && (
            <span className="text-gray-500">
              Last sync: {new Date(syncStatus.lastSync).toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      {/* Profile Edit Modal */}
      <ProfileEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={() => {
          // Refresh profile data after save
          handleRefresh()
          console.log('Profile updated successfully - refreshing real-time data')
        }}
      />
    </AnimatedCard>
  )
}

/**
 * Calculate profile completion percentage
 */
function getProfileCompletionPercentage(profile: any): number {
  const fields = [
    profile.first_name,
    profile.last_name,
    profile.phone,
    profile.user_email
  ]
  
  const completedFields = fields.filter(field => field && field.trim()).length
  return Math.round((completedFields / fields.length) * 100)
}
