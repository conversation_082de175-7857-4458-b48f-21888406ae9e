"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, Building2, Calendar, ArrowRight, RefreshCw, Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useDashboardData } from '@/components/containers/dashboard-data-container';
import Link from 'next/link';

interface QRInteractionsDisplayProps {
  showTitle?: boolean;
  className?: string;
}

export function QRInteractionsDisplay({ 
  showTitle = true, 
  className = "" 
}: QRInteractionsDisplayProps) {
  const { recentInteractions, interactionsLoading, error, fetchInteractions } = useDashboardData();
  
  // Fetch interactions when component mounts
  React.useEffect(() => {
    fetchInteractions();
  }, []);

  if (interactionsLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2 text-[#3A56FF]" />
          <p className="text-gray-600 text-sm">Loading interactions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-red-600 mb-4">Failed to load interactions</p>
            <Button onClick={fetchInteractions} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!recentInteractions || recentInteractions.length === 0) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Recent Connections
            </CardTitle>
          </CardHeader>
        )}
        <CardContent>
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-2">No connections yet</p>
            <p className="text-gray-400 text-sm mb-4">
              Start scanning QR codes to see your connections here
            </p>
            <Link href="/scan">
              <Button className="bg-[#3A56FF] hover:bg-[#3A56FF]/90">
                Start Scanning
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Recent Connections
            </div>
            <Badge variant="secondary">{recentInteractions.length}</Badge>
          </CardTitle>
        </CardHeader>
      )}
      <CardContent>
        <div className="space-y-4">
          {recentInteractions.map((interaction) => (
            <div
              key={interaction.id}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center space-x-4">
                {/* Icon based on interaction type */}
                <div className="flex-shrink-0">
                  {interaction.interaction_type === 'business_scan' ? (
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Building2 className="h-5 w-5 text-blue-600" />
                    </div>
                  ) : (
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <Users className="h-5 w-5 text-green-600" />
                    </div>
                  )}
                </div>

                {/* Interaction details */}
                <div className="flex-1 min-w-0">
                  {interaction.interaction_type === 'business_scan' && interaction.business ? (
                    <div>
                      <p className="font-medium text-gray-900 truncate">
                        {interaction.business.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {interaction.business.category} • Business scan
                      </p>
                    </div>
                  ) : interaction.interaction_type === 'user_scan' && interaction.scanned_profile ? (
                    <div>
                      <p className="font-medium text-gray-900 truncate">
                        {interaction.scanned_profile.first_name} {interaction.scanned_profile.last_name}
                      </p>
                      <p className="text-sm text-gray-500">
                        User connection
                        {interaction.scanned_profile.is_card_holder && (
                          <Badge variant="secondary" className="ml-2">VIP</Badge>
                        )}
                      </p>
                    </div>
                  ) : (
                    <div>
                      <p className="font-medium text-gray-900">Unknown connection</p>
                      <p className="text-sm text-gray-500">
                        {interaction.interaction_type.replace('_', ' ')}
                      </p>
                    </div>
                  )}
                </div>

                {/* Timestamp */}
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="h-4 w-4 mr-1" />
                  {formatDistanceToNow(new Date(interaction.created_at), { addSuffix: true })}
                </div>
              </div>

              {/* Action arrow */}
              <ArrowRight className="h-4 w-4 text-gray-400" />
            </div>
          ))}
        </div>

        {/* View all link */}
        <div className="mt-6 text-center">
          <Link href="/qr-connections">
            <Button variant="outline" className="w-full">
              View All Connections
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
