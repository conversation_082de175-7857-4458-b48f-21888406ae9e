"use client";

import { useState } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserQRCode } from '@/components/user-qr-code';
import { DashboardQRScanner } from '@/components/dashboard-qr-scanner';
import { QRCodePreloader } from '@/components/qr-code-preloader';
import { QrCode, Camera, Users, Gift } from 'lucide-react';

interface QRCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRedeemClick?: () => void;
}

export function QRCodeModal({ isOpen, onClose, onRedeemClick }: QRCodeModalProps) {
  const [activeTab, setActiveTab] = useState('your-qr');

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Preload QR codes when modal opens */}
        {isOpen && <QRCodePreloader />}
        
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            QR Code Center
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="your-qr" className="flex items-center gap-2">
              <QrCode className="h-4 w-4" />
              Your QR Code
            </TabsTrigger>
            <TabsTrigger value="scanner" className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              Scan QR Codes
            </TabsTrigger>
          </TabsList>

          <TabsContent value="your-qr" className="space-y-4">
            <div className="text-center space-y-4">
              <h3 className="text-lg font-semibold">Your Personal QR Code</h3>
              <p className="text-gray-600 text-sm">
                Share this QR code with businesses to earn FUSE rewards
              </p>
              
              <div className="flex justify-center">
                <UserQRCode size={200} showActions={true} />
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-900">How it works:</span>
                </div>
                <ul className="text-sm text-blue-800 space-y-1 text-left">
                  <li>• Show this QR code to participating businesses</li>
                  <li>• Earn FUSE tokens for each scan</li>
                  <li>• Build connections with local businesses</li>
                  <li>• Track your rewards in the dashboard</li>
                </ul>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="scanner" className="space-y-4">
            <div className="text-center space-y-4">
              <h3 className="text-lg font-semibold">Scan QR Codes</h3>
              <p className="text-gray-600 text-sm">
                Scan business QR codes to earn FUSE rewards and connect with other users
              </p>

              <DashboardQRScanner
                onScanSuccess={(result) => {
                  console.log('Scan result:', result);
                  // Optionally close modal on successful scan
                  if (result.success) {
                    setTimeout(() => onClose(), 2000);
                  }
                }}
              />

              <div className="flex gap-2">
                <Button
                  onClick={onRedeemClick}
                  className="bg-green-600 hover:bg-green-700 text-white flex-1"
                >
                  <Gift className="h-4 w-4 mr-2" />
                  Redeem Code
                </Button>
              </div>

              <div className="bg-green-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Camera className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-900">Scanning Tips:</span>
                </div>
                <ul className="text-sm text-green-800 space-y-1 text-left">
                  <li>• Hold your device steady</li>
                  <li>• Ensure good lighting</li>
                  <li>• Position QR code within the frame</li>
                  <li>• Allow camera permissions when prompted</li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
