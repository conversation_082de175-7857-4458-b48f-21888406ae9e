"use client";

import React from "react";
import { useAuth } from "@/contexts/auth-context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, CreditCard, Calendar, Building2, Mail, Phone } from "lucide-react";

interface ProfileSummaryProps {
  showBusinessInfo?: boolean;
  compact?: boolean;
}

export function ProfileSummary({ showBusinessInfo = false, compact = false }: ProfileSummaryProps) {
  const { user, profile, isBusinessOwner, isLoading } = useAuth();

  if (isLoading) {
    return (
      <Card className="bg-[#1A1A1A] border-white/10">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-700 rounded w-1/2"></div>
            <div className="h-4 bg-gray-700 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!user || !profile) {
    return (
      <Card className="bg-[#1A1A1A] border-white/10">
        <CardContent className="p-6">
          <p className="text-white/60">Profile information not available</p>
        </CardContent>
      </Card>
    );
  }

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleDateString();
  };

  const getCardTierColor = (tier: string | null | undefined) => {
    if (!tier) return "bg-gray-500/20 text-gray-400";
    
    const tierLower = tier.toLowerCase();
    switch (tierLower) {
      case 'monthly': return "bg-blue-500/20 text-blue-400";
      case 'premium': return "bg-purple-500/20 text-purple-400";
      case 'gold': return "bg-yellow-500/20 text-yellow-400";
      case 'platinum': return "bg-gray-300/20 text-gray-300";
      case 'diamond': return "bg-cyan-500/20 text-cyan-400";
      case 'obsidian': return "bg-black/40 text-white border border-white/20";
      default: return "bg-gray-500/20 text-gray-400";
    }
  };

  if (compact) {
    return (
      <div className="bg-[#1A1A1A]/80 backdrop-blur-sm border border-white/10 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-[#3A56FF]/20 rounded-full flex items-center justify-center">
            <User className="h-5 w-5 text-[#3A56FF]" />
          </div>
          <div className="flex-1">
            <h3 className="text-white font-semibold">
              {profile.first_name} {profile.last_name}
            </h3>
            <div className="flex items-center gap-2">
              {profile.is_card_holder ? (
                <Badge className={`text-xs ${getCardTierColor(profile.card_tier)}`}>
                  {profile.card_tier || 'VIP'} Card
                </Badge>
              ) : (
                <Badge className="bg-yellow-500/20 text-yellow-400 text-xs">
                  No VIP Card
                </Badge>
              )}
              {isBusinessOwner && (
                <Badge className="bg-green-500/20 text-green-400 text-xs">
                  Business Owner
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card className="bg-[#1A1A1A] border-white/10 text-white">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Profile Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-400">Full Name</p>
            <p className="font-medium">
              {profile.first_name} {profile.last_name}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-400">Email</p>
            <p className="font-medium flex items-center gap-2">
              <Mail className="h-4 w-4" />
              {profile.user_email || user.email}
            </p>
          </div>
          {profile.phone && (
            <div>
              <p className="text-sm text-gray-400">Phone</p>
              <p className="font-medium flex items-center gap-2">
                <Phone className="h-4 w-4" />
                {profile.phone}
              </p>
            </div>
          )}
          <div>
            <p className="text-sm text-gray-400">Member Since</p>
            <p className="font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              {formatDate(profile.created_at)}
            </p>
          </div>
        </div>

        {/* VIP Card Status */}
        <div className="border-t border-white/10 pt-4">
          <p className="text-sm text-gray-400 mb-2">VIP Card Status</p>
          <div className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            {profile.is_card_holder ? (
              <div className="flex items-center gap-2">
                <Badge className={getCardTierColor(profile.card_tier)}>
                  {profile.card_tier || 'VIP'} Card Holder
                </Badge>
                {profile.membership_end_date && (
                  <span className="text-xs text-gray-400">
                    Expires: {formatDate(profile.membership_end_date)}
                  </span>
                )}
              </div>
            ) : (
              <Badge className="bg-yellow-500/20 text-yellow-400">
                No VIP Card
              </Badge>
            )}
          </div>
        </div>

        {/* Business Status */}
        {showBusinessInfo && (
          <div className="border-t border-white/10 pt-4">
            <p className="text-sm text-gray-400 mb-2">Business Status</p>
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              {isBusinessOwner ? (
                <Badge className="bg-green-500/20 text-green-400">
                  Business Owner
                </Badge>
              ) : (
                <Badge className="bg-gray-500/20 text-gray-400">
                  Not a Business Owner
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* XRP Wallet */}
        {profile.xrp_wallet_address && (
          <div className="border-t border-white/10 pt-4">
            <p className="text-sm text-gray-400">XRP Wallet</p>
            <p className="font-mono text-xs text-white/80 break-all">
              {profile.xrp_wallet_address}
            </p>
          </div>
        )}

        {/* Debug Info (only in development) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="border-t border-white/10 pt-4">
            <p className="text-sm text-gray-400">Debug Info</p>
            <div className="text-xs text-gray-500 space-y-1">
              <p>User ID: {user.id}</p>
              <p>Profile ID: {profile.id}</p>
              <p>Is Card Holder: {profile.is_card_holder ? 'Yes' : 'No'}</p>
              <p>Card Tier: {profile.card_tier || 'None'}</p>
              <p>Business Applicant: {profile.is_business_applicant ? 'Yes' : 'No'}</p>
              <p>Referring Business ID: {profile.referring_business_id || 'None'}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
