"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Loader2, Save, X, User, Phone, Mail, Wallet } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useProfileRealtime } from "@/hooks/use-profile-realtime"

interface ProfileEditModalProps {
  isOpen: boolean
  onClose: () => void
  onSave?: () => void
}

interface ProfileFormData {
  first_name: string
  last_name: string
  phone: string
  user_email: string
  xrp_wallet_address: string
}

export function ProfileEditModal({ isOpen, onClose, onSave }: ProfileEditModalProps) {
  const { user } = useAuth()
  const { profile: realtimeProfile, updateProfile, syncStatus } = usePro<PERSON>leRealtime()
  
  // Fallback to auth context profile if real-time not available
  const { profile: authProfile } = useAuth()
  const profile = realtimeProfile || authProfile
  
  const [formData, setFormData] = useState<ProfileFormData>({
    first_name: '',
    last_name: '',
    phone: '',
    user_email: '',
    xrp_wallet_address: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load current profile data when modal opens
  useEffect(() => {
    if (isOpen && profile) {
      setFormData({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        phone: profile.phone || '',
        user_email: profile.user_email || user?.email || '',
        xrp_wallet_address: profile.xrp_wallet_address || ''
      })
    }
  }, [isOpen, profile, user])

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    if (!user?.id) {
      setError('User not authenticated')
      return
    }

    setIsSaving(true)
    setError(null)

    try {
      // Prepare clean update data
      const updates = {
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
        phone: formData.phone.trim(),
        user_email: formData.user_email.trim(),
        xrp_wallet_address: formData.xrp_wallet_address.trim()
      }

      // Remove empty values and convert to null
      Object.keys(updates).forEach(key => {
        if (updates[key] === '') {
          updates[key] = null
        }
      })

      console.log('Updating profile with real-time optimistic updates:', Object.keys(updates))

      // Use real-time update with optimistic updates
      const success = await updateProfile(updates)

      if (success) {
        console.log('Profile updated successfully with real-time sync')
        
        // Call the onSave callback if provided
        if (onSave) {
          onSave()
        }

        onClose()
      } else {
        throw new Error('Failed to update profile')
      }

    } catch (error) {
      console.error('Error updating profile:', error)
      setError(error instanceof Error ? error.message : 'Failed to update profile')
    } finally {
      setIsSaving(false)
    }
  }

  const isFormValid = () => {
    return formData.first_name.trim() && 
           formData.last_name.trim() && 
           formData.user_email.trim()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] bg-[#1A1A1A] border-white/10">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-white">
            <User className="h-5 w-5" />
            Edit Profile
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-3">
              <User className="h-4 w-4 text-blue-400" />
              <span className="text-sm font-medium text-white">Basic Information</span>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="first_name" className="text-sm text-gray-400">
                  First Name *
                </Label>
                <Input
                  id="first_name"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  placeholder="Enter first name"
                  className="bg-white/5 border-white/10 text-white"
                />
              </div>
              
              <div>
                <Label htmlFor="last_name" className="text-sm text-gray-400">
                  Last Name *
                </Label>
                <Input
                  id="last_name"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  placeholder="Enter last name"
                  className="bg-white/5 border-white/10 text-white"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="user_email" className="text-sm text-gray-400">
                Email Address *
              </Label>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-gray-400" />
                <Input
                  id="user_email"
                  value={formData.user_email}
                  onChange={(e) => handleInputChange('user_email', e.target.value)}
                  placeholder="Enter email address"
                  className="bg-white/5 border-white/10 text-white"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="phone" className="text-sm text-gray-400">
                Phone Number
              </Label>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-400" />
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="Enter phone number"
                  className="bg-white/5 border-white/10 text-white"
                />
              </div>
            </div>
          </div>

          {/* Wallet Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-3">
              <Wallet className="h-4 w-4 text-green-400" />
              <span className="text-sm font-medium text-white">Wallet Information</span>
            </div>
            
            <div>
              <Label htmlFor="xrp_wallet_address" className="text-sm text-gray-400">
                XRP Wallet Address
              </Label>
              <Input
                id="xrp_wallet_address"
                value={formData.xrp_wallet_address}
                onChange={(e) => handleInputChange('xrp_wallet_address', e.target.value)}
                placeholder="Enter XRP wallet address"
                className="bg-white/5 border-white/10 text-white font-mono text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                Connect your XRP wallet to enable crypto payments and FUSE token management
              </p>
            </div>
          </div>

          {/* Current Status and Membership Info */}
          {profile && (
            <div className="space-y-4">
              <div className="space-y-2">
                <span className="text-sm font-medium text-white">Current Status</span>
                <div className="flex flex-wrap gap-2">
                  {profile.is_card_holder && (
                    <Badge className="bg-green-500/20 text-green-400">
                      VIP Card Holder
                    </Badge>
                  )}
                  {profile.card_tier && (
                    <Badge className="bg-blue-500/20 text-blue-400">
                      {profile.card_tier} Member
                    </Badge>
                  )}
                  {profile.is_business_applicant && (
                    <Badge className="bg-purple-500/20 text-purple-400">
                      Business Applicant
                    </Badge>
                  )}
                  {profile.xrp_wallet_address && (
                    <Badge className="bg-yellow-500/20 text-yellow-400">
                      Wallet Connected
                    </Badge>
                  )}
                  {profile.referring_business_id && (
                    <Badge className="bg-indigo-500/20 text-indigo-400">
                      Business Referred
                    </Badge>
                  )}
                </div>
              </div>

              {/* Membership Details */}
              {profile.is_card_holder && (profile.membership_start_date || profile.membership_end_date) && (
                <div className="bg-white/5 border border-white/10 rounded-lg p-3">
                  <div className="text-sm font-medium text-white mb-2">Membership Details</div>
                  <div className="space-y-1 text-xs text-gray-400">
                    {profile.membership_start_date && (
                      <div>Started: {new Date(profile.membership_start_date).toLocaleDateString()}</div>
                    )}
                    {profile.membership_end_date && (
                      <div>Expires: {new Date(profile.membership_end_date).toLocaleDateString()}</div>
                    )}
                    {profile.membership_end_date && (
                      <div className="text-green-400">
                        Days remaining: {Math.max(0, Math.ceil((new Date(profile.membership_end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t border-white/10">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSaving}
            className="border-white/20 text-white hover:bg-white/10"
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !isFormValid()}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}