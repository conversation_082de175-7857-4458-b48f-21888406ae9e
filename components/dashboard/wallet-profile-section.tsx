"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Wallet, ExternalLink, <PERSON>py, CheckCircle, AlertCircle, ChevronDown, ChevronUp, Unlink, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AnimatedCard } from "@/components/animated-card"
import { WalletConnectButton } from "@/components/wallet/wallet-connect-button"
import { useSimpleWallet } from "@/hooks/use-simple-wallet"
import { useDashboardProfile } from "@/hooks/use-dashboard-profile"
import { walletService } from "@/lib/wallet-service"
import { useAuth } from "@/contexts/auth-context"

interface WalletProfileSectionProps {
  className?: string
}

export function WalletProfileSection({ className = "" }: WalletProfileSectionProps) {
  const { user, profile } = useAuth()
  const { isConnected, walletAddress, isLoading, disconnect } = useSimpleWallet()
  const [isExpanded, setIsExpanded] = useState(false)
  const [copied, setCopied] = useState(false)
  const [isDisconnecting, setIsDisconnecting] = useState(false)
  const [disconnectError, setDisconnectError] = useState<string>('')

  const handleCopyAddress = async (address: string) => {
    try {
      await navigator.clipboard.writeText(address)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy address:', error)
    }
  }

  const handleDisconnectWallet = async () => {
    if (!user?.id) {
      setDisconnectError('User not authenticated')
      return
    }

    setIsDisconnecting(true)
    setDisconnectError('')

    try {
      // Use the centralized wallet service
      const result = await walletService.disconnectWallet(user.id, 'xrp')
      
      if (result.success) {
        // Also disconnect from the wallet hook
        await disconnect()
        console.log('✅ Wallet disconnected successfully')
      } else {
        setDisconnectError(result.error || 'Failed to disconnect wallet')
        console.error('❌ Failed to disconnect wallet:', result.error)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setDisconnectError(errorMessage)
      console.error('❌ Exception disconnecting wallet:', error)
    } finally {
      setIsDisconnecting(false)
    }
  }

  const currentWalletAddress = walletAddress || profile?.xrp_wallet_address
  const hasWallet = Boolean(currentWalletAddress)

  return (
    <AnimatedCard className={`bg-[#1A1A1A] border border-white/10 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div 
        className="p-4 cursor-pointer select-none"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              hasWallet ? 'bg-green-500/20' : 'bg-gray-500/20'
            }`}>
              <Wallet className={`h-5 w-5 ${hasWallet ? 'text-green-400' : 'text-gray-400'}`} />
            </div>
            <div>
              <h3 className="font-semibold text-white">Xaman Wallet</h3>
              <div className="flex items-center gap-2">
                <Badge className={`text-xs ${
                  hasWallet ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                }`}>
                  {hasWallet ? 'Connected' : 'Not Connected'}
                </Badge>
                {isLoading && (
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                )}
              </div>
            </div>
          </div>
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="h-5 w-5 text-gray-400" />
          </motion.div>
        </div>
      </div>

      {/* Expandable Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="px-4 pb-4 border-t border-white/10">
              {hasWallet ? (
                <div className="space-y-4 mt-4">
                  {/* Connected Wallet Info */}
                  <div className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-white">Wallet Address</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopyAddress(currentWalletAddress!)}
                        className="h-6 px-2 text-xs"
                      >
                        {copied ? (
                          <CheckCircle className="h-3 w-3 text-green-400" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs font-mono text-green-400 break-all">
                      {currentWalletAddress}
                    </p>
                  </div>

                  {/* Wallet Actions */}
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <WalletConnectButton className="flex-1 text-sm" />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open('https://xumm.app', '_blank')}
                        className="px-3"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleDisconnectWallet}
                      disabled={isDisconnecting}
                      className="w-full text-sm"
                    >
                      {isDisconnecting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Disconnecting...
                        </>
                      ) : (
                        <>
                          <Unlink className="h-4 w-4 mr-2" />
                          Disconnect Wallet
                        </>
                      )}
                    </Button>
                    {disconnectError && (
                      <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-2">
                        <p className="text-red-400 text-xs">{disconnectError}</p>
                      </div>
                    )}
                  </div>

                  {/* Wallet Features */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-white">Available Features</h4>
                    <div className="grid grid-cols-1 gap-2 text-xs">
                      <div className="flex items-center gap-2 text-gray-300">
                        <CheckCircle className="h-3 w-3 text-green-400" />
                        <span>VIP Card Payments</span>
                      </div>
                      <div className="flex items-center gap-2 text-gray-300">
                        <CheckCircle className="h-3 w-3 text-green-400" />
                        <span>FUSE Token Management</span>
                      </div>
                      <div className="flex items-center gap-2 text-gray-300">
                        <CheckCircle className="h-3 w-3 text-green-400" />
                        <span>XRP Transactions</span>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4 mt-4">
                  {/* Not Connected State */}
                  <div className="text-center py-4">
                    <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-400 mb-4">
                      Connect your Xaman wallet to access payment features and manage FUSE tokens
                    </p>
                    <WalletConnectButton className="w-full" />
                  </div>

                  {/* Benefits */}
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-white">Why Connect?</h4>
                    <div className="grid grid-cols-1 gap-2 text-xs">
                      <div className="flex items-center gap-2 text-gray-300">
                        <div className="w-2 h-2 bg-blue-400 rounded-full" />
                        <span>Purchase VIP cards with crypto</span>
                      </div>
                      <div className="flex items-center gap-2 text-gray-300">
                        <div className="w-2 h-2 bg-blue-400 rounded-full" />
                        <span>Manage FUSE token rewards</span>
                      </div>
                      <div className="flex items-center gap-2 text-gray-300">
                        <div className="w-2 h-2 bg-blue-400 rounded-full" />
                        <span>Access exclusive crypto features</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </AnimatedCard>
  )
}
