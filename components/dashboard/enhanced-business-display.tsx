"use client"

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Building2, Bar<PERSON>hart3, Users, Plus, ExternalLink, Clock, CheckCircle, Lock } from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import Link from 'next/link'
import { AnimatedSection } from '@/components/animated-section'
import { AddMoreBusinessModal } from '@/components/business/add-more-business-modal'
import { getCategoryInfo } from '@/lib/business-categories'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

interface Business {
  id: string
  name: string
  category: string
  premium_discount: string
  logo_url?: string
  website?: string
  is_active: boolean
  business_spotlight: boolean
  interaction_count: number
  referral_count: number
  created_at: string
}

interface AccessRequest {
  id: string
  business_id: string
  business_name: string
  status: string
  requested_at: string
}

interface BusinessOwnershipData {
  businesses: Business[]
  hasBusinesses: boolean
  isBusinessOwner: boolean
  accessRequests: AccessRequest[]
  hasPendingRequests: boolean
  stats: {
    totalBusinesses: number
    totalInteractions: number
    totalReferrals: number
  }
}

interface EnhancedBusinessDisplayProps {
  variant?: 'full' | 'compact'
  className?: string
}

const getBusinessCategoryInfo = (category: string) => {
  // Use standardized categories from lib
  const categoryInfo = getCategoryInfo(category)
  return categoryInfo ? { icon: categoryInfo.icon, color: categoryInfo.color } : { icon: '🏢', color: 'bg-gray-500' }
}

export function EnhancedBusinessDisplay({ variant = 'full', className = '' }: EnhancedBusinessDisplayProps) {
  const { user } = useAuth()
  const [businessData, setBusinessData] = useState<BusinessOwnershipData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showAddMoreModal, setShowAddMoreModal] = useState(false)
  const [showAnalyticsComingSoon, setShowAnalyticsComingSoon] = useState(false)

  useEffect(() => {
    const fetchBusinessData = async () => {
      if (!user?.id) {
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/dashboard/business-ownership-simple?userId=${user.id}`, {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache',
          },
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()

        if (result.success) {
          setBusinessData(result.data)
          console.log('✅ Business data loaded:', result.data)
        } else {
          throw new Error(result.error || 'Failed to load business data')
        }

      } catch (err: any) {
        console.error('❌ Error loading business data:', err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchBusinessData()
  }, [user?.id])

  if (!user) {
    return null
  }

  if (isLoading) {
    return (
      <div className={`${className}`}>
        <div className="bg-black/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
          <div className="animate-pulse">
            <div className="h-4 bg-white/20 rounded w-1/3 mb-4"></div>
            <div className="h-8 bg-white/20 rounded w-2/3 mb-2"></div>
            <div className="h-4 bg-white/20 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <div className="bg-red-500/10 backdrop-blur-sm rounded-2xl p-6 border border-red-500/20">
          <div className="text-center text-red-400">
            <p className="font-semibold">Error loading business data</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  if (!businessData) {
    return null
  }

  // Show business owner interface if user has businesses
  if (businessData.hasBusinesses && businessData.businesses.length > 0) {
    const primaryBusiness = businessData.businesses[0]
    const categoryInfo = getBusinessCategoryInfo(primaryBusiness.category)

    return (
      <AnimatedSection className={className}>
        <div className="relative bg-black/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
          <div className="text-center text-white space-y-4">
            <div className="inline-flex items-center px-3 py-1 bg-green-500 text-white rounded-full text-xs font-semibold">
              ✅ Business Owner
            </div>

            <h3 className="text-2xl font-bold text-yellow-400">
              {primaryBusiness.name}
            </h3>

            <div className="flex justify-center">
              <div className={`w-20 h-20 ${categoryInfo.color} rounded-lg flex items-center justify-center`}>
                <span className="text-3xl">{categoryInfo.icon}</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm text-white/80">
                <span className="font-semibold">{primaryBusiness.category}</span>
                {primaryBusiness.business_spotlight && (
                  <span className="ml-2 px-2 py-1 bg-yellow-500 text-black rounded-full text-xs font-bold">
                    ⭐ SPOTLIGHT
                  </span>
                )}
              </div>
              
              <div className="text-sm text-green-400 font-semibold">
                {primaryBusiness.premium_discount}
              </div>
              
              {/* Business stats */}
              <div className="grid grid-cols-2 gap-3 text-center text-sm mt-3 pt-3 border-t border-white/20">
                <div>
                  <div className="font-bold text-blue-400">{primaryBusiness.interaction_count || 0}</div>
                  <div className="text-white/70 text-xs">QR Scans</div>
                </div>
                <div>
                  <div className="font-bold text-purple-400">{primaryBusiness.referral_count || 0}</div>
                  <div className="text-white/70 text-xs">Referrals</div>
                </div>
              </div>

              {/* Multiple businesses indicator */}
              {businessData.businesses.length > 1 && (
                <div className="text-xs text-white/60 pt-2 border-t border-white/10">
                  +{businessData.businesses.length - 1} more business{businessData.businesses.length > 2 ? 'es' : ''}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Link href="/dashboard/business">
                <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-6 py-2 rounded-lg w-full border text-sm">
                  <Building2 className="w-4 h-4 mr-2" />
                  Manage Business
                </Button>
              </Link>
              <div className="grid grid-cols-3 gap-2">
                <div className="relative">
                  <Button 
                    onClick={() => setShowAnalyticsComingSoon(true)}
                    className="bg-yellow-500/50 text-black/50 font-bold px-2 py-2 rounded-lg w-full text-xs cursor-not-allowed relative"
                    disabled
                  >
                    <div className="flex items-center justify-center">
                      <Lock className="w-3 h-3 mr-1" />
                      Analytics
                    </div>
                    <div className="absolute inset-0 bg-gray-900/30 rounded-lg flex items-center justify-center">
                      <span className="text-[10px] text-white font-bold">SOON</span>
                    </div>
                  </Button>
                </div>
                <Link href="/industry">
                  <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 px-2 py-2 rounded-lg border text-xs">
                    <ExternalLink className="w-3 h-3 mr-1" />
                    Directory
                  </Button>
                </Link>
                <Button 
                  onClick={() => {
                    console.log('🔄 Add More button clicked - opening modal')
                    setShowAddMoreModal(true)
                  }}
                  className="bg-green-500 hover:bg-green-400 text-white px-2 py-2 rounded-lg w-full text-xs"
                >
                  <Plus className="w-3 h-3 mr-1" />
                  Add More
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Pending access requests */}
        {businessData.hasPendingRequests && (
          <div className="mt-4 bg-yellow-500/10 backdrop-blur-sm rounded-xl p-4 border border-yellow-500/20">
            <div className="text-center text-yellow-400">
              <Clock className="w-5 h-5 mx-auto mb-2" />
              <p className="font-semibold text-sm">Pending Business Access</p>
              <p className="text-xs text-yellow-300 mt-1">
                {businessData.accessRequests.length} request{businessData.accessRequests.length > 1 ? 's' : ''} under review
              </p>
              
              {/* Add More button for business applicants */}
              <div className="mt-3">
                <Button 
                  onClick={() => {
                    console.log('🔄 Add More button clicked from pending section - opening modal')
                    setShowAddMoreModal(true)
                  }}
                  className="bg-blue-500 hover:bg-blue-400 text-white px-4 py-2 rounded-lg text-xs"
                >
                  <Plus className="w-3 h-3 mr-1" />
                  Add Another Business
                </Button>
              </div>
            </div>
          </div>
        )}
      </AnimatedSection>
    )
  }

  // Show business application interface for non-business owners
  return (
    <AnimatedSection className={className}>
      <div className="relative bg-black/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
        <div className="text-center text-white space-y-4">
          <div className="inline-flex items-center px-3 py-1 bg-blue-500 text-white rounded-full text-xs font-semibold">
            🏢 For Business Owners
          </div>

          <h3 className="text-xl font-bold text-white">
            Grow Your Business
          </h3>

          <p className="text-white/80 text-sm">
            Join our network and attract more customers with loyalty rewards.
          </p>

          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="flex items-center text-white/70">
              <CheckCircle className="w-3 h-3 mr-1 text-green-400" />
              40% retention boost
            </div>
            <div className="flex items-center text-white/70">
              <CheckCircle className="w-3 h-3 mr-1 text-green-400" />
              Directory listing
            </div>
            <div className="flex items-center text-white/70">
              <CheckCircle className="w-3 h-3 mr-1 text-green-400" />
              Analytics access
            </div>
            <div className="flex items-center text-white/70">
              <CheckCircle className="w-3 h-3 mr-1 text-green-400" />
              Free setup
            </div>
          </div>

          <div className="space-y-2">
            <Link href="/register-business">
              <Button className="bg-green-500 hover:bg-green-400 text-white font-semibold px-6 py-2 rounded-lg w-full text-sm">
                <Plus className="w-4 h-4 mr-2" />
                Register Business
              </Button>
            </Link>
          </div>

          {/* Show pending requests if any */}
          {businessData.hasPendingRequests && (
            <div className="mt-4 pt-4 border-t border-white/20">
              <div className="text-yellow-400 text-sm">
                <Clock className="w-4 h-4 mx-auto mb-1" />
                <p className="font-semibold">Application Under Review</p>
                <p className="text-xs text-yellow-300 mt-1">
                  We'll notify you once approved
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add More Business Modal */}
      <AddMoreBusinessModal
        isOpen={showAddMoreModal}
        onClose={() => {
          console.log('🔄 Closing Add More modal')
          setShowAddMoreModal(false)
        }}
        onSuccess={() => {
          console.log('✅ Add More modal success - refreshing')
          // Refresh business data
          window.location.reload()
        }}
      />

      {/* Analytics Coming Soon Modal */}
      <Dialog open={showAnalyticsComingSoon} onOpenChange={setShowAnalyticsComingSoon}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5 text-yellow-500" />
              Analytics Coming Soon
            </DialogTitle>
          </DialogHeader>
          <div className="text-center py-6">
            <div className="mb-4">
              <BarChart3 className="h-16 w-16 text-yellow-500 mx-auto mb-4 opacity-50" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Advanced Business Analytics
            </h3>
            <p className="text-gray-600 mb-4">
              We're working on powerful analytics features including customer insights, 
              performance metrics, and growth tracking tools.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
              <p className="text-yellow-800 text-sm font-medium">Coming Very Soon!</p>
              <p className="text-yellow-700 text-sm">Expected release: Q2 2025</p>
            </div>
            <Button 
              onClick={() => setShowAnalyticsComingSoon(false)}
              className="bg-yellow-500 hover:bg-yellow-600 text-white"
            >
              Got it, thanks!
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </AnimatedSection>
  )
}
