"use client"

import { motion } from "framer-motion"
import { buttonHoverVariants } from "@/lib/utils"
import type React from "react"

interface AnimatedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline"
  children: React.ReactNode
  icon?: React.ReactNode
  className?: string
}

export function AnimatedButton({ variant = "primary", children, icon, className = "", ...props }: AnimatedButtonProps) {
  const baseClasses = "px-4 py-2 rounded-md font-medium flex items-center justify-center"

  const variantClasses = {
    primary: "bg-[#3A56FF] text-white",
    secondary: "bg-[#E6E9FF] text-[#3A56FF]",
    outline: "bg-transparent border border-[#3A56FF] text-[#3A56FF]",
  }

  const buttonClass = `${baseClasses} ${variantClasses[variant]} ${className}`

  return (
    <motion.button
      className={buttonClass}
      variants={buttonHoverVariants}
      initial="initial"
      whileHover="hover"
      whileTap="tap"
      {...props}
    >
      {children}
      {icon && <span className="ml-2">{icon}</span>}
    </motion.button>
  )
}
