"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zap } from "lucide-react"
import Image from "next/image"
import { useState } from "react"

interface BusinessLogoProps {
  src?: string | null
  alt?: string
  businessName?: string
  businessId?: string
  className?: string
  fallbackClassName?: string
  priority?: boolean
  size?: 'small' | 'medium' | 'large'
  futuristic?: boolean
  glowEffect?: boolean
  metadata?: {
    width?: number
    height?: number
    fileSize?: number
    mimeType?: string
  }
}

export function BusinessLogo({
  src,
  alt,
  businessName,
  businessId,
  className = "",
  fallbackClassName = "",
  priority = false,
  size = 'medium',
  futuristic = false,
  glowEffect = false,
}: BusinessLogoProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  // Size configurations for standardized containers
  const sizeConfig = {
    small: { container: 'w-12 h-12', icon: 'w-6 h-6' },
    medium: { container: 'w-16 h-16', icon: 'w-8 h-8' },
    large: { container: 'w-20 h-20', icon: 'w-10 h-10' }
  }

  const currentSize = sizeConfig[size]

  // Check if we have a valid logo URL
  const hasValidLogo = src &&
    src !== "/placeholder-logo.png" &&
    src !== "/placeholder.svg" &&
    src.trim() !== "" &&
    !imageError

  // Standardized fallback component
  const FallbackIcon = () => (
    <div className={`
      ${currentSize.container}
      flex items-center justify-center rounded-lg overflow-hidden
      ${futuristic
        ? "bg-gradient-to-br from-gray-900/80 to-black/80 border border-[#316bff]/30 backdrop-blur-sm"
        : "bg-gray-100 border border-gray-300"
      }
      ${fallbackClassName}
    `}>
      {futuristic ? (
        <div className="relative">
          <Zap className={`${currentSize.icon} text-[#316bff] animate-pulse`} />
          <Sparkles className="absolute -top-1 -right-1 w-3 h-3 text-[#00d4ff] animate-bounce" />
        </div>
      ) : (
        <Building2 className={`${currentSize.icon} text-gray-400`} />
      )}
      {businessName && (
        <span className="sr-only">{businessName} logo</span>
      )}
    </div>
  )

  // If no valid logo, show fallback
  if (!hasValidLogo) {
    return <FallbackIcon />
  }

  return (
    <div className={`
      ${currentSize.container}
      relative overflow-hidden rounded-lg
      ${futuristic
        ? "bg-gradient-to-br from-gray-900/30 to-black/30 border border-[#316bff]/30 backdrop-blur-sm"
        : "bg-white border border-gray-200"
      }
      ${glowEffect ? 'hover:shadow-lg hover:shadow-[#316bff]/20 transition-all duration-300' : ''}
      ${className}
    `}>
      {imageLoading && (
        <div className={`absolute inset-0 flex items-center justify-center ${futuristic ? 'bg-gray-900/50' : 'bg-gray-100'} animate-pulse rounded-lg`}>
          <div className="w-4 h-4 border-2 border-[#316bff] border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      <Image
        src={src}
        alt={alt || `${businessName} logo` || "Business logo"}
        fill
        className={`object-contain p-2 ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        priority={priority}
        onLoad={() => setImageLoading(false)}
        onError={() => {
          setImageError(true)
          setImageLoading(false)
        }}
        // Handle different image formats and potential CORS issues
        unoptimized={src?.includes('supabase') || src?.includes('blob.vercel-storage.com') || src?.startsWith('data:') || src?.endsWith('.svg')}
      />

      {/* Fallback if image fails to load */}
      {imageError && <FallbackIcon />}
    </div>
  )
}
