"use client"

import { useEffect, useRef } from "react"

interface GoogleCalendarButtonProps {
  className?: string
  label?: string
  color?: string
}

export function GoogleCalendarButton({
  className = "",
  label = "Book an appointment",
  color = "#039BE5",
}: GoogleCalendarButtonProps) {
  const buttonRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Load the CSS
    const link = document.createElement("link")
    link.href = "https://calendar.google.com/calendar/scheduling-button-script.css"
    link.rel = "stylesheet"
    document.head.appendChild(link)

    // Load the script
    const script = document.createElement("script")
    script.src = "https://calendar.google.com/calendar/scheduling-button-script.js"
    script.async = true
    document.body.appendChild(script)

    // Initialize the button when the script is loaded
    script.onload = () => {
      if (buttonRef.current && window.calendar && window.calendar.schedulingButton) {
        window.calendar.schedulingButton.load({
          url: "https://calendar.google.com/calendar/appointments/schedules/AcZssZ1GfYuxvhKTwKqSGxwGBL0MEwAwKVw7iojwl_6ORMfCUaChKpMxznAZ4wP7DSim39YsCuFy8c52?gv=true",
          color: color,
          label: label,
          target: buttonRef.current,
        })
      }
    }

    return () => {
      // Clean up
      document.head.removeChild(link)
      document.body.removeChild(script)
    }
  }, [color, label])

  return (
  <div className={`${className} flex flex-wrap justify-center gap-4`} ref={buttonRef}></div>
)
}
