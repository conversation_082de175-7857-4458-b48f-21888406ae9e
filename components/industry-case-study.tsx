import type React from "react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

interface IndustryCaseStudyProps {
  industry: string
}

const IndustryCaseStudy: React.FC<IndustryCaseStudyProps> = ({ industry }) => {
  return (
    <>
      {industry === "retail" && (
        <div className="bg-white/5 p-6 rounded-lg backdrop-blur-sm">
          <h3 className="text-xl font-semibold mb-3">Industry Insight</h3>
          <p className="mb-4">
            Crypto-based payments are projected to exceed $10 billion in annual volume by 2027, growing at a 17.2% CAGR.
            — Statista Crypto Payments Forecast, 2024
          </p>
          <Link href="/resources">
            <Button variant="outline" className="border-white/20 hover:bg-white/10">
              View Reference
            </Button>
          </Link>
        </div>
      )}
    </>
  )
}

export default IndustryCaseStudy
