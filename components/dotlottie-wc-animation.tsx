"use client"

import { useEffect, useRef } from "react"

interface DotLottieWCAnimationProps {
  src: string
  width?: string
  height?: string
  speed?: string
  autoplay?: boolean
  loop?: boolean
  className?: string
  style?: React.CSSProperties
}

export function DotLottieWCAnimation({
  src,
  width = "300px",
  height = "300px", 
  speed = "1",
  autoplay = true,
  loop = true,
  className = "",
  style = {}
}: DotLottieWCAnimationProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const scriptLoadedRef = useRef(false)

  useEffect(() => {
    const loadScript = async () => {
      // Check if script is already loaded
      const existingScript = document.querySelector('script[src*="dotlottie-wc"]')
      if (existingScript) {
        // Wait a bit for the script to initialize
        setTimeout(createAnimation, 100)
        return
      }

      try {
        // Create and load the script with better error handling
        const script = document.createElement("script")
        script.src = "https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js"
        script.type = "module"
        script.crossOrigin = "anonymous"

        script.onload = () => {
          console.log("dotlottie-wc script loaded successfully")
          scriptLoadedRef.current = true
          // Wait for web component to register
          setTimeout(createAnimation, 200)
        }

        script.onerror = (error) => {
          console.error("Failed to load dotlottie-wc script:", error)
          console.error("Falling back to iframe approach")
          createIframeFallback()
        }

        document.head.appendChild(script)
      } catch (error) {
        console.error("Error loading dotlottie-wc:", error)
        createIframeFallback()
      }
    }

    const createAnimation = () => {
      if (!containerRef.current) return

      // Clear any existing content
      containerRef.current.innerHTML = ""

      try {
        // Create the dotlottie-wc element
        const lottieElement = document.createElement("dotlottie-wc")
        lottieElement.setAttribute("src", src)
        lottieElement.setAttribute("speed", speed)
        
        // Set style attributes
        lottieElement.style.width = width
        lottieElement.style.height = height
        
        // Apply additional styles
        Object.assign(lottieElement.style, style)
        
        // Set boolean attributes
        if (autoplay) {
          lottieElement.setAttribute("autoplay", "")
        }
        if (loop) {
          lottieElement.setAttribute("loop", "")
        }

        // Add error handling
        lottieElement.addEventListener("error", (event) => {
          console.error("Lottie animation error:", event)
          createFallback()
        })

        // Add load event listener
        lottieElement.addEventListener("load", () => {
          console.log("Lottie animation loaded successfully")
        })

        containerRef.current.appendChild(lottieElement)
      } catch (error) {
        console.error("Error creating dotlottie-wc element:", error)
        createFallback()
      }
    }

    const createFallback = () => {
      if (!containerRef.current) return

      containerRef.current.innerHTML = `
        <div style="
          width: ${width};
          height: ${height};
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f0f0f0;
          border-radius: 8px;
          color: #666;
          font-weight: 500;
          font-size: 14px;
        ">
          Animation Loading...
        </div>
      `
    }

    const createIframeFallback = () => {
      if (!containerRef.current) return

      // Convert lottie.host URL to embed URL
      const getEmbedUrl = (url: string): string => {
        const match = url.match(/lottie\.host\/([^\/]+)\/([^\/]+)\.lottie/)
        if (match) {
          const [, id, filename] = match
          return `https://lottie.host/embed/${id}/${filename}.html`
        }
        return url
      }

      const embedUrl = getEmbedUrl(src)

      containerRef.current.innerHTML = `
        <iframe
          src="${embedUrl}"
          width="${width.replace('px', '')}"
          height="${height.replace('px', '')}"
          style="border: 0; overflow: hidden;"
          title="Lottie Animation"
          allowfullscreen
        ></iframe>
      `
    }

    loadScript()

    return () => {
      // Cleanup if needed
      if (containerRef.current) {
        containerRef.current.innerHTML = ""
      }
    }
  }, [src, width, height, speed, autoplay, loop, style])

  return (
    <div 
      ref={containerRef} 
      className={`inline-block ${className}`}
      style={{ width, height }}
    >
      {/* Lottie animation will be inserted here */}
    </div>
  )
}
