"use client"

import React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader } from "@/components/ui/dialog"
import { X } from "lucide-react"
import { OneClickPurchaseFlow } from "./one-click-purchase-flow"

interface PurchaseModalProps {
  isOpen: boolean
  onClose: () => void
}

export function PurchaseModal({ isOpen, onClose }: PurchaseModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto p-0">
        <DialogHeader className="p-6 pb-0">
          <button
            onClick={onClose}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
          >
            <X className="h-6 w-6" />
            <span className="sr-only">Close</span>
          </button>
        </DialogHeader>
        
        <div className="p-6 pt-0">
          <OneClickPurchaseFlow onClose={onClose} />
        </div>
      </DialogContent>
    </Dialog>
  )
}
