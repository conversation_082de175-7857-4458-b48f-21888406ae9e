"use client"

import React, { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, CreditCard, CheckCircle, ArrowRight, Star, Gift } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { QuickRegisterForm } from "@/components/auth/quick-register-form"
import Image from "next/image"

interface OneClickPurchaseFlowProps {
  onClose?: () => void
  className?: string
}

export function OneClickPurchaseFlow({ onClose, className = "" }: OneClickPurchaseFlowProps) {
  const [step, setStep] = useState(1) // 1: Value Prop, 2: Register, 3: Payment, 4: Success
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()

  // If user is already authenticated, skip to payment
  React.useEffect(() => {
    if (isAuthenticated && user) {
      setStep(3)
    }
  }, [isAuthenticated, user])

  const handlePurchase = async () => {
    if (!user) {
      setError("Please complete registration first")
      return
    }

    setIsProcessing(true)
    setError(null)

    try {
      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cardType: "Premium Card",
          price: 100,
          userId: user.id,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to create checkout session")
      }

      const data = await response.json()
      
      // Redirect to Stripe Checkout
      window.location.href = data.url

    } catch (error: any) {
      console.error("Error initiating payment:", error)
      setError(error.message)
      setIsProcessing(false)
    }
  }

  const handleRegistrationSuccess = () => {
    setStep(3)
  }

  const discountExamples = [
    { business: "Local Restaurant", discount: "25% off meals" },
    { business: "Coffee Shop", discount: "Buy 1 Get 1 Free" },
    { business: "Retail Store", discount: "20% off purchases" },
    { business: "Service Provider", discount: "15% off services" },
  ]

  return (
    <div className={`w-full max-w-2xl mx-auto ${className}`}>
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-600">
            Step {step} of {isAuthenticated ? 3 : 4}
          </span>
          <span className="text-sm text-gray-500">
            {step === 1 && "See the Value"}
            {step === 2 && "Quick Sign Up"}
            {step === 3 && "Secure Payment"}
            {step === 4 && "All Done!"}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-500"
            style={{ 
              width: `${(step / (isAuthenticated ? 3 : 4)) * 100}%` 
            }}
          />
        </div>
      </div>

      <AnimatePresence mode="wait">
        {/* Step 1: Value Proposition */}
        {step === 1 && (
          <motion.div
            key="value-prop"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 relative w-32 h-20">
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png"
                    alt="VIP Card"
                    fill
                    className="object-contain"
                  />
                </div>
                <CardTitle className="text-3xl font-bold text-gray-900">
                  Get Your VIP Card Now
                </CardTitle>
                <CardDescription className="text-xl text-gray-600">
                  Instant access to exclusive discounts at 73+ businesses
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Discount Examples */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {discountExamples.map((example, index) => (
                    <div key={index} className="flex items-center p-4 bg-green-50 rounded-lg border border-green-200">
                      <Gift className="h-6 w-6 text-green-600 mr-3 flex-shrink-0" />
                      <div>
                        <p className="font-semibold text-gray-900">{example.business}</p>
                        <p className="text-green-700 font-medium">{example.discount}</p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Key Benefits */}
                <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                  <h3 className="font-bold text-lg text-blue-900 mb-3 flex items-center">
                    <Star className="h-5 w-5 mr-2" />
                    What You Get
                  </h3>
                  <ul className="space-y-2 text-blue-800">
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-blue-600" />
                      Instant access to all discounts
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-blue-600" />
                      Digital card on your phone
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-blue-600" />
                      No monthly fees - one-time payment
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-blue-600" />
                      Save hundreds on your purchases
                    </li>
                  </ul>
                </div>

                {/* Price and CTA */}
                <div className="text-center">
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">$100</span>
                    <span className="text-lg text-gray-600 ml-2">one-time</span>
                  </div>
                  <Button
                    onClick={() => setStep(isAuthenticated ? 3 : 2)}
                    className="w-full h-16 text-xl font-bold bg-blue-600 hover:bg-blue-700"
                  >
                    Get My VIP Card Now
                    <ArrowRight className="ml-2 h-6 w-6" />
                  </Button>
                  <p className="text-sm text-gray-500 mt-2">
                    Secure checkout • 30-day money-back guarantee
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Step 2: Registration */}
        {step === 2 && !isAuthenticated && (
          <motion.div
            key="registration"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <QuickRegisterForm
              onSuccess={handleRegistrationSuccess}
              showTitle={false}
              redirectTo=""
            />
            <div className="text-center mt-4">
              <Button
                variant="ghost"
                onClick={() => setStep(1)}
                className="text-gray-600 hover:text-gray-800"
              >
                ← Back to Details
              </Button>
            </div>
          </motion.div>
        )}

        {/* Step 3: Payment */}
        {step === 3 && (
          <motion.div
            key="payment"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  Complete Your Purchase
                </CardTitle>
                <CardDescription className="text-lg text-gray-600">
                  Secure payment powered by Stripe
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription className="text-base">{error}</AlertDescription>
                  </Alert>
                )}

                {/* Order Summary */}
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="font-bold text-lg mb-4">Order Summary</h3>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-700">VIP Premium Card</span>
                    <span className="font-semibold">$100.00</span>
                  </div>
                  <div className="flex justify-between items-center text-lg font-bold border-t pt-2">
                    <span>Total</span>
                    <span>$100.00</span>
                  </div>
                </div>

                <Button
                  onClick={handlePurchase}
                  disabled={isProcessing}
                  className="w-full h-16 text-xl font-bold bg-blue-600 hover:bg-blue-700"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-6 w-6 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard className="mr-2 h-6 w-6" />
                      Pay Securely with Stripe
                    </>
                  )}
                </Button>

                <div className="text-center">
                  <Button
                    variant="ghost"
                    onClick={() => setStep(isAuthenticated ? 1 : 2)}
                    className="text-gray-600 hover:text-gray-800"
                    disabled={isProcessing}
                  >
                    ← Back
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {onClose && (
        <div className="text-center mt-6">
          <Button variant="ghost" onClick={onClose} className="text-gray-500">
            Cancel
          </Button>
        </div>
      )}
    </div>
  )
}
