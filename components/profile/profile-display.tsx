"use client"

import { useDashboardProfile, getDisplayName, getMembershipStatus, getCardTierColor } from "@/hooks/use-dashboard-profile"
import { useAuth } from "@/contexts/auth-context"
import { Badge } from "@/components/ui/badge"
import { User, Mail, Phone, Wallet, Building2, Crown } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"

interface ProfileDisplayProps {
  variant?: 'full' | 'compact' | 'minimal' | 'header'
  showBusinessStatus?: boolean
  showMembershipStatus?: boolean
  showContactInfo?: boolean
  className?: string
}

export function ProfileDisplay({
  variant = 'compact',
  showBusinessStatus = false,
  showMembershipStatus = true,
  showContactInfo = false,
  className = ""
}: ProfileDisplayProps) {
  const { profile, isLoading, error } = useDashboardProfile()
  const { isBusinessOwner } = useAuth()

  if (isLoading) {
    return <ProfileDisplaySkeleton variant={variant} />
  }

  if (error || !profile) {
    return (
      <div className={`text-gray-500 text-sm ${className}`}>
        {error || 'Profile not available'}
      </div>
    )
  }

  const displayName = getDisplayName(profile)
  const fullName = profile.first_name && profile.last_name
    ? `${profile.first_name} ${profile.last_name}`
    : displayName
  const membershipInfo = getMembershipStatus(profile)
  const hasPhone = !!profile.phone
  const hasWallet = !!profile.xrp_wallet_address

  // Minimal variant - just name and status
  if (variant === 'minimal') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="w-8 h-8 bg-[#3A56FF]/20 rounded-full flex items-center justify-center">
          <User className="h-4 w-4 text-[#3A56FF]" />
        </div>
        <div>
          <p className="text-sm font-medium text-white">{displayName}</p>
          {profile.is_card_holder && (
            <Badge className={`text-xs ${getCardTierColor(profile.card_tier)}`}>
              {profile.card_tier || 'VIP'}
            </Badge>
          )}
        </div>
      </div>
    )
  }

  // Header variant - for navigation/header use
  if (variant === 'header') {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        <div className="w-10 h-10 bg-[#3A56FF]/20 rounded-full flex items-center justify-center">
          <User className="h-5 w-5 text-[#3A56FF]" />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">{displayName}</p>
          <p className="text-xs text-gray-500 truncate">{profile.user_email}</p>
        </div>
        {profile.is_card_holder && (
          <Crown className="h-4 w-4 text-yellow-500" />
        )}
      </div>
    )
  }

  // Compact variant - single line with key info
  if (variant === 'compact') {
    return (
      <div className={`bg-[#1A1A1A]/80 backdrop-blur-sm border border-white/10 rounded-lg p-4 ${className}`}>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-[#3A56FF]/20 rounded-full flex items-center justify-center">
            <User className="h-5 w-5 text-[#3A56FF]" />
          </div>
          <div className="flex-1">
            <h3 className="text-white font-semibold">{fullName || displayName}</h3>
            <div className="flex items-center gap-2 mt-1">
              {showMembershipStatus && (
                <Badge className={`text-xs ${membershipInfo.color}`}>
                  {membershipInfo.label}
                </Badge>
              )}
              {profile.is_card_holder && (
                <Badge className={`text-xs ${getCardTierColor(profile.card_tier)}`}>
                  {profile.card_tier || 'VIP'} Card
                </Badge>
              )}
              {showBusinessStatus && isBusinessOwner && (
                <Badge className="bg-green-500/20 text-green-400 text-xs">
                  <Building2 className="h-3 w-3 mr-1" />
                  Business Owner
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Full variant - detailed profile information
  return (
    <div className={`bg-[#1A1A1A] border border-white/10 rounded-lg p-6 text-white ${className}`}>
      <div className="flex items-center gap-4 mb-6">
        <div className="w-16 h-16 bg-[#3A56FF]/20 rounded-full flex items-center justify-center">
          <User className="h-8 w-8 text-[#3A56FF]" />
        </div>
        <div className="flex-1">
          <h2 className="text-xl font-bold">{fullName || displayName}</h2>
          <p className="text-gray-400">{profile.user_email}</p>
          <div className="flex items-center gap-2 mt-2">
            {showMembershipStatus && (
              <Badge className={membershipInfo.color}>
                {membershipInfo.label}
              </Badge>
            )}
            {profile.is_card_holder && (
              <Badge className={getCardTierColor(profile.card_tier)}>
                <Crown className="h-3 w-3 mr-1" />
                {profile.card_tier || 'VIP'} Card
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      {showContactInfo && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-gray-400" />
            <span className="text-sm">{profile.user_email}</span>
          </div>
          {hasPhone && profile.phone && (
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-gray-400" />
              <span className="text-sm">{profile.phone}</span>
            </div>
          )}
          {hasWallet && profile.xrp_wallet_address && (
            <div className="flex items-center gap-2 md:col-span-2">
              <Wallet className="h-4 w-4 text-gray-400" />
              <span className="text-xs font-mono text-gray-300 break-all">
                {profile.xrp_wallet_address}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Business Status */}
      {showBusinessStatus && (
        <div className="border-t border-white/10 pt-4">
          <div className="flex items-center gap-2">
            <Building2 className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-400">Business Status:</span>
            {isBusinessOwner ? (
              <Badge className="bg-green-500/20 text-green-400">
                Business Owner
              </Badge>
            ) : (
              <Badge className="bg-gray-500/20 text-gray-400">
                Not a Business Owner
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

function ProfileDisplaySkeleton({ variant }: { variant: string }) {
  if (variant === 'minimal') {
    return (
      <div className="flex items-center gap-2">
        <Skeleton className="w-8 h-8 rounded-full" />
        <div>
          <Skeleton className="h-4 w-20 mb-1" />
          <Skeleton className="h-3 w-16" />
        </div>
      </div>
    )
  }

  if (variant === 'header') {
    return (
      <div className="flex items-center gap-3">
        <Skeleton className="w-10 h-10 rounded-full" />
        <div className="flex-1">
          <Skeleton className="h-4 w-24 mb-1" />
          <Skeleton className="h-3 w-32" />
        </div>
      </div>
    )
  }

  if (variant === 'compact') {
    return (
      <div className="bg-[#1A1A1A]/80 backdrop-blur-sm border border-white/10 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <Skeleton className="w-10 h-10 rounded-full" />
          <div className="flex-1">
            <Skeleton className="h-5 w-32 mb-2" />
            <div className="flex gap-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Full variant skeleton
  return (
    <div className="bg-[#1A1A1A] border border-white/10 rounded-lg p-6">
      <div className="flex items-center gap-4 mb-6">
        <Skeleton className="w-16 h-16 rounded-full" />
        <div className="flex-1">
          <Skeleton className="h-6 w-40 mb-2" />
          <Skeleton className="h-4 w-48 mb-2" />
          <div className="flex gap-2">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-5 w-20" />
          </div>
        </div>
      </div>
    </div>
  )
}
