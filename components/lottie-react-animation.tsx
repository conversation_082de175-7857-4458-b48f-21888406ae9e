"use client"

import { useLottie } from "lottie-react"
import { useEffect, useState } from "react"

interface LottieReactAnimationProps {
  src: string
  width?: string | number
  height?: string | number
  loop?: boolean
  autoplay?: boolean
  speed?: number
  className?: string
}

export function LottieReactAnimation({
  src,
  width = 300,
  height = 300,
  loop = true,
  autoplay = true,
  speed = 1,
  className = ""
}: LottieReactAnimationProps) {
  const [animationData, setAnimationData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAnimationData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Fetch the Lottie JSON data from the URL
        const response = await fetch(src)
        
        if (!response.ok) {
          throw new Error(`Failed to fetch animation: ${response.status}`)
        }
        
        const data = await response.json()
        setAnimationData(data)
      } catch (err) {
        console.error("Error loading Lottie animation:", err)
        setError(err instanceof Error ? err.message : "Failed to load animation")
      } finally {
        setLoading(false)
      }
    }

    fetchAnimationData()
  }, [src])

  const options = {
    animationData: animationData,
    loop: loop,
    autoplay: autoplay,
  }

  const { View, setSpeed, play, pause, stop } = useLottie(options)

  // Set speed when component mounts or speed changes
  useEffect(() => {
    if (setSpeed) {
      setSpeed(speed)
    }
  }, [setSpeed, speed])

  // Auto-play control
  useEffect(() => {
    if (autoplay && play) {
      play()
    }
  }, [autoplay, play])

  if (loading) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={{ width, height }}
      >
        <div className="text-gray-500 text-sm">Loading animation...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div 
        className={`flex items-center justify-center bg-red-50 border border-red-200 rounded-lg ${className}`}
        style={{ width, height }}
      >
        <div className="text-red-600 text-sm text-center px-4">
          <div className="font-medium">Animation Error</div>
          <div className="text-xs mt-1">{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div 
      className={`flex items-center justify-center ${className}`}
      style={{ width, height }}
    >
      <div style={{ width: "100%", height: "100%" }}>
        {View}
      </div>
    </div>
  )
}

// Specific component for your animation
export function YourLottieReactAnimation() {
  return (
    <LottieReactAnimation
      src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
      width={300}
      height={300}
      loop={true}
      autoplay={true}
      speed={1}
    />
  )
}

// Alternative component with controls
export function LottieWithControls({ src, ...props }: LottieReactAnimationProps) {
  const [animationData, setAnimationData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchAnimationData = async () => {
      try {
        const response = await fetch(src)
        const data = await response.json()
        setAnimationData(data)
      } catch (err) {
        console.error("Error loading animation:", err)
      } finally {
        setLoading(false)
      }
    }

    fetchAnimationData()
  }, [src])

  const options = {
    animationData: animationData,
    loop: props.loop ?? true,
    autoplay: props.autoplay ?? true,
  }

  const { View, setSpeed, play, pause, stop, setDirection } = useLottie(options)

  if (loading || !animationData) {
    return (
      <div className="flex flex-col items-center space-y-4">
        <div 
          className="flex items-center justify-center bg-gray-100 rounded-lg"
          style={{ width: props.width || 300, height: props.height || 300 }}
        >
          <div className="text-gray-500">Loading...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center space-y-4">
      <div style={{ width: props.width || 300, height: props.height || 300 }}>
        {View}
      </div>
      
      {/* Control buttons */}
      <div className="flex space-x-2">
        <button 
          onClick={() => play && play()}
          className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
        >
          Play
        </button>
        <button 
          onClick={() => pause && pause()}
          className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
        >
          Pause
        </button>
        <button 
          onClick={() => stop && stop()}
          className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
        >
          Stop
        </button>
        <button 
          onClick={() => setSpeed && setSpeed(0.5)}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          0.5x
        </button>
        <button 
          onClick={() => setSpeed && setSpeed(1)}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          1x
        </button>
        <button 
          onClick={() => setSpeed && setSpeed(2)}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          2x
        </button>
      </div>
    </div>
  )
}
