"use client"

import { motion } from "framer-motion"
import { useOnboardingModal } from "@/contexts/onboarding-modal-context"
import { ArrowR<PERSON>, CheckCircle, Building2, Loader2 } from "lucide-react"
import Image from "next/image"
import { useAuth } from "@/contexts/auth-context"
import { useSearchParams } from "next/navigation"
import { Suspense } from "react"

function WelcomeStepContent() {
  const { nextStep, isBusinessApplicant, applicationData } = useOnboardingModal()
  const { user } = useAuth()
  const searchParams = useSearchParams()
  const justConfirmed = searchParams.get('confirmed') === 'true'
  const isRequired = searchParams.get('required') === 'true'

  const handleNextStep = () => {
    nextStep()
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="text-center max-w-2xl mx-auto"
    >
      {/* Enhanced Logo Container with Hover Effect */}
      <motion.div
        className="mb-12"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <div className="relative inline-block">
          <motion.div
            className="bg-white rounded-2xl p-8 shadow-2xl border border-gray-100 hover:shadow-3xl transition-all duration-300 hover:-translate-y-2"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <div className="flex flex-col items-center space-y-4">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png"
                width={120}
                height={120}
                alt="Fuse Logo"
                className="drop-shadow-lg"
              />
              <div className="text-center">
                <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#3A56FF] to-[#6366F1] bg-clip-text text-transparent">
                  Welcome to Fuse
                </h1>
                <div className="w-24 h-1 bg-gradient-to-r from-[#3A56FF] to-[#6366F1] rounded-full mx-auto mt-2"></div>
              </div>
            </div>
          </motion.div>

          {/* Floating particles effect */}
          <div className="absolute -top-2 -right-2 w-4 h-4 bg-[#3A56FF] rounded-full opacity-60 animate-pulse"></div>
          <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-[#6366F1] rounded-full opacity-40 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
          <div className="absolute top-1/2 -right-4 w-2 h-2 bg-[#3A56FF] rounded-full opacity-50 animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
      </motion.div>

      {justConfirmed && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl shadow-lg"
        >
          <div className="flex items-center justify-center mb-3">
            <div className="bg-green-100 p-2 rounded-full mr-3">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-green-800">Email Confirmed!</h3>
          </div>
          <p className="text-green-700 text-center">
            Thank you for confirming your email. Your account is now fully activated and ready to go!
          </p>
        </motion.div>
      )}

      {isBusinessApplicant && applicationData && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-lg"
        >
          <div className="flex items-center justify-center mb-3">
            <div className="bg-blue-100 p-2 rounded-full mr-3">
              <Building2 className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-blue-800">Business Application Received</h3>
          </div>
          <p className="text-blue-700 mb-2 text-center">
            Thank you for applying to join our network with <strong>{applicationData.business_name}</strong>.
          </p>
          <p className="text-sm text-blue-600 text-center">
            Your application is being reviewed. You'll be notified when it's approved.
          </p>
        </motion.div>
      )}

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.5 }}
        className="mb-8"
      >
        <h2 className="text-2xl md:text-3xl font-bold mb-4 text-gray-800">
          Let's Get You Started!
        </h2>
        <p className="text-lg text-gray-600 leading-relaxed">
          We'll help you set up your profile to unlock exclusive rewards,
          connect with premium businesses, and get the most out of your Fuse membership.
        </p>
      </motion.div>

      {isRequired ? (
        // Required profile completion - no skip option
        <div className="text-center">
          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4 mb-6 mx-auto max-w-md">
            <div className="flex items-center justify-center gap-2 text-yellow-400 mb-2">
              <span className="text-lg">⚡</span>
              <span className="font-semibold">Profile Required</span>
            </div>
            <p className="text-white/80 text-sm">
              Please complete your profile to continue with your VIP card purchase.
            </p>
          </div>
          
          <motion.button
            onClick={handleNextStep}
            className="bg-gradient-to-r from-[#3A56FF] to-[#6366F1] text-white px-8 py-4 rounded-xl font-semibold hover:from-[#2A46EF] hover:to-[#5855F5] transition-all duration-300 flex items-center justify-center mx-auto shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            Complete Required Profile
            <ArrowRight className="ml-2 h-5 w-5" />
          </motion.button>
        </div>
      ) : (
        // Optional profile completion - with skip option
        <div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <motion.button
              onClick={handleNextStep}
              className="bg-gradient-to-r from-[#3A56FF] to-[#6366F1] text-white px-8 py-4 rounded-xl font-semibold hover:from-[#2A46EF] hover:to-[#5855F5] transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              Complete Profile Setup
              <ArrowRight className="ml-2 h-5 w-5" />
            </motion.button>

            <motion.button
              onClick={() => {
                // Skip to dashboard for casual browsing
                window.location.href = '/dashboard'
              }}
              className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              Skip for Now
            </motion.button>
          </div>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.5 }}
            className="text-sm text-gray-500 mt-4 text-center"
          >
            You can complete your profile later. Full profile setup is only required when purchasing a VIP card.
          </motion.p>
        </div>
      )}
    </motion.div>
  )
}

function WelcomeStepFallback() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center max-w-2xl mx-auto"
    >
      <div className="mb-12">
        <div className="relative inline-block">
          <div className="bg-white rounded-2xl p-8 shadow-2xl border border-gray-100">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="w-16 h-16 text-[#3A56FF] animate-spin" />
              <div className="text-center">
                <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#3A56FF] to-[#6366F1] bg-clip-text text-transparent">
                  Loading...
                </h1>
                <div className="w-24 h-1 bg-gradient-to-r from-[#3A56FF] to-[#6366F1] rounded-full mx-auto mt-2"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <p className="text-lg text-gray-600">
        Please wait while we prepare your welcome experience.
      </p>
    </motion.div>
  )
}

export function WelcomeStep() {
  return (
    <Suspense fallback={<WelcomeStepFallback />}>
      <WelcomeStepContent />
    </Suspense>
  )
}
