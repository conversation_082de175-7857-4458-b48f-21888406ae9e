"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { useOnboarding } from "@/contexts/onboarding-context"
import { Loader2, Tag } from "lucide-react"

const INTEREST_OPTIONS = [
  { id: "retail", label: "Retail" },
  { id: "food", label: "Food & Beverage" },
  { id: "tech", label: "Technology" },
  { id: "finance", label: "Finance" },
  { id: "health", label: "Healthcare" },
  { id: "education", label: "Education" },
  { id: "entertainment", label: "Entertainment" },
  { id: "travel", label: "Travel" },
  { id: "fitness", label: "Fitness" },
  { id: "beauty", label: "Beauty & Wellness" },
  { id: "home", label: "Home & Garden" },
  { id: "automotive", label: "Automotive" },
]

export function InterestsStep() {
  const { nextStep, prevStep } = useOnboarding()
  const [isLoading, setIsLoading] = useState(false)
  const [selected, setSelected] = useState<string[]>([])

  const toggleInterest = (id: string) => {
    if (selected.includes(id)) {
      setSelected(selected.filter((item) => item !== id))
    } else {
      setSelected([...selected, id])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

   
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      className="max-w-2xl mx-auto"
    >
      <div className="text-center mb-8">
        <div className="bg-[#3A56FF]/10 p-3 rounded-full inline-flex mb-4">
          <Tag className="h-6 w-6 text-[#3A56FF]" />
        </div>
        <h2 className="text-2xl md:text-3xl font-bold mb-2">Select Your Interests</h2>
        <p className="text-gray-600">Choose the categories that interest you to help us personalize your experience.</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
          {INTEREST_OPTIONS.map((interest) => (
            <div
              key={interest.id}
              onClick={() => toggleInterest(interest.id)}
              className={`p-3 rounded-lg border cursor-pointer transition-all ${
                selected.includes(interest.id)
                  ? "border-[#3A56FF] bg-[#3A56FF]/5"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="flex items-center">
                <div
                  className={`w-4 h-4 rounded-full mr-2 flex items-center justify-center ${
                    selected.includes(interest.id) ? "bg-[#3A56FF]" : "border border-gray-300"
                  }`}
                >
                  {selected.includes(interest.id) && <div className="w-2 h-2 rounded-full bg-white"></div>}
                </div>
                <span>{interest.label}</span>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-between pt-4">
          <button
            type="button"
            onClick={prevStep}
            className="text-gray-600 px-6 py-2 rounded-md font-medium hover:bg-gray-100 transition-colors"
          >
            Back
          </button>
          <button
            type="submit"
            disabled={isLoading || selected.length === 0}
            className="bg-[#3A56FF] text-white px-6 py-2 rounded-md font-medium hover:bg-[#2A46EF] transition-colors disabled:opacity-70 flex items-center"
          >
            {isLoading ? (
              <>
                <Loader2 className="animate-spin h-4 w-4 mr-2" />
                Saving...
              </>
            ) : (
              "Continue"
            )}
          </button>
        </div>
      </form>
    </motion.div>
  )
}
