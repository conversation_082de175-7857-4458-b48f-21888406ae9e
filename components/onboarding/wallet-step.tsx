"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { useOnboarding } from "@/contexts/onboarding-context"
import { Loader2, Wallet } from "lucide-react"
import Image from "next/image"

export function WalletStep() {
  const { nextStep, prevStep } = useOnboarding()
  const [isLoading, setIsLoading] = useState(false)
  const [walletOption, setWalletOption] = useState<string | null>(null)

  const handleContinue = async () => {
    setIsLoading(true)

    try {
      // Here you would typically connect the wallet
      // For now, we'll just simulate a delay
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setIsLoading(false)
      nextStep()
    } catch (error) {
      console.error("Error connecting wallet:", error)
      setIsLoading(false)
    }
  }

  const walletOptions = [
    {
      id: "xaman",
      name: "Xaman",
      logo: "/metamask-logo.png",
      description: "Connect with Xaman (formerly XUMM)",
    },
    {
      id: "metamask",
      name: "MetaMask",
      logo: "/metamask-logo.png",
      description: "Connect with MetaMask wallet",
    },
    {
      id: "walletconnect",
      name: "WalletConnect",
      logo: "/walletconnect-logo.png",
      description: "Connect with WalletConnect",
    },
    {
      id: "coinbase",
      name: "Coinbase Wallet",
      logo: "/coinbase-wallet-logo.png",
      description: "Connect with Coinbase Wallet",
    },
  ]

  return (
    <motion.div
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      className="max-w-2xl mx-auto"
    >
      <div className="text-center mb-8">
        <div className="bg-[#3A56FF]/10 p-3 rounded-full inline-flex mb-4">
          <Wallet className="h-6 w-6 text-[#3A56FF]" />
        </div>
        <h2 className="text-2xl md:text-3xl font-bold mb-2">Connect Your Wallet</h2>
        <p className="text-gray-600">
          Connect your digital wallet to access exclusive features and manage your digital assets.
        </p>
      </div>

      <div className="space-y-4 mb-8">
        {walletOptions.map((wallet) => (
          <div
            key={wallet.id}
            onClick={() => setWalletOption(wallet.id)}
            className={`p-4 rounded-lg border cursor-pointer transition-all flex items-center ${
              walletOption === wallet.id ? "border-[#3A56FF] bg-[#3A56FF]/5" : "border-gray-200 hover:border-gray-300"
            }`}
          >
            <div className="mr-4">
              <Image src={wallet.logo || "/placeholder.svg"} width={40} height={40} alt={`${wallet.name} logo`} />
            </div>
            <div>
              <h3 className="font-medium">{wallet.name}</h3>
              <p className="text-sm text-gray-600">{wallet.description}</p>
            </div>
          </div>
        ))}
      </div>

      <div className="text-center mb-8">
        <p className="text-sm text-gray-500">
          Don't have a wallet yet? You can skip this step and connect later from your dashboard.
        </p>
      </div>

      <div className="flex justify-between">
        <button
          onClick={prevStep}
          className="text-gray-600 px-6 py-2 rounded-md font-medium hover:bg-gray-100 transition-colors"
        >
          Back
        </button>
        <button
          onClick={handleContinue}
          disabled={isLoading}
          className="bg-[#3A56FF] text-white px-6 py-2 rounded-md font-medium hover:bg-[#2A46EF] transition-colors disabled:opacity-70 flex items-center"
        >
          {isLoading ? (
            <>
              <Loader2 className="animate-spin h-4 w-4 mr-2" />
              Connecting...
            </>
          ) : walletOption ? (
            "Connect Wallet"
          ) : (
            "Skip for Now"
          )}
        </button>
      </div>
    </motion.div>
  )
}
