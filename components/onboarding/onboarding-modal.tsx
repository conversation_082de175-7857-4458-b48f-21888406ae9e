"use client"

import { useOnboardingModal } from "@/contexts/onboarding-modal-context"
import { WelcomeStep } from "./welcome-step"
import { ProfileStep } from "./profile-step"
import { InterestsStep } from "./interests-step"
import { WalletStep } from "./wallet-step"
import { CompleteStep } from "./complete-step"
import { AnimatePresence, motion } from "framer-motion"
import { X } from "lucide-react"

export function OnboardingModal() {
  const { isOpen, currentStep, progress, closeOnboarding } = useOnboardingModal()

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="sticky top-0 bg-white z-10 px-6 py-4 border-b flex justify-between items-center">
          <h2 className="text-xl font-bold">Complete Your Profile</h2>
          <button 
            onClick={closeOnboarding}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        {/* Progress bar */}
        <div className="px-6 py-4 border-b">
          <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-[#3A56FF] transition-all duration-500 ease-in-out"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <div className="flex justify-between mt-2 text-sm text-gray-500">
            <span>Welcome</span>
            <span>Profile</span>
            <span>Interests</span>
            <span>Wallet</span>
            <span>Complete</span>
          </div>
        </div>

        {/* Step content */}
        <div className="p-6">
          <AnimatePresence mode="wait">
            {currentStep === "welcome" && <WelcomeStep />}
            {currentStep === "profile" && <ProfileStep />}
            {currentStep === "wallet" && <WalletStep />}
            {currentStep === "complete" && <CompleteStep />}
          </AnimatePresence>
        </div>
      </motion.div>
    </div>
  )
}