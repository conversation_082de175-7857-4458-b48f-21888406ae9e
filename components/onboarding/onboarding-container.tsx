"use client"

import { useOnboarding } from "@/contexts/onboarding-context"
import { WelcomeStep } from "./welcome-step"
import { ProfileStep } from "./profile-step"
import { WalletStep } from "./wallet-step"
import { CompleteStep } from "./complete-step"
import { AnimatePresence } from "framer-motion"

export function OnboardingContainer() {
  const { currentStep, progress } = useOnboarding()

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Progress bar */}
      <div className="mb-8">
        <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
          <div
            className="h-full bg-[#3A56FF] transition-all duration-500 ease-in-out"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between mt-2 text-sm text-gray-500">
          <span>Welcome</span>
          <span>Profile</span>
          <span>Wallet</span>
          <span>Complete</span>
        </div>
      </div>

      {/* Step content */}
      <div className="bg-white p-8 rounded-lg shadow-md">
        <AnimatePresence mode="wait">
          {currentStep === "welcome" && <WelcomeStep />}
          {currentStep === "profile" && <ProfileStep />}
          {currentStep === "wallet" && <WalletStep />}
          {currentStep === "complete" && <CompleteStep />}
        </AnimatePresence>
      </div>
    </div>
  )
}
