"use client"

import { motion } from "framer-motion"
import { useOnboarding } from "@/contexts/onboarding-context"
import { CheckCircle } from "lucide-react"
import { useEffect } from "react"

export function CompleteStep() {
  const { completeOnboarding, profileData } = useOnboarding()

  // Trigger confetti effect when component mounts
  useEffect(() => {
    const initConfetti = async () => {
      const confetti = (await import("canvas-confetti")).default;

      const duration = 3 * 1000
      const animationEnd = Date.now() + duration
      const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 }

      function randomInRange(min: number, max: number) {
        return Math.random() * (max - min) + min
      }

      const interval: any = setInterval(() => {
        const timeLeft = animationEnd - Date.now()

        if (timeLeft <= 0) {
          return clearInterval(interval)
        }

        const particleCount = 50 * (timeLeft / duration)
        // since particles fall down, start a bit higher than random
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 },
        })
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
        })
      }, 250)

      return () => clearInterval(interval)
    };

    initConfetti();
  }, [])

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="text-center max-w-2xl mx-auto"
    >
      <div className="mb-8">
        <div className="bg-green-100 p-4 rounded-full inline-flex mb-4">
          <CheckCircle className="h-12 w-12 text-green-600" />
        </div>
        <h1 className="text-3xl md:text-4xl font-bold mb-4">You're All Set!</h1>
        <p className="text-lg text-gray-600 mb-8">
          Thanks for completing your profile, {profileData.fullName || "there"}! Your account is now ready to use.
        </p>
      </div>

      <div className="bg-gray-50 p-6 rounded-lg mb-8">
        <h3 className="font-bold text-xl mb-4">What's Next?</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h4 className="font-medium mb-2">Explore Loyalty Cards</h4>
            <p className="text-sm text-gray-600">
              Browse our selection of loyalty cards and choose the one that fits your needs.
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h4 className="font-medium mb-2">Connect with Businesses</h4>
            <p className="text-sm text-gray-600">
              Discover businesses in your area that are part of the Fuse.vip network.
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h4 className="font-medium mb-2">Earn Rewards</h4>
            <p className="text-sm text-gray-600">
              Start earning rewards by using your loyalty cards at participating businesses.
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h4 className="font-medium mb-2">Manage Your Wallet</h4>
            <p className="text-sm text-gray-600">
              Connect your digital wallet to access exclusive features and manage your assets.
            </p>
          </div>
        </div>
      </div>

      <button
        onClick={completeOnboarding}
        className="bg-[#3A56FF] text-white px-8 py-3 rounded-md font-medium hover:bg-[#2A46EF] transition-colors"
      >
        Go to Dashboard
      </button>
    </motion.div>
  )
}
