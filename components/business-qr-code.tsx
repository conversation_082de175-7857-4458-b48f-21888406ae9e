"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { QrCode, Download, RefreshCw, Copy, CheckCircle } from "lucide-react";
import { toast } from "sonner";

interface BusinessQRCodeProps {
  businessId: string;
  businessName: string;
  size?: number;
  showActions?: boolean;
  className?: string;
}

export function BusinessQRCode({ 
  businessId,
  businessName,
  size = 256, 
  showActions = true, 
  className = "" 
}: BusinessQRCodeProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [copied, setCopied] = useState(false);

  const generateQRCode = async (forceRegenerate = false) => {
    if (!businessId) return;

    try {
      setIsLoading(true);

      const response = await fetch('/api/generate-business-qr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId,
          size
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate QR code');
      }

      if (result.success && result.qrCodeUrl) {
        setQrCodeUrl(result.qrCodeUrl);
        if (forceRegenerate) {
          toast.success('QR code regenerated successfully!');
        }
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to generate QR code');
    } finally {
      setIsLoading(false);
      setIsRegenerating(false);
    }
  };

  const handleRegenerate = async () => {
    setIsRegenerating(true);
    await generateQRCode(true);
  };

  const handleDownload = () => {
    if (!qrCodeUrl) return;

    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `${businessName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_qr_code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('QR code downloaded successfully!');
  };

  const handleCopyLink = async () => {
    if (!businessId) return;

    const qrLink = `${window.location.origin}/scan?business_id=${businessId}`;
    
    try {
      await navigator.clipboard.writeText(qrLink);
      setCopied(true);
      toast.success('QR code link copied to clipboard!');
      
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast.error('Failed to copy link to clipboard');
    }
  };

  const handleCopyQRData = async () => {
    if (!qrCodeUrl) return;

    try {
      // Convert data URL to blob and copy to clipboard
      const response = await fetch(qrCodeUrl);
      const blob = await response.blob();
      
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      
      toast.success('QR code image copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy QR code:', error);
      // Fallback: copy the data URL
      try {
        await navigator.clipboard.writeText(qrCodeUrl);
        toast.success('QR code data copied to clipboard!');
      } catch (fallbackError) {
        toast.error('Failed to copy QR code');
      }
    }
  };

  useEffect(() => {
    generateQRCode();
  }, [businessId, size]);

  return (
    <Card className={`w-full max-w-md mx-auto ${className}`}>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <QrCode className="h-5 w-5" />
          Business QR Code
        </CardTitle>
        <p className="text-sm text-gray-600">
          Customers scan this code to earn FUSE rewards
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex justify-center">
          {isLoading ? (
            <div className="flex items-center justify-center w-64 h-64 bg-gray-100 rounded-lg">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-500">Generating QR code...</p>
              </div>
            </div>
          ) : qrCodeUrl ? (
            <div className="relative">
              <img 
                src={qrCodeUrl} 
                alt={`QR Code for ${businessName}`}
                className="rounded-lg shadow-md border"
                style={{ width: size, height: size }}
              />
              {isRegenerating && (
                <div className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-lg">
                  <RefreshCw className="h-6 w-6 animate-spin text-gray-600" />
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center w-64 h-64 bg-gray-100 rounded-lg">
              <div className="text-center">
                <QrCode className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-500">Failed to generate QR code</p>
              </div>
            </div>
          )}
        </div>

        <div className="text-center">
          <h3 className="font-semibold text-gray-900">{businessName}</h3>
          <p className="text-sm text-gray-600">Business ID: {businessId.slice(0, 8)}...</p>
        </div>

        {showActions && qrCodeUrl && (
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={handleDownload}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Download
              </Button>
              
              <Button
                onClick={handleCopyQRData}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Copy className="h-4 w-4" />
                Copy Image
              </Button>
            </div>
            
            <Button
              onClick={handleCopyLink}
              variant="outline"
              size="sm"
              className="w-full flex items-center gap-2"
            >
              {copied ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4" />
                  Copy Scan Link
                </>
              )}
            </Button>
            
            <Button
              onClick={handleRegenerate}
              variant="ghost"
              size="sm"
              className="w-full flex items-center gap-2"
              disabled={isRegenerating}
            >
              <RefreshCw className={`h-4 w-4 ${isRegenerating ? 'animate-spin' : ''}`} />
              {isRegenerating ? 'Regenerating...' : 'Regenerate'}
            </Button>
          </div>
        )}

        <div className="bg-blue-50 rounded-lg p-3">
          <h4 className="text-sm font-semibold text-blue-900 mb-1">How it works:</h4>
          <ul className="text-xs text-blue-800 space-y-1">
            <li>• VIP cardholders scan this code to earn 100 FUSE</li>
            <li>• Each customer can scan once per day</li>
            <li>• Display this QR code prominently in your business</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
