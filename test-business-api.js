// Test script to verify business API is working
// This can be run in the browser console when logged in

async function testBusinessAPI() {
  console.log('🧪 Testing business API...');
  
  try {
    const response = await fetch('/api/dashboard/business', {
      credentials: 'include',
      headers: {
        'Cache-Control': 'no-cache',
      },
    });
    
    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);
    
    const result = await response.json();
    console.log('Response data:', result);
    
    if (response.ok) {
      if (result.business) {
        console.log('✅ Business found:', result.business.name);
        console.log('Business ID:', result.business.id);
        console.log('User ID:', result.business.user_id);
      } else {
        console.log('ℹ️ No business found for this user');
      }
    } else {
      console.error('❌ API Error:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error);
  }
}

// Also test the business access API
async function testBusinessAccessAPI() {
  console.log('🧪 Testing business access API...');
  
  try {
    const response = await fetch('/api/business-access-optimized', {
      credentials: 'include'
    });
    
    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);
    
    const result = await response.json();
    console.log('Response data:', result);
    
    if (response.ok) {
      console.log('✅ Business access check successful');
      console.log('Has business access:', result.data?.has_business_access);
      console.log('Is admin:', result.data?.is_admin);
    } else {
      console.error('❌ API Error:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error);
  }
}

// Run both tests
console.log('Running business API tests...');
testBusinessAPI();
testBusinessAccessAPI();
