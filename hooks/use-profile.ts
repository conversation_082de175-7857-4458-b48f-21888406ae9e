"use client"

import { useAuth } from "@/contexts/auth-context"
import { useMemo } from "react"

/**
 * Custom hook for accessing user profile data with computed properties
 * This provides a clean interface for profile information across the app
 */
export function useProfile() {
  const { user, profile, isLoading, isBusinessOwner } = useAuth()

  const profileData = useMemo(() => {
    if (!user || !profile) {
      return {
        isLoaded: false,
        displayName: 'User',
        fullName: '',
        email: '',
        isCardHolder: false,
        cardTier: null,
        isBusinessOwner: false,
        hasPhone: false,
        hasWallet: false,
        membershipStatus: 'inactive',
        profile: null,
        user: null
      }
    }

    // Compute display name
    const displayName = profile.first_name 
      ? `${profile.first_name} ${profile.last_name || ''}`.trim()
      : user.email?.split('@')[0] || 'User'

    // Compute full name
    const fullName = profile.first_name && profile.last_name
      ? `${profile.first_name} ${profile.last_name}`
      : profile.first_name || ''

    // Compute membership status
    let membershipStatus = 'inactive'
    if (profile.is_card_holder) {
      if (profile.membership_end_date) {
        const endDate = new Date(profile.membership_end_date)
        const now = new Date()
        membershipStatus = endDate > now ? 'active' : 'expired'
      } else {
        membershipStatus = 'active' // Lifetime membership
      }
    }

    return {
      isLoaded: true,
      displayName,
      fullName,
      email: profile.user_email || user.email || '',
      isCardHolder: profile.is_card_holder || false,
      cardTier: profile.card_tier || null,
      isBusinessOwner: isBusinessOwner || false,
      hasPhone: !!profile.phone,
      hasWallet: !!profile.xrp_wallet_address,
      membershipStatus,
      profile,
      user
    }
  }, [user, profile, isBusinessOwner])

  return {
    ...profileData,
    isLoading,
    refreshProfile: async () => {
      // This would trigger a refresh of the auth context
      // The auth context handles profile caching
    }
  }
}

/**
 * Get card tier display color
 */
export function getCardTierColor(tier?: string) {
  switch (tier?.toLowerCase()) {
    case 'premium':
      return 'bg-blue-500/20 text-blue-400'
    case 'elite':
      return 'bg-purple-500/20 text-purple-400'
    case 'executive':
      return 'bg-gold-500/20 text-yellow-400'
    default:
      return 'bg-gray-500/20 text-gray-400'
  }
}

/**
 * Get membership status display info
 */
export function getMembershipStatusInfo(status: string) {
  switch (status) {
    case 'active':
      return {
        color: 'bg-green-500/20 text-green-400',
        label: 'Active Member',
        icon: '✓'
      }
    case 'expired':
      return {
        color: 'bg-red-500/20 text-red-400',
        label: 'Membership Expired',
        icon: '⚠️'
      }
    case 'inactive':
    default:
      return {
        color: 'bg-gray-500/20 text-gray-400',
        label: 'Not a Member',
        icon: '○'
      }
  }
}
