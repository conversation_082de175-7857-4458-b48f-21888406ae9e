/**
 * React Hook for SuperUser Data
 * Provides easy access to businesses and profiles data via SuperUser service
 */

import { useState, useEffect, useCallback } from 'react'

interface BusinessData {
  id: string
  name: string
  logo_url?: string
  website?: string
  category?: string
  premium_discount?: string
  is_active: boolean
  business_spotlight?: boolean
  business_address?: string
  contact_name?: string
  contact_email?: string
  contact_phone?: string
  user_id?: string
  created_at: string
  updated_at?: string
}

interface ProfileData {
  id: string
  first_name?: string
  last_name?: string
  user_email?: string
  phone?: string
  is_card_holder: boolean
  is_business_applicant: boolean
  card_tier?: string
  xrp_wallet_address?: string
  membership_start_date?: string
  membership_end_date?: string
  referring_business_id?: string
  created_at: string
  updated_at?: string
}

interface SuperUserDataState {
  businesses: BusinessData[]
  profiles: ProfileData[]
  loading: boolean
  error: string | null
  lastFetch: Date | null
}

interface UseSuperUserDataOptions {
  autoFetch?: boolean
  activeBusinessesOnly?: boolean
  cardHoldersOnly?: boolean
  useCache?: boolean
}

export function useSuperUserData(options: UseSuperUserDataOptions = {}) {
  const {
    autoFetch = true,
    activeBusinessesOnly = false,
    cardHoldersOnly = false,
    useCache = true
  } = options

  const [state, setState] = useState<SuperUserDataState>({
    businesses: [],
    profiles: [],
    loading: false,
    error: null,
    lastFetch: null
  })

  const fetchAllData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const params = new URLSearchParams({
        cache: useCache.toString(),
        activeBusinesses: activeBusinessesOnly.toString(),
        cardHolders: cardHoldersOnly.toString()
      })

      const response = await fetch(`/api/superuser/all-data?${params}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch data')
      }

      setState(prev => ({
        ...prev,
        businesses: result.data.businesses || [],
        profiles: result.data.profiles || [],
        loading: false,
        lastFetch: new Date()
      }))

      console.log('✅ SuperUser Hook: Data fetched successfully', {
        businesses: result.data.businesses?.length || 0,
        profiles: result.data.profiles?.length || 0
      })

    } catch (error) {
      console.error('❌ SuperUser Hook: Error fetching data:', error)
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [useCache, activeBusinessesOnly, cardHoldersOnly])

  const fetchBusinesses = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const params = new URLSearchParams({
        cache: useCache.toString(),
        active: activeBusinessesOnly.toString()
      })

      const response = await fetch(`/api/superuser/businesses?${params}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch businesses')
      }

      setState(prev => ({
        ...prev,
        businesses: result.data || [],
        loading: false,
        lastFetch: new Date()
      }))

      console.log('✅ SuperUser Hook: Businesses fetched successfully', {
        count: result.data?.length || 0
      })

    } catch (error) {
      console.error('❌ SuperUser Hook: Error fetching businesses:', error)
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [useCache, activeBusinessesOnly])

  const fetchProfiles = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const params = new URLSearchParams({
        cache: useCache.toString(),
        cardHolders: cardHoldersOnly.toString()
      })

      const response = await fetch(`/api/superuser/profiles?${params}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch profiles')
      }

      setState(prev => ({
        ...prev,
        profiles: result.data || [],
        loading: false,
        lastFetch: new Date()
      }))

      console.log('✅ SuperUser Hook: Profiles fetched successfully', {
        count: result.data?.length || 0
      })

    } catch (error) {
      console.error('❌ SuperUser Hook: Error fetching profiles:', error)
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }))
    }
  }, [useCache, cardHoldersOnly])

  const clearCache = useCallback(async () => {
    try {
      const response = await fetch('/api/superuser/businesses?all=true', {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      console.log('✅ SuperUser Hook: Cache cleared successfully')
    } catch (error) {
      console.error('❌ SuperUser Hook: Error clearing cache:', error)
    }
  }, [])

  const refresh = useCallback(() => {
    fetchAllData()
  }, [fetchAllData])

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      fetchAllData()
    }
  }, [autoFetch, fetchAllData])

  return {
    ...state,
    fetchAllData,
    fetchBusinesses,
    fetchProfiles,
    clearCache,
    refresh,
    // Computed values
    activeBusinesses: state.businesses.filter(b => b.is_active),
    cardHolders: state.profiles.filter(p => p.is_card_holder),
    businessApplicants: state.profiles.filter(p => p.is_business_applicant)
  }
}
