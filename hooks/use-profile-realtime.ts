"use client"

import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import profileRealtimeCache, { ProfileData, ProfileSyncStatus } from '@/lib/profile-realtime-cache';

export interface UseProfileRealtimeOptions {
  enableOptimisticUpdates?: boolean;
  autoFetch?: boolean;
}

export interface ProfileRealtimeResult {
  profile: ProfileData | null;
  isLoading: boolean;
  syncStatus: ProfileSyncStatus;
  updateProfile: (updates: Partial<ProfileData>) => Promise<boolean>;
  optimisticWalletUpdate: (walletAddress: string | null) => void;
  optimisticVipUpdate: (isCardHolder: boolean, cardTier?: string) => void;
  refreshProfile: () => Promise<void>;
  handleRefresh: () => Promise<void>;
}

/**
 * Enhanced profile hook with real-time subscriptions and optimistic updates
 * Based on the business dashboard real-time pattern
 */
export function useProfileRealtime(options: UseProfileRealtimeOptions = {}): ProfileRealtimeResult {
  const { 
    enableOptimisticUpdates = true,
    autoFetch = true 
  } = options;

  const { user } = useAuth();
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [syncStatus, setSyncStatus] = useState<ProfileSyncStatus>({
    status: 'idle',
    lastSync: null,
    isOnline: navigator.onLine
  });

  const unsubscribeRef = useRef<(() => void) | null>(null);
  const unsubscribeStatusRef = useRef<(() => void) | null>(null);

  // Network status detection
  useEffect(() => {
    const handleOnline = () => {
      profileRealtimeCache.updateNetworkStatus(true);
    };
    
    const handleOffline = () => {
      profileRealtimeCache.updateNetworkStatus(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Fetch profile data with cache support
  const fetchProfileData = useCallback(async (useCache = true, retryCount = 0): Promise<ProfileData | null> => {
    if (!user?.id) {
      console.log('No user ID available for profile fetch');
      return null;
    }

    setIsLoading(true);
    setSyncStatus(prev => ({ ...prev, status: 'syncing' }));

    try {
      // Try cache first
      if (useCache) {
        const cached = profileRealtimeCache.get(user.id);
        if (cached) {
          console.log('✅ Using cached profile data');
          setIsLoading(false);
          setSyncStatus(prev => ({ ...prev, status: 'idle' }));
          return cached;
        }
      }

      console.log('🔄 Fetching fresh profile data from API');

      // Fetch from API
      const response = await fetch('/api/dashboard/profiles', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401 && retryCount < 2) {
          console.log(`⏳ Auth not ready, retrying profile fetch (attempt ${retryCount + 1})...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
          return fetchProfileData(false, retryCount + 1);
        }
        
        throw new Error(`Profile fetch failed: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.error);
      }

      const profileData: ProfileData = result.profile;
      
      // Cache the data
      profileRealtimeCache.set(user.id, profileData);
      
      setSyncStatus(prev => ({ 
        ...prev, 
        status: 'idle',
        lastSync: new Date()
      }));

      console.log('✅ Profile data fetched and cached successfully');
      
      return profileData;

    } catch (error) {
      console.error('❌ Error fetching profile data:', error);
      setSyncStatus(prev => ({ ...prev, status: 'error' }));
      
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Setup real-time subscription and initial data fetch
  useEffect(() => {
    if (!user?.id) {
      setProfile(null);
      return;
    }

    console.log(`🔄 Setting up profile real-time subscription for user ${user.id}`);

    // Subscribe to profile changes
    unsubscribeRef.current = profileRealtimeCache.subscribe(
      user.id,
      (updatedProfile: ProfileData) => {
        console.log('🔄 Real-time profile update received:', updatedProfile);
        setProfile(updatedProfile);
      }
    );

    // Subscribe to sync status changes
    unsubscribeStatusRef.current = profileRealtimeCache.subscribeToStatus(
      user.id,
      (status: ProfileSyncStatus) => {
        setSyncStatus(status);
      }
    );

    // Initial data fetch
    if (autoFetch) {
      fetchProfileData().then(data => {
        if (data) {
          setProfile(data);
        }
      });
    }

    return () => {
      if (unsubscribeRef.current) {
        console.log('🔄 Cleaning up profile real-time subscription');
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
      
      if (unsubscribeStatusRef.current) {
        unsubscribeStatusRef.current();
        unsubscribeStatusRef.current = null;
      }
    };
  }, [user?.id, autoFetch, fetchProfileData]);

  // Update profile with optimistic updates
  const updateProfile = useCallback(async (updates: Partial<ProfileData>): Promise<boolean> => {
    if (!user?.id || !profile) {
      console.warn('Cannot update profile: no user or profile data');
      return false;
    }

    let originalProfile: ProfileData | null = null;

    try {
      setSyncStatus(prev => ({ ...prev, status: 'syncing' }));

      // Optimistic update
      if (enableOptimisticUpdates) {
        originalProfile = { ...profile };
        const optimisticProfile = profileRealtimeCache.optimisticUpdate(user.id, updates);
        if (optimisticProfile) {
          setProfile(optimisticProfile);
        }
      }

      // API call
      const response = await fetch('/api/dashboard/profiles', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error(`Profile update failed: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.error);
      }

      // Update cache with confirmed data
      const updatedProfile: ProfileData = result.profile;
      profileRealtimeCache.set(user.id, updatedProfile);
      setProfile(updatedProfile);

      setSyncStatus(prev => ({ 
        ...prev, 
        status: 'idle',
        lastSync: new Date()
      }));

      // Show success feedback
      const successDiv = document.createElement('div');
      successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse';
      successDiv.textContent = '✅ Profile updated successfully!';
      document.body.appendChild(successDiv);
      setTimeout(() => successDiv.remove(), 3000);

      console.log('✅ Profile updated successfully');
      return true;

    } catch (error) {
      console.error('❌ Error updating profile:', error);
      
      // Revert optimistic update on error
      if (enableOptimisticUpdates && originalProfile) {
        profileRealtimeCache.revertOptimisticUpdate(user.id, originalProfile);
        setProfile(originalProfile);
      }

      setSyncStatus(prev => ({ ...prev, status: 'error' }));

      // Show error feedback
      const errorDiv = document.createElement('div');
      errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
      errorDiv.textContent = '❌ Failed to update profile';
      document.body.appendChild(errorDiv);
      setTimeout(() => errorDiv.remove(), 5000);

      return false;
    }
  }, [user?.id, profile, enableOptimisticUpdates]);

  // Optimistic wallet update
  const optimisticWalletUpdate = useCallback((walletAddress: string | null) => {
    if (!user?.id) return;

    const optimisticProfile = profileRealtimeCache.optimisticWalletUpdate(user.id, walletAddress);
    if (optimisticProfile) {
      setProfile(optimisticProfile);
      console.log(`💳 Optimistic wallet update: ${walletAddress ? 'connected' : 'disconnected'}`);
    }
  }, [user?.id]);

  // Optimistic VIP update
  const optimisticVipUpdate = useCallback((isCardHolder: boolean, cardTier?: string) => {
    if (!user?.id) return;

    const optimisticProfile = profileRealtimeCache.optimisticVipUpdate(user.id, isCardHolder, cardTier);
    if (optimisticProfile) {
      setProfile(optimisticProfile);
      console.log(`🎫 Optimistic VIP update: ${isCardHolder ? `card holder (${cardTier})` : 'not a card holder'}`);
    }
  }, [user?.id]);

  // Manual refresh
  const refreshProfile = useCallback(async (): Promise<void> => {
    if (!user?.id) return;
    
    profileRealtimeCache.invalidate(user.id);
    const freshData = await fetchProfileData(false);
    if (freshData) {
      setProfile(freshData);
    }
  }, [user?.id, fetchProfileData]);

  // Alias for refreshProfile (matching business pattern)
  const handleRefresh = refreshProfile;

  return {
    profile,
    isLoading,
    syncStatus,
    updateProfile,
    optimisticWalletUpdate,
    optimisticVipUpdate,
    refreshProfile,
    handleRefresh,
  };
}

/**
 * Lightweight hook for components that only need sync status
 */
export function useProfileSyncStatus() {
  const { user } = useAuth();
  const [syncStatus, setSyncStatus] = useState<ProfileSyncStatus>({
    status: 'idle',
    lastSync: null,
    isOnline: navigator.onLine
  });

  useEffect(() => {
    if (!user?.id) return;

    const unsubscribe = profileRealtimeCache.subscribeToStatus(
      user.id,
      setSyncStatus
    );

    return unsubscribe;
  }, [user?.id]);

  return syncStatus;
}