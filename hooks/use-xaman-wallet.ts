'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { getSupabaseClient } from '@/lib/supabase';
import { authStateManager } from '@/lib/auth-state-manager';

// Types for Xaman OAuth2 PKCE
interface XummPkceState {
  me?: {
    sub: string;
    account: string;
    name?: string;
  };
  sdk?: any;
}

interface XummPkce {
  authorize: () => Promise<any>;
  logout: () => Promise<void>;
  state: () => Promise<XummPkceState>;
  on: (event: string, callback: (data?: any) => void) => void;
}

declare global {
  interface Window {
    XummPkce: new (apiKey: string, options?: {
      implicit?: boolean;
      redirectUrl?: string;
    }) => XummPkce;
  }
}

export function useXamanWallet() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');
  const [xummPkce, setXummPkce] = useState<XummPkce | null>(null);

  // Initialize Xaman OAuth2 PKCE SDK
  useEffect(() => {
    const loadXummPkce = () => {
      if (typeof window === 'undefined' || window.XummPkce) {
        initializeXummPkce();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://xumm.app/assets/cdn/xumm-oauth2-pkce.min.js?v=2.7.1';
      script.async = true;

      script.onload = () => {
        initializeXummPkce();
      };

      script.onerror = () => {
        console.error('Failed to load Xaman OAuth2 PKCE SDK');
      };

      document.head.appendChild(script);
    };

    loadXummPkce();
  }, []);

  const initializeXummPkce = useCallback(() => {
    if (!window.XummPkce) return;

    const apiKey = process.env.NEXT_PUBLIC_XAMAN_API_KEY;
    if (!apiKey) {
      console.error('Missing Xaman API key');
      return;
    }

    try {
      // Use consistent redirect URL logic
      const getBaseRedirectUrl = () => {
        if (typeof window === 'undefined') return 'https://fuse.vip/dashboard';

        // For production, always use the production domain
        if (window.location.hostname === 'fuse.vip' || window.location.hostname === 'www.fuse.vip') {
          return 'https://fuse.vip/dashboard';
        }

        // For development, use localhost
        return window.location.origin + '/dashboard';
      };

      const redirectUrl = getBaseRedirectUrl();
      console.log('🔧 Xaman Hook - Using redirect URL:', redirectUrl);

      const xumm = new window.XummPkce(apiKey, {
        implicit: true,
        redirectUrl: redirectUrl
      });

      setXummPkce(xumm);

      // Set up event listeners
      xumm.on('error', (error) => {
        console.error('Xaman error:', error);
        setIsLoading(false);
      });

      xumm.on('success', () => {
        checkWalletState(xumm);
      });

      xumm.on('retrieved', () => {
        checkWalletState(xumm);
      });

      // Check initial state
      checkWalletState(xumm);
    } catch (error) {
      console.error('Failed to initialize Xaman OAuth2 PKCE:', error);
    }
  }, []);

  const checkWalletState = useCallback(async (xumm: XummPkce) => {
    try {
      const state = await xumm.state();
      if (state?.me?.sub) {
        setWalletAddress(state.me.sub);
        setIsConnected(true);
        
        // Update profile if user is logged in
        if (user?.id) {
          updateProfile(state.me.sub);
        }
      } else {
        setWalletAddress('');
        setIsConnected(false);
      }
    } catch (error) {
      console.error('Failed to check wallet state:', error);
      setWalletAddress('');
      setIsConnected(false);
    }
  }, [user?.id]);

  const updateProfile = async (address: string, userToken?: string) => {
    if (!user?.id) return;

    try {
      // Use the new wallet connection API route instead of direct Supabase calls
      const response = await fetch('/api/wallet-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: user.id,
          walletAddress: address,
          userToken: userToken,
          action: 'connect'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error updating profile via API:', errorData.error);
        return;
      }

      const result = await response.json();
      console.log('✅ Profile updated successfully via API:', result);
    } catch (err) {
      console.error('Unexpected error updating profile via API:', err);
    }
  };

  const connect = useCallback(async () => {
    setIsLoading(true);
    
    try {
      // Wait for SDK to be ready if not already initialized
      if (!xummPkce) {
        console.log('Xaman SDK not ready, waiting...');
        
        // Wait up to 10 seconds for SDK to initialize
        let attempts = 0;
        const maxAttempts = 50; // 50 * 200ms = 10 seconds
        
        while (!xummPkce && attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 200));
          attempts++;
        }
        
        if (!xummPkce) {
          throw new Error('XUMM SDK not initialized - please refresh the page and try again');
        }
      }

      console.log('Xaman SDK is ready, proceeding with connection...');

      // Store authentication state for wallet return (if user is logged in)
      let sessionId: string | undefined;
      if (user?.id) {
        try {
          sessionId = authStateManager.storeAuthState({
            user,
            targetRoute: window.location.pathname + window.location.search,
            walletContext: {
              transactionId: undefined, // No specific transaction for connection
            }
          });

          console.log('🔐 Stored auth state for wallet connection:', {
            sessionId,
            targetRoute: window.location.pathname + window.location.search
          });

          // Update the redirect URL with session ID for this connection
          const baseRedirectUrl = typeof window !== 'undefined' 
            ? (window.location.hostname === 'fuse.vip' || window.location.hostname === 'www.fuse.vip')
              ? 'https://fuse.vip/dashboard'
              : window.location.origin + '/dashboard'
            : 'https://fuse.vip/dashboard';

          const redirectUrl = authStateManager.createWalletReturnUrl(baseRedirectUrl, sessionId);
          
          // Temporarily update the redirect URL for this authorization
          // Note: This is a bit hacky but necessary since we can't pass redirect URL to authorize()
          if (xummPkce && typeof xummPkce === 'object' && 'redirectUrl' in xummPkce) {
            (xummPkce as any).redirectUrl = redirectUrl;
          }

          console.log('🔧 Updated Xaman redirect URL for this session:', redirectUrl);
        } catch (stateError) {
          console.warn('⚠️ Failed to store auth state, proceeding without it:', stateError);
        }
      } else {
        console.log('ℹ️ User not logged in, skipping auth state storage');
      }

      await xummPkce.authorize();
      return true;
    } catch (error) {
      console.error('Failed to connect wallet:', error);
      setIsLoading(false);
      return false;
    }
  }, [xummPkce, user]);

  const disconnect = useCallback(async () => {
    if (!xummPkce) return;

    setIsLoading(true);
    try {
      await xummPkce.logout();
      setWalletAddress('');
      setIsConnected(false);

      // Update profile to remove wallet address but keep user logged in
      if (user?.id) {
        const supabase = getSupabaseClient();
        if (supabase) {
          await supabase
            .from('profiles')
            .update({ xrp_wallet_address: null })
            .eq('id', user.id);
        }
      }
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    } finally {
      setIsLoading(false);
    }
  }, [xummPkce, user?.id]);

  const createPayment = useCallback(async (destination: string, amount: string, currency: string = 'XRP') => {
    if (!xummPkce || !isConnected) {
      throw new Error('Wallet not connected');
    }

    const state = await xummPkce.state();
    if (!state?.sdk) {
      throw new Error('SDK not available');
    }

    const payload = await state.sdk.payload.create({
      TransactionType: 'Payment',
      Destination: destination,
      Amount: currency === 'XRP' ? amount : {
        currency: currency,
        issuer: 'rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU', // Treasury wallet
        value: amount
      }
    });

    return payload;
  }, [xummPkce, isConnected]);

  const createTrustline = useCallback(async (currency: string = 'FUSE', issuer?: string) => {
    if (!xummPkce || !isConnected) {
      throw new Error('Wallet not connected');
    }

    const state = await xummPkce.state();
    if (!state?.sdk) {
      throw new Error('SDK not available');
    }

    const payload = await state.sdk.payload.create({
      TransactionType: 'TrustSet',
      LimitAmount: {
        currency: currency,
        issuer: issuer || 'rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU', // Treasury wallet
        value: '1000000000'
      }
    });

    return payload;
  }, [xummPkce, isConnected]);

  return {
    isLoading,
    isConnected,
    walletAddress,
    xummPkce,
    connect,
    disconnect,
    createPayment,
    createTrustline,
  };
}
