import { useState, useCallback } from 'react';
import { useXRPPayment } from './use-xrp-payment';
import { pricingService } from '@/lib/pricing-service';
import { createClient } from '@/utils/supabase/client';

// VIP Card tiers configuration
export const VIP_CARD_TIERS = {
  monthly: {
    name: 'Monthly VIP Card',
    price: 9.99,
    duration: 30, // days
    stripeButtonId: 'buy_btn_1RbT1cE4IOnXedOCDlLSY0gA',
    cardTier: 'Premium'
  },
  annual: {
    name: 'Premium Card',
    price: 100,
    duration: 365, // days
    stripeButtonId: 'buy_btn_1ReD59E4IOnXedOCfvsWehxl',
    cardTier: 'Premium'
  },
  lifetime: {
    name: 'Obsidian Card',
    price: 1500,
    duration: null, // lifetime
    stripeButtonId: null, // XRPL only for now
    cardTier: 'Obsidian'
  }
} as const;

export type VIPCardTier = keyof typeof VIP_CARD_TIERS;
export type PaymentMethod = 'stripe' | 'XRP' | 'FUSE';

interface VIPPaymentState {
  isProcessing: boolean;
  currentStep: string;
  error: string | null;
  conversionRate: number | null;
  cryptoAmount: string | null;
}

interface VIPPaymentResult {
  success: boolean;
  error?: string;
  transactionId?: string;
  purchaseId?: string;
}

// Treasury wallet address from user's memory
const TREASURY_WALLET = 'rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU';

export function useVIPCardPayment() {
  const {
    createXRPPayment,
    createFUSEPayment,
    isConnected,
    walletAddress,
    isProcessing: xrpProcessing
  } = useXRPPayment();

  const [vipState, setVipState] = useState<VIPPaymentState>({
    isProcessing: false,
    currentStep: '',
    error: null,
    conversionRate: null,
    cryptoAmount: null
  });

  // Create referral record
  const createReferral = useCallback(async (
    supabase: any,
    userId: string,
    purchaseId: string
  ) => {
    try {
      // Get user's referring business from profile
      const { data: profile } = await supabase
        .from('profiles')
        .select('referring_business_id')
        .eq('id', userId)
        .single();

      if (profile?.referring_business_id) {
        // Create referral record
        const { error: referralError } = await supabase
          .from('referrals')
          .insert({
            user_id: userId,
            business_id: profile.referring_business_id,
            purchase_id: purchaseId,
            referral_code: `REF-${Date.now()}`,
            primary_commission: 20.00, // 20% primary commission
            secondary_commission: 5.00, // 5% secondary commission
            status: 'pending'
          });

        if (referralError) {
          console.error('Failed to create referral:', referralError);
        } else {
          console.log('Referral created successfully');
        }
      }
    } catch (error) {
      console.error('Error in createReferral:', error);
    }
  }, []);

  // Calculate crypto amount for USD price
  const calculateCryptoAmount = useCallback(async (
    usdPrice: number,
    paymentMethod: 'XRP' | 'FUSE'
  ): Promise<{ amount: string; rate: number }> => {
    try {
      if (paymentMethod === 'XRP') {
        const xrpAmount = await pricingService.calculateXRPAmount(usdPrice);
        const prices = await pricingService.getCurrentPrices();
        return { amount: xrpAmount, rate: prices.xrpUsd };
      } else {
        const fuseAmount = await pricingService.calculateFUSEAmount(usdPrice);
        const prices = await pricingService.getCurrentPrices();
        return { amount: fuseAmount, rate: prices.fuseUsd };
      }
    } catch (error) {
      console.error('Error calculating crypto amount:', error);
      throw new Error('Failed to calculate crypto amount');
    }
  }, []);

  // Process Stripe payment
  const processStripePayment = useCallback(async (
    tier: VIPCardTier
  ): Promise<VIPPaymentResult> => {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return { success: false, error: 'Please sign in first' };
    }

    const cardConfig = VIP_CARD_TIERS[tier];

    if (!cardConfig.stripeButtonId) {
      return { success: false, error: 'Stripe payment not available for this tier' };
    }

    try {
      setVipState(prev => ({
        ...prev,
        isProcessing: true,
        error: null,
        currentStep: 'Creating Stripe checkout session...'
      }));

      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cardType: cardConfig.name,
          price: cardConfig.price,
          userId: session.user.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create checkout session");
      }

      const data = await response.json();

      setVipState(prev => ({
        ...prev,
        isProcessing: false,
        currentStep: 'Redirecting to Stripe...'
      }));

      return {
        success: true,
        redirectUrl: data.url
      };

    } catch (error: any) {
      console.error('Stripe payment error:', error);
      setVipState(prev => ({
        ...prev,
        isProcessing: false,
        error: error.message
      }));

      return { success: false, error: error.message };
    }
  }, []);

  // Process crypto payment (XRP or FUSE)
  const processCryptoPayment = useCallback(async (
    tier: VIPCardTier,
    paymentMethod: 'XRP' | 'FUSE'
  ): Promise<VIPPaymentResult> => {
    if (!isConnected || !walletAddress) {
      return { success: false, error: 'Wallet not connected' };
    }

    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return { success: false, error: 'Please sign in first' };
    }

    const cardConfig = VIP_CARD_TIERS[tier];
    
    try {
      setVipState(prev => ({ 
        ...prev, 
        isProcessing: true, 
        error: null,
        currentStep: 'Calculating crypto amount...'
      }));

      // Calculate crypto amount
      const { amount: cryptoAmount, rate } = await calculateCryptoAmount(
        cardConfig.price, 
        paymentMethod
      );

      setVipState(prev => ({ 
        ...prev, 
        conversionRate: rate,
        cryptoAmount,
        currentStep: `Preparing payment of ${cryptoAmount} ${paymentMethod} (≈ $${cardConfig.price})...`
      }));

      // Create payment payload
      const paymentOptions = {
        memo: `VIP_CARD_${tier.toUpperCase()}`,
        instruction: `Purchase ${cardConfig.name} for ${cryptoAmount} ${paymentMethod}`,
        identifier: `vip_${tier}_${Date.now()}`,
        returnUrl: '/checkout-success'
      };

      setVipState(prev => ({ 
        ...prev, 
        currentStep: 'Please sign the transaction in your Xaman Wallet...'
      }));

      // Create payment based on method
      const paymentResult = paymentMethod === 'XRP' 
        ? await createXRPPayment(TREASURY_WALLET, cryptoAmount, paymentOptions)
        : await createFUSEPayment(TREASURY_WALLET, cryptoAmount, paymentOptions);

      if (!paymentResult.success) {
        throw new Error(paymentResult.error || 'Payment creation failed');
      }

      setVipState(prev => ({ 
        ...prev, 
        currentStep: 'Transaction signed! Processing payment...'
      }));

      // Record purchase in database
      const { error: purchaseError, data: purchaseResult } = await supabase
        .from('purchases')
        .insert({
          user_id: session.user.id,
          method: paymentMethod.toLowerCase(),
          amount: Number(cryptoAmount),
          status: 'completed',
          transaction_id: paymentResult.transactionId || `${paymentMethod}_${Date.now()}`,
          card_type: cardConfig.name,
          currency: paymentMethod.toLowerCase(),
          metadata: {
            payment_method: paymentMethod,
            crypto_amount: cryptoAmount,
            usd_equivalent: cardConfig.price,
            conversion_rate: rate,
            tier: tier,
            wallet_address: walletAddress
          }
        })
        .select()
        .single();

      if (purchaseError) {
        throw new Error(`Failed to record purchase: ${purchaseError.message}`);
      }

      // Update user profile
      const membershipEndDate = cardConfig.duration
        ? new Date(Date.now() + cardConfig.duration * 24 * 60 * 60 * 1000).toISOString()
        : null; // lifetime

      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          is_card_holder: true,
          membership_start_date: new Date().toISOString(),
          membership_end_date: membershipEndDate,
          card_tier: cardConfig.cardTier
        })
        .eq('id', session.user.id);

      if (updateError) {
        console.error('Failed to update profile:', updateError);
      }

      // Create referral if applicable
      if (purchaseResult) {
        await createReferral(supabase, session.user.id, purchaseResult.id);
      }

      setVipState(prev => ({
        ...prev,
        isProcessing: false,
        currentStep: 'Purchase completed successfully!'
      }));

      // Redirect to success page
      setTimeout(() => {
        window.location.href = '/checkout-success';
      }, 1000);

      return {
        success: true,
        transactionId: paymentResult.transactionId,
        purchaseId: purchaseResult?.id
      };

    } catch (error: any) {
      console.error('Crypto payment error:', error);
      setVipState(prev => ({
        ...prev,
        isProcessing: false,
        error: error.message || 'Payment failed'
      }));

      return { success: false, error: error.message };
    }
  }, [isConnected, walletAddress, createXRPPayment, createFUSEPayment, calculateCryptoAmount, createReferral]);

  // Main purchase function that routes to appropriate payment method
  const purchaseVIPCard = useCallback(async (
    tier: VIPCardTier,
    paymentMethod: PaymentMethod
  ): Promise<VIPPaymentResult> => {
    if (paymentMethod === 'stripe') {
      const result = await processStripePayment(tier);
      if (result.success && result.redirectUrl) {
        window.location.href = result.redirectUrl;
      }
      return result;
    } else {
      return processCryptoPayment(tier, paymentMethod);
    }
  }, [processStripePayment, processCryptoPayment]);

  // Get pricing preview
  const getPricingPreview = useCallback(async (
    tier: VIPCardTier,
    paymentMethod: PaymentMethod
  ) => {
    try {
      const cardConfig = VIP_CARD_TIERS[tier];

      if (paymentMethod === 'stripe') {
        return {
          usdPrice: cardConfig.price,
          cryptoAmount: null,
          currency: 'USD',
          conversionRate: 1,
          cardName: cardConfig.name,
          stripeButtonId: cardConfig.stripeButtonId
        };
      } else {
        const { amount, rate } = await calculateCryptoAmount(cardConfig.price, paymentMethod);
        return {
          usdPrice: cardConfig.price,
          cryptoAmount: amount,
          currency: paymentMethod,
          conversionRate: rate,
          cardName: cardConfig.name
        };
      }
    } catch (error) {
      console.error('Error getting pricing preview:', error);
      return null;
    }
  }, [calculateCryptoAmount]);

  return {
    // State
    isProcessing: vipState.isProcessing || xrpProcessing,
    currentStep: vipState.currentStep,
    error: vipState.error,
    conversionRate: vipState.conversionRate,
    cryptoAmount: vipState.cryptoAmount,
    isConnected,
    walletAddress,
    
    // Actions
    purchaseVIPCard,
    getPricingPreview,
    
    // Config
    VIP_CARD_TIERS
  };
}
