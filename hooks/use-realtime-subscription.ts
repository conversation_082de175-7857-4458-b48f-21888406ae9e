import { useEffect, useRef } from 'react'
import { subscribeToPurchases } from '@/lib/supabase'

// CLEANED UP: Removed all unnecessary event listeners as requested
// Only keeping purchase-related subscriptions for Stripe webhook functionality

export function usePurchasesSubscription(
  callback: (payload: any) => void,
  businessId?: string,
  enabled: boolean = true
) {
  const subscriptionRef = useRef<{ unsubscribe: () => void } | null>(null)

  useEffect(() => {
    if (!enabled) return

    subscriptionRef.current = subscribeToPurchases(callback, businessId)

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe()
        subscriptionRef.current = null
      }
    }
  }, [businessId, enabled])

  useEffect(() => {
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe()
      }
    }
  }, [])
}
