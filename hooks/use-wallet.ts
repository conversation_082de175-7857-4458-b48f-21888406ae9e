// DISABLED: This hook conflicts with useXamanWallet. Use useXamanWallet instead.

export function useWallet() {
  // DISABLED: This hook conflicts with useXamanWallet. Use useXamanWallet instead.
  console.warn('useWallet hook is disabled to prevent conflicts. Use useXamanWallet instead.');
  
  // Return minimal interface to prevent breaking existing imports
  return {
    isConnecting: false,
    isConnected: false,
    walletAddress: '',
    xrpWalletAddress: '',
    accountInfo: null,
    connect: async () => { throw new Error('useWallet is disabled. Use useXamanWallet instead.'); },
    disconnect: async () => { throw new Error('useWallet is disabled. Use useXamanWallet instead.'); },
    fetchAccountInfo: async () => { throw new Error('useWallet is disabled. Use useXamanWallet instead.'); },
    xummSDK: null,
    createPayment: async () => { throw new Error('useWallet is disabled. Use useXamanWallet instead.'); },
    createTrustline: async () => { throw new Error('useWallet is disabled. Use useXamanWallet instead.'); },
  };
}
