/**
 * Static Businesses Hook
 * 
 * React hook that serves pre-generated static businesses data
 * instead of making runtime API calls. This eliminates database
 * connection pool issues and API rate limiting.
 */

import { useState, useEffect, useCallback } from 'react';
import { BusinessData } from '@/types/business';
import {
  getStaticBusinesses,
  getActiveBusinesses,
  getSpotlightBusinesses,
  getBusinessCategories,
  searchBusinesses,
  getBusinessesByCategory,
  getBusinessById,
  isStaticDataAvailable,
  getStaticDataMetadata
} from '@/lib/static-data-provider';

interface UseStaticBusinessesOptions {
  level?: 'minimal' | 'standard' | 'full';
  category?: string;
  spotlight?: boolean;
  search?: string;
  autoLoad?: boolean;
}

interface UseStaticBusinessesReturn {
  businesses: BusinessData[];
  categories: string[];
  isLoading: boolean;
  error: string | null;
  metadata: any;
  refetch: () => Promise<void>;
  searchBusinesses: (query: string) => Promise<void>;
  filterByCategory: (category: string) => Promise<void>;
  clearFilters: () => Promise<void>;
}

/**
 * Main hook for accessing static businesses data
 */
export function useStaticBusinesses(options: UseStaticBusinessesOptions = {}): UseStaticBusinessesReturn {
  const {
    level = 'standard',
    category,
    spotlight = false,
    search,
    autoLoad = true
  } = options;

  const [businesses, setBusinesses] = useState<BusinessData[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [metadata, setMetadata] = useState<any>(null);

  const loadData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Check if static data is available
      const isAvailable = await isStaticDataAvailable();
      if (!isAvailable) {
        throw new Error('Static data not available or outdated. Please regenerate static data.');
      }

      // Load businesses based on options
      let businessesData: BusinessData[];

      if (search) {
        businessesData = await searchBusinesses(search, level);
      } else if (category) {
        businessesData = await getBusinessesByCategory(category, level);
      } else if (spotlight) {
        businessesData = await getSpotlightBusinesses();
      } else {
        businessesData = await getActiveBusinesses();
      }

      // Load categories and metadata
      const [categoriesData, metadataData] = await Promise.all([
        getBusinessCategories(),
        getStaticDataMetadata()
      ]);

      setBusinesses(businessesData);
      setCategories(categoriesData);
      setMetadata(metadataData);

    } catch (err: any) {
      console.error('Error loading static businesses data:', err);
      setError(err.message || 'Failed to load businesses data');
    } finally {
      setIsLoading(false);
    }
  }, [level, category, spotlight, search]);

  const refetch = useCallback(async () => {
    await loadData();
  }, [loadData]);

  const searchBusinessesHandler = useCallback(async (query: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const results = await searchBusinesses(query, level);
      setBusinesses(results);
    } catch (err: any) {
      setError(err.message || 'Search failed');
    } finally {
      setIsLoading(false);
    }
  }, [level]);

  const filterByCategory = useCallback(async (categoryName: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const results = await getBusinessesByCategory(categoryName, level);
      setBusinesses(results);
    } catch (err: any) {
      setError(err.message || 'Filter failed');
    } finally {
      setIsLoading(false);
    }
  }, [level]);

  const clearFilters = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const results = await getActiveBusinesses();
      setBusinesses(results);
    } catch (err: any) {
      setError(err.message || 'Failed to clear filters');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (autoLoad) {
      loadData();
    }
  }, [loadData, autoLoad]);

  return {
    businesses,
    categories,
    isLoading,
    error,
    metadata,
    refetch,
    searchBusinesses: searchBusinessesHandler,
    filterByCategory,
    clearFilters
  };
}

/**
 * Hook for getting a single business by ID
 */
export function useStaticBusiness(id: string, level: 'minimal' | 'standard' | 'full' = 'full') {
  const [business, setBusiness] = useState<BusinessData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadBusiness = useCallback(async () => {
    if (!id) return;

    setIsLoading(true);
    setError(null);

    try {
      const businessData = await getBusinessById(id, level);
      setBusiness(businessData);

      if (!businessData) {
        setError('Business not found');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load business');
    } finally {
      setIsLoading(false);
    }
  }, [id, level]);

  useEffect(() => {
    loadBusiness();
  }, [loadBusiness]);

  return {
    business,
    isLoading,
    error,
    refetch: loadBusiness
  };
}

/**
 * Hook for getting business categories only
 */
export function useStaticCategories() {
  const [categories, setCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadCategories = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const categoriesData = await getBusinessCategories();
      setCategories(categoriesData);
    } catch (err: any) {
      setError(err.message || 'Failed to load categories');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  return {
    categories,
    isLoading,
    error,
    refetch: loadCategories
  };
}

/**
 * Hook for checking static data status
 */
export function useStaticDataStatus() {
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [metadata, setMetadata] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const checkStatus = useCallback(async () => {
    setIsLoading(true);

    try {
      const [available, metadataData] = await Promise.all([
        isStaticDataAvailable(),
        getStaticDataMetadata()
      ]);

      setIsAvailable(available);
      setMetadata(metadataData);
    } catch (err) {
      setIsAvailable(false);
      setMetadata(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    checkStatus();
  }, [checkStatus]);

  return {
    isAvailable,
    metadata,
    isLoading,
    refetch: checkStatus
  };
}
