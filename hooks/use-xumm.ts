// DISABLED: This hook conflicts with useXamanWallet. Use useXamanWallet instead.

export function useXumm() {
  // DISABLED: This hook conflicts with useXamanWallet. Use useXamanWallet instead.
  console.warn('useXumm hook is disabled to prevent conflicts. Use useXamanWallet instead.');
  
  // Return minimal interface to prevent breaking existing imports
  return {
    isInitialized: false,
    isConnected: false,
    account: null,
    isLoading: false,
    error: 'useXumm is disabled. Use useXamanWallet instead.',
    xummSDK: null,
    connect: async () => { throw new Error('useXumm is disabled. Use useXamanWallet instead.'); },
    disconnect: async () => { throw new Error('useXumm is disabled. Use useXamanWallet instead.'); },
    createPayment: async () => { throw new Error('useXumm is disabled. Use useXamanWallet instead.'); },
  };
}
