"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"

interface DashboardProfile {
  id: string
  user_email: string
  first_name: string
  last_name: string
  phone: string
  is_card_holder: boolean
  card_tier: string | null
  xrp_wallet_address: string
  created_at: string
  is_profile_complete: boolean
}

interface UseDashboardProfileReturn {
  profile: DashboardProfile | null
  isLoading: boolean
  error: string | null
  refreshProfile: () => Promise<void>
  updateProfile: (updates: Partial<DashboardProfile>) => Promise<boolean>
}

/**
 * Hook for accessing minimal dashboard profile data
 * Uses the optimized profile API for better performance
 */
export function useDashboardProfile(): UseDashboardProfileReturn {
  const { user } = useAuth()
  const [profile, setProfile] = useState<DashboardProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadProfile = async () => {
    if (!user) {
      setProfile(null)
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/dashboard/profile-minimal?userId=${user.id}`)
      const result = await response.json()

      if (response.ok && result.success) {
        setProfile(result.profile)
      } else {
        setError(result.error || 'Failed to load profile')
        console.error('Failed to load dashboard profile:', result.error)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Error loading dashboard profile:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const updateProfile = async (updates: Partial<DashboardProfile>): Promise<boolean> => {
    if (!user) return false

    try {
      const response = await fetch(`/api/dashboard/profile-minimal?userId=${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setProfile(result.profile)
        return true
      } else {
        setError(result.error || 'Failed to update profile')
        console.error('Failed to update profile:', result.error)
        return false
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Error updating profile:', err)
      return false
    }
  }

  useEffect(() => {
    loadProfile()
  }, [user])

  return {
    profile,
    isLoading,
    error,
    refreshProfile: loadProfile,
    updateProfile
  }
}

/**
 * Get display name from profile data
 */
export function getDisplayName(profile: DashboardProfile | null): string {
  if (!profile) return 'User'
  
  if (profile.first_name && profile.last_name) {
    return `${profile.first_name} ${profile.last_name}`
  }
  
  if (profile.first_name) {
    return profile.first_name
  }
  
  return profile.user_email?.split('@')[0] || 'User'
}

/**
 * Check if profile is complete for onboarding
 */
export function isProfileComplete(profile: DashboardProfile | null): boolean {
  if (!profile) return false
  return profile.is_profile_complete
}

/**
 * Get membership status info
 */
export function getMembershipStatus(profile: DashboardProfile | null) {
  if (!profile || !profile.is_card_holder) {
    return {
      status: 'inactive',
      label: 'No Membership',
      color: 'bg-gray-500/20 text-gray-400'
    }
  }

  return {
    status: 'active',
    label: profile.card_tier || 'VIP Member',
    color: getCardTierColor(profile.card_tier)
  }
}

/**
 * Get card tier display color
 */
export function getCardTierColor(tier?: string | null) {
  switch (tier?.toLowerCase()) {
    case 'premium':
      return 'bg-blue-500/20 text-blue-400'
    case 'elite':
      return 'bg-purple-500/20 text-purple-400'
    case 'executive':
      return 'bg-yellow-500/20 text-yellow-400'
    default:
      return 'bg-gray-500/20 text-gray-400'
  }
}
