'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { XAMAN_CONFIG } from '@/lib/xaman-config'

// TypeScript declarations for the Xumm client SDK (CDN version)
// This is separate from dashboard OAuth flow - focused on direct wallet interactions
declare global {
  interface Window {
    Xumm: any
  }
}

interface XummUser {
  account: Promise<string>
  networkType: string
  networkEndpoint: string
}

interface XummClient {
  authorize: () => void
  logout: () => void
  on: (event: string, callback: (data?: any) => void) => void
  user: XummUser
  userstore: {
    set: (key: string, value: any) => Promise<void>
    get: (key: string) => Promise<any>
    delete: (key: string) => Promise<void>
    list: () => Promise<string[]>
  }
  payload: {
    createAndSubscribe: (payload: any, callback: (event: any) => void) => Promise<any>
  }
}

interface XummClientState {
  isConnected: boolean
  isLoading: boolean
  account: string | null
  networkType: string | null
  error: string | null
  sdkReady: boolean
}

/**
 * Client-side Xumm SDK Hook for Direct Wallet Interactions
 * 
 * Purpose: Handle wallet interactions (payments, trustlines) without backend dependency
 * Separate from: Dashboard OAuth flow (used for secure airdrop wallet registration)
 * Use cases: FUSE trustline setup, direct payments, wallet operations
 * 
 * This protects backend resources by handling transactions client-side via CDN SDK
 */
export const useXummClient = () => {
  const [state, setState] = useState<XummClientState>({
    isConnected: false,
    isLoading: false,
    account: null,
    networkType: null,
    error: null,
    sdkReady: false
  })
  
  const xummRef = useRef<XummClient | null>(null)
  const sdkLoadedRef = useRef(false)

  // Load the Xumm SDK from CDN
  const loadXummSDK = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      // Check if already loaded
      if (window.Xumm || sdkLoadedRef.current) {
        resolve()
        return
      }

      const script = document.createElement('script')
      script.src = 'https://xumm.app/assets/cdn/xumm.min.js'
      script.async = true
      
      script.onload = () => {
        sdkLoadedRef.current = true
        resolve()
      }
      
      script.onerror = () => {
        reject(new Error('Failed to load Xumm SDK'))
      }
      
      document.head.appendChild(script)
    })
  }, [])

  // Initialize Xumm client
  const initializeXumm = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      
      await loadXummSDK()
      
      if (!window.Xumm) {
        throw new Error('Xumm SDK not available')
      }

      if (!XAMAN_CONFIG.API_KEY) {
        throw new Error('Xumm API key not configured')
      }

      // Initialize Xumm client
      const xumm = new window.Xumm(XAMAN_CONFIG.API_KEY)
      xummRef.current = xumm

      // Set up event listeners
      xumm.on('ready', () => {
        console.log('Xumm SDK ready')
        setState(prev => ({ ...prev, sdkReady: true }))
      })

      xumm.on('success', async () => {
        console.log('Xumm connection successful')
        try {
          const account = await xumm.user.account
          setState(prev => ({
            ...prev,
            isConnected: true,
            account,
            networkType: xumm.user.networkType,
            isLoading: false
          }))
        } catch (error) {
          console.error('Error getting user account:', error)
          setState(prev => ({
            ...prev,
            error: 'Failed to get account information',
            isLoading: false
          }))
        }
      })

      xumm.on('logout', () => {
        console.log('Xumm logout')
        setState(prev => ({
          ...prev,
          isConnected: false,
          account: null,
          networkType: null,
          isLoading: false
        }))
      })

      xumm.on('error', (error: any) => {
        console.error('Xumm error:', error)
        setState(prev => ({
          ...prev,
          error: error?.message || 'Connection error',
          isLoading: false
        }))
      })

      xumm.on('timeout', () => {
        console.warn('Xumm timeout')
        setState(prev => ({
          ...prev,
          error: 'Connection timeout',
          isLoading: false
        }))
      })

      setState(prev => ({ ...prev, isLoading: false }))
      
    } catch (error) {
      console.error('Failed to initialize Xumm:', error)
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Initialization failed',
        isLoading: false
      }))
    }
  }, [loadXummSDK])

  // Connect wallet
  const connect = useCallback(async () => {
    if (!xummRef.current) {
      await initializeXumm()
    }
    
    if (xummRef.current) {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      xummRef.current.authorize()
    }
  }, [initializeXumm])

  // Disconnect wallet
  const disconnect = useCallback(() => {
    if (xummRef.current) {
      xummRef.current.logout()
    }
  }, [])

  // Create and subscribe to payload (for transactions)
  const createPayload = useCallback(async (payload: any) => {
    if (!xummRef.current) {
      throw new Error('Xumm not initialized')
    }

    return new Promise((resolve, reject) => {
      xummRef.current!.payload.createAndSubscribe(payload, (event: any) => {
        if ('opened' in event.data) {
          console.log('User opened payload')
        }
        
        if ('signed' in event.data) {
          console.log('User signed transaction')
          resolve(event.data)
        }
        
        if ('expired' in event.data) {
          reject(new Error('Payload expired'))
        }
        
        if ('declined' in event.data) {
          reject(new Error('User declined transaction'))
        }
      })
    })
  }, [])

  // Userstore methods
  const userstore = {
    set: useCallback(async (key: string, value: any) => {
      if (!xummRef.current) {
        throw new Error('Xumm not initialized')
      }
      return xummRef.current.userstore.set(key, value)
    }, []),
    
    get: useCallback(async (key: string) => {
      if (!xummRef.current) {
        throw new Error('Xumm not initialized')
      }
      return xummRef.current.userstore.get(key)
    }, []),
    
    delete: useCallback(async (key: string) => {
      if (!xummRef.current) {
        throw new Error('Xumm not initialized')
      }
      return xummRef.current.userstore.delete(key)
    }, []),
    
    list: useCallback(async () => {
      if (!xummRef.current) {
        throw new Error('Xumm not initialized')
      }
      return xummRef.current.userstore.list()
    }, [])
  }

  // Initialize on mount
  useEffect(() => {
    initializeXumm()
  }, [initializeXumm])

  // Check if mobile device
  const isMobile = useCallback(() => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
  }, [])

  return {
    // State
    ...state,
    
    // Methods
    connect,
    disconnect,
    createPayload,
    userstore,
    
    // Utilities
    isMobile,
    
    // Raw SDK access (for advanced use cases)
    sdk: xummRef.current
  }
}