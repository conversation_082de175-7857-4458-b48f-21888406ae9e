import { useState, useCallback } from 'react';
import { useXamanWallet } from './use-xaman-wallet';
import { FUSE_TOKEN } from '@/lib/fuse-token-utils';
import { XAMAN_CONFIG } from '@/lib/xaman-config';

// Types for XRP payments following XRPL Payment specification
interface XRPPaymentAmount {
  currency?: string;
  issuer?: string;
  value: string;
}

interface XRPPaymentPayload {
  txjson: {
    TransactionType: 'Payment';
    Destination: string;
    Amount: string | XRPPaymentAmount;
    DestinationTag?: number;
    Memos?: Array<{
      Memo: {
        MemoType: string;
        MemoData: string;
      };
    }>;
    Fee?: string;
    Sequence?: number;
    AccountTxnID?: string;
    Flags?: number;
    LastLedgerSequence?: number;
    Paths?: any[];
    SendMax?: string | XRPPaymentAmount;
    DeliverMin?: string | XRPPaymentAmount;
  };
  options?: {
    return_url?: {
      app?: string;
      web?: string;
    };
    force_network?: 'MAINNET' | 'TESTNET';
  };
  custom_meta?: {
    identifier?: string;
    instruction?: string;
    blob?: {
      appName?: string;
      appIcon?: string;
    };
  };
}

interface PaymentOptions {
  memo?: string;
  destinationTag?: number;
  returnUrl?: string;
  instruction?: string;
  identifier?: string;
  fee?: string;
  sendMax?: string | XRPPaymentAmount;
  deliverMin?: string | XRPPaymentAmount;
}

interface PaymentResult {
  success: boolean;
  payloadUuid?: string;
  deepLink?: string;
  qrCode?: string;
  error?: string;
}

interface PaymentState {
  isProcessing: boolean;
  lastPayment?: {
    uuid: string;
    amount: string;
    currency: string;
    destination: string;
    timestamp: number;
  };
}

export function useXRPPayment() {
  const { isConnected, walletAddress, xummPkce } = useXamanWallet();
  const [paymentState, setPaymentState] = useState<PaymentState>({
    isProcessing: false,
  });

  // Helper to convert XRP drops to XRP amount
  const dropsToXRP = (drops: string): string => {
    return (parseInt(drops) / 1000000).toString();
  };

  // Helper to convert XRP amount to drops
  const xrpToDrops = (xrp: string): string => {
    return (parseFloat(xrp) * 1000000).toString();
  };

  // Create XRP native payment
  const createXRPPayment = useCallback(async (
    destination: string,
    amount: string, // Amount in XRP (not drops)
    options: PaymentOptions = {}
  ): Promise<PaymentResult> => {
    if (!xummPkce || !isConnected) {
      return { success: false, error: 'Wallet not connected' };
    }

    if (!walletAddress) {
      return { success: false, error: 'No wallet address available' };
    }

    try {
      setPaymentState(prev => ({ ...prev, isProcessing: true }));

      // Convert XRP to drops for the payment
      const amountInDrops = xrpToDrops(amount);

      const payload: XRPPaymentPayload = {
        txjson: {
          TransactionType: 'Payment',
          Destination: destination,
          Amount: amountInDrops,
        },
        options: {
          return_url: options.returnUrl ? {
            app: options.returnUrl,
            web: options.returnUrl,
          } : {
            app: XAMAN_CONFIG.getRedirectUrl(`${XAMAN_CONFIG.REDIRECT_URLS.PAYMENT_SUCCESS}?type=xrp&amount=${amount}&destination=${destination}`),
            web: XAMAN_CONFIG.getRedirectUrl(`${XAMAN_CONFIG.REDIRECT_URLS.PAYMENT_SUCCESS}?type=xrp&amount=${amount}&destination=${destination}`),
          },
          force_network: 'MAINNET',
        },
        custom_meta: {
          identifier: options.identifier || `xrp_payment_${Date.now()}`,
          instruction: options.instruction || `Send ${amount} XRP to complete your purchase`,
          blob: {
            appName: '$Fuse Rewards',
            appIcon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png',
          },
        },
      };

      // Add optional fields
      if (options.destinationTag) {
        payload.txjson.DestinationTag = options.destinationTag;
      }

      if (options.fee) {
        payload.txjson.Fee = options.fee;
      }

      if (options.sendMax) {
        payload.txjson.SendMax = options.sendMax;
      }

      if (options.deliverMin) {
        payload.txjson.DeliverMin = options.deliverMin;
      }

      // Add memo if provided
      if (options.memo) {
        payload.txjson.Memos = [{
          Memo: {
            MemoType: Buffer.from('memo').toString('hex').toUpperCase(),
            MemoData: Buffer.from(options.memo).toString('hex').toUpperCase(),
          },
        }];
      }

      // Get SDK from OAuth2 PKCE state
      const state = await xummPkce.state();
      if (!state?.sdk) {
        return { success: false, error: 'SDK not available' };
      }

      const response = await state.sdk.payload.create(payload);

      // Store payment details
      setPaymentState(prev => ({
        ...prev,
        isProcessing: false,
        lastPayment: {
          uuid: response.uuid,
          amount,
          currency: 'XRP',
          destination,
          timestamp: Date.now(),
        },
      }));

      return {
        success: true,
        payloadUuid: response.uuid,
        deepLink: response.next?.always,
        qrCode: response.refs?.qr_png,
      };

    } catch (error) {
      console.error('XRP payment creation failed:', error);
      setPaymentState(prev => ({ ...prev, isProcessing: false }));
      return { success: false, error: 'Failed to create XRP payment' };
    }
  }, [xummPkce, isConnected, walletAddress]);

  // Create FUSE token payment
  const createFUSEPayment = useCallback(async (
    destination: string,
    amount: string, // Amount in FUSE tokens
    options: PaymentOptions = {}
  ): Promise<PaymentResult> => {
    if (!xummPkce || !isConnected) {
      return { success: false, error: 'Wallet not connected' };
    }

    if (!walletAddress) {
      return { success: false, error: 'No wallet address available' };
    }

    try {
      setPaymentState(prev => ({ ...prev, isProcessing: true }));

      const payload: XRPPaymentPayload = {
        txjson: {
          TransactionType: 'Payment',
          Destination: destination,
          Amount: {
            currency: FUSE_TOKEN.currency,
            issuer: FUSE_TOKEN.issuer,
            value: amount,
          },
        },
        options: {
          return_url: options.returnUrl ? {
            app: options.returnUrl,
            web: options.returnUrl,
          } : {
            app: XAMAN_CONFIG.getRedirectUrl(`${XAMAN_CONFIG.REDIRECT_URLS.PAYMENT_SUCCESS}?type=fuse&amount=${amount}&destination=${destination}`),
            web: XAMAN_CONFIG.getRedirectUrl(`${XAMAN_CONFIG.REDIRECT_URLS.PAYMENT_SUCCESS}?type=fuse&amount=${amount}&destination=${destination}`),
          },
          force_network: 'MAINNET',
        },
        custom_meta: {
          identifier: options.identifier || `fuse_payment_${Date.now()}`,
          instruction: options.instruction || `Send ${amount} FUSE tokens to complete your purchase`,
          blob: {
            appName: '$Fuse Rewards',
            appIcon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png',
          },
        },
      };

      // Add optional fields
      if (options.destinationTag) {
        payload.txjson.DestinationTag = options.destinationTag;
      }

      if (options.fee) {
        payload.txjson.Fee = options.fee;
      }

      if (options.sendMax) {
        payload.txjson.SendMax = options.sendMax;
      }

      if (options.deliverMin) {
        payload.txjson.DeliverMin = options.deliverMin;
      }

      // Add memo if provided
      if (options.memo) {
        payload.txjson.Memos = [{
          Memo: {
            MemoType: Buffer.from('memo').toString('hex').toUpperCase(),
            MemoData: Buffer.from(options.memo).toString('hex').toUpperCase(),
          },
        }];
      }

      // Get SDK from OAuth2 PKCE state
      const state = await xummPkce.state();
      if (!state?.sdk) {
        return { success: false, error: 'SDK not available' };
      }

      const response = await state.sdk.payload.create(payload);

      // Store payment details
      setPaymentState(prev => ({
        ...prev,
        isProcessing: false,
        lastPayment: {
          uuid: response.uuid,
          amount,
          currency: 'FUSE',
          destination,
          timestamp: Date.now(),
        },
      }));

      return {
        success: true,
        payloadUuid: response.uuid,
        deepLink: response.next?.always,
        qrCode: response.refs?.qr_png,
      };

    } catch (error) {
      console.error('FUSE payment creation failed:', error);
      setPaymentState(prev => ({ ...prev, isProcessing: false }));
      return { success: false, error: 'Failed to create FUSE payment' };
    }
  }, [xummPkce, isConnected, walletAddress]);

  // Generic payment creator - automatically detects currency type
  const createPayment = useCallback(async (
    destination: string,
    amount: string,
    currency: 'XRP' | 'FUSE' = 'XRP',
    options: PaymentOptions = {}
  ): Promise<PaymentResult> => {
    if (currency === 'XRP') {
      return createXRPPayment(destination, amount, options);
    } else {
      return createFUSEPayment(destination, amount, options);
    }
  }, [createXRPPayment, createFUSEPayment]);

  // Check payment status by UUID
  const checkPaymentStatus = useCallback(async (uuid: string): Promise<{
    status: 'pending' | 'signed' | 'expired' | 'error';
    txHash?: string;
    error?: string;
  }> => {
    try {
      const response = await fetch(`/api/xumm-payload?uuid=${uuid}`);
      if (!response.ok) {
        throw new Error('Failed to fetch payment status');
      }

      const data = await response.json();
      
      if (data.payloadData?.meta?.signed === true) {
        return {
          status: 'signed',
          txHash: data.payloadData.response?.txid,
        };
      } else if (data.payloadData?.meta?.expired === true) {
        return { status: 'expired' };
      } else {
        return { status: 'pending' };
      }
    } catch (error) {
      console.error('Error checking payment status:', error);
      return { status: 'error', error: 'Failed to check payment status' };
    }
  }, []);

  // Clear payment state
  const clearPaymentState = useCallback(() => {
    setPaymentState({ isProcessing: false });
  }, []);

  return {
    // Payment creation methods
    createXRPPayment,
    createFUSEPayment,
    createPayment,
    
    // Payment status methods
    checkPaymentStatus,
    clearPaymentState,
    
    // State
    isProcessing: paymentState.isProcessing,
    lastPayment: paymentState.lastPayment,
    
    // Wallet state
    isConnected,
    walletAddress,
    
    // Utilities
    dropsToXRP,
    xrpToDrops,
  };
}