"use client"

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { createClient } from '@/utils/supabase/client'
import { walletService, type WalletConnectionResult } from '@/lib/wallet-service'

declare global {
  interface Window {
    XummPkce: any;
  }
}

export function useSimpleWallet() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');
  const [xummPkce, setXummPkce] = useState<any>(null);
  const [sdkReady, setSdkReady] = useState(false);

  // Load XUMM OAuth2 PKCE SDK
  useEffect(() => {
    const loadXummSDK = () => {
      if (window.XummPkce) {
        console.log('✅ XUMM PKCE already loaded');
        setSdkReady(true);
        return;
      }

      console.log('📦 Loading XUMM OAuth2 PKCE SDK...');
      const script = document.createElement('script');
      script.src = 'https://xumm.app/assets/cdn/xumm-oauth2-pkce.min.js?v=2.7.1';
      script.async = true;

      script.onload = () => {
        console.log('✅ XUMM OAuth2 PKCE SDK loaded successfully');
        if (window.XummPkce) {
          setSdkReady(true);
        } else {
          console.error('❌ XUMM OAuth2 PKCE SDK loaded but XummPkce not found on window');
        }
      };
      script.onerror = (error) => {
        console.error('❌ Failed to load XUMM OAuth2 PKCE SDK:', error);
        alert('Failed to load wallet service. Please check your internet connection and refresh the page.');
      };
      document.head.appendChild(script);
    };

    loadXummSDK();
  }, []);

  // Initialize XUMM PKCE when SDK is ready
  useEffect(() => {
    if (!sdkReady || xummPkce) return;

    try {
      // Use environment variable for API key
      const apiKey = process.env.NEXT_PUBLIC_XAMAN_API_KEY;
      console.log('🔍 Environment variable check:', {
        apiKey: apiKey ? `${apiKey.substring(0, 8)}...` : 'NOT FOUND',
        allEnvKeys: Object.keys(process.env).filter(key => key.includes('XAMAN')),
        NODE_ENV: process.env.NODE_ENV
      });
      
      if (!apiKey) {
        console.error('❌ Missing XAMAN API key in environment variables');
        console.error('Available environment variables:', Object.keys(process.env));
        alert('Wallet service not configured. Please contact support.');
        return;
      }

      const redirectUrl = typeof window !== 'undefined'
        ? `${window.location.origin}/wallet-connected`
        : 'https://fuse.vip/wallet-connected';

      console.log('🔧 Initializing XUMM PKCE with:', {
        apiKey: apiKey.substring(0, 8) + '...',
        redirectUrl
      });

      const pkce = new window.XummPkce(apiKey, {
        implicit: true,
        redirectUrl: redirectUrl
      });

      console.log('✅ XUMM PKCE initialized successfully');
      setXummPkce(pkce);
      
      // Test the PKCE instance
      if (typeof pkce.state !== 'function') {
        console.error('❌ XUMM PKCE instance is missing required methods');
        return;
      }

      // Check if already connected
      pkce.state().then(async (state: any) => {
        console.log('🔍 Checking existing wallet state:', state);
        if (state?.me?.sub) {
          console.log('✅ Found existing wallet connection:', state.me.sub);
          setWalletAddress(state.me.sub);
          setIsConnected(true);

          // Save to profile if user is logged in
          if (user?.id) {
            const saveResult = await saveWalletToProfile(state.me.sub);
            if (!saveResult.success) {
              console.error('❌ Failed to save wallet to profile:', saveResult.error);
              // Don't fail the whole connection, but log the error
            }
          }
        } else {
          console.log('ℹ️ No existing wallet connection found');
        }
      }).catch((error: any) => {
        console.error('❌ Error checking wallet state:', error);
      });

      // Listen for authorization events
      pkce.on('success', () => {
        console.log('🎉 Wallet authorization successful, fetching wallet state...');
        pkce.state().then(async (state: any) => {
          if (state?.me?.sub) {
            console.log('✅ Wallet connected successfully:', {
              address: state.me.sub,
              userAuthenticated: !!user?.id,
              userEmail: user?.email
            });

            setWalletAddress(state.me.sub);
            setIsConnected(true);
            setIsLoading(false);

            // Save to profile if user is logged in
            if (user?.id) {
              console.log('💾 Saving wallet to authenticated user profile...');
              const saveResult = await saveWalletToProfile(state.me.sub);
              if (!saveResult.success) {
                console.error('❌ Failed to save wallet to profile:', saveResult.error);
                // Don't fail the whole connection, but log the error
              }
            } else {
              console.warn('⚠️ Wallet connected but user not authenticated - cannot save to profile');
            }
          }
        }).catch((error: any) => {
          console.error('❌ Error fetching wallet state after success:', error);
          setIsLoading(false);
        });
      });

      pkce.on('error', (error: any) => {
        console.error('XUMM authorization error:', error);
        setIsLoading(false);
      });

    } catch (error) {
      console.error('❌ Failed to initialize XUMM PKCE:', error);
      alert('Failed to initialize wallet service. Please refresh the page and try again.');
    }
  }, [sdkReady, user?.id]);

  const saveWalletToProfile = async (address: string): Promise<WalletConnectionResult> => {
    if (!user?.id) {
      console.warn('⚠️ Cannot save wallet to profile: User not authenticated');
      return { success: false, error: 'User not authenticated' };
    }

    console.log('💾 Saving wallet address to profile using centralized service:', {
      userId: user.id,
      userEmail: user.email,
      walletAddress: `${address.substring(0, 6)}...${address.substring(address.length - 4)}`
    });

    try {
      // Use the centralized wallet service
      const result = await walletService.connectWallet(user.id, address, 'xrp');
      
      if (result.success) {
        console.log('✅ Wallet address successfully saved to profile via service');
      } else {
        console.error('❌ Failed to save wallet via service:', result.error);
      }
      
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Exception saving wallet to profile:', error);
      return { success: false, error: errorMessage };
    }
  };

  const connect = useCallback(async () => {
    // Check if user is authenticated first
    if (!user) {
      console.error('🚫 Wallet connection requires authentication. Please sign in first.');
      setIsLoading(false);
      // Show user-friendly error instead of throwing
      alert('Please sign in to your account before connecting your wallet.');
      return false;
    }

    if (!xummPkce) {
      console.error('🚫 XUMM PKCE not initialized');
      setIsLoading(false);
      alert('Wallet service not ready. Please refresh the page and try again.');
      return false;
    }

    console.log('🔗 Starting wallet connection for authenticated user:', user.email);
    setIsLoading(true);

    try {
      // Store user context for OAuth callback to use if authentication context is lost
      console.log('💾 Storing user context for wallet flow...');
      localStorage.setItem('wallet_flow_user_context', JSON.stringify({
        userId: user.id,
        userEmail: user.email,
        timestamp: new Date().toISOString()
      }));

      await xummPkce.authorize();
      console.log('✅ Wallet authorization initiated successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to connect wallet:', error);
      setIsLoading(false);
      // Clean up stored context on failure
      localStorage.removeItem('wallet_flow_user_context');
      alert('Failed to connect wallet. Please try again.');
      return false;
    }
  }, [xummPkce, user]);

  const disconnect = useCallback(async () => {
    if (!xummPkce) return;

    try {
      await xummPkce.logout();
      setWalletAddress('');
      setIsConnected(false);
      
      // Also disconnect from our service if user is authenticated
      if (user?.id) {
        const disconnectResult = await walletService.disconnectWallet(user.id, 'xrp');
        if (!disconnectResult.success) {
          console.error('❌ Failed to disconnect wallet from service:', disconnectResult.error);
        }
      }
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
    }
  }, [xummPkce, user?.id]);

  const createPayment = useCallback(async (destination: string, amount: string) => {
    if (!xummPkce || !isConnected) {
      throw new Error('Wallet not connected');
    }

    const state = await xummPkce.state();
    if (!state?.sdk) {
      throw new Error('SDK not available');
    }

    const payload = await state.sdk.payload.create({
      TransactionType: 'Payment',
      Destination: destination,
      Amount: amount,
      Memos: [{
        Memo: {
          MemoType: Buffer.from('fuse-vip-payment', 'utf8').toString('hex').toUpperCase(),
          MemoData: Buffer.from(JSON.stringify({
            service: 'fuse-vip',
            timestamp: Date.now()
          }), 'utf8').toString('hex').toUpperCase()
        }
      }]
    });

    return payload;
  }, [xummPkce, isConnected]);

  const createTrustline = useCallback(async (currency: string = 'FUSE', issuer?: string) => {
    if (!xummPkce || !isConnected) {
      throw new Error('Wallet not connected');
    }

    const state = await xummPkce.state();
    if (!state?.sdk) {
      throw new Error('SDK not available');
    }

    const payload = await state.sdk.payload.create({
      TransactionType: 'TrustSet',
      LimitAmount: {
        currency: currency,
        issuer: issuer || 'rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU', // Treasury wallet
        value: '1000000000'
      }
    });

    return payload;
  }, [xummPkce, isConnected]);

  return {
    isLoading,
    isConnected,
    walletAddress,
    xummPkce,
    sdkReady,
    connect,
    disconnect,
    createPayment,
    createTrustline,
  };
}
