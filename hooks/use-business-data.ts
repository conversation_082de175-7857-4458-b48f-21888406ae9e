"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { withErrorHandling, setCachedData, getCachedDataWithFallback } from "@/lib/error-handling"

interface BusinessData {
  id: string
  name: string
  logo_url?: string
  website?: string
  category?: string
  premium_discount?: number
  is_active: boolean
  business_spotlight?: boolean
  contact_info?: any
  business_address?: string
  created_at: string
}

interface UseBusinessDataReturn {
  businesses: BusinessData[]
  userBusiness: BusinessData | null
  hasUserBusiness: boolean
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

/**
 * Hook for fetching user's business data with lazy loading and error handling
 */
export function useBusinessData(): UseBusinessDataReturn {
  const { user } = useAuth()
  const [businesses, setBusinesses] = useState<BusinessData[]>([])
  const [userBusiness, setUserBusiness] = useState<BusinessData | null>(null)
  const [hasUserBusiness, setHasUserBusiness] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchBusinessData = async () => {
    if (!user?.id) return

    setIsLoading(true)
    setError(null)

    try {
      // Check cache first
      const cachedUserBusiness = getCachedDataWithFallback<BusinessData>(`user_business_${user.id}`, 300000) // 5 minutes
      const cachedHasBusiness = getCachedDataWithFallback<boolean>(`has_business_${user.id}`, 300000)

      if (cachedUserBusiness !== null && cachedHasBusiness !== null) {
        setUserBusiness(cachedUserBusiness)
        setHasUserBusiness(cachedHasBusiness)
        setIsLoading(false)
        return
      }

      // First, check if user has any business (minimal query)
      const hasBusiness = await withErrorHandling(
        async () => {
          const response = await fetch(`/api/check-user-business?user_id=${user.id}`)
          const result = await response.json()
          return response.ok && result.success ? result.data : false
        },
        false,
        'check user business'
      )

      setHasUserBusiness(hasBusiness)
      setCachedData(`has_business_${user.id}`, hasBusiness)

      // Only fetch full business data if user has a business
      if (hasBusiness) {
        const businessData = await withErrorHandling(
          async () => {
            const response = await fetch(`/api/user-businesses?user_id=${user.id}`)
            const result = await response.json()
            return response.ok && result.success ? result.data : []
          },
          [],
          'fetch user businesses'
        )

        const primaryBusiness = businessData && businessData.length > 0 ? businessData[0] : null
        setUserBusiness(primaryBusiness)
        setCachedData(`user_business_${user.id}`, primaryBusiness)
      } else {
        setUserBusiness(null)
        setCachedData(`user_business_${user.id}`, null)
      }

    } catch (err: any) {
      console.error('Error fetching business data:', err)
      setError(err.message || 'Failed to load business data')
      
      // Try to use cached data even if expired
      const fallbackBusiness = getCachedDataWithFallback<BusinessData>(`user_business_${user.id}`, 86400000) // 24 hours
      const fallbackHasBusiness = getCachedDataWithFallback<boolean>(`has_business_${user.id}`, 86400000)
      
      if (fallbackBusiness !== null) {
        setUserBusiness(fallbackBusiness)
      }
      if (fallbackHasBusiness !== null) {
        setHasUserBusiness(fallbackHasBusiness)
      }
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (user?.id) {
      fetchBusinessData()
    }
  }, [user?.id])

  return {
    businesses,
    userBusiness,
    hasUserBusiness,
    isLoading,
    error,
    refetch: fetchBusinessData
  }
}

/**
 * Hook for fetching all active businesses (for public display)
 */
export function useActiveBusinesses() {
  const [businesses, setBusinesses] = useState<BusinessData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchActiveBusinesses = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Check cache first
      const cachedBusinesses = getCachedDataWithFallback<BusinessData[]>('active_businesses', 600000) // 10 minutes
      if (cachedBusinesses) {
        setBusinesses(cachedBusinesses)
        setIsLoading(false)
        return
      }

      const businessData = await withErrorHandling(
        async () => {
          // Use consolidated API with standard fields
          let response = await fetch('/api/businesses?fields=standard')
          let result = await response.json()

          if (response.ok && result.data) {
            return result.data
          }

          // Fallback to minimal fields if standard fails
          console.warn('Standard fields failed, falling back to minimal fields')
          response = await fetch('/api/businesses?fields=minimal')
          result = await response.json()
          return response.ok && result.data ? result.data : []
        },
        [],
        'fetch active businesses'
      )

      setBusinesses(businessData || [])
      setCachedData('active_businesses', businessData || [])

    } catch (err: any) {
      console.error('Error fetching active businesses:', err)
      setError(err.message || 'Failed to load businesses')
      
      // Try to use cached data even if expired
      const fallbackBusinesses = getCachedDataWithFallback<BusinessData[]>('active_businesses', 86400000) // 24 hours
      if (fallbackBusinesses) {
        setBusinesses(fallbackBusinesses)
      }
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchActiveBusinesses()
  }, [])

  return {
    businesses,
    isLoading,
    error,
    refetch: fetchActiveBusinesses
  }
}

/**
 * Hook for checking if user is a business owner (minimal query)
 */
export function useIsBusinessOwner() {
  const { user } = useAuth()
  const [isBusinessOwner, setIsBusinessOwner] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const checkBusinessOwner = async () => {
      if (!user?.id) {
        setIsBusinessOwner(false)
        return
      }

      setIsLoading(true)

      try {
        // Check cache first
        const cached = getCachedDataWithFallback<boolean>(`is_business_owner_${user.id}`, 300000) // 5 minutes
        if (cached !== null) {
          setIsBusinessOwner(cached)
          setIsLoading(false)
          return
        }

        const hasBusiness = await withErrorHandling(
          async () => {
            const response = await fetch(`/api/check-user-business?user_id=${user.id}`)
            const result = await response.json()
            return response.ok && result.success ? result.data : false
          },
          false,
          'check business owner status'
        )

        setIsBusinessOwner(hasBusiness)
        setCachedData(`is_business_owner_${user.id}`, hasBusiness)

      } catch (err) {
        console.error('Error checking business owner status:', err)
        setIsBusinessOwner(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkBusinessOwner()
  }, [user?.id])

  return { isBusinessOwner, isLoading }
}
