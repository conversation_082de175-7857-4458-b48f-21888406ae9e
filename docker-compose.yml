version: '3.8'

services:
  # Main Next.js application
  fuse-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    container_name: fuse-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - NEXT_TELEMETRY_DISABLED=1
    env_file:
      - .env.local
    volumes:
      # Persistent storage for uploads
      - app_uploads:/app/uploads
      # Cache volume for better performance
      - app_cache:/app/.next/cache
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - fuse-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    depends_on:
      - logo-processor

  # Logo processing service
  logo-processor:
    build:
      context: ./docker/logo-processor
      dockerfile: Dockerfile
      target: runtime
    container_name: fuse-logo-processor
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - ALLOWED_ORIGINS=http://localhost:3000,https://fuse.vip,https://www.fuse.vip
    volumes:
      # Persistent storage for processed logos
      - logo_storage:/app/processed
      # Temporary upload storage
      - logo_temp:/app/temp
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - fuse-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis for caching (optional, for production scaling)
  redis:
    image: redis:7-alpine
    container_name: fuse-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - fuse-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: fuse-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - logo_storage:/var/www/logos:ro
    depends_on:
      - logo-processor
    restart: unless-stopped
    networks:
      - fuse-network
    profiles:
      - production

volumes:
  # Application volumes
  app_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./docker/volumes/uploads
  app_cache:
    driver: local

  # Logo processor volumes
  logo_storage:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./docker/volumes/logos
  logo_temp:
    driver: local

  # Redis data
  redis_data:
    driver: local

networks:
  fuse-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: fuse-br0
