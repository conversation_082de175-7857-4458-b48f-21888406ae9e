import { test, expect } from '@playwright/test';

test.describe('Dashboard Business Access Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
  });

  test('should not immediately redirect to login when accessing /dashboard/business', async ({ page }) => {
    // Navigate directly to dashboard/business
    await page.goto('/dashboard/business');
    
    // Wait a moment for any immediate redirects
    await page.waitForTimeout(2000);
    
    // Should not be immediately redirected to login
    const currentUrl = page.url();
    expect(currentUrl).not.toContain('/login');
    
    // Should show loading state or content, not immediate redirect
    const loadingIndicator = page.locator('text="Loading", text="Authenticating", [class*="animate-spin"]');
    const businessContent = page.locator('text="Business", text="Dashboard"');
    
    const hasLoadingOrContent = await loadingIndicator.isVisible() || await businessContent.isVisible();
    expect(hasLoadingOrContent).toBe(true);
  });

  test('should handle authentication gracefully with retry mechanism', async ({ page }) => {
    // Listen for network requests to the business access API
    const apiRequests: string[] = [];
    page.on('request', request => {
      if (request.url().includes('/api/business-access-optimized')) {
        apiRequests.push(request.url());
      }
    });

    // Navigate to dashboard/business
    await page.goto('/dashboard/business');
    
    // Wait for the page to attempt authentication
    await page.waitForTimeout(5000);
    
    // Should have made at least one request to the business access API
    expect(apiRequests.length).toBeGreaterThan(0);
    
    // Check that the page is not stuck in an error state
    const errorMessages = page.locator('text="Authentication required", text="Auth session missing"');
    const isErrorVisible = await errorMessages.isVisible();
    
    // If there are errors, they should be temporary (retry mechanism should handle them)
    if (isErrorVisible) {
      // Wait for retry
      await page.waitForTimeout(3000);
      
      // Error should be resolved or page should show proper loading state
      const stillHasError = await errorMessages.isVisible();
      const hasLoading = await page.locator('text="Loading", [class*="animate-spin"]').isVisible();
      
      expect(stillHasError && !hasLoading).toBe(false);
    }
  });

  test('should show proper loading states during authentication', async ({ page }) => {
    // Navigate to dashboard/business
    await page.goto('/dashboard/business');
    
    // Should show loading state initially
    const loadingStates = page.locator('text="Loading Dashboard", text="Authenticating", text="Loading business data"');
    
    // Wait for loading state to appear
    await expect(loadingStates.first()).toBeVisible({ timeout: 3000 });
    
    // Loading state should have proper styling
    const loadingContainer = page.locator('[class*="animate-spin"], [class*="animate-pulse"]');
    await expect(loadingContainer.first()).toBeVisible();
  });

  test('should handle API errors without crashing', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Navigate to dashboard/business
    await page.goto('/dashboard/business');
    
    // Wait for the page to load and handle any API calls
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Check that there are no unhandled JavaScript errors
    const criticalErrors = consoleErrors.filter(error => 
      !error.includes('Failed to fetch') && // Network errors are expected in tests
      !error.includes('NetworkError') &&
      !error.includes('ERR_INTERNET_DISCONNECTED')
    );
    
    expect(criticalErrors).toHaveLength(0);
  });

  test('should not redirect to login for authenticated users', async ({ page }) => {
    // Mock authentication by setting up session storage/cookies
    await page.goto('/');
    
    // Try to simulate an authenticated state
    await page.evaluate(() => {
      // Set some mock auth data
      localStorage.setItem('supabase.auth.token', 'mock-token');
    });
    
    // Navigate to dashboard/business
    await page.goto('/dashboard/business');
    
    // Wait for authentication check
    await page.waitForTimeout(4000);
    
    // Should not redirect to login if properly authenticated
    const currentUrl = page.url();
    expect(currentUrl).toContain('/dashboard/business');
  });

  test('should handle business data fetch with proper error handling', async ({ page }) => {
    // Listen for API responses
    const apiResponses: any[] = [];
    page.on('response', response => {
      if (response.url().includes('/api/dashboard/business')) {
        apiResponses.push({
          url: response.url(),
          status: response.status(),
          ok: response.ok()
        });
      }
    });

    // Navigate to dashboard/business
    await page.goto('/dashboard/business');
    
    // Wait for API calls to complete
    await page.waitForTimeout(5000);
    
    // Should have attempted to fetch business data
    expect(apiResponses.length).toBeGreaterThan(0);
    
    // Check the page state based on API response
    if (apiResponses.some(r => r.ok)) {
      // If API succeeded, should show business content or registration form
      const businessContent = page.locator('text="Business Dashboard", text="Register your business"');
      await expect(businessContent.first()).toBeVisible({ timeout: 5000 });
    } else {
      // If API failed, should show appropriate error handling or retry
      const errorHandling = page.locator('text="Loading", text="Retry", [class*="animate-spin"]');
      await expect(errorHandling.first()).toBeVisible();
    }
  });

  test('should maintain consistent UI state during authentication flow', async ({ page }) => {
    // Navigate to dashboard/business
    await page.goto('/dashboard/business');
    
    // Check initial state
    await page.waitForTimeout(1000);
    
    // Should not show multiple conflicting states
    const loadingElements = page.locator('[class*="animate-spin"], text="Loading"');
    const errorElements = page.locator('text="Error", text="Failed"');
    const contentElements = page.locator('text="Business Dashboard", text="Register"');
    
    const loadingCount = await loadingElements.count();
    const errorCount = await errorElements.count();
    const contentCount = await contentElements.count();
    
    // Should have a clear, single state
    const totalVisibleStates = loadingCount + errorCount + contentCount;
    expect(totalVisibleStates).toBeGreaterThan(0); // Should show something
    expect(totalVisibleStates).toBeLessThan(5); // Should not show too many conflicting elements
  });

  test('should handle network connectivity issues gracefully', async ({ page }) => {
    // Navigate to dashboard/business
    await page.goto('/dashboard/business');
    
    // Simulate network issues by going offline
    await page.context().setOffline(true);
    
    // Wait a moment
    await page.waitForTimeout(2000);
    
    // Go back online
    await page.context().setOffline(false);
    
    // Wait for recovery
    await page.waitForTimeout(3000);
    
    // Page should recover gracefully
    const errorState = page.locator('text="Network error", text="Connection failed"');
    const recoveryState = page.locator('text="Loading", text="Syncing", [class*="animate-spin"]');
    
    // Should either show recovery or have recovered
    const hasRecoveryIndicator = await errorState.isVisible() || await recoveryState.isVisible();
    
    // Page should not be completely broken
    const pageTitle = await page.title();
    expect(pageTitle).toBeTruthy();
  });
});
