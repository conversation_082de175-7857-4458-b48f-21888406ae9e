import { test, expect } from '@playwright/test';

test.describe('Wallet Connection Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('/');
  });

  test('should initialize XUMM SDK without errors', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Navigate to a page that uses wallet functionality
    await page.goto('/fuse');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check that there are no XUMM SDK initialization errors
    const xummErrors = consoleErrors.filter(error => 
      error.includes('XUMM SDK not initialized') || 
      error.includes('Failed to initialize XUMM SDK')
    );
    
    expect(xummErrors).toHaveLength(0);
  });

  test('should show wallet connect button when not connected', async ({ page }) => {
    // Navigate to a page with wallet functionality
    await page.goto('/fuse');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Look for wallet connect button or component
    const walletButton = page.locator('button:has-text("Connect"), button:has-text("Wallet")').first();
    
    // Should be visible and not disabled due to SDK not being initialized
    if (await walletButton.isVisible()) {
      const isDisabled = await walletButton.isDisabled();
      expect(isDisabled).toBe(false);
    }
  });

  test('should handle wallet connection click without SDK errors', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Navigate to a page with wallet functionality
    await page.goto('/fuse');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Wait a bit for SDK to initialize
    await page.waitForTimeout(2000);
    
    // Try to find and click a wallet connect button
    const walletButton = page.locator('button:has-text("Connect Xaman"), button:has-text("Connect Wallet")').first();
    
    if (await walletButton.isVisible()) {
      await walletButton.click();
      
      // Wait a moment for any errors to appear
      await page.waitForTimeout(1000);
      
      // Check that clicking didn't cause SDK initialization errors
      const sdkErrors = consoleErrors.filter(error => 
        error.includes('XUMM SDK not initialized') || 
        error.includes('Xaman SDK not initialized')
      );
      
      expect(sdkErrors).toHaveLength(0);
    }
  });

  test('should load wallet components without crashing', async ({ page }) => {
    // Navigate to upgrade page which has wallet components
    await page.goto('/upgrade');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check that the page loaded successfully
    await expect(page).toHaveTitle(/Fuse/);
    
    // Look for wallet-related elements
    const walletElements = page.locator('[data-testid*="wallet"], [class*="wallet"], button:has-text("Xaman")');
    
    // If wallet elements exist, they should be visible
    const count = await walletElements.count();
    if (count > 0) {
      await expect(walletElements.first()).toBeVisible();
    }
  });

  test('should handle multiple wallet implementations gracefully', async ({ page }) => {
    // Listen for console messages
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      consoleMessages.push(`${msg.type()}: ${msg.text()}`);
    });

    // Navigate to a page that might use multiple wallet implementations
    await page.goto('/xaman-demo');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Wait for SDK initialization
    await page.waitForTimeout(3000);
    
    // Check for successful initialization messages
    const initMessages = consoleMessages.filter(msg => 
      msg.includes('XUMM SDK ready') || 
      msg.includes('Ready (e.g. hide loading state)')
    );
    
    // Should have at least one successful initialization
    expect(initMessages.length).toBeGreaterThan(0);
  });

  test('should not show conflicting wallet states', async ({ page }) => {
    // Navigate to a page with wallet functionality
    await page.goto('/fuse');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Wait for components to initialize
    await page.waitForTimeout(2000);
    
    // Check that we don't have multiple conflicting wallet states
    const connectButtons = page.locator('button:has-text("Connect")');
    const loadingButtons = page.locator('button:has-text("Loading")');
    const connectedIndicators = page.locator('text="Connected", text="Wallet connected"');
    
    const connectCount = await connectButtons.count();
    const loadingCount = await loadingButtons.count();
    const connectedCount = await connectedIndicators.count();
    
    // Should not have multiple conflicting states visible simultaneously
    const totalStates = connectCount + loadingCount + connectedCount;
    expect(totalStates).toBeLessThanOrEqual(2); // Allow some reasonable overlap
  });
});
