'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { XummPayment } from '@/components/xumm/xumm-payment';
import { useXamanWallet } from '@/hooks/use-xaman-wallet';
import { useAuth } from '@/contexts/auth-context';
import { Badge } from '@/components/ui/badge';
import { Wallet, ExternalLink } from 'lucide-react';

export default function XummTestPage() {
  const { user } = useAuth();
  const {
    isConnected,
    walletAddress,
    isLoading,
    connect,
    disconnect,
  } = useXamanWallet();

  // Mock the missing properties for compatibility
  const isInitialized = true;
  const account = walletAddress ? { address: walletAddress, balance: '0' } : null;
  const error = null;
  const formatBalance = (balance: string) => balance;
  
  const [showPayment, setShowPayment] = useState(false);
  const [paymentData, setPaymentData] = useState({
    destination: 'rDNvpGy5zAw6JBFxBP9d8KMQFqG8VKfKHU',
    amount: '1',
    memo: 'Test payment from Fuse.vip'
  });

  const handlePaymentSuccess = (txHash: string) => {
    console.log('Payment successful:', txHash);
    setShowPayment(false);
  };

  const handlePaymentError = (error: Error) => {
    console.error('Payment error:', error);
    setShowPayment(false);
  };

  const handlePaymentCancel = () => {
    console.log('Payment cancelled');
    setShowPayment(false);
  };

  if (!user) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>XUMM Integration Test</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Please sign in to test XUMM integration.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">XUMM Integration Test</h1>
        <div className="flex items-center gap-2">
          <Badge variant={isInitialized ? 'default' : 'secondary'}>
            {isInitialized ? 'SDK Ready' : 'Loading SDK...'}
          </Badge>
          <Badge variant={isConnected ? 'default' : 'outline'}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </Badge>
        </div>
      </div>

      {error && (
        <Card>
          <CardContent className="pt-6">
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-700">Error: {error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Wallet Connection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            Wallet Connection
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isConnected && account ? (
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-md">
                <p className="text-sm text-gray-600">Connected Address:</p>
                <p className="font-mono text-sm break-all">{account.address}</p>
              </div>
              <div className="p-4 bg-gray-50 rounded-md">
                <p className="text-sm text-gray-600">Balance:</p>
                <p className="text-lg font-bold">{formatBalance(account.balance)} XRP</p>
              </div>
              <Button 
                onClick={disconnect} 
                variant="destructive"
                disabled={isLoading}
              >
                {isLoading ? 'Disconnecting...' : 'Disconnect Wallet'}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-gray-600">Connect your XUMM wallet to start using XRPL features.</p>
              <Button 
                onClick={connect} 
                disabled={isLoading || !isInitialized}
              >
                {isLoading ? 'Connecting...' : 'Connect XUMM Wallet'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Testing */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Testing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!showPayment ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="destination">Destination Address</Label>
                  <Input
                    id="destination"
                    value={paymentData.destination}
                    onChange={(e) => setPaymentData({...paymentData, destination: e.target.value})}
                    placeholder="rXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
                  />
                </div>
                <div>
                  <Label htmlFor="amount">Amount (XRP)</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.000001"
                    value={paymentData.amount}
                    onChange={(e) => setPaymentData({...paymentData, amount: e.target.value})}
                    placeholder="1.000000"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="memo">Memo (optional)</Label>
                <Input
                  id="memo"
                  value={paymentData.memo}
                  onChange={(e) => setPaymentData({...paymentData, memo: e.target.value})}
                  placeholder="Payment memo"
                />
              </div>
              <Button 
                onClick={() => setShowPayment(true)}
                disabled={!isConnected || !paymentData.destination || !paymentData.amount}
              >
                Create Test Payment
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <XummPayment
                destination={paymentData.destination}
                amount={paymentData.amount}
                memo={paymentData.memo}
                description="Test payment from Fuse.vip"
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onCancel={handlePaymentCancel}
              />
              <Button 
                onClick={() => setShowPayment(false)}
                variant="outline"
              >
                Cancel Payment
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Links and Documentation */}
      <Card>
        <CardHeader>
          <CardTitle>Resources</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex items-center gap-2">
            <ExternalLink className="h-4 w-4" />
            <a 
              href="https://xumm.readme.io/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              XUMM SDK Documentation
            </a>
          </div>
          <div className="flex items-center gap-2">
            <ExternalLink className="h-4 w-4" />
            <a 
              href="https://xrpl.org/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              XRPL Documentation
            </a>
          </div>
          <div className="flex items-center gap-2">
            <ExternalLink className="h-4 w-4" />
            <a 
              href="https://xumm.app/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              XUMM App
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}