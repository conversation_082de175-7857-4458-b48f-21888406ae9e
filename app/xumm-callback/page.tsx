'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { getSupabaseClient } from '@/lib/supabase';
import { profileCache } from '@/lib/profile-cache';

function XummCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing wallet connection...');
  const [walletAddress, setWalletAddress] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get URL parameters
        const txid = searchParams.get('txid');
        const result = searchParams.get('result');
        const uuid = searchParams.get('uuid');
        const type = searchParams.get('type');
        const isMobile = searchParams.get('mobile') === 'true';

        console.log('Processing callback with params:', { txid, result, uuid, type, isMobile });

        if (result === 'tesSUCCESS' && txid) {
          setMessage('Fetching wallet address...');
          
          // Fetch transaction details to get the wallet address
          const xrpAddress = await fetchWalletAddressFromTransaction(txid, uuid);
          
          if (xrpAddress) {
            console.log('XRP Address found:', xrpAddress);
            setWalletAddress(xrpAddress);
            
            // Update local storage immediately
            localStorage.setItem('fuse_wallet_address', xrpAddress);
            localStorage.setItem('fuse_wallet_connected', 'true');
            
            // Update profile in database if user is authenticated
            if (user?.id) {
              await updateUserProfile(user.id, xrpAddress);
            }
            
            setStatus('success');
            setMessage('Wallet connected successfully!');
            
            // Clean up stored UUID
            localStorage.removeItem('xaman_payload_uuid');
            
            // Redirect to dashboard after a short delay
            setTimeout(() => {
              router.push('/dashboard');
            }, 2000);
          } else {
            throw new Error('Could not retrieve wallet address from transaction');
          }
        } else if (result === 'rejected') {
          setStatus('error');
          setMessage('Connection was rejected. Please try again.');
        } else if (result === 'cancelled') {
          setStatus('error');
          setMessage('Connection was cancelled. Please try again.');
        } else {
          setStatus('error');
          setMessage('Connection failed. Please try again.');
        }
      } catch (error) {
        console.error('Callback processing error:', error);
        setStatus('error');
        setMessage('An error occurred while processing the connection.');
      }
    };

    const fetchWalletAddressFromTransaction = async (txid: string, uuid?: string | null): Promise<string | null> => {
      try {
        // Try to get address from XUMM API first using the payload UUID
        if (uuid) {
          const payloadResponse = await fetch(`/api/xumm-payload?uuid=${uuid}`);
          
          if (payloadResponse.ok) {
            const payloadData = await payloadResponse.json();
            console.log('Payload data:', payloadData);
            
            // Extract account from payload response
            if (payloadData.walletAddress) {
              return payloadData.walletAddress;
            }
          }
        }
        
        // Fallback: Try to get from XRPL transaction (if available)
        // This requires additional XRPL API calls which we'll skip for now
        // In a production app, you'd query the XRPL ledger directly
        
        // As a last resort, check if we have the address in localStorage from SDK
        const savedAddress = localStorage.getItem('fuse_wallet_address');
        if (savedAddress) {
          return savedAddress;
        }
        
        throw new Error('Could not retrieve wallet address');
      } catch (error) {
        console.error('Error fetching wallet address:', error);
        return null;
      }
    };

    const updateUserProfile = async (userId: string, xrpAddress: string) => {
      try {
        const supabase = getSupabaseClient();
        if (!supabase) {
          console.warn('Supabase client not available');
          return;
        }

        const { error } = await supabase
          .from('profiles')
          .update({
            xrp_wallet_address: xrpAddress,
            wallet_connected_at: new Date().toISOString(),
            wallet_last_used: new Date().toISOString()
          })
          .eq('id', userId);

        if (error) {
          console.error('Error updating profile:', error);
        } else {
          console.log('Profile updated successfully with XRP address');
          // Update cache
          profileCache.updateWalletInCache(userId, xrpAddress);
        }
      } catch (error) {
        console.error('Error updating user profile:', error);
      }
    };

    handleCallback();
  }, [searchParams, router, user]);

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-12 w-12 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-12 w-12 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-12 w-12 text-red-500" />;
    }
  };

  const getButtonText = () => {
    switch (status) {
      case 'success':
        return 'Go to Dashboard';
      case 'error':
        return 'Try Again';
      default:
        return 'Please wait...';
    }
  };

  const handleButtonClick = () => {
    if (status === 'success') {
      router.push('/dashboard');
    } else if (status === 'error') {
      router.push('/wallet');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {getIcon()}
          </div>
          <CardTitle className="text-2xl">
            {status === 'loading' && 'Processing...'}
            {status === 'success' && 'Success!'}
            {status === 'error' && 'Connection Failed'}
          </CardTitle>
          <CardDescription className="text-lg">
            {message}
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          {walletAddress && status === 'success' && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-sm text-green-800 font-medium">Wallet Address:</p>
              <p className="text-xs text-green-700 font-mono break-all">
                {walletAddress}
              </p>
            </div>
          )}
          
          {status !== 'loading' && (
            <Button 
              onClick={handleButtonClick}
              className="w-full"
              variant={status === 'error' ? 'outline' : 'default'}
            >
              {getButtonText()}
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function XummCallbackLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
          </div>
          <CardTitle className="text-2xl">Processing...</CardTitle>
          <CardDescription className="text-lg">
            Processing wallet connection...
          </CardDescription>
        </CardHeader>
      </Card>
    </div>
  );
}

export default function XummCallbackPage() {
  return (
    <Suspense fallback={<XummCallbackLoading />}>
      <XummCallbackContent />
    </Suspense>
  );
}
