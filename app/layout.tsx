import type React from "react"
import "./globals.css"
import { Inter } from "next/font/google"
import type { Metadata } from "next"
import { SiteHeader } from "@/components/site-header"
import { SiteFooter } from "@/components/site-footer"
import { PageTransition } from "@/components/page-transition"
import { ScrollProgressBar } from "@/components/scroll-progress-bar"
import { AuthProvider } from "@/contexts/auth-context"
import { OnboardingModalProvider } from "@/contexts/onboarding-modal-context"
import { ChatProvider } from "@/components/chat/chat-provider"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "sonner"
import { SpeedInsights } from "@vercel/speed-insights/next"
import { Analytics } from "@vercel/analytics/next"
import { ErrorBoundary } from "@/components/error-boundary"

const inter = Inter({ subsets: ["latin"] })


export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false, // Prevents zoom on mobile games
}

export const metadata: Metadata = {
  title: "Fuse.vip - Loyalty Reimagined. Commerce Reconnected!",
  description:
    "Fuse.vip provides innovative loyalty solutions and digital transformation for businesses across various industries.",
  generator: "v0.dev",
  openGraph: {
    title: "Fuse.vip - Loyalty Reimagined. Commerce Reconnected!",
    description: "Fuse.vip provides innovative loyalty solutions and digital transformation for businesses across various industries.",
    url: "https://www.fuse.vip/",
    siteName: "Fuse.vip",
    images: [
      {
        url: "https://www.fuse.vip/images/fuse-logo.png",
        width: 1200,
        height: 630,
        alt: "Fuse.vip Logo",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Fuse.vip - Loyalty Reimagined. Commerce Reconnected!",
    description: "Fuse.vip provides innovative loyalty solutions and digital transformation for businesses across various industries.",
    images: ["https://www.fuse.vip/images/fuse-logo.png"],
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta httpEquiv="Permissions-Policy" content="payment=*, camera=*, microphone=*, geolocation=*, fullscreen=*" />
        {/* Xaman OAuth2 PKCE SDK - loaded dynamically in components */}
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <AuthProvider>
          <OnboardingModalProvider>
            <ErrorBoundary>
              <ScrollProgressBar />
              <div className="flex flex-col min-h-screen">
                <SiteHeader />
                <main className="flex-grow">
                  <PageTransition>{children}</PageTransition>
                </main>
                <SiteFooter />
              </div>
              <ChatProvider />
              <Toaster position="top-right" richColors />
            </ErrorBoundary>
          </OnboardingModalProvider>
          </AuthProvider>
          <SpeedInsights />
          <Analytics />
        </ThemeProvider>
      </body>
    </html>
  )
}
