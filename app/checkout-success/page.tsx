"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { getSupabaseClient } from "@/lib/supabase"
import { CheckCircle, Loader2 } from "lucide-react"
import Link from "next/link"
import { AnimatedSection } from "@/components/animated-section"
import { AnimatedCard } from "@/components/animated-card"

function CheckoutSuccessContent() {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [isGuest, setIsGuest] = useState(false)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [error, setError] = useState("")
  const [loading, setLoading] = useState(false)
  const [accountCreated, setAccountCreated] = useState(false)
  const [vipUpdateStatus, setVipUpdateStatus] = useState<'pending' | 'success' | 'error'>('pending')

  useEffect(() => {
    const isGuestPurchase = searchParams.get("users") === "true"
    setIsGuest(isGuestPurchase)

    const guestEmail = searchParams.get("email")
    if (guestEmail) {
      setEmail(decodeURIComponent(guestEmail))
    }

    // Update VIP status for authenticated users
    if (!isGuestPurchase) {
      const updateVIPStatus = async () => {
        try {
          const sessionId = searchParams.get("session_id")
          const response = await fetch('/api/vip-purchase-update', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user_id: 'current_user', // Will be extracted from auth
              tier: 'Premium',
              membership_duration_months: 12,
              purchase_amount: 100,
              session_id: sessionId
            }),
          })

          if (response.ok) {
            setVipUpdateStatus('success')
          } else {
            setVipUpdateStatus('error')
            console.error('Failed to update VIP status')
          }
        } catch (error) {
          setVipUpdateStatus('error')
          console.error('Error updating VIP status:', error)
        }
      }

      updateVIPStatus()

      const timer = setTimeout(() => {
        router.push("/dashboard?tab=card")
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [searchParams, router])

  const handleSignup = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (password !== confirmPassword) {
      setError("Passwords do not match")
      return
    }

    setLoading(true)
    setError("")

    try {
      const supabase = getSupabaseClient()
      if (!supabase) {
        throw new Error("Supabase client not initialized")
      }

      const { data, error: signupError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/dashboard?tab=card`,
        },
      })

      if (signupError) throw signupError

      // Ensure profile exists using API route
      if (data.user?.id) {
        try {
          const profileResponse = await fetch('/api/ensure-profile', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user_id: data.user.id,
              email: email,
            }),
          })

          if (!profileResponse.ok) {
            console.warn('Profile creation may have failed, but continuing...')
          }
        } catch (profileError) {
          console.warn('Profile creation error:', profileError)
          // Continue anyway - profile can be created later
        }
      }

      setAccountCreated(true)

      setTimeout(() => {
        router.push("/dashboard?tab=card")
      }, 3000)
    } catch (err) {
      console.error(err)
      setError((err as Error).message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black p-6">
      <AnimatedSection>
        <AnimatedCard className="max-w-md w-full p-8 text-center">
          <div className="flex justify-center mb-6">
            <CheckCircle className="h-16 w-16 text-green-500 drop-shadow-lg" />
          </div>

          <h1 className="text-3xl font-bold text-orange-400 mb-4">Purchase Successful!</h1>
          <div className="h-1 w-32 mx-auto bg-gradient-to-r from-transparent via-orange-400 to-transparent rounded-full mb-6"></div>
          <p className="text-gray-300 mb-4">
            Thank you for becoming a Fuse VIP member. <br />
            You can now enjoy your premium rewards at participating businesses.
          </p>
          
          {!isGuest && (
            <div className="mb-8">
              {vipUpdateStatus === 'pending' && (
                <div className="flex items-center justify-center gap-2 text-blue-400">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Activating your VIP membership...</span>
                </div>
              )}
              {vipUpdateStatus === 'success' && (
                <div className="flex items-center justify-center gap-2 text-green-400">
                  <CheckCircle className="h-4 w-4" />
                  <span>VIP membership activated successfully!</span>
                </div>
              )}
              {vipUpdateStatus === 'error' && (
                <div className="text-yellow-400 text-sm">
                  <p>Your payment was successful! Your VIP status will be updated shortly.</p>
                  <p>If you don't see VIP access in a few minutes, please contact support.</p>
                </div>
              )}
            </div>
          )}

          {isGuest && !accountCreated ? (
            <form onSubmit={handleSignup} className="space-y-4">
              <div>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full p-3 rounded-md border bg-gray-800 border-gray-700 text-white"
                  placeholder="Email Address"
                  readOnly
                  required
                />
              </div>
              <div>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full p-3 rounded-md border bg-gray-800 border-gray-700 text-white"
                  placeholder="Password"
                  required
                />
              </div>
              <div>
                <input
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="w-full p-3 rounded-md border bg-gray-800 border-gray-700 text-white"
                  placeholder="Confirm Password"
                  required
                />
              </div>
              {error && <div className="text-red-500 text-sm">{error}</div>}

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-md shadow-lg transition"
              >
                {loading ? "Creating Account..." : "Create Account"}
              </button>
            </form>
          ) : (
            <Link
              href="/dashboard?tab=card"
              className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md mt-6 text-lg font-medium transition"
            >
              Go to My Profile
            </Link>
          )}
        </AnimatedCard>
      </AnimatedSection>
    </div>
  )
}

function CheckoutSuccessFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black p-6">
      <AnimatedSection>
        <AnimatedCard className="max-w-md w-full p-8 text-center">
          <div className="flex justify-center mb-6">
            <Loader2 className="h-16 w-16 text-blue-500 animate-spin drop-shadow-lg" />
          </div>
          <h1 className="text-3xl font-bold text-orange-400 mb-4">Loading...</h1>
          <div className="h-1 w-32 mx-auto bg-gradient-to-r from-transparent via-orange-400 to-transparent rounded-full mb-6"></div>
          <p className="text-gray-300">
            Please wait while we process your purchase details.
          </p>
        </AnimatedCard>
      </AnimatedSection>
    </div>
  )
}

export default function CheckoutSuccess() {
  return (
    <Suspense fallback={<CheckoutSuccessFallback />}>
      <CheckoutSuccessContent />
    </Suspense>
  )
}
