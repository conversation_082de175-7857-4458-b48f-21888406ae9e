"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { Check, Building2, Users, Gift } from 'lucide-react'
import { Link } from "@/components/ui/link"
import { Badge } from "@/components/ui/badge"
import { FastCyclingBusinesses } from "@/components/business/fast-cycling-businesses"
import { DiscountPreviewBanner } from "@/components/discount/discount-preview-banner"
import { FloatingPurchaseButton } from "@/components/ui/quick-purchase-button"

export default function Home() {

  return (
    <>
      {/* Hero Section - Refined messaging and layout */}
      <section className="relative w-full min-h-screen bg-gradient-to-b from-[#000814] via-[#000d1a] to-black overflow-hidden flex items-center justify-center text-white px-4 sm:px-8 py-20">
        {/* Background Image with overlay */}
        <div className="absolute inset-0 z-0 after:content-[''] after:absolute after:inset-0 after:bg-radial-gradient after:from-transparent after:to-black/70">
          <Image
            src="/images/landing-bckgrnd-2.png"
            alt="Fuse.vip Background"
            width={1600}
            height={900}
            className="object-cover w-full h-full opacity-30"
            priority
          />
        </div>

        {/* Foreground Content */}
        <div className="relative z-10 flex flex-col lg:flex-row w-full max-w-7xl mx-auto items-center justify-between gap-12">
          {/* Left Column - Value Proposition */}
          <div className="flex flex-col space-y-6 max-w-xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Badge className="bg-[#316bff]/20 text-[#316bff] hover:bg-[#316bff]/30 mb-4">
                Revolutionizing Customer Loyalty
              </Badge>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.1 }}
              className="text-4xl sm:text-5xl md:text-6xl font-bold text-white leading-tight mb-8"
            >
              Transform Your Business With <span className="text-[#FF914D]">Fuse.Vip</span>
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.15 }}
              className="text-xl text-gray-300 mb-8 max-w-2xl"
            >
              Join our growing network of businesses offering exclusive rewards to VIP customers. Increase retention, build loyalty, and earn cryptocurrency rewards.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
              className="pt-4"
            >
              <p className="text-sm text-gray-400 flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" /> Purchase with Stripe
              </p>
              <p className="text-sm text-gray-400 flex items-center gap-2 mt-1">
                <Check className="h-4 w-4 text-green-500" /> Join 70+ businesses already using Fuse.vip
              </p>
            </motion.div>
          </div>

          {/* Right Column - Product Showcase */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="relative w-full max-w-md lg:max-w-lg xl:max-w-xl"
          >
            <div className="relative bg-gradient-to-br from-[#1a1f2e] to-[#0d1117] p-4 rounded-xl border border-gray-800 shadow-2xl">
              <div className="absolute -top-2 -right-2 bg-[#FFD700] text-black text-xs font-bold py-1 px-3 rounded-full">
                PREMIUM
              </div>
              <div className="flex items-center justify-between border-b border-gray-800 pb-3 mb-4">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <div className="text-xs text-gray-400">Fuse.vip Dashboard</div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-semibold">FUSE.vip Network</h3>
                    <p className="text-sm text-gray-400">Est. March, 2025</p>
                  </div>
                  <Badge className="bg-green-500/20 text-green-400 hover:bg-green-500/30">
                    +24% ↑
                  </Badge>
                </div>
                <div className="w-full">
                  <img
                    src="/images/resources1.jpg"
                    alt="Fuse VIP Network"
                    className="w-full h-auto rounded-lg object-cover"
                  />
                </div>
                <div className="bg-white/5 p-4 rounded-lg">
                  <h4 className="text-sm font-medium mb-4 text-center">Choose Your Fuse Journey</h4>
                  <div className="grid grid-cols-3 gap-2">
                    <Link href="/resources">
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 300 }}
                        className="bg-gradient-to-br from-[#316bff]/20 to-[#316bff]/10 p-3 rounded-lg cursor-pointer hover:from-[#316bff]/30 hover:to-[#316bff]/20 transition-all"
                      >
                        <div className="flex flex-col items-center text-center">
                          <Building2 className="h-6 w-6 text-[#316bff] mb-2" />
                          <p className="text-xs text-white font-medium">Business Benefits</p>
                        </div>
                      </motion.div>
                    </Link>

                    <Link href="/industry">
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 300 }}
                        className="bg-gradient-to-br from-[#FF914D]/20 to-[#FF914D]/10 p-3 rounded-lg cursor-pointer hover:from-[#FF914D]/30 hover:to-[#FF914D]/20 transition-all"
                      >
                        <div className="flex flex-col items-center text-center">
                          <Gift className="h-6 w-6 text-[#FF914D] mb-2" />
                          <p className="text-xs text-white font-medium">Fuse Discounts</p>
                        </div>
                      </motion.div>
                    </Link>

                    <Link href="/upgrade">
                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 300 }}
                        className="bg-gradient-to-br from-green-500/20 to-green-500/10 p-3 rounded-lg cursor-pointer hover:from-green-500/30 hover:to-green-500/20 transition-all"
                      >
                        <div className="flex flex-col items-center text-center">
                          <Users className="h-6 w-6 text-green-400 mb-2" />
                          <p className="text-xs text-white font-medium">Customer Benefits</p>
                        </div>
                      </motion.div>
                    </Link>
                  </div>
                </div>

              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Discount Preview Banner */}
      <DiscountPreviewBanner className="mb-0" />

      {/* Featured Businesses Section */}
      <FastCyclingBusinesses />

      {/* Social Proof Section */}
      <section className="bg-white py-10">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap items-center justify-center gap-6 md:gap-10">
            <p className="text-gray-500 text-sm font-medium w-full text-center mb-4">BUILT ON PROVEN TECHNOLOGIES</p>
            {[
              { name: 'Stripe', url: 'https://stripe.com/', },
              { name: 'XRP', url: 'https://xrpl.org/', },
              { name: 'Xaman Wallet', url: 'https://xaman.app/#features', },
              { name: 'Vercel', url: 'https://vercel.com/', }
            ].map((brand, index) => (
              <a
                key={index}
                href={brand.url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-[#316bff] hover:bg-[#2151d3] text-white px-4 py-2 rounded-md text-sm sm:text-base font-medium transition-all"
              >
                {brand.name}
              </a>
            ))}
          </div>
        </div>
      </section>

      {/* Floating Purchase Button for Mobile */}
      <FloatingPurchaseButton className="md:hidden" />

    </>
  );
}
