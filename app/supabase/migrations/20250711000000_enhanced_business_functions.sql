-- Enhanced business functions for better ownership tracking and access management
-- This migration adds improved functions for business ownership detection and management

-- Function to get comprehensive business ownership data
CREATE OR REPLACE FUNCTION get_user_business_data(user_uuid UUID)
RETURNS TABLE (
  business_id UUID,
  business_name TEXT,
  category TEXT,
  premium_discount TEXT,
  logo_url TEXT,
  website TEXT,
  is_active BOOLEAN,
  business_spotlight BOOLEAN,
  interaction_count BIGINT,
  referral_count BIGINT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    b.id as business_id,
    b.name as business_name,
    b.category,
    b.premium_discount,
    b.logo_url,
    b.website,
    b.is_active,
    b.business_spotlight,
    COALESCE(bv.visit_count, 0) as interaction_count,
    COALESCE(br.referral_count, 0) as referral_count,
    b.created_at
  FROM businesses b
  LEFT JOIN (
    SELECT business_id, COUNT(*) as visit_count
    FROM business_visits
    GROUP BY business_id
  ) bv ON b.id = bv.business_id
  LEFT JOIN (
    SELECT referring_business_id, COUNT(*) as referral_count
    FROM businesses
    WHERE referring_business_id IS NOT NULL
    GROUP BY referring_business_id
  ) br ON b.id = br.referring_business_id
  WHERE b.user_id = user_uuid AND b.is_active = true
  ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get businesses that need user_id assignment
CREATE OR REPLACE FUNCTION get_unassigned_businesses()
RETURNS TABLE (
  business_id UUID,
  business_name TEXT,
  category TEXT,
  contact_email TEXT,
  contact_name TEXT,
  contact_phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  days_unassigned INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    b.id as business_id,
    b.name as business_name,
    b.category,
    b.contact_email,
    b.contact_name,
    b.contact_phone,
    b.created_at,
    EXTRACT(DAY FROM NOW() - b.created_at)::INTEGER as days_unassigned
  FROM businesses b
  WHERE b.user_id IS NULL
  ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get pending business access requests for a user
CREATE OR REPLACE FUNCTION get_user_access_requests(user_uuid UUID)
RETURNS TABLE (
  request_id UUID,
  business_id UUID,
  business_name TEXT,
  status TEXT,
  requested_at TIMESTAMP WITH TIME ZONE,
  days_pending INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    bar.id as request_id,
    bar.business_id,
    b.name as business_name,
    bar.status,
    bar.requested_at,
    EXTRACT(DAY FROM NOW() - bar.requested_at)::INTEGER as days_pending
  FROM business_access_requests bar
  JOIN businesses b ON bar.business_id = b.id
  WHERE bar.user_id = user_uuid AND bar.status = 'pending'
  ORDER BY bar.requested_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has business access (improved version)
CREATE OR REPLACE FUNCTION has_business_access_enhanced(user_uuid UUID)
RETURNS TABLE (
  has_business_access BOOLEAN,
  is_admin BOOLEAN,
  business_count INTEGER,
  pending_requests INTEGER
) AS $$
DECLARE
  business_count_val INTEGER;
  pending_requests_val INTEGER;
  is_admin_val BOOLEAN;
BEGIN
  -- Count user's businesses
  SELECT COUNT(*) INTO business_count_val
  FROM businesses
  WHERE user_id = user_uuid AND is_active = true;

  -- Count pending access requests
  SELECT COUNT(*) INTO pending_requests_val
  FROM business_access_requests
  WHERE user_id = user_uuid AND status = 'pending';

  -- Check if user is admin
  SELECT COALESCE(
    (SELECT true FROM portal_roles WHERE user_id = user_uuid AND role = 'admin'),
    false
  ) INTO is_admin_val;

  RETURN QUERY
  SELECT 
    (business_count_val > 0 OR is_admin_val) as has_business_access,
    is_admin_val as is_admin,
    business_count_val as business_count,
    pending_requests_val as pending_requests;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to assign business to user (for admin use)
CREATE OR REPLACE FUNCTION assign_business_to_user(
  business_uuid UUID,
  user_uuid UUID,
  assigned_by_uuid UUID
)
RETURNS TABLE (
  success BOOLEAN,
  message TEXT
) AS $$
DECLARE
  business_exists BOOLEAN;
  user_exists BOOLEAN;
  is_admin_user BOOLEAN;
BEGIN
  -- Check if assigning user is admin
  SELECT EXISTS(
    SELECT 1 FROM portal_roles 
    WHERE user_id = assigned_by_uuid AND role = 'admin'
  ) INTO is_admin_user;

  IF NOT is_admin_user THEN
    RETURN QUERY SELECT false, 'Only admin users can assign businesses';
    RETURN;
  END IF;

  -- Check if business exists and is unassigned
  SELECT EXISTS(
    SELECT 1 FROM businesses 
    WHERE id = business_uuid AND user_id IS NULL
  ) INTO business_exists;

  IF NOT business_exists THEN
    RETURN QUERY SELECT false, 'Business not found or already assigned';
    RETURN;
  END IF;

  -- Check if user exists
  SELECT EXISTS(
    SELECT 1 FROM auth.users 
    WHERE id = user_uuid
  ) INTO user_exists;

  IF NOT user_exists THEN
    RETURN QUERY SELECT false, 'User not found';
    RETURN;
  END IF;

  -- Assign business to user
  UPDATE businesses 
  SET user_id = user_uuid, updated_at = NOW()
  WHERE id = business_uuid;

  -- Update user profile to mark as business applicant
  UPDATE profiles 
  SET is_business_applicant = true
  WHERE id = user_uuid;

  -- Log the assignment (if audit table exists)
  INSERT INTO business_access_requests (
    user_id, 
    business_id, 
    status, 
    reviewed_by, 
    reviewed_at,
    admin_notes,
    message
  ) VALUES (
    user_uuid,
    business_uuid,
    'approved',
    assigned_by_uuid,
    NOW(),
    'Business assigned by admin',
    'Business ownership assigned via admin panel'
  ) ON CONFLICT DO NOTHING;

  RETURN QUERY SELECT true, 'Business successfully assigned to user';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_user_business_data(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_unassigned_businesses() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_access_requests(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION has_business_access_enhanced(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION assign_business_to_user(UUID, UUID, UUID) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_businesses_user_id_active ON businesses(user_id, is_active) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_businesses_unassigned ON businesses(created_at) WHERE user_id IS NULL;
CREATE INDEX IF NOT EXISTS idx_business_access_requests_user_status ON business_access_requests(user_id, status);
CREATE INDEX IF NOT EXISTS idx_business_visits_business_id ON business_visits(business_id);

-- Add comment for documentation
COMMENT ON FUNCTION get_user_business_data(UUID) IS 'Returns comprehensive business data for a user including interaction and referral counts';
COMMENT ON FUNCTION get_unassigned_businesses() IS 'Returns businesses that need user_id assignment for admin processing';
COMMENT ON FUNCTION get_user_access_requests(UUID) IS 'Returns pending business access requests for a user';
COMMENT ON FUNCTION has_business_access_enhanced(UUID) IS 'Enhanced business access check with additional metadata';
COMMENT ON FUNCTION assign_business_to_user(UUID, UUID, UUID) IS 'Admin function to assign unassigned businesses to users';
