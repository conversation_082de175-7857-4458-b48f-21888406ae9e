-- =============================================
-- VIP CODE REDEMPTION FUNCTIONS AND POLICIES
-- Migration: 20250714020000_add_vip_code_redemption_functions.sql
-- Purpose: Create missing redeem_vip_code function and RLS policies
-- =============================================

-- Create the missing redeem_vip_code function
CREATE OR REPLACE FUNCTION redeem_vip_code(
    p_code TEXT,
    p_user_id UUID
) RETURNS TABLE (
    success BOOLEAN,
    message TEXT,
    card_tier TEXT,
    membership_start_date TIMESTAMP WITH TIME ZONE,
    membership_end_date TIMESTAMP WITH TIME ZONE,
    redemption_id UUID
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_vip_code RECORD;
    v_existing_redemption RECORD;
    v_redemption_id UUID;
    v_membership_start TIMESTAMP WITH TIME ZONE;
    v_membership_end TIMESTAMP WITH TIME ZONE;
    v_card_tier TEXT;
BEGIN
    -- Check if code exists and is valid
    SELECT * INTO v_vip_code 
    FROM vip_codes 
    WHERE code = p_code 
    AND (is_active IS NULL OR is_active = TRUE)
    AND (expires_at IS NULL OR expires_at > NOW())
    AND (remaining_uses IS NULL OR remaining_uses > 0);

    -- If code doesn't exist or is invalid
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 'Invalid or expired VIP code', NULL::TEXT, NULL::TIMESTAMP WITH TIME ZONE, NULL::TIMESTAMP WITH TIME ZONE, NULL::UUID;
        RETURN;
    END IF;

    -- Check if user has already redeemed this code
    SELECT * INTO v_existing_redemption
    FROM vip_redemptions
    WHERE code = p_code AND redeemed_by = p_user_id;

    IF FOUND THEN
        RETURN QUERY SELECT false, 'This VIP code has already been redeemed by this user', NULL::TEXT, NULL::TIMESTAMP WITH TIME ZONE, NULL::TIMESTAMP WITH TIME ZONE, NULL::UUID;
        RETURN;
    END IF;

    -- Set membership dates (1 year from redemption)
    v_membership_start := NOW();
    v_membership_end := NOW() + INTERVAL '1 year';
    
    -- Determine card tier (default to Premium if not specified)
    v_card_tier := 'Premium';

    -- Begin transaction for atomic operations
    BEGIN
        -- Insert redemption record
        INSERT INTO vip_redemptions (code, redeemed_by, redeemed_at)
        VALUES (p_code, p_user_id, NOW())
        RETURNING id INTO v_redemption_id;

        -- Update remaining uses if applicable
        UPDATE vip_codes 
        SET remaining_uses = CASE 
            WHEN remaining_uses IS NOT NULL THEN remaining_uses - 1
            ELSE NULL
        END
        WHERE code = p_code;

        -- Update user profile with VIP status
        UPDATE profiles 
        SET 
            is_card_holder = TRUE,
            card_tier = v_card_tier::card_tier_enum,
            membership_start_date = v_membership_start,
            membership_end_date = v_membership_end,
            updated_at = NOW()
        WHERE id = p_user_id;

        -- Return success result
        RETURN QUERY SELECT true, 'VIP code redeemed successfully', v_card_tier, v_membership_start, v_membership_end, v_redemption_id;

    EXCEPTION
        WHEN OTHERS THEN
            -- Roll back and return error
            RETURN QUERY SELECT false, 'Error processing VIP code redemption: ' || SQLERRM, NULL::TEXT, NULL::TIMESTAMP WITH TIME ZONE, NULL::TIMESTAMP WITH TIME ZONE, NULL::UUID;
    END;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION redeem_vip_code(TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION redeem_vip_code(TEXT, UUID) TO service_role;

-- Create RLS policies for vip_codes table
CREATE POLICY "Service role can manage vip_codes"
ON vip_codes FOR ALL
USING (current_setting('role') = 'service_role');

CREATE POLICY "Authenticated users can view active vip_codes"
ON vip_codes FOR SELECT
USING (
    auth.uid() IS NOT NULL 
    AND (is_active IS NULL OR is_active = TRUE)
    AND (expires_at IS NULL OR expires_at > NOW())
);

-- Create RLS policies for vip_redemptions table
CREATE POLICY "Service role can manage vip_redemptions"
ON vip_redemptions FOR ALL
USING (current_setting('role') = 'service_role');

CREATE POLICY "Users can view their own redemptions"
ON vip_redemptions FOR SELECT
USING (auth.uid() = redeemed_by);

CREATE POLICY "Users can create their own redemptions"
ON vip_redemptions FOR INSERT
WITH CHECK (auth.uid() = redeemed_by);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_vip_codes_active_expires ON vip_codes(code, is_active, expires_at);
CREATE INDEX IF NOT EXISTS idx_vip_codes_remaining_uses ON vip_codes(remaining_uses) WHERE remaining_uses IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_vip_redemptions_user_code ON vip_redemptions(redeemed_by, code);
CREATE INDEX IF NOT EXISTS idx_vip_redemptions_redeemed_at ON vip_redemptions(redeemed_at);

-- Add helpful comments
COMMENT ON FUNCTION redeem_vip_code(TEXT, UUID) IS 'Atomically redeems a VIP code and updates user profile with membership status';
COMMENT ON POLICY "Service role can manage vip_codes" ON vip_codes IS 'Allows service role operations for admin VIP code management';
COMMENT ON POLICY "Authenticated users can view active vip_codes" ON vip_codes IS 'Allows authenticated users to view active VIP codes for validation';
COMMENT ON POLICY "Users can view their own redemptions" ON vip_redemptions IS 'Allows users to view their own redemption history';
COMMENT ON POLICY "Users can create their own redemptions" ON vip_redemptions IS 'Allows users to create redemption records for their own codes';