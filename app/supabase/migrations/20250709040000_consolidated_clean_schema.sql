-- CONSOLIDATED CLEAN SCHEMA MIGRATION
-- Created: 2025-07-09
-- Purpose: Single migration representing current working production state
-- Replaces: All previous 27+ conflicting migrations

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON>reate custom types
DO $$ BEGIN
    CREATE TYPE card_tier_enum AS ENUM ('Monthly', 'Premium', 'Gold', 'Platinum', 'Diamond', 'Obsidian');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =============================================
-- CORE TABLES (PROPER ORDER)
-- =============================================

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Main businesses table (CREATE FIRST)
CREATE TABLE IF NOT EXISTS businesses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    logo_url TEXT DEFAULT ''::text,
    website TEXT,
    category TEXT NOT NULL,
    premium_discount TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    business_spotlight BOOLEAN DEFAULT FALSE,
    contact_name TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    business_address TEXT,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    display_order INTEGER,
    contact_info TEXT,
    latitude NUMERIC,
    longitude NUMERIC,
    business_referral TEXT,
    referring_business_id UUID REFERENCES businesses(id) ON DELETE SET NULL,
    loyalty_reward_frequency TEXT DEFAULT 'monthly'::text,
    logo_optimized_url TEXT,
    logo_alt_text TEXT,
    logo_width INTEGER,
    logo_height INTEGER,
    logo_file_size INTEGER,
    logo_mime_type TEXT
);

-- Profiles table (NOW SAFE TO REFERENCE businesses)
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name TEXT,
    last_name TEXT,
    phone TEXT,
    user_email TEXT,
    is_card_holder BOOLEAN DEFAULT FALSE,
    is_business_applicant BOOLEAN DEFAULT FALSE,
    xrp_wallet_address TEXT,
    membership_start_date TIMESTAMP WITH TIME ZONE,
    membership_end_date TIMESTAMP WITH TIME ZONE,
    card_tier card_tier_enum DEFAULT 'Premium',
    referring_business_id UUID REFERENCES businesses(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Purchases table
CREATE TABLE IF NOT EXISTS purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    method TEXT,
    amount NUMERIC,
    status TEXT,
    transaction_id TEXT,
    metadata JSONB,
    wallet_address TEXT,
    guest_email TEXT,
    stripe_session_id TEXT,
    stripe_payment_intent_id TEXT,
    card_type TEXT,
    currency TEXT DEFAULT 'usd'::text,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Business visits table
CREATE TABLE IF NOT EXISTS business_visits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    business_id UUID NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
    scanned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    fuse_earned INTEGER DEFAULT 100,
    visit_metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- QR interactions table
CREATE TABLE IF NOT EXISTS qr_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scanner_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    scanned_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    scanned_business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
    interaction_type TEXT NOT NULL,
    qr_data TEXT NOT NULL,
    fuse_tokens_awarded INTEGER DEFAULT 0,
    location_data JSONB,
    interaction_metadata JSONB DEFAULT '{}'::jsonb,
    scan_count INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Referrals table
CREATE TABLE IF NOT EXISTS referrals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
    purchase_id UUID REFERENCES purchases(id) ON DELETE CASCADE,
    referral_code TEXT DEFAULT 'TEMP-CODE'::text,
    primary_commission NUMERIC NOT NULL,
    secondary_commission NUMERIC NOT NULL,
    status TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Network applications table
CREATE TABLE IF NOT EXISTS network_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_name TEXT NOT NULL,
    website TEXT,
    category TEXT,
    contact_name TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    business_address TEXT,
    proposed_discount TEXT NOT NULL,
    logo_url TEXT,
    logo_path TEXT,
    logo_data TEXT,
    status TEXT DEFAULT '''pending'';'::text,
    user_id UUID DEFAULT auth.uid(),
    referring_business_id UUID REFERENCES businesses(id) ON DELETE SET NULL,
    loyalty_reward_frequency TEXT DEFAULT 'monthly'::text,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Physical cards table
CREATE TABLE IF NOT EXISTS physical_cards (
    id UUID PRIMARY KEY,
    card_number TEXT,
    assigned_name TEXT,
    user_id UUID DEFAULT auth.uid(),
    is_active BOOLEAN,
    tier TEXT DEFAULT 'Premium'::text,
    issue_date TIMESTAMP WITH TIME ZONE,
    expiration_date TIMESTAMP WITH TIME ZONE,
    expiry_date TIMESTAMP WITH TIME ZONE,
    redeemed_at TIMESTAMP WITH TIME ZONE,
    card_image TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User QR codes table
CREATE TABLE IF NOT EXISTS user_qr_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    qr_data TEXT NOT NULL,
    qr_code_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- VIP codes table
CREATE TABLE IF NOT EXISTS vip_codes (
    code TEXT PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN,
    max_uses INTEGER,
    remaining_uses INTEGER
);

-- VIP redemptions table
CREATE TABLE IF NOT EXISTS vip_redemptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code TEXT REFERENCES vip_codes(code) ON DELETE CASCADE,
    redeemed_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    redeemed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Additional supporting tables
CREATE TABLE IF NOT EXISTS business_access_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    business_id UUID NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
    message TEXT,
    status TEXT DEFAULT 'pending'::text,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    admin_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS business_discount_codes (
    id UUID PRIMARY KEY,
    business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
    code TEXT,
    description TEXT,
    discount_percentage NUMERIC,
    is_active BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Portal roles and user roles
CREATE TABLE IF NOT EXISTS portal_roles (
    id INTEGER PRIMARY KEY,
    role_name TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES portal_roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    revoked_at TIMESTAMP WITH TIME ZONE,
    revoked_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics and metadata tables
CREATE TABLE IF NOT EXISTS business_scan_analytics (
    business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
    business_name TEXT,
    total_scans BIGINT,
    unique_scanners BIGINT,
    total_tokens_distributed BIGINT,
    scan_date TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS schema_metadata (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Simple todos table
CREATE SEQUENCE IF NOT EXISTS todos_id_seq;
CREATE TABLE IF NOT EXISTS todos (
    id INTEGER PRIMARY KEY DEFAULT nextval('todos_id_seq'::regclass),
    task TEXT NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Core business indexes
CREATE INDEX IF NOT EXISTS idx_businesses_active ON businesses(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_businesses_user_id ON businesses(user_id);
CREATE INDEX IF NOT EXISTS idx_businesses_category ON businesses(category);
CREATE INDEX IF NOT EXISTS idx_businesses_created_at ON businesses(created_at);

-- Profile indexes
CREATE INDEX IF NOT EXISTS idx_profiles_user_email ON profiles(user_email);
CREATE INDEX IF NOT EXISTS idx_profiles_card_holder ON profiles(is_card_holder) WHERE is_card_holder = true;

-- Purchase indexes
CREATE INDEX IF NOT EXISTS idx_purchases_user_id ON purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_purchases_status ON purchases(status);
CREATE INDEX IF NOT EXISTS idx_purchases_created_at ON purchases(created_at);

-- Business visits indexes
CREATE INDEX IF NOT EXISTS idx_business_visits_user_id ON business_visits(user_id);
CREATE INDEX IF NOT EXISTS idx_business_visits_business_id ON business_visits(business_id);
CREATE INDEX IF NOT EXISTS idx_business_visits_scanned_at ON business_visits(scanned_at);

-- QR interactions indexes
CREATE INDEX IF NOT EXISTS idx_qr_interactions_scanner_user_id ON qr_interactions(scanner_user_id);
CREATE INDEX IF NOT EXISTS idx_qr_interactions_business_id ON qr_interactions(scanned_business_id);
CREATE INDEX IF NOT EXISTS idx_qr_interactions_created_at ON qr_interactions(created_at);

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_visits ENABLE ROW LEVEL SECURITY;
ALTER TABLE qr_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE network_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE physical_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_qr_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE vip_redemptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_access_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

-- =============================================
-- RLS POLICIES
-- =============================================

-- Public business access (CRITICAL FOR PUBLIC SCHEMA ACCESS)
DROP POLICY IF EXISTS "public_businesses_select" ON businesses;
CREATE POLICY "public_businesses_select" ON businesses
  FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Allow anonymous users to view all businesses" ON businesses;
CREATE POLICY "Allow anonymous users to view all businesses" ON businesses
  FOR SELECT TO anon USING (true);

DROP POLICY IF EXISTS "Public can view active businesses" ON businesses;
CREATE POLICY "Public can view active businesses" ON businesses
  FOR SELECT TO public USING (is_active = true);

-- Service role access
DROP POLICY IF EXISTS "Service role can access all businesses" ON businesses;
CREATE POLICY "Service role can access all businesses" ON businesses
  FOR ALL TO public USING (auth.role() = 'service_role'::text);

-- Authenticated user policies
DROP POLICY IF EXISTS "Allow authenticated users to insert businesses" ON businesses;
CREATE POLICY "Allow authenticated users to insert businesses" ON businesses
  FOR INSERT TO authenticated WITH CHECK (true);

DROP POLICY IF EXISTS "Allow authenticated users to update their own businesses" ON businesses;
CREATE POLICY "Allow authenticated users to update their own businesses" ON businesses
  FOR UPDATE TO authenticated USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON businesses;
CREATE POLICY "Enable delete for users based on user_id" ON businesses
  FOR DELETE TO authenticated USING (auth.uid() = user_id);

-- Profile policies
DROP POLICY IF EXISTS "own_profile_access" ON profiles;
CREATE POLICY "own_profile_access" ON profiles
  FOR ALL TO authenticated USING (auth.uid() = id);

-- Purchase policies
DROP POLICY IF EXISTS "own_purchases_access" ON purchases;
CREATE POLICY "own_purchases_access" ON purchases
  FOR ALL TO authenticated USING (auth.uid() = user_id);

-- Business data access for owners
DROP POLICY IF EXISTS "business_purchases_access" ON purchases;
CREATE POLICY "business_purchases_access" ON purchases
  FOR SELECT TO authenticated USING (
    EXISTS (
      SELECT 1 FROM businesses WHERE user_id = auth.uid()
    )
  );

-- Business visits policies
DROP POLICY IF EXISTS "business_visits_user_access" ON business_visits;
CREATE POLICY "business_visits_user_access" ON business_visits
  FOR ALL TO authenticated USING (auth.uid() = user_id);

-- QR interactions policies
DROP POLICY IF EXISTS "qr_interactions_user_access" ON qr_interactions;
CREATE POLICY "qr_interactions_user_access" ON qr_interactions
  FOR ALL TO authenticated USING (auth.uid() = scanner_user_id);

-- Network applications policies
DROP POLICY IF EXISTS "network_applications_user_access" ON network_applications;
CREATE POLICY "network_applications_user_access" ON network_applications
  FOR ALL TO authenticated USING (auth.uid() = user_id);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Update timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers
DROP TRIGGER IF EXISTS update_businesses_updated_at ON businesses;
CREATE TRIGGER update_businesses_updated_at
    BEFORE UPDATE ON businesses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_purchases_updated_at ON purchases;
CREATE TRIGGER update_purchases_updated_at
    BEFORE UPDATE ON purchases
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- SCHEMA METADATA
-- =============================================

-- Record this migration
INSERT INTO schema_metadata (key, value) VALUES
('last_migration', '20250709040000_consolidated_clean_schema')
ON CONFLICT (key) DO UPDATE SET
value = EXCLUDED.value,
updated_at = NOW();

INSERT INTO schema_metadata (key, value) VALUES
('schema_version', '1.0.0')
ON CONFLICT (key) DO UPDATE SET
value = EXCLUDED.value,
updated_at = NOW();

INSERT INTO schema_metadata (key, value) VALUES
('migration_cleanup_date', '2025-07-09')
ON CONFLICT (key) DO UPDATE SET
value = EXCLUDED.value,
updated_at = NOW();
