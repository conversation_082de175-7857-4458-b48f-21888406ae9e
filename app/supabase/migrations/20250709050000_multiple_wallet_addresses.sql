-- Migration to support multiple wallet addresses per user
-- This allows users to connect multiple XRP wallets for different purposes

-- Create user_wallets table for multiple wallet addresses
CREATE TABLE IF NOT EXISTS user_wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    wallet_address TEXT NOT NULL,
    wallet_type TEXT DEFAULT 'xrp' CHECK (wallet_type IN ('xrp', 'fuse')),
    is_primary BOOLEAN DEFAULT FALSE,
    nickname TEXT, -- User-friendly name for the wallet
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique wallet addresses
    UNIQUE(wallet_address),
    -- Ensure only one primary wallet per user per type
    UNIQUE(user_id, wallet_type, is_primary) DEFERRABLE INITIALLY DEFERRED
);

-- Add wallet_connected_at and wallet_last_used columns to profiles if they don't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'wallet_connected_at') THEN
        ALTER TABLE profiles ADD COLUMN wallet_connected_at TIMESTAMP WITH TIME ZONE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'wallet_last_used') THEN
        ALTER TABLE profiles ADD COLUMN wallet_last_used TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_wallets_user_id ON user_wallets(user_id);
CREATE INDEX IF NOT EXISTS idx_user_wallets_address ON user_wallets(wallet_address);
CREATE INDEX IF NOT EXISTS idx_user_wallets_primary ON user_wallets(user_id, is_primary) WHERE is_primary = true;
CREATE INDEX IF NOT EXISTS idx_user_wallets_active ON user_wallets(user_id, is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_wallets_type ON user_wallets(user_id, wallet_type);

-- Enable RLS
ALTER TABLE user_wallets ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own wallets" ON user_wallets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own wallets" ON user_wallets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own wallets" ON user_wallets
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own wallets" ON user_wallets
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to migrate existing wallet addresses
CREATE OR REPLACE FUNCTION migrate_existing_wallets()
RETURNS void AS $$
BEGIN
    -- Migrate existing xrp_wallet_address from profiles to user_wallets
    INSERT INTO user_wallets (user_id, wallet_address, wallet_type, is_primary, nickname, connected_at, last_used)
    SELECT 
        id as user_id,
        xrp_wallet_address as wallet_address,
        'xrp' as wallet_type,
        true as is_primary,
        'Primary XRP Wallet' as nickname,
        wallet_connected_at,
        wallet_last_used
    FROM profiles 
    WHERE xrp_wallet_address IS NOT NULL 
    AND xrp_wallet_address != ''
    ON CONFLICT (wallet_address) DO NOTHING;
    
    RAISE NOTICE 'Migrated existing wallet addresses to user_wallets table';
END;
$$ LANGUAGE plpgsql;

-- Create function to get primary wallet for user
CREATE OR REPLACE FUNCTION get_primary_wallet(user_uuid UUID, wallet_type_param TEXT DEFAULT 'xrp')
RETURNS TEXT AS $$
DECLARE
    primary_wallet TEXT;
BEGIN
    SELECT wallet_address INTO primary_wallet
    FROM user_wallets
    WHERE user_id = user_uuid 
    AND wallet_type = wallet_type_param 
    AND is_primary = true 
    AND is_active = true;
    
    RETURN primary_wallet;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to set primary wallet
CREATE OR REPLACE FUNCTION set_primary_wallet(user_uuid UUID, wallet_addr TEXT, wallet_type_param TEXT DEFAULT 'xrp')
RETURNS boolean AS $$
BEGIN
    -- First, unset any existing primary wallet of this type
    UPDATE user_wallets 
    SET is_primary = false, updated_at = NOW()
    WHERE user_id = user_uuid AND wallet_type = wallet_type_param AND is_primary = true;
    
    -- Set the new primary wallet
    UPDATE user_wallets 
    SET is_primary = true, updated_at = NOW(), last_used = NOW()
    WHERE user_id = user_uuid AND wallet_address = wallet_addr AND wallet_type = wallet_type_param;
    
    -- Also update the profiles table for backward compatibility
    UPDATE profiles 
    SET xrp_wallet_address = wallet_addr, wallet_last_used = NOW()
    WHERE id = user_uuid;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update profiles table when primary wallet changes
CREATE OR REPLACE FUNCTION sync_primary_wallet_to_profiles()
RETURNS TRIGGER AS $$
BEGIN
    -- If a wallet is being set as primary, update profiles table
    IF NEW.is_primary = true AND NEW.wallet_type = 'xrp' THEN
        UPDATE profiles 
        SET xrp_wallet_address = NEW.wallet_address, 
            wallet_connected_at = NEW.connected_at,
            wallet_last_used = NEW.last_used
        WHERE id = NEW.user_id;
    END IF;
    
    -- If a primary wallet is being deactivated or removed, clear profiles
    IF (OLD.is_primary = true AND NEW.is_primary = false) OR 
       (OLD.is_primary = true AND NEW.is_active = false) THEN
        -- Check if there's another primary wallet
        IF NOT EXISTS (
            SELECT 1 FROM user_wallets 
            WHERE user_id = NEW.user_id 
            AND wallet_type = 'xrp' 
            AND is_primary = true 
            AND is_active = true 
            AND id != NEW.id
        ) THEN
            UPDATE profiles 
            SET xrp_wallet_address = NULL
            WHERE id = NEW.user_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_sync_primary_wallet
    AFTER UPDATE ON user_wallets
    FOR EACH ROW
    EXECUTE FUNCTION sync_primary_wallet_to_profiles();

-- Run the migration function
SELECT migrate_existing_wallets();

-- Record this migration
INSERT INTO schema_metadata (key, value) VALUES
('last_migration', '20250709050000_multiple_wallet_addresses')
ON CONFLICT (key) DO UPDATE SET
value = EXCLUDED.value,
updated_at = NOW();

COMMIT;
