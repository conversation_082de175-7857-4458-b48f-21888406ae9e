-- Add transaction_logs table for audit trail of XRPL transactions
-- This table will track trustline verifications and other transaction-related activities

CREATE TABLE IF NOT EXISTS transaction_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    transaction_hash VARCHAR(64) NOT NULL,
    transaction_type VARCHAR(50) NOT NULL, -- 'trustline_verification', 'payment', 'trustset', etc.
    wallet_address VARCHAR(35),
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'verified', 'failed', 'expired'
    metadata JSONB DEFAULT '{}',
    error_message TEXT,
    verified_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transaction_logs_user_id ON transaction_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_transaction_hash ON transaction_logs(transaction_hash);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_transaction_type ON transaction_logs(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_status ON transaction_logs(status);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_wallet_address ON transaction_logs(wallet_address);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_created_at ON transaction_logs(created_at);

-- Enable Row Level Security
ALTER TABLE transaction_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for transaction_logs
-- Users can view their own transaction logs
CREATE POLICY "Users can view their own transaction logs" ON transaction_logs
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

-- Users can insert their own transaction logs (for client-side logging)
CREATE POLICY "Users can insert their own transaction logs" ON transaction_logs
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

-- Service role can access all transaction logs (for API operations)
CREATE POLICY "Service role can access all transaction logs" ON transaction_logs
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Admins can view all transaction logs
CREATE POLICY "Admins can view all transaction logs" ON transaction_logs
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_transaction_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_transaction_logs_updated_at
    BEFORE UPDATE ON transaction_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_transaction_logs_updated_at();

-- Add comment to table
COMMENT ON TABLE transaction_logs IS 'Audit trail for XRPL transactions including trustline verifications and payments';
COMMENT ON COLUMN transaction_logs.transaction_hash IS 'XRPL transaction hash (64 hex characters)';
COMMENT ON COLUMN transaction_logs.transaction_type IS 'Type of transaction: trustline_verification, payment, trustset, etc.';
COMMENT ON COLUMN transaction_logs.wallet_address IS 'XRP wallet address that initiated the transaction';
COMMENT ON COLUMN transaction_logs.metadata IS 'Additional transaction details in JSON format';
