-- Add Xaman user token fields to profiles table
-- This enables storing user tokens for push notifications and persistent sign-in

-- Add xaman_user_token field to store the user token from Xaman
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'xaman_user_token') THEN
        ALTER TABLE profiles ADD COLUMN xaman_user_token TEXT;
        RAISE NOTICE 'Added xaman_user_token column to profiles table';
    ELSE
        RAISE NOTICE 'xaman_user_token column already exists in profiles table';
    END IF;
END $$;

-- Add xaman_user_token_expires_at field to track token expiration (30 days from last successful sign)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'xaman_user_token_expires_at') THEN
        ALTER TABLE profiles ADD COLUMN xaman_user_token_expires_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added xaman_user_token_expires_at column to profiles table';
    ELSE
        RAISE NOTICE 'xaman_user_token_expires_at column already exists in profiles table';
    END IF;
END $$;

-- Add xaman_last_successful_sign field to track when user last successfully signed a payload
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'xaman_last_successful_sign') THEN
        ALTER TABLE profiles ADD COLUMN xaman_last_successful_sign TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added xaman_last_successful_sign column to profiles table';
    ELSE
        RAISE NOTICE 'xaman_last_successful_sign column already exists in profiles table';
    END IF;
END $$;

-- Create index on xaman_user_token for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_xaman_user_token ON profiles(xaman_user_token) WHERE xaman_user_token IS NOT NULL;

-- Create index on xaman_user_token_expires_at for cleanup operations
CREATE INDEX IF NOT EXISTS idx_profiles_xaman_token_expires ON profiles(xaman_user_token_expires_at) WHERE xaman_user_token_expires_at IS NOT NULL;

-- Create function to check if user token is valid (not expired)
CREATE OR REPLACE FUNCTION is_xaman_user_token_valid(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    token_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
    SELECT xaman_user_token_expires_at INTO token_expires_at
    FROM profiles
    WHERE id = user_id AND xaman_user_token IS NOT NULL;
    
    -- If no token or expiration date, return false
    IF token_expires_at IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check if token is still valid (not expired)
    RETURN token_expires_at > NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update user token and set expiration (30 days from now)
CREATE OR REPLACE FUNCTION update_xaman_user_token(user_id UUID, new_token TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE profiles
    SET 
        xaman_user_token = new_token,
        xaman_user_token_expires_at = NOW() + INTERVAL '30 days',
        xaman_last_successful_sign = NOW()
    WHERE id = user_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to extend user token expiration (called after successful payload sign)
CREATE OR REPLACE FUNCTION extend_xaman_user_token_expiration(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE profiles
    SET 
        xaman_user_token_expires_at = NOW() + INTERVAL '30 days',
        xaman_last_successful_sign = NOW()
    WHERE id = user_id AND xaman_user_token IS NOT NULL;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clean up expired user tokens
CREATE OR REPLACE FUNCTION cleanup_expired_xaman_tokens()
RETURNS INTEGER AS $$
DECLARE
    cleaned_count INTEGER;
BEGIN
    UPDATE profiles
    SET 
        xaman_user_token = NULL,
        xaman_user_token_expires_at = NULL
    WHERE xaman_user_token_expires_at < NOW();
    
    GET DIAGNOSTICS cleaned_count = ROW_COUNT;
    
    RAISE NOTICE 'Cleaned up % expired Xaman user tokens', cleaned_count;
    RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION is_xaman_user_token_valid(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION update_xaman_user_token(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION extend_xaman_user_token_expiration(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_xaman_tokens() TO service_role;

-- Add comment to document the purpose
COMMENT ON COLUMN profiles.xaman_user_token IS 'Xaman user token for push notifications and persistent sign-in';
COMMENT ON COLUMN profiles.xaman_user_token_expires_at IS 'Expiration timestamp for Xaman user token (30 days from last successful sign)';
COMMENT ON COLUMN profiles.xaman_last_successful_sign IS 'Timestamp of last successful payload signature with Xaman';
