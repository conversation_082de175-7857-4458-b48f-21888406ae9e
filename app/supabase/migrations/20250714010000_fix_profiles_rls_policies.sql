-- =============================================
-- FIX PROFILES RLS POLICIES FOR USER OPERATIONS
-- Migration: 20250714010000_fix_profiles_rls_policies
-- =============================================

-- Drop the existing overly broad policy
DROP POLICY IF EXISTS "own_profile_access" ON profiles;

-- Create specific policies for each operation type

-- Policy 1: Users can view their own profile
CREATE POLICY "Users can view own profile"
ON profiles FOR SELECT
USING (auth.uid() = id);

-- Policy 2: Users can insert their own profile (for initial creation)
CREATE POLICY "Users can create own profile"
ON profiles FOR INSERT
WITH CHECK (auth.uid() = id);

-- Policy 3: Users can update their own profile
CREATE POLICY "Users can update own profile"
ON profiles FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Policy 4: <PERSON><PERSON> can view all profiles
CREATE POLICY "Admins can view all profiles"
ON profiles FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM portal_roles pr
    JOIN user_roles ur ON pr.id = ur.role_id
    WHERE ur.user_id = auth.uid()
    AND pr.role_name = 'admin'
    AND ur.is_active = true
  )
);

-- Policy 5: Admins can update all profiles (for admin management)
CREATE POLICY "Admins can update all profiles"
ON profiles FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM portal_roles pr
    JOIN user_roles ur ON pr.id = ur.role_id
    WHERE ur.user_id = auth.uid()
    AND pr.role_name = 'admin'
    AND ur.is_active = true
  )
);

-- Policy 6: Service role can do everything (for API operations)
CREATE POLICY "Service role full access"
ON profiles FOR ALL
USING (current_setting('role') = 'service_role');

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_id ON profiles(id);
CREATE INDEX IF NOT EXISTS idx_profiles_user_email ON profiles(user_email);
CREATE INDEX IF NOT EXISTS idx_profiles_card_holder ON profiles(is_card_holder) WHERE is_card_holder = true;
CREATE INDEX IF NOT EXISTS idx_profiles_business_applicant ON profiles(is_business_applicant) WHERE is_business_applicant = true;

-- Add comments for documentation
COMMENT ON POLICY "Users can view own profile" ON profiles IS 'Allows users to read their own profile data';
COMMENT ON POLICY "Users can create own profile" ON profiles IS 'Allows users to create their initial profile';
COMMENT ON POLICY "Users can update own profile" ON profiles IS 'Allows users to update their own profile fields';
COMMENT ON POLICY "Admins can view all profiles" ON profiles IS 'Allows admins to view all user profiles';
COMMENT ON POLICY "Admins can update all profiles" ON profiles IS 'Allows admins to update any user profile';
COMMENT ON POLICY "Service role full access" ON profiles IS 'Allows service role operations for API endpoints';