import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.0.0';

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({
        error: 'Missing Authorization header'
      }), {
        status: 401,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json' 
        }
      });
    }

    // Create client with Auth context of the user that called the function.
    // This way your row-level-security (RLS) policies are applied.
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    );

    // Get the session or user object
    const token = authHeader.replace('Bearer ', '');
    const { data } = await supabaseClient.auth.getUser(token);
    const user = data.user;

    if (!user) {
      return new Response(JSON.stringify({
        error: 'Unauthorized'
      }), {
        status: 401,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json' 
        }
      });
    }

    // Example: Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        // Example: Get user's data with RLS applied
        const { data: userData, error: userError } = await supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (userError) {
          throw new Error(`Failed to fetch user data: ${userError.message}`);
        }

        return new Response(JSON.stringify({ user: userData }), {
          headers: { 
            ...corsHeaders,
            'Content-Type': 'application/json' 
          },
          status: 200,
        });

      case 'POST':
        // Example: Create new data with RLS applied
        const body = await req.json();
        
        const { data: newData, error: insertError } = await supabaseClient
          .from('some_table')
          .insert({
            ...body,
            user_id: user.id, // Ensure user owns the data
          })
          .select()
          .single();

        if (insertError) {
          throw new Error(`Failed to create data: ${insertError.message}`);
        }

        return new Response(JSON.stringify({ data: newData }), {
          headers: { 
            ...corsHeaders,
            'Content-Type': 'application/json' 
          },
          status: 201,
        });

      default:
        return new Response(JSON.stringify({
          error: 'Method not allowed'
        }), {
          status: 405,
          headers: { 
            ...corsHeaders,
            'Content-Type': 'application/json' 
          }
        });
    }

  } catch (err: any) {
    console.error('Edge function error:', err);
    
    return new Response(JSON.stringify({
      error: err.message || 'Internal server error'
    }), {
      status: 500,
      headers: { 
        ...corsHeaders,
        'Content-Type': 'application/json' 
      }
    });
  }
});

/* 
IMPORTANT NOTES FOR EDGE FUNCTIONS:

1. **Always create client with user auth context** for RLS enforcement:
   ```typescript
   const supabaseClient = createClient(
     Deno.env.get('SUPABASE_URL') ?? '',
     Deno.env.get('SUPABASE_ANON_KEY') ?? '',
     {
       global: {
         headers: { Authorization: req.headers.get('Authorization')! },
       },
     }
   );
   ```

2. **Use service role client only when necessary** (for admin operations):
   ```typescript
   const supabaseAdmin = createClient(
     Deno.env.get('SUPABASE_URL') ?? '',
     Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
   );
   ```

3. **Always validate Authorization header** before proceeding.

4. **Extract user from token** for user context:
   ```typescript
   const token = authHeader.replace('Bearer ', '');
   const { data } = await supabaseClient.auth.getUser(token);
   const user = data.user;
   ```

5. **Use consistent error handling** with proper HTTP status codes.

6. **Include CORS headers** in all responses.

7. **Type your requests** with TypeScript for better development experience.
*/
