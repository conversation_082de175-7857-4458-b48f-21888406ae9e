"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle, ArrowLeft, Mail, RefreshCw, Loader2 } from "lucide-react"
import Link from "next/link"

function AuthCodeErrorContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [countdown, setCountdown] = useState(10)

  // Get error message from URL params
  const errorMessage = searchParams.get('error')

  // Auto-redirect to login after 10 seconds
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          router.push('/login')
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [router])

  const handleResendEmail = () => {
    // Redirect to reset password page for password reset errors
    if (errorMessage && errorMessage.toLowerCase().includes('recovery')) {
      router.push('/reset-password')
    } else {
      // Redirect to login with a flag to show resend option
      router.push('/login?resend=true')
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-xl shadow-2xl p-8 text-center">
          {/* Error Icon */}
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>

          {/* Title */}
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Authentication Error
          </h1>

          {/* Description */}
          <div className="text-gray-600 mb-8 space-y-3">
            {errorMessage && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-700 font-medium">
                  Error: {errorMessage}
                </p>
              </div>
            )}
            <p>
              We couldn't verify your email confirmation link. This could happen if:
            </p>
            <ul className="text-left text-sm space-y-2 bg-gray-50 p-4 rounded-lg">
              <li>• The link has expired</li>
              <li>• The link has already been used</li>
              <li>• The link was corrupted or incomplete</li>
            </ul>
          </div>

          {/* Actions */}
          <div className="space-y-4">
            <Button
              onClick={handleResendEmail}
              className="w-full bg-[#3A56FF] hover:bg-[#2A46EF] text-white"
            >
              <Mail className="w-4 h-4 mr-2" />
              {errorMessage && errorMessage.toLowerCase().includes('recovery')
                ? 'Request New Password Reset'
                : 'Request New Confirmation Email'}
            </Button>

            <Link href="/login">
              <Button variant="outline" className="w-full">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Login
              </Button>
            </Link>
          </div>

          {/* Auto-redirect notice */}
          <div className="mt-6 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-700">
              <RefreshCw className="w-4 h-4 inline mr-1" />
              Automatically redirecting to login in {countdown} seconds
            </p>
          </div>

          {/* Help text */}
          <div className="mt-6 text-xs text-gray-500">
            <p>
              Still having trouble? Contact support at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-[#3A56FF] hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

function AuthCodeErrorFallback() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-xl shadow-2xl p-8 text-center">
          <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <Loader2 className="w-8 h-8 text-gray-400 animate-spin" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Loading...
          </h1>
          <p className="text-gray-600">
            Please wait while we process your request.
          </p>
        </div>
      </div>
    </div>
  )
}

export default function AuthCodeErrorPage() {
  return (
    <Suspense fallback={<AuthCodeErrorFallback />}>
      <AuthCodeErrorContent />
    </Suspense>
  )
}
