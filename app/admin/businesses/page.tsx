"use client"

import { <PERSON>Header } from "@/components/page-header"
import { ProtectedRoute } from "@/components/protected-route"
import { AdminBusinessManagement } from "@/components/admin/admin-business-management"

export default function AdminBusinessesPage() {
  return (
    <ProtectedRoute requiredRole="admin">
      <PageHeader
        title="Business Management"
        subtitle="ADMIN PORTAL"
        description="Manage all businesses and access requests in the Fuse VIP network."
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          <AdminBusinessManagement />
        </div>
      </section>
    </ProtectedRoute>
  )
}