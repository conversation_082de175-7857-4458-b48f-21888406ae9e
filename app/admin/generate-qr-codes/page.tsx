"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { QrCode, Users, CheckCircle, XCircle, RefreshCw } from "lucide-react";
import { toast } from "sonner";

interface GenerationResult {
  userId: string;
  success: boolean;
  error?: string;
}

interface BulkGenerationResponse {
  success: boolean;
  message: string;
  generated: number;
  errors: number;
  results: GenerationResult[];
}

export default function GenerateQRCodesPage() {
  const [adminKey, setAdminKey] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [results, setResults] = useState<BulkGenerationResponse | null>(null);

  const handleBulkGeneration = async () => {
    if (!adminKey.trim()) {
      toast.error("Admin key is required");
      return;
    }

    setIsGenerating(true);
    setResults(null);

    try {
      const response = await fetch(`/api/generate-qr-codes?adminKey=${encodeURIComponent(adminKey)}`, {
        method: 'GET',
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate QR codes');
      }

      setResults(result);
      
      if (result.success) {
        toast.success(`Successfully generated ${result.generated} QR codes!`);
      } else {
        toast.error(result.message || 'Failed to generate QR codes');
      }
    } catch (error) {
      console.error('Error generating QR codes:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to generate QR codes');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              QR Code Generation Utility
            </h1>
            <p className="text-xl text-gray-600">
              Generate QR codes for all users who don't have them yet
            </p>
          </div>

          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                Bulk QR Code Generation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label htmlFor="adminKey" className="block text-sm font-medium text-gray-700 mb-2">
                  Admin Key
                </label>
                <Input
                  id="adminKey"
                  type="password"
                  value={adminKey}
                  onChange={(e) => setAdminKey(e.target.value)}
                  placeholder="Enter admin key"
                  className="max-w-md"
                />
              </div>

              <Button
                onClick={handleBulkGeneration}
                disabled={isGenerating || !adminKey.trim()}
                className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    Generating QR Codes...
                  </>
                ) : (
                  <>
                    <QrCode className="h-4 w-4" />
                    Generate QR Codes for All Users
                  </>
                )}
              </Button>

              <Alert>
                <QrCode className="h-4 w-4" />
                <AlertDescription>
                  This will generate QR codes for all users who don't currently have one. 
                  The process may take a few minutes depending on the number of users.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {results && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Generation Results
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{results.generated}</div>
                    <div className="text-sm text-gray-600">Successfully Generated</div>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{results.errors}</div>
                    <div className="text-sm text-gray-600">Errors</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{results.results.length}</div>
                    <div className="text-sm text-gray-600">Total Processed</div>
                  </div>
                </div>

                <Alert className="mb-4">
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    {results.message}
                  </AlertDescription>
                </Alert>

                {results.results.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Detailed Results:</h4>
                    <div className="max-h-96 overflow-y-auto space-y-2">
                      {results.results.map((result, index) => (
                        <div
                          key={index}
                          className={`flex items-center justify-between p-3 rounded-lg ${
                            result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                          }`}
                        >
                          <div className="flex items-center gap-2">
                            {result.success ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-600" />
                            )}
                            <span className="font-mono text-sm">{result.userId}</span>
                          </div>
                          {result.error && (
                            <span className="text-sm text-red-600">{result.error}</span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          <div className="mt-8 text-center">
            <Alert className="max-w-2xl mx-auto">
              <QrCode className="h-4 w-4" />
              <AlertDescription>
                <strong>Note:</strong> This is an admin utility. QR codes are automatically generated 
                for new users during registration. Use this tool only to generate QR codes for 
                existing users who registered before the QR code feature was implemented.
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    </div>
  );
}
