"use client"

import { useState, useEffect } from "react"
import { setDirectAuthMode, shouldUseDirectAuth } from "@/lib/auth-direct"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, CheckCircle, Database, Cloud } from "lucide-react"

export default function AuthModePage() {
  const [isDirectMode, setIsDirectMode] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    setIsDirectMode(shouldUseDirectAuth())
  }, [])

  const toggleAuthMode = async () => {
    setIsLoading(true)
    try {
      const newMode = !isDirectMode
      setDirectAuthMode(newMode)
      setIsDirectMode(newMode)
      
      // Show confirmation
      if (newMode) {
        alert("✅ Direct auth mode enabled. Authentication will bypass Supabase auth service.")
      } else {
        alert("✅ Standard auth mode enabled. Authentication will use Supabase auth service.")
      }
    } catch (error) {
      console.error("Error toggling auth mode:", error)
      alert("❌ Failed to toggle auth mode")
    } finally {
      setIsLoading(false)
    }
  }

  const testConnection = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth-direct', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get_user',
          email: '<EMAIL>'
        }),
      })

      if (response.status === 404) {
        alert("✅ Direct auth connection working (test user not found, which is expected)")
      } else if (response.ok) {
        alert("✅ Direct auth connection working")
      } else {
        const data = await response.json()
        alert(`⚠️ Direct auth response: ${response.status} - ${data.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error("Connection test error:", error)
      alert("❌ Connection test failed: " + error.message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Authentication Mode Control</h1>
        <p className="text-gray-600">
          Manage the authentication system mode and test direct database connections.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Current Mode Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {isDirectMode ? (
                <>
                  <Database className="h-5 w-5 text-orange-500" />
                  Direct Auth Mode
                </>
              ) : (
                <>
                  <Cloud className="h-5 w-5 text-blue-500" />
                  Standard Auth Mode
                </>
              )}
            </CardTitle>
            <CardDescription>
              Current authentication method being used
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Badge variant={isDirectMode ? "destructive" : "default"}>
                  {isDirectMode ? "DIRECT DATABASE" : "SUPABASE AUTH"}
                </Badge>
                {isDirectMode ? (
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                ) : (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
              </div>
              
              <div className="text-sm text-gray-600">
                {isDirectMode ? (
                  <>
                    <p className="mb-2">
                      <strong>Direct Mode:</strong> Authentication bypasses Supabase auth service 
                      and connects directly to the database.
                    </p>
                    <p>
                      Use this mode when Supabase auth service is experiencing 503 errors 
                      or other service disruptions.
                    </p>
                  </>
                ) : (
                  <>
                    <p className="mb-2">
                      <strong>Standard Mode:</strong> Authentication uses Supabase auth service 
                      with direct database fallback.
                    </p>
                    <p>
                      This is the recommended mode for normal operation. The system will 
                      automatically fall back to direct auth if Supabase auth fails.
                    </p>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Controls Card */}
        <Card>
          <CardHeader>
            <CardTitle>Controls</CardTitle>
            <CardDescription>
              Test connections and toggle authentication modes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button
                onClick={testConnection}
                disabled={isLoading}
                variant="outline"
                className="w-full"
              >
                {isLoading ? "Testing..." : "Test Direct Auth Connection"}
              </Button>
              
              <Button
                onClick={toggleAuthMode}
                disabled={isLoading}
                className="w-full"
                variant={isDirectMode ? "default" : "destructive"}
              >
                {isLoading ? "Switching..." : (
                  isDirectMode ? "Switch to Standard Mode" : "Switch to Direct Mode"
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Information Card */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>How It Works</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Automatic Fallback</h4>
              <p className="text-gray-600">
                When users try to sign in/up and Supabase auth returns 503 errors, 
                the system automatically retries 3 times, then falls back to direct 
                database authentication.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Manual Override</h4>
              <p className="text-gray-600">
                You can manually enable direct auth mode using the toggle above. 
                This forces all authentication to use the direct database connection.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Database Connection</h4>
              <p className="text-gray-600">
                Direct auth connects to: <code className="bg-gray-100 px-1 rounded">
                  postgresql://postgres:[PASSWORD]@db.haqbtbpmyadkocakqnew.supabase.co:5432/postgres
                </code>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
