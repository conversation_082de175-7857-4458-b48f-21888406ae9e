"use client";

import { useState, useEffect } from "react";
import { PageHeader } from "@/components/page-header";
import { AnimatedCard } from "@/components/animated-card";
import { useAuth } from "@/contexts/auth-context";
import { getSupabaseClient } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import { BarChart3, TrendingUp, Users, Calendar, MapPin } from "lucide-react";

interface BusinessVisitStats {
  business_id: string;
  business_name: string;
  business_category: string;
  total_visits: number;
  unique_visitors: number;
  visits_this_week: number;
  visits_this_month: number;
  last_visit: string;
}

export default function BusinessVisitsContent() {
  const { user } = useAuth();
  const [stats, setStats] = useState<BusinessVisitStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalStats, setTotalStats] = useState({
    totalVisits: 0,
    totalBusinesses: 0,
    totalUniqueVisitors: 0,
    totalFuseDistributed: 0
  });

  useEffect(() => {
    if (user) {
      loadBusinessVisitStats();
    }
  }, [user]);

  async function loadBusinessVisitStats() {
    setLoading(true);
    try {
      // Get current date ranges
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Fetch QR interaction data for business scans
      const supabase = getSupabaseClient();
      if (!supabase) {
        console.error('Supabase client not available');
        setLoading(false);
        return;
      }

      const { data: visits, error } = await supabase
        .from('qr_interactions')
        .select(`
          scanned_business_id,
          scanner_user_id,
          created_at,
          interaction_metadata,
          businesses:scanned_business_id (
            name,
            category
          )
        `)
        .eq('interaction_type', 'business_scan')
        .order('created_at', { ascending: false });

      if (error) {
        console.error("Error loading QR interactions:", error);
        return;
      }

      if (!visits || visits.length === 0) {
        console.log("No business visits found");
        setStats([]);
        return;
      }

      // Group visits by business
      const businessVisitMap = new Map<string, {
        business_id: string;
        business_name: string;
        business_category: string;
        visits: any[];
        unique_visitors: Set<string>;
        visits_this_week: number;
        visits_this_month: number;
        last_visit: string;
      }>();

      visits.forEach((visit: any) => {
        const businessId = visit.scanned_business_id;
        const businessName = visit.businesses?.name || 'Unknown Business';
        const businessCategory = visit.businesses?.category || 'Uncategorized';
        const visitDate = new Date(visit.created_at);
        const visitorId = visit.scanner_user_id;

        if (!businessVisitMap.has(businessId)) {
          businessVisitMap.set(businessId, {
            business_id: businessId,
            business_name: businessName,
            business_category: businessCategory,
            visits: [],
            unique_visitors: new Set(),
            visits_this_week: 0,
            visits_this_month: 0,
            last_visit: visit.created_at
          });
        }

        const businessData = businessVisitMap.get(businessId)!;
        businessData.visits.push(visit);
        businessData.unique_visitors.add(visitorId);

        // Count visits in time ranges
        if (visitDate >= oneWeekAgo) {
          businessData.visits_this_week++;
        }
        if (visitDate >= oneMonthAgo) {
          businessData.visits_this_month++;
        }

        // Update last visit if this is more recent
        if (new Date(visit.created_at) > new Date(businessData.last_visit)) {
          businessData.last_visit = visit.created_at;
        }
      });

      // Convert to stats array
      const businessStats: BusinessVisitStats[] = Array.from(businessVisitMap.values()).map(business => ({
        business_id: business.business_id,
        business_name: business.business_name,
        business_category: business.business_category,
        total_visits: business.visits.length,
        unique_visitors: business.unique_visitors.size,
        visits_this_week: business.visits_this_week,
        visits_this_month: business.visits_this_month,
        last_visit: business.last_visit
      }));

      // Sort by total visits descending
      businessStats.sort((a, b) => b.total_visits - a.total_visits);

      setStats(businessStats);

      // Calculate totals
      const totals = {
        totalVisits: businessStats.reduce((sum, b) => sum + b.total_visits, 0),
        totalBusinesses: businessStats.length,
        totalUniqueVisitors: new Set(visits.map((v: any) => v.scanner_user_id)).size,
        totalFuseDistributed: businessStats.reduce((sum, b) => sum + b.total_visits, 0) // 1 FUSE per visit
      };

      setTotalStats(totals);

    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <ProtectedRoute requiredRole="admin">
      <PageHeader
        title="Business Visit Analytics"
        subtitle="ADMIN PORTAL"
        description="Monitor QR code scans and FUSE reward distribution across all businesses."
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm text-center">
              <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-3xl font-bold text-gray-900">{totalStats.totalVisits}</div>
              <div className="text-gray-600 text-sm">Total Visits</div>
            </AnimatedCard>

            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm text-center">
              <MapPin className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-3xl font-bold text-gray-900">{totalStats.totalBusinesses}</div>
              <div className="text-gray-600 text-sm">Active Businesses</div>
            </AnimatedCard>

            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm text-center">
              <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-3xl font-bold text-gray-900">{totalStats.totalUniqueVisitors}</div>
              <div className="text-gray-600 text-sm">Unique Visitors</div>
            </AnimatedCard>

            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm text-center">
              <TrendingUp className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-3xl font-bold text-gray-900">{totalStats.totalFuseDistributed}</div>
              <div className="text-gray-600 text-sm">FUSE Distributed</div>
            </AnimatedCard>
          </div>

          {/* Business Visit Details */}
          <AnimatedCard className="bg-white rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Business Visit Details</h2>
              <p className="text-gray-600 mt-1">Detailed breakdown of QR code scans by business</p>
            </div>

            {loading ? (
              <div className="p-12 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading business visit data...</p>
              </div>
            ) : stats.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Business
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Visits
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unique Visitors
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        This Week
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        This Month
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        FUSE Distributed
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Visit
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {stats.map((business) => (
                      <tr key={business.business_id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {business.business_name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {business.business_category}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-semibold text-gray-900">
                            {business.total_visits}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {business.unique_visitors}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {business.visits_this_week}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {business.visits_this_month}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-semibold text-green-600">
                            {business.total_visits} FUSE
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatDate(business.last_visit)}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No business visits yet
                </h3>
                <p className="text-gray-600">
                  Business visit data will appear here once customers start scanning QR codes.
                </p>
              </div>
            )}
          </AnimatedCard>
        </div>
      </section>
    </ProtectedRoute>
  );
}
