'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'

export async function login(formData: FormData) {
  const supabase = await createClient()

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  try {
    const { error } = await supabase.auth.signInWithPassword(data)

    if (error) {
      console.error('Login error:', error)

      // Handle 503 Service Unavailable errors
      if (error.message?.includes('503') || error.message?.includes('Service Unavailable')) {
        redirect('/error?message=Authentication service is temporarily unavailable. Please try again in a few minutes.')
      }

      redirect('/error?message=' + encodeURIComponent(error.message))
    }

    revalidatePath('/', 'layout')
    redirect('/dashboard')
  } catch (error: any) {
    console.error('Login error:', error)

    // Handle network errors and 503 responses
    if (error.status === 503 || error.message?.includes('503') || error.message?.includes('Service Unavailable')) {
      redirect('/error?message=Authentication service is temporarily unavailable. Please try again in a few minutes.')
    }

    redirect('/error?message=An unexpected error occurred during login.')
  }
}

export async function signup(formData: FormData) {
  const supabase = await createClient()

  // type-casting here for convenience
  // in practice, you should validate your inputs
  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  try {
    const { error } = await supabase.auth.signUp(data)

    if (error) {
      console.error('Signup error:', error)

      // Handle 503 Service Unavailable errors
      if (error.message?.includes('503') || error.message?.includes('Service Unavailable')) {
        redirect('/error?message=Authentication service is temporarily unavailable. Please try again in a few minutes.')
      }

      redirect('/error?message=' + encodeURIComponent(error.message))
    }

    revalidatePath('/', 'layout')
    redirect('/dashboard')
  } catch (error: any) {
    console.error('Signup error:', error)

    // Handle network errors and 503 responses
    if (error.status === 503 || error.message?.includes('503') || error.message?.includes('Service Unavailable')) {
      redirect('/error?message=Authentication service is temporarily unavailable. Please try again in a few minutes.')
    }

    redirect('/error?message=An unexpected error occurred during signup.')
  }
}

export async function signOut() {
  const supabase = await createClient()
  await supabase.auth.signOut()
  revalidatePath('/', 'layout')
  redirect('/login')
}
