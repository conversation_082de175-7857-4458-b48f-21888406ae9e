"use client"

import dynamic from "next/dynamic"
import { PageHeader } from "@/components/page-header"
import { Loader2 } from "lucide-react"

const LoginFormWrapper = dynamic(
  () => import("@/components/auth/login-form-wrapper").then(mod => ({ default: mod.LoginFormWrapper })),
  {
    ssr: false,
    loading: () => (
      <div className="w-full max-w-md mx-auto">
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3A56FF]" />
            <p className="text-gray-600">Loading login form...</p>
          </div>
        </div>
      </div>
    )
  }
)

export default function LoginPage() {
  return (
    <div className="container max-w-screen-xl mx-auto py-12">
      <PageHeader title="Sign In" description="Sign in to access your account and manage your business" />
      <div className="mt-8">
        <LoginFormWrapper />
      </div>
    </div>
  )
}
