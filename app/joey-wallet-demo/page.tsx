"use client"

import { useState } from 'react'
import { motion } from 'framer-motion'
import { JoeyWalletConnect } from '@/components/wallet/joey-wallet-connect'
import { JoeyWalletSession } from '@/lib/joey-wallet'

export default function JoeyWalletDemoPage() {
  const [session, setSession] = useState<JoeyWalletSession | null>(null)
  const [error, setError] = useState('')

  const handleConnect = (newSession: JoeyWalletSession) => {
    setSession(newSession)
    setError('')
    console.log('Joey Wallet connected:', newSession)
  }

  const handleDisconnect = () => {
    setSession(null)
    setError('')
    console.log('Joey Wallet disconnected')
  }

  const handleError = (errorMessage: string) => {
    setError(errorMessage)
    console.error('Joey Wallet error:', errorMessage)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900">
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-2xl mx-auto"
        >
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              Joey Wallet Integration Demo
            </h1>
            <p className="text-gray-300">
              Test the WalletConnect v2 integration with Joey Wallet for XRPL payments
            </p>
          </div>

          {/* Joey Wallet Connection Component */}
          <div className="mb-8">
            <JoeyWalletConnect
              onConnect={handleConnect}
              onDisconnect={handleDisconnect}
              onError={handleError}
            />
          </div>

          {/* Session Information */}
          {session && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-green-900/20 border border-green-800 rounded-xl p-6 mb-6"
            >
              <h3 className="text-xl font-bold text-green-400 mb-4">Connection Details</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">Address:</span>
                  <span className="text-white font-mono">{session.address}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Connected:</span>
                  <span className="text-white">{session.connected ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Timestamp:</span>
                  <span className="text-white">{new Date(session.timestamp).toLocaleString()}</span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-red-900/20 border border-red-800 rounded-xl p-6 mb-6"
            >
              <h3 className="text-xl font-bold text-red-400 mb-2">Error</h3>
              <p className="text-red-300">{error}</p>
            </motion.div>
          )}

          {/* Instructions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-blue-900/20 border border-blue-800 rounded-xl p-6"
          >
            <h3 className="text-xl font-bold text-blue-400 mb-4">How to Test</h3>
            <ol className="space-y-2 text-gray-300 text-sm">
              <li className="flex">
                <span className="text-blue-400 mr-2">1.</span>
                Download Joey Wallet from the App Store or Google Play
              </li>
              <li className="flex">
                <span className="text-blue-400 mr-2">2.</span>
                Set up your wallet and ensure it's connected to XRPL Mainnet
              </li>
              <li className="flex">
                <span className="text-blue-400 mr-2">3.</span>
                Click "Connect Joey Wallet" above
              </li>
              <li className="flex">
                <span className="text-blue-400 mr-2">4.</span>
                Open Joey Wallet app when prompted (or scan QR code)
              </li>
              <li className="flex">
                <span className="text-blue-400 mr-2">5.</span>
                Approve the connection in Joey Wallet
              </li>
            </ol>
          </motion.div>

          {/* Technical Note */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-8 bg-yellow-900/20 border border-yellow-800 rounded-xl p-6"
          >
            <h3 className="text-xl font-bold text-yellow-400 mb-4">Technical Note</h3>
            <p className="text-gray-300 text-sm">
              This demo uses WalletConnect v2 to establish a connection with Joey Wallet. 
              The transaction signing functionality is currently a placeholder - in production, 
              you would implement the actual transaction signing flow using WalletConnect session requests.
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}