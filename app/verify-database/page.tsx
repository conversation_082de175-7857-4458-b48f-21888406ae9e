"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, Database, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface TableInfo {
  accessible: boolean
  record_count: number
  table_size: string
  error: string | null
}

interface VerificationResult {
  timestamp: string
  connection: boolean
  tables: Record<string, TableInfo>
  summary: {
    total_tables: number
    accessible_tables: number
    failed_tables: number
    total_records: number
  }
  relationship_test?: {
    success: boolean
    sample_data?: any[]
    error?: string
  }
}

export default function VerifyDatabasePage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<VerificationResult | null>(null)
  const [error, setError] = useState<string | null>(null)

  const runVerification = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/verify-database')
      const data = await response.json()
      
      if (data.success) {
        setResult(data.data)
      } else {
        setError(data.error || 'Verification failed')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    runVerification()
  }, [])

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Database className="w-8 h-8" />
            Database Verification
          </h1>
          <p className="text-gray-600 mt-2">
            Comprehensive check of all database tables and connections
          </p>
        </div>
        
        <Button onClick={runVerification} disabled={loading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Verifying...' : 'Run Verification'}
        </Button>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <XCircle className="w-5 h-5" />
              <span className="font-medium">Verification Failed</span>
            </div>
            <p className="text-red-600 mt-2">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  {result.connection ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500" />
                  )}
                  <span className="font-medium">Connection</span>
                </div>
                <p className="text-2xl font-bold mt-2">
                  {result.connection ? 'Active' : 'Failed'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <Database className="w-5 h-5 text-blue-500" />
                  <span className="font-medium">Tables</span>
                </div>
                <p className="text-2xl font-bold mt-2">
                  {result.summary.accessible_tables}/{result.summary.total_tables}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="font-medium">Records</span>
                </div>
                <p className="text-2xl font-bold mt-2">
                  {result.summary.total_records.toLocaleString()}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  {result.summary.failed_tables === 0 ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-yellow-500" />
                  )}
                  <span className="font-medium">Status</span>
                </div>
                <p className="text-2xl font-bold mt-2">
                  {result.summary.failed_tables === 0 ? 'Healthy' : 'Issues'}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Tables Detail */}
          <Card>
            <CardHeader>
              <CardTitle>Table Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(result.tables).map(([tableName, info]) => (
                  <div key={tableName} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {info.accessible ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-500" />
                      )}
                      <span className="font-medium">{tableName}</span>
                      {info.error && (
                        <Badge variant="destructive" className="text-xs">
                          Error
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>{info.record_count.toLocaleString()} records</span>
                      <span>{info.table_size}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Relationship Test */}
          {result.relationship_test && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {result.relationship_test.success ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500" />
                  )}
                  Relationship Test
                </CardTitle>
              </CardHeader>
              <CardContent>
                {result.relationship_test.success ? (
                  <div>
                    <p className="text-green-600 mb-3">✅ Table relationships working correctly</p>
                    {result.relationship_test.sample_data && result.relationship_test.sample_data.length > 0 && (
                      <div className="text-sm">
                        <p className="font-medium mb-2">Sample joined data:</p>
                        <pre className="bg-gray-100 p-3 rounded text-xs overflow-x-auto">
                          {JSON.stringify(result.relationship_test.sample_data.slice(0, 2), null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-red-600">❌ {result.relationship_test.error}</p>
                )}
              </CardContent>
            </Card>
          )}

          <div className="text-sm text-gray-500">
            Last verified: {new Date(result.timestamp).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  )
}
