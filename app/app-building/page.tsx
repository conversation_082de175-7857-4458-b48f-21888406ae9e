"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { ArrowRight, CheckCircle2, Smartphone, Code, Database, Zap, Cloud, Rocket, Star, Users } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { PageHeader } from "@/components/page-header"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function AppBuildingPage() {
  const [selectedTier, setSelectedTier] = useState("mobile")

  const appTools = [
    {
      name: "No-Code App Builder",
      description: "Build mobile and web apps without writing code",
      icon: <Smartphone className="w-8 h-8 text-[#4ECDC4]" />,
      fuseCost: "20 FUSE",
      features: ["Drag-and-drop interface", "Pre-built components", "Real-time preview"]
    },
    {
      name: "Cross-Platform Deploy",
      description: "Deploy to iOS, Android, and web simultaneously",
      icon: <Cloud className="w-8 h-8 text-[#4ECDC4]" />,
      fuseCost: "15 FUSE",
      features: ["Multi-platform builds", "App store optimization", "Automated testing"]
    },
    {
      name: "API & Database Tools",
      description: "Connect your app to databases and external APIs",
      icon: <Database className="w-8 h-8 text-[#4ECDC4]" />,
      fuseCost: "12 FUSE",
      features: ["Database integration", "REST API builder", "Real-time sync"]
    },
    {
      name: "App Store Publishing",
      description: "Streamlined publishing to app stores",
      icon: <Rocket className="w-8 h-8 text-[#4ECDC4]" />,
      fuseCost: "25 FUSE",
      features: ["Store submission", "Review assistance", "Marketing materials"]
    }
  ]

  const tiers = [
    {
      id: "mobile",
      name: "Mobile First",
      fuseCost: "40 FUSE",
      description: "Perfect for mobile app development",
      features: [
        "No-code app builder",
        "iOS & Android deployment",
        "Basic API integrations",
        "App store publishing",
        "Community support"
      ],
      popular: false
    },
    {
      id: "fullstack",
      name: "Full-Stack", 
      fuseCost: "75 FUSE",
      description: "Complete app development solution",
      features: [
        "Everything in Mobile First",
        "Web app deployment",
        "Advanced database tools",
        "Custom API development",
        "Priority support",
        "Analytics dashboard"
      ],
      popular: true
    },
    {
      id: "enterprise",
      name: "Enterprise",
      fuseCost: "150 FUSE",
      description: "For large-scale app projects",
      features: [
        "Everything in Full-Stack",
        "White-label solutions",
        "Custom integrations",
        "Dedicated infrastructure",
        "24/7 support",
        "Team collaboration tools"
      ],
      popular: false
    }
  ]

  return (
    <>
      <PageHeader
        title="App Building with $FUSE"
        subtitle="BUILD • DEPLOY • SCALE"
        description="Submit $FUSE tokens to access no-code app builders, cross-platform deployment, and professional publishing tools."
      />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-[#4ECDC4]/10 to-[#6FDDDD]/10">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-1/2">
              <Badge className="bg-[#4ECDC4]/20 text-[#4ECDC4] hover:bg-[#4ECDC4]/30 mb-6">
                App Building World
              </Badge>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Build Powerful Apps with <span className="text-[#4ECDC4]">$FUSE</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Create mobile and web applications using no-code platforms, deploy across multiple platforms, 
                and publish to app stores - all powered by $FUSE tokens.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-[#4ECDC4] to-[#6FDDDD] hover:from-[#3DBDB3] hover:to-[#5ECCCC] text-white"
                >
                  <Zap className="w-5 h-5 mr-2" />
                  Start Building Apps
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
                
                <Button variant="outline" size="lg">
                  <Smartphone className="w-5 h-5 mr-2" />
                  View App Examples
                </Button>
              </div>
            </div>

            <div className="lg:w-1/2">
              <div className="relative">
                <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <Badge className="bg-[#4ECDC4]/20 text-[#4ECDC4]">No-Code</Badge>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-20 bg-gradient-to-b from-[#4ECDC4] to-[#6FDDDD] rounded-lg flex items-center justify-center">
                        <Smartphone className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="h-3 bg-gradient-to-r from-[#4ECDC4] to-[#6FDDDD] rounded w-3/4 mb-2"></div>
                        <div className="h-2 bg-gray-200 rounded w-full"></div>
                        <div className="h-2 bg-gray-200 rounded w-5/6 mt-1"></div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-2 mt-6">
                      <div className="h-16 bg-gradient-to-br from-[#4ECDC4]/20 to-[#6FDDDD]/20 rounded-lg flex items-center justify-center">
                        <Code className="w-6 h-6 text-[#4ECDC4]" />
                      </div>
                      <div className="h-16 bg-gradient-to-br from-[#4ECDC4]/20 to-[#6FDDDD]/20 rounded-lg flex items-center justify-center">
                        <Database className="w-6 h-6 text-[#4ECDC4]" />
                      </div>
                      <div className="h-16 bg-gradient-to-br from-[#4ECDC4]/20 to-[#6FDDDD]/20 rounded-lg flex items-center justify-center">
                        <Cloud className="w-6 h-6 text-[#4ECDC4]" />
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-[#4ECDC4] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                  Cross-Platform
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* App Tools Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Professional App Development Tools
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Submit $FUSE tokens to unlock powerful no-code platforms and deployment tools for building world-class applications.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {appTools.map((tool, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 border border-gray-100 hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-gradient-to-br from-[#4ECDC4]/20 to-[#6FDDDD]/20 rounded-xl">
                    {tool.icon}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-xl font-bold text-gray-900">{tool.name}</h3>
                      <Badge className="bg-[#4ECDC4]/20 text-[#4ECDC4]">{tool.fuseCost}</Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{tool.description}</p>
                    
                    <div className="space-y-2">
                      {tool.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <CheckCircle2 className="w-4 h-4 text-[#4ECDC4]" />
                          <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Tiers */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Choose Your App Building Package
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Select the perfect package for your app development needs. All packages are unlocked with $FUSE tokens.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {tiers.map((tier) => (
              <div
                key={tier.id}
                className={`relative bg-white rounded-2xl p-8 border-2 transition-all duration-300 ${
                  tier.popular 
                    ? 'border-[#4ECDC4] shadow-lg scale-105' 
                    : 'border-gray-200 hover:border-[#4ECDC4]/50'
                }`}
              >
                {tier.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-[#4ECDC4] text-white px-4 py-1">Most Popular</Badge>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.name}</h3>
                  <div className="text-3xl font-bold text-[#4ECDC4] mb-2">{tier.fuseCost}</div>
                  <p className="text-gray-600">{tier.description}</p>
                </div>

                <div className="space-y-4 mb-8">
                  {tier.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <CheckCircle2 className="w-5 h-5 text-[#4ECDC4] flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <Button 
                  className={`w-full ${
                    tier.popular
                      ? 'bg-gradient-to-r from-[#4ECDC4] to-[#6FDDDD] hover:from-[#3DBDB3] hover:to-[#5ECCCC] text-white'
                      : 'bg-white border-2 border-[#4ECDC4] text-[#4ECDC4] hover:bg-[#4ECDC4] hover:text-white'
                  }`}
                  onClick={() => setSelectedTier(tier.id)}
                >
                  Submit {tier.fuseCost}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#4ECDC4] to-[#6FDDDD] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Launch Your App?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Join the next generation of app creators building with no-code platforms. 
            Start your app development journey with $FUSE tokens today.
          </p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button size="lg" className="bg-white text-[#4ECDC4] hover:bg-gray-100">
              <Zap className="w-5 h-5 mr-2" />
              Start Building Now
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            
            <Link href="/fuse">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <Smartphone className="w-5 h-5 mr-2" />
                Back to $FUSE Worlds
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </>
  )
}
