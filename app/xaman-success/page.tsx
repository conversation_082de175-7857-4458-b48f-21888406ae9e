'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle, Loader2, Wallet } from 'lucide-react';

function XamanSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing Xaman request...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get URL parameters
        const txid = searchParams.get('txid');
        const result = searchParams.get('result');
        const uuid = searchParams.get('uuid');
        const type = searchParams.get('type') || 'transaction';

        if (result === 'tesSUCCESS' && txid) {
          setStatus('success');
          setMessage(`${type.charAt(0).toUpperCase() + type.slice(1)} completed successfully!`);
          
          // Redirect based on type
          setTimeout(() => {
            if (type === 'signin' || type === 'connect') {
              router.push('/dashboard');
            } else if (type === 'payment') {
              router.push('/dashboard/cards');
            } else {
              router.push('/dashboard');
            }
          }, 2000);
        } else if (result === 'rejected') {
          setStatus('error');
          setMessage('Request was rejected. Please try again.');
        } else if (result === 'cancelled') {
          setStatus('error');
          setMessage('Request was cancelled. Please try again.');
        } else {
          setStatus('error');
          setMessage('Request failed. Please try again.');
        }
      } catch (error) {
        console.error('Xaman callback processing error:', error);
        setStatus('error');
        setMessage('An error occurred while processing the request.');
      }
    };

    handleCallback();
  }, [searchParams, router]);

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-12 w-12 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-12 w-12 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-12 w-12 text-red-500" />;
    }
  };

  const getButtonText = () => {
    switch (status) {
      case 'success':
        return 'Continue';
      case 'error':
        return 'Try Again';
      default:
        return 'Please wait...';
    }
  };

  const handleButtonClick = () => {
    if (status === 'success') {
      router.push('/dashboard');
    } else if (status === 'error') {
      router.push('/wallet');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {getIcon()}
          </div>
          <CardTitle className="text-2xl">
            {status === 'loading' && 'Processing...'}
            {status === 'success' && 'Success!'}
            {status === 'error' && 'Request Failed'}
          </CardTitle>
          <CardDescription className="text-lg">
            {message}
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <div className="flex items-center justify-center gap-2 mb-4 text-sm text-gray-600">
            <Wallet className="h-4 w-4" />
            <span>Xaman Wallet Integration</span>
          </div>
          
          {status !== 'loading' && (
            <Button 
              onClick={handleButtonClick}
              className="w-full"
              variant={status === 'error' ? 'outline' : 'default'}
            >
              {getButtonText()}
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function XamanSuccessLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
          </div>
          <CardTitle className="text-2xl">Processing...</CardTitle>
          <CardDescription className="text-lg">
            Please wait while we process your Xaman request...
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
            <Wallet className="h-4 w-4" />
            <span>Xaman Wallet Integration</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function XamanSuccessPage() {
  return (
    <Suspense fallback={<XamanSuccessLoading />}>
      <XamanSuccessContent />
    </Suspense>
  );
}
