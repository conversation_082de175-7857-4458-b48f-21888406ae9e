"use client";

import { useAuth } from "@/contexts/auth-context";
import { getSupabaseClient } from "@/lib/supabase";
import { useState, useEffect } from "react";

export default function DebugAuthPage() {
  const { user, profile, isBusinessOwner, isLoading } = useAuth();
  const [businessData, setBusinessData] = useState<any>(null);
  const [authDebugInfo, setAuthDebugInfo] = useState<any>(null);

  useEffect(() => {
    const debugAuth = async () => {
      if (!user) return;

      try {
        // Get current session info
        const supabase = getSupabaseClient();
        if (!supabase) return;
        const { data: sessionData } = await supabase.auth.getSession();
        
        // Test the authenticated API endpoint
        const businessResponse = await fetch('/api/dashboard/business', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache',
          },
        });
        const businessResult = await businessResponse.json();

        // Check localStorage for any auth overrides
        const localStorageAuth = typeof window !== 'undefined' ? {
          directAuth: localStorage.getItem('use_direct_auth'),
          storedToken: localStorage.getItem('supabase.auth.token'),
        } : null;

        setAuthDebugInfo({
          currentUser: {
            id: user.id,
            email: user.email,
          },
          session: {
            hasSession: !!sessionData.session,
            expiresAt: sessionData.session?.expires_at,
          },
          profile: profile,
          isBusinessOwner,
          localStorage: localStorageAuth,
        });

        setBusinessData(businessResult);
      } catch (error) {
        console.error('Debug auth error:', error);
      }
    };

    debugAuth();
  }, [user, profile, isBusinessOwner]);

  const handleClearAuth = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('use_direct_auth');
      localStorage.removeItem('supabase.auth.token');
      window.location.reload();
    }
  };

  const handleTestBusiness = () => {
    if (typeof window !== 'undefined') {
      window.location.href = '/dashboard/business';
    }
  };

  if (isLoading) {
    return <div className="p-8">Loading authentication debug info...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Authentication Debug Information</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Current Auth State */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Current Auth State</h2>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(authDebugInfo, null, 2)}
            </pre>
          </div>

          {/* Business Data */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Business Data</h2>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(businessData, null, 2)}
            </pre>
          </div>
        </div>

        {/* Expected vs Actual */}
        <div className="mt-8 bg-gray-800 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Expected vs Actual</h2>
          <div className="space-y-2">
            <p><strong>Expected User ID for "The Art of Pitching":</strong> 58dc33cf-44eb-4fa2-9da2-789db8a12913</p>
            <p><strong>Expected Email:</strong> <EMAIL></p>
            <p><strong>Current User ID:</strong> {user?.id || 'Not logged in'}</p>
            <p><strong>Current Email:</strong> {user?.email || 'Not logged in'}</p>
            <p><strong>Match:</strong> {user?.id === '58dc33cf-44eb-4fa2-9da2-789db8a12913' ? '✅ YES' : '❌ NO'}</p>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-8 space-x-4">
          <button
            onClick={handleClearAuth}
            className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded"
          >
            Clear Auth Cache & Reload
          </button>

          <button
            onClick={handleTestBusiness}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded"
          >
            Test Business Dashboard
          </button>
        </div>
      </div>
    </div>
  );
}
