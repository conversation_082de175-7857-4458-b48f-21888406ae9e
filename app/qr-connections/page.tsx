"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { QRInteractions } from "@/components/qr-interactions";
import { UserQRCode } from "@/components/user-qr-code";
import { ArrowLeft, QrCode, RefreshCw } from "lucide-react";

interface QRStats {
  userConnections: number;
  businessVisits: number;
}

export default function QRConnectionsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [stats, setStats] = useState<QRStats>({ userConnections: 0, businessVisits: 0 });
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  const fetchStats = async () => {
    if (!user) return;

    try {
      setIsLoadingStats(true);
      const response = await fetch(`/api/qr-interactions?userId=${user.id}&type=stats`);
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchStats();
    }
  }, [user]);

  const handleGoBack = () => {
    router.push('/dashboard');
  };

  const handleScanQR = () => {
    router.push('/scan');
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <Button
              onClick={handleGoBack}
              variant="ghost"
              className="text-gray-600 hover:bg-gray-100"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Button>
            
            <Button
              onClick={handleScanQR}
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
            >
              <QrCode className="h-4 w-4" />
              Scan QR Code
            </Button>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                QR Code Connections
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Manage your QR code and view all your connections with other users and businesses
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
              {/* User QR Code */}
              <div className="flex justify-center">
                <UserQRCode size={320} />
              </div>

              {/* Quick Stats */}
              <div className="space-y-6">
                <div className="bg-white rounded-xl p-6 shadow-lg border">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Connection Stats
                    </h3>
                    {isLoadingStats && <RefreshCw className="h-4 w-4 animate-spin text-gray-400" />}
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {isLoadingStats ? '...' : stats.userConnections}
                      </div>
                      <div className="text-sm text-gray-600">User Connections</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {isLoadingStats ? '...' : stats.businessVisits}
                      </div>
                      <div className="text-sm text-gray-600">Business Visits</div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-lg border">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    How It Works
                  </h3>
                  <div className="space-y-3 text-sm text-gray-600">
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">1</div>
                      <p>Share your QR code with other Fuse.VIP users</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">2</div>
                      <p>Scan other users' QR codes to connect</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">3</div>
                      <p>Visit businesses and scan their QR codes for rewards</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">4</div>
                      <p>Build your network and unlock exclusive benefits</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* All Interactions */}
            <div className="mb-8">
              <QRInteractions limit={50} showTitle={true} />
            </div>

            {/* Call to Action */}
            <div className="text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">
                Start Building Your Network
              </h3>
              <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
                Connect with other Fuse.VIP users and businesses to unlock exclusive rewards, 
                discounts, and networking opportunities.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={handleScanQR}
                  className="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-3"
                >
                  <QrCode className="h-5 w-5 mr-2" />
                  Scan QR Code
                </Button>
                <Button
                  onClick={() => router.push('/industry')}
                  variant="outline"
                  className="border-white text-white hover:bg-white/10 font-semibold px-8 py-3"
                >
                  Find Businesses
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
