"use client"

import { <PERSON><PERSON>eader } from "@/components/page-header"
import { CtaSection } from "@/components/cta-section"
import {
  Search,
  MapPin,
  X,
  Tag,
  Check,
  Sparkles,
  ChevronRight,
  Eye,
} from "lucide-react"
import Image from "next/image"
import <PERSON>ript from "next/script"
import { AnimatedSection } from "@/components/animated-section"
import { AnimatedCard } from "@/components/animated-card"
import { AnimatedButton } from "@/components/animated-button"
import { useState, useEffect, useMemo, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { SiteFooter } from "@/components/site-footer"
import { useAuth } from "@/contexts/auth-context"
import { BusinessLogo } from "@/components/business-logo"
import ThemeToggle from "@/components/theme-toggle"
import { useTheme } from "next-themes"
import { SkyBackground } from "@/components/sky-background"

// Train Station Style Business Carousel Component
function BusinessCarousel({ businesses }: { businesses: any[] }) {
  const [currentBatch, setCurrentBatch] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const [expandedDiscount, setExpandedDiscount] = useState<string | null>(null)
  
  // Group businesses into batches of 5
  const batchSize = 5
  const batches = useMemo(() => {
    const chunks = []
    for (let i = 0; i < businesses.length; i += batchSize) {
      chunks.push(businesses.slice(i, i + batchSize))
    }
    return chunks
  }, [businesses])

  // Auto-cycle through batches
  useEffect(() => {
    if (batches.length <= 1 || isPaused) return

    const interval = setInterval(() => {
      setCurrentBatch((prev) => (prev + 1) % batches.length)
    }, 4000) // Change every 4 seconds

    return () => clearInterval(interval)
  }, [batches.length, isPaused])

  if (batches.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">No businesses found matching your criteria.</p>
      </div>
    )
  }

  const currentBusinesses = batches[currentBatch] || []
  const totalBatches = batches.length
  const totalBusinesses = businesses.length

  return (
    <div 
      className="relative"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      {/* Train Station Style Header */}
      <div className="bg-gradient-to-r from-gray-900/90 to-black/90 backdrop-blur-sm rounded-t-xl border border-[#316bff]/30 p-4 mb-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-white font-mono text-sm tracking-wide">DEPARTURES</span>
          </div>
          <div className="text-[#00d4ff] font-mono text-sm">
            BATCH {currentBatch + 1}/{totalBatches} • {totalBusinesses} TOTAL
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-3 bg-gray-800 rounded-full h-1">
          <div 
            className="bg-gradient-to-r from-[#316bff] to-[#00d4ff] h-1 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${((currentBatch + 1) / totalBatches) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Business Display Area */}
      <div className="bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm rounded-b-xl border-x border-b border-[#316bff]/30 min-h-[400px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentBatch}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            className="p-6"
          >
            <div className="space-y-3">
              {currentBusinesses.map((business, index) => (
                <motion.div
                  key={business.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="group relative bg-gradient-to-r from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-lg p-4 border border-gray-700/50 hover:border-[#316bff]/50 transition-all duration-300 cursor-pointer hover:bg-gradient-to-r hover:from-[#316bff]/10 hover:to-[#00d4ff]/10"
                  onClick={() => {
                    if (business.website) {
                      const url = business.website.startsWith('http') ? business.website : `https://${business.website}`
                      window.open(url, '_blank', 'noopener,noreferrer')
                    } else if (business.contact_info) {
                      // Show contact info in an alert or copy to clipboard
                      alert(`Contact Info for ${business.name}:\n${business.contact_info}`)
                    }
                  }}
                >
                  <div className="flex items-start gap-4">
                    {/* Standardized Logo Container */}
                    <BusinessLogo
                      src={business.logo_url}
                      alt={`${business.name} logo`}
                      businessName={business.name}
                      businessId={business.id}
                      size="medium"
                      futuristic={true}
                      glowEffect={true}
                    />

                    {/* Business Info - New Hierarchy */}
                    <div className="flex-1 min-w-0 space-y-2">
                      {/* PRIMARY: Business Name as Headline */}
                      <h3 className="text-lg md:text-xl font-bold text-white group-hover:text-[#00d4ff] transition-colors duration-300 leading-tight">
                        {business.name}
                      </h3>

                      {/* SECONDARY: Category and Address */}
                      <div className="space-y-1">
                        <p className="text-xs md:text-sm text-gray-400 uppercase tracking-wider font-mono">
                          {business.category}
                        </p>
                        {(business.business_address || business.address) && (
                          <div className="flex items-center text-xs text-gray-500">
                            <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                            <span className="truncate font-mono">{business.business_address || business.address}</span>
                          </div>
                        )}
                      </div>

                      {/* BADGES ROW: No longer competing with business name */}
                      <div className="flex flex-wrap gap-2 pt-1">
                        {business.premium_discount && (
                          <div className="flex items-center">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                setExpandedDiscount(business.id)
                              }}
                              className="group/discount bg-gradient-to-r from-[#FF5722] to-[#FF8A65] text-white text-xs px-2 py-1 rounded font-mono tracking-wide max-w-[200px] truncate hover:max-w-none hover:bg-gradient-to-r hover:from-[#FF6722] hover:to-[#FF9A75] transition-all duration-300 flex items-center gap-1"
                            >
                              <span className="truncate">{business.premium_discount}</span>
                              {business.premium_discount.length > 30 && (
                                <Eye className="h-3 w-3 text-white/80 group-hover/discount:text-white flex-shrink-0" />
                              )}
                            </button>
                          </div>
                        )}
                        {business.is_active && (
                          <span className="bg-gradient-to-r from-[#316bff] to-[#00d4ff] text-white text-xs px-2 py-1 rounded font-mono">
                            ACTIVE
                          </span>
                        )}
                        <div className="flex items-center gap-1 text-xs">
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                          <span className="text-green-400 font-mono">ONLINE</span>
                        </div>
                        {!business.website && business.contact_info && (
                          <span className="bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs px-2 py-1 rounded font-mono">
                            CONTACT INFO
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Navigation Arrows */}
        {totalBatches > 1 && (
          <div className="flex justify-between items-center px-6 pb-6">
            {/* Previous Button */}
            <button
              onClick={() => setCurrentBatch((prev) => (prev - 1 + totalBatches) % totalBatches)}
              className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-gray-800/60 to-gray-900/60 hover:from-[#316bff]/20 hover:to-[#00d4ff]/20 border border-gray-700/50 hover:border-[#316bff]/50 rounded-full transition-all duration-300 group"
              aria-label="Previous batch"
            >
              <svg className="w-5 h-5 text-gray-400 group-hover:text-[#00d4ff] transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Batch Indicator */}
            <div className="text-center">
              <div className="text-[#00d4ff] font-mono text-sm font-bold">
                {currentBatch + 1} / {totalBatches}
              </div>
              <div className="text-xs text-gray-500 font-mono">
                {totalBusinesses} BUSINESSES
              </div>
            </div>

            {/* Next Button */}
            <button
              onClick={() => setCurrentBatch((prev) => (prev + 1) % totalBatches)}
              className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-gray-800/60 to-gray-900/60 hover:from-[#316bff]/20 hover:to-[#00d4ff]/20 border border-gray-700/50 hover:border-[#316bff]/50 rounded-full transition-all duration-300 group"
              aria-label="Next batch"
            >
              <svg className="w-5 h-5 text-gray-400 group-hover:text-[#00d4ff] transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Navigation Help Text */}
      {totalBatches > 1 && (
        <div className="mt-4 text-center">
          <span className="text-xs text-gray-500 font-mono tracking-wide">
            {isPaused ? 'HOVER TO PAUSE • ' : ''}USE ARROWS TO NAVIGATE • AUTO-ADVANCING IN {isPaused ? 'PAUSED' : '4S'}
          </span>
        </div>
      )}

      {/* Discount Modal */}
      <AnimatePresence>
        {expandedDiscount && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setExpandedDiscount(null)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-gradient-to-br from-gray-900/95 to-black/95 backdrop-blur-lg rounded-xl p-6 max-w-lg w-full border border-[#316bff]/30 shadow-xl shadow-[#316bff]/20"
              onClick={(e) => e.stopPropagation()}
            >
              {(() => {
                const business = businesses.find(b => b.id === expandedDiscount)
                if (!business) return null
                
                return (
                  <>
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <BusinessLogo
                          src={business.logo_url}
                          alt={`${business.name} logo`}
                          businessName={business.name}
                          businessId={business.id}
                          size="small"
                          futuristic={true}
                          glowEffect={true}
                        />
                        <div>
                          <h3 className="text-lg font-bold text-white">{business.name}</h3>
                          <p className="text-xs text-gray-400 uppercase tracking-wider font-mono">
                            {business.category}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => setExpandedDiscount(null)}
                        className="text-gray-400 hover:text-white transition-colors p-1"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                    
                    <div className="bg-gradient-to-r from-[#FF5722]/20 to-[#FF8A65]/20 rounded-lg p-4 border border-[#FF5722]/30">
                      <div className="flex items-center gap-2 mb-2">
                        <Tag className="h-4 w-4 text-[#FF5722]" />
                        <span className="text-[#FF5722] font-semibold text-sm uppercase tracking-wide">Premium Discount</span>
                      </div>
                      <p className="text-white text-sm leading-relaxed">{business.premium_discount}</p>
                    </div>

                    {business.website && (
                      <div className="mt-4 pt-4 border-t border-gray-700">
                        <button
                          onClick={() => {
                            const url = business.website.startsWith('http') ? business.website : `https://${business.website}`
                            window.open(url, '_blank', 'noopener,noreferrer')
                          }}
                          className="w-full bg-gradient-to-r from-[#316bff] to-[#00d4ff] hover:from-[#2557d6] hover:to-[#00b8e6] text-white px-4 py-2 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 font-medium"
                        >
                          Visit Website
                          <ChevronRight className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </>
                )
              })()}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default function IndustryPage() {
  const [businesses, setBusinesses] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [stateFilter, setStateFilter] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [loading, setLoading] = useState(true)
  const [supabaseError, setSupabaseError] = useState<string | null>(null)

  // VIP Card state management
  const [user, setUser] = useState<any>(null)
  const [selectedCard, setSelectedCard] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState("")
  const [guestEmail, setGuestEmail] = useState("")
  const [showGuestEmailInput, setShowGuestEmailInput] = useState(false)

  // Use auth context
  const authContext = useAuth()
  
  // Theme toggle
  const { theme, setTheme } = useTheme()
  
  // Theme toggle function
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark")
  }

  // Use useEffect to safely set user state after component mounts
  useEffect(() => {
    if (authContext && authContext.user) {
      setUser(authContext.user)
    }
  }, [authContext])

  useEffect(() => {
    console.log('🚀 Component mounted, fetching businesses...')
    fetchBusinesses() // Load businesses when component mounts
  }, [])

  // Debug: Log when businesses state changes
  useEffect(() => {
    console.log(`🏢 Businesses state updated: ${businesses.length} businesses`)
  }, [businesses])

  // Note: No need to refetch when filters change since we do client-side filtering

  const fetchBusinesses = async () => {
    try {
      setLoading(true)
      setSupabaseError(null)

      console.log(`🔄 Fetching businesses via consolidated API...`)

      // Build query parameters for the working consolidated API
      const params = new URLSearchParams({
        fields: 'standard', // Use standard fields for good performance with owner info
        limit: '100' // Get all businesses for carousel
      })

      console.log(`📊 Fetching businesses with filters:`, {
        category: selectedCategory,
        state: stateFilter,
        search: searchTerm
      })

      // Use the working consolidated businesses API
      try {
        const apiUrl = `/api/businesses?${params}`
        const response = await fetch(apiUrl)

        if (response.ok) {
          const result = await response.json()
          console.log(`✅ Successfully loaded ${result.data?.length || 0} businesses via consolidated API`)

          // Store all businesses and let the filteredBusinesses useMemo handle filtering
          const allBusinesses = result.data || []
          console.log(`📊 Received ${allBusinesses.length} businesses from API`)
          console.log(`📋 Sample business:`, allBusinesses[0])

          console.log('🔄 About to set businesses state...')
          setBusinesses(allBusinesses)
          console.log('✅ Businesses state set successfully')
          setSupabaseError(null)
          return
        } else {
          const errorText = await response.text()
          console.error("❌ Consolidated API failed:", errorText)
          throw new Error(`Consolidated API failed: ${errorText}`)
        }
      } catch (fetchError) {
        console.error("❌ Consolidated API fetch failed:", fetchError)
        setSupabaseError("Unable to load businesses at this time. Please try again later.")
        setBusinesses([])
      }
    } catch (error: any) {
      console.error("❌ Error fetching businesses:", error)
      setSupabaseError(error.message)
      setBusinesses([])
    } finally {
      setLoading(false)
    }
  }

  const categories = useMemo(() => {
    const cats = new Set(businesses.map((b) => b.category).filter(Boolean))
    return ["spotlight", "all", ...Array.from(cats)].filter(Boolean)
  }, [businesses])

  const extractState = (address: string) => {
    if (!address) return ""
    const parts = address.split(",")
    if (parts.length >= 3) {
      return parts[2].trim()
    }
    return ""
  }

  const filteredBusinesses = useMemo(() => {
    console.log(`🔍 Filtering ${businesses.length} businesses with:`, {
      searchTerm,
      stateFilter,
      selectedCategory
    })

    const filtered = businesses.filter((business) => {
      const matchesSearch =
        business.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        business.description?.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesState = !stateFilter || extractState(business.business_address || business.address) === stateFilter

      if (selectedCategory === "spotlight") {
        return matchesSearch && matchesState && (business.business_spotlight || business.is_active)
      } else if (selectedCategory === "all") {
        return matchesSearch && matchesState
      } else {
        return matchesSearch && matchesState && business.category === selectedCategory
      }
    })

    console.log(`✅ Filtered to ${filtered.length} businesses`)
    return filtered
  }, [businesses, searchTerm, stateFilter, selectedCategory])

  // VIP Cards data
  const cards = [
    {
      name: "Monthly VIP Card",
      requirement: "0",
      rewards: {
        referral: "15%",
        affiliate: "0.5%",
      },
      description: "Just a taste of fuse? Perfect for trying out our VIP benefits.",
      cardImage:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png",
      features: [
        { name: "Digital loyalty card", included: true },
        { name: "Basic rewards tracking", included: true },
        { name: "Access to standard promotions", included: true },
        { name: "Mobile app access", included: true },
        { name: "Premium rewards", included: false },
        { name: "Priority support", included: false },
        { name: "Exclusive events access", included: false },
      ],
      price: 9.99,
      cta: "Start Monthly VIP!",
      popular: false,
    },
    {
      name: "Premium Card",
      requirement: "0",
      rewards: {
        referral: "20%",
        affiliate: "1%",
      },
      description: "Start your journey with our premium VIP Card.",
      cardImage:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png",
      features: [
        { name: "Digital loyalty card", included: true },
        { name: "Basic rewards tracking", included: true },
        { name: "Access to standard promotions", included: true },
        { name: "Mobile app access", included: true },
        { name: "Premium rewards", included: true },
        { name: "Priority support", included: true },
        { name: "Exclusive events access", included: false },
      ],
      price: 100,
      cta: "Get Your Discounts Now!",
      popular: false,
      type: "one-time",
    },
    {
      name: "Lifetime VIP Card",
      requirement: "0",
      rewards: {
        referral: "25%",
        affiliate: "2%",
      },
      description: "Ultimate lifetime access with maximum benefits.",
      cardImage:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/obsidian-card-sZV0uG2g9pJ0BiQRLvn14MJIFGvzDn.png",
      features: [
        { name: "Digital loyalty card", included: true },
        { name: "Advanced rewards tracking", included: true },
        { name: "Access to all promotions", included: true },
        { name: "Mobile app access", included: true },
        { name: "Premium rewards", included: true },
        { name: "Priority support", included: true },
        { name: "Exclusive events access", included: true },
        { name: "Lifetime validity", included: true },
      ],
      price: 1500,
      cta: "Get Lifetime Access!",
      popular: true,
      type: "lifetime",
    },
    {
      name: "Gold Card",
      requirement: "2,500",
      rewards: {
        referral: "25%",
        affiliate: "2%",
      },
      description: "Upgrade to Gold for enhanced rewards and benefits.",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/gold-card-bflkHLVolhMSi3L5RexoL9fh7wsqBq.png",
      features: [
        { name: "Digital loyalty card", included: true },
        { name: "Enhanced rewards tracking", included: true },
        { name: "Access to gold-tier promotions", included: true },
        { name: "Mobile app access", included: true },
        { name: "Premium rewards", included: true },
        { name: "Priority support", included: false },
        { name: "Exclusive events access", included: false },
      ],
      price: 250,
      cta: "Get Your Discounts Now!",
      popular: false,
    },
    {
      name: "Platinum Card",
      requirement: "10,000",
      rewards: {
        referral: "30%",
        affiliate: "3%",
      },
      description: "Experience premium benefits with our Platinum VIP Card.",
      cardImage:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/platinum-card-IQekSEB3CMuXes5qUTxZc55ZeKUVOy.png",
      features: [
        { name: "Digital loyalty card", included: true },
        { name: "Advanced rewards tracking", included: true },
        { name: "Access to platinum-tier promotions", included: true },
        { name: "Mobile app access", included: true },
        { name: "Premium rewards", included: true },
        { name: "Priority support", included: true },
        { name: "Exclusive events access", included: false },
      ],
      price: null, // Hide price
      cta: "Coming Soon",
      popular: false,
    },
    {
      name: "Diamond Card",
      requirement: "50,000",
      rewards: {
        referral: "35%",
        affiliate: "4%",
      },
      description: "Unlock elite benefits with our Diamond VIP Card.",
      cardImage:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/diamond-card-I9uaKzc6KKTRSgl9mrz4SP2Y2myuiF.png",
      features: [
        { name: "Digital loyalty card", included: true },
        { name: "Premium rewards tracking", included: true },
        { name: "Access to diamond-tier promotions", included: true },
        { name: "Mobile app access", included: true },
        { name: "Premium rewards", included: true },
        { name: "Priority support", included: true },
        { name: "Exclusive events access", included: true },
      ],
      price: null, // Hide price
      cta: "Coming Soon",
      popular: false,
    },
    {
      name: "Obsidian Card",
      requirement: "150,000",
      rewards: {
        referral: "40%",
        affiliate: "5%",
      },
      description: "Our most exclusive VIP Card with unparalleled benefits.",
      cardImage:
        "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/obsidian-card-sZV0uG2g9pJ0BiQRLvn14MJIFGvzDn.png",
      features: [
        { name: "Digital loyalty card", included: true },
        { name: "Elite rewards tracking", included: true },
        { name: "Access to obsidian-tier promotions", included: true },
        { name: "Mobile app access", included: true },
        { name: "Premium rewards", included: true },
        { name: "VIP support", included: true },
        { name: "Exclusive events access", included: true },
        { name: "Concierge service", included: true },
      ],
      price: null, // Hide price
      cta: "Coming Soon",
      popular: false,
    },
  ]

  const US_STATES = [
    "Alabama",
    "Alaska",
    "Arizona",
    "Arkansas",
    "California",
    "Colorado",
    "Connecticut",
    "Delaware",
    "Florida",
    "Georgia",
    "Hawaii",
    "Idaho",
    "Illinois",
    "Indiana",
    "Iowa",
    "Kansas",
    "Kentucky",
    "Louisiana",
    "Maine",
    "Maryland",
    "Massachusetts",
    "Michigan",
    "Minnesota",
    "Mississippi",
    "Missouri",
    "Montana",
    "Nebraska",
    "Nevada",
    "New Hampshire",
    "New Jersey",
    "New Mexico",
    "New York",
    "North Carolina",
    "North Dakota",
    "Ohio",
    "Oklahoma",
    "Oregon",
    "Pennsylvania",
    "Rhode Island",
    "South Carolina",
    "South Dakota",
    "Tennessee",
    "Texas",
    "Utah",
    "Vermont",
    "Virginia",
    "Washington",
    "West Virginia",
    "Wisconsin",
    "Wyoming",
  ]

  // Function to handle direct purchase of Premium Card - requires authentication
  const handleDirectPurchase = async () => {
    setIsProcessing(true)
    setError("")

    try {
      // Require user authentication
      if (!user) {
        throw new Error("Please sign in to purchase a membership card.")
      }

      // Find the Premium Card
      const premiumCard = cards.find((card) => card.name === "Premium Card")

      if (!premiumCard) {
        throw new Error("Premium Card not found")
      }

      // Create a checkout session with Stripe
      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cardType: premiumCard.name,
          price: premiumCard.price,
          userId: user?.id,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to create checkout session")
      }

      const data = await response.json()

      // Redirect to Stripe Checkout
      window.location.href = data.url
    } catch (error: any) {
      console.error("Error initiating payment:", error)
      setError(error.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handlePurchase = useCallback(
    async (card: any) => {
      // Only allow purchase of cards with prices (Monthly VIP Card and Premium Card)
      if (!card.price) {
        return
      }

      // For Monthly VIP Card, redirect to upgrade page with Stripe buy button
      if (card.name === "Monthly VIP Card") {
        window.location.href = "/upgrade"
        return
      }

      // For Lifetime VIP Card, redirect to upgrade page with Stripe buy button
      if (card.name === "Lifetime VIP Card") {
        window.location.href = "/upgrade"
        return
      }

      setSelectedCard(card)
      setIsProcessing(true)
      setError("")

      try {
        // If user is not authenticated, ask for email
        if (!user) {
          if (!showGuestEmailInput) {
            setShowGuestEmailInput(true)
            setIsProcessing(false)
            return
          }

          // Validate email
          if (!guestEmail || !/\S+@\S+\.\S+/.test(guestEmail)) {
            throw new Error("Please enter a valid email address")
          }
        }

        // Create a checkout session with Stripe
        const response = await fetch("/api/create-checkout-session", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            cardType: card.name,
            price: card.price,
            userId: user?.id || null,
            guestEmail: !user ? guestEmail : null,
            isGuest: !user,
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to create checkout session")
        }

        const data = await response.json()

        // Redirect to Stripe Checkout
        window.location.href = data.url
      } catch (error: any) {
        console.error("Error initiating payment:", error)
        setError(error.message)
      } finally {
        setIsProcessing(false)
      }
    },
    [user, showGuestEmailInput, guestEmail, selectedCard],
  )

  const handleGuestEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedCard) {
      handlePurchase(selectedCard)
    } else {
      // If no card is selected, use the Premium Card
      handleDirectPurchase()
    }
  }

  return (
    <>
      {/* Stripe Script for Monthly VIP Card */}
      <Script
        src="https://js.stripe.com/v3/buy-button.js"
        strategy="lazyOnload"
      />

      {/* Animated Sky Background */}
      <SkyBackground />
      
      {/* Theme Toggle - Fixed position, away from mobile menu */}
      <div className="fixed bottom-6 left-6 z-50">
        <ThemeToggle theme={theme as "light" | "dark"} toggleTheme={toggleTheme} />
      </div>
      
      {/* Page content container */}
      <div className="relative">
        <div className="py-8">
          <PageHeader
            title="Fuse Discounts"
            subtitle="EXCLUSIVE MEMBER BENEFITS"
            description="Unlock exclusive discounts and rewards at our partner businesses across multiple industries."
          />
        </div>

        {/* Explore Businesses Section */}
        <section className="py-20 bg-white/40 dark:bg-black/40 backdrop-blur-sm">
          <div className="container mx-auto px-4">
            <AnimatedSection>
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
                  Explore Our <span className="text-[#316bff]">Partner Network</span>
                </h2>
                <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                  Discover businesses in our network that offer exclusive benefits and discounts to Fuse.Vip members.
                </p>
              </div>
            </AnimatedSection>

            <div className="mb-8 flex flex-col md:flex-row gap-4 justify-center">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search businesses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md w-full md:w-64 bg-white/70 dark:bg-black/30 text-gray-900 dark:text-white"
                />
              </div>

              <select
                value={stateFilter}
                onChange={(e) => setStateFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white/70 dark:bg-black/30 text-gray-900 dark:text-white"
              >
                <option value="">All States</option>
                {US_STATES.map((state) => (
                  <option key={state} value={state}>
                    {state}
                  </option>
                ))}
              </select>

              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white/70 dark:bg-black/30 text-gray-900 dark:text-white"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category === "spotlight"
                      ? "Featured Partners"
                      : category === "all"
                        ? "All Categories"
                        : category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#316bff]"></div>
                <p className="text-gray-400 mt-4">Loading businesses...</p>
              </div>
            ) : supabaseError ? (
              <div className="text-center py-12">
                <p className="text-red-400 mb-4">{supabaseError}</p>
                <button
                  onClick={fetchBusinesses}
                  className="bg-[#316bff] hover:bg-[#2557d6] text-white px-6 py-2 rounded-lg transition-colors"
                >
                  Try Again
                </button>
              </div>
            ) : (
              <BusinessCarousel businesses={filteredBusinesses} />
            )}
          </div>
        </section>

        {/* VIP Cards Section */}
        <section className="py-20 bg-white/40 dark:bg-black/40 backdrop-blur-sm">
          <div className="container mx-auto px-4">
            <AnimatedSection>
              <div className="text-center mb-16">
                <h2 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900 dark:text-white">
                  VIP <span className="text-[#316bff]">Membership Cards</span>
                </h2>
                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
                  Unlock exclusive discounts and rewards with our premium membership cards.
                </p>
              </div>
            </AnimatedSection>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
              {cards.slice(0, 3).map((card) => (
                <AnimatedCard
                  key={card.name}
                  className={`relative bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm rounded-2xl p-8 border ${
                    card.popular ? "border-[#316bff] shadow-lg shadow-[#316bff]/20" : "border-gray-700"
                  } hover:border-[#316bff]/50 transition-all duration-300`}
                >
                  {card.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-[#316bff] text-white px-4 py-2 rounded-full text-sm font-bold">
                        Most Popular
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <div className="w-full h-48 mb-6 rounded-xl overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900">
                      <Image
                        src={card.cardImage}
                        alt={`${card.name} VIP Card`}
                        width={300}
                        height={200}
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-2">{card.name}</h3>
                    <p className="text-gray-300 mb-4">{card.description}</p>
                    {card.price && (
                      <div className="text-4xl font-bold text-[#316bff] mb-2">
                        ${card.price}
                        {card.name === "Monthly VIP Card" && <span className="text-lg">/mo</span>}
                      </div>
                    )}
                  </div>

                  <div className="space-y-3 mb-8">
                    {card.features.slice(0, 4).map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center">
                        {feature.included ? (
                          <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                        ) : (
                          <X className="h-5 w-5 text-gray-500 mr-3 flex-shrink-0" />
                        )}
                        <span className={feature.included ? "text-white" : "text-gray-500"}>
                          {feature.name}
                        </span>
                      </div>
                    ))}
                  </div>

                  <div className="text-center">
                    <AnimatedButton
                      onClick={() => handlePurchase(card)}
                      disabled={isProcessing || !card.price}
                      className={`w-full py-3 px-6 rounded-lg font-bold transition-all duration-300 ${
                        card.price
                          ? "bg-[#316bff] hover:bg-[#2557d6] text-white"
                          : "bg-gray-700 text-gray-400 cursor-not-allowed"
                      }`}
                    >
                      {card.price
                        ? (isProcessing ? "Processing..." : card.cta)
                        : "Coming Soon"
                      }
                    </AnimatedButton>
                  </div>
                </AnimatedCard>
              ))}
            </div>

            {/* Guest Email Input Modal */}
            {showGuestEmailInput && (
              <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                <div className="bg-gray-900 rounded-2xl p-8 max-w-md w-full border border-gray-700">
                  <h3 className="text-2xl font-bold text-white mb-4">Complete Your Purchase</h3>
                  <p className="text-gray-300 mb-6">
                    Enter your email to continue with your VIP card purchase.
                  </p>
                  <form onSubmit={handleGuestEmailSubmit}>
                    <input
                      type="email"
                      value={guestEmail}
                      onChange={(e) => setGuestEmail(e.target.value)}
                      placeholder="Enter your email address"
                      className="w-full px-4 py-3 bg-black/50 border border-gray-600 rounded-lg text-white mb-4"
                      required
                    />
                    <div className="flex gap-4">
                      <button
                        type="button"
                        onClick={() => {
                          setShowGuestEmailInput(false)
                          setGuestEmail("")
                          setSelectedCard(null)
                        }}
                        className="flex-1 py-3 px-6 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={isProcessing}
                        className="flex-1 py-3 px-6 bg-[#316bff] hover:bg-[#2557d6] text-white rounded-lg transition-colors disabled:opacity-50"
                      >
                        {isProcessing ? "Processing..." : "Continue"}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {error && (
              <div className="mt-8 text-center">
                <p className="text-red-400 bg-red-900/20 border border-red-800 rounded-lg p-4 max-w-md mx-auto">
                  {error}
                </p>
              </div>
            )}
          </div>
        </section>



        <SiteFooter />
      </div>
    </>
  )
}
