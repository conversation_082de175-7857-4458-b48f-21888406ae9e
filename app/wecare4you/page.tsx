"use client";

import { PageHeader } from "@/components/page-header";
import { AnimatedSection } from "@/components/animated-section";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { PublicRoute } from "@/components/public-route";
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { CreditCard, UserPlus, ArrowRight, Gift, CheckCircle } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function WeCare4YouPage() {
  const { user } = useAuth();
  const router = useRouter();

  const handleGetStarted = () => {
    if (user) {
      router.push("/dashboard");
    } else {
      router.push("/register");
    }
  };

  const steps = [
    {
      number: "1",
      title: "Create Your Free Account",
      description: "Sign up for a free Fuse.vip account to get started with our digital loyalty platform.",
      icon: UserPlus,
    },
    {
      number: "2", 
      title: "Access Your Dashboard",
      description: "Once registered, log in and navigate to your personal dashboard to manage your cards.",
      icon: ArrowRight,
    },
    {
      number: "3",
      title: "Redeem Your Physical Card",
      description: "Use the redeem code feature in your dashboard to convert your physical card to digital.",
      icon: CreditCard,
    },
  ];

  return (
    <PublicRoute>
      <PageHeader
        title="We Care 4 You"
        subtitle="PHYSICAL TO DIGITAL"
        description="Transform your physical loyalty card into a powerful digital experience. Get started with your free account today."
      />

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-br from-[#000814] via-[#001122] to-black">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="text-center mb-12">
              <div className="flex justify-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full flex items-center justify-center">
                  <Gift className="h-10 w-10 text-white" />
                </div>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Ready to Go Digital?
              </h2>
              <p className="text-white/80 text-lg max-w-2xl mx-auto mb-8">
                Your physical card is just the beginning. Unlock the full potential of digital loyalty rewards, 
                exclusive benefits, and seamless experiences across our entire network.
              </p>
              
              {user ? (
                <Button
                  onClick={() => router.push("/dashboard")}
                  className="bg-[#3A56FF] hover:bg-[#3A56FF]/90 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200"
                >
                  Go to Dashboard
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              ) : (
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    onClick={() => router.push("/register")}
                    className="bg-[#3A56FF] hover:bg-[#3A56FF]/90 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200"
                  >
                    Create Free Account
                    <UserPlus className="ml-2 h-5 w-5" />
                  </Button>
                  <Button
                    onClick={() => router.push("/login")}
                    variant="outline"
                    className="border-white bg-white/10 text-white hover:bg-white hover:text-[#3A56FF] px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200"
                  >
                    Already Have Account?
                  </Button>
                </div>
              )}
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Steps Section */}
      <section className="py-16 bg-[#f8f9fa]">
        <div className="container mx-auto px-4">
          <AnimatedSection>
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Simple 3-Step Process
              </h2>
              <p className="text-[#4a4a4a] text-lg max-w-2xl mx-auto">
                Converting your physical card to digital is quick and easy. Follow these simple steps to get started.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {steps.map((step, index) => (
              <AnimatedSection key={step.number} delay={index * 0.1}>
                <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-full flex items-center justify-center mx-auto mb-6">
                      <step.icon className="h-8 w-8 text-white" />
                    </div>
                    <div className="w-8 h-8 bg-[#3A56FF] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-sm font-bold">
                      {step.number}
                    </div>
                    <h3 className="text-xl font-bold mb-4">{step.title}</h3>
                    <p className="text-[#4a4a4a] leading-relaxed">{step.description}</p>
                  </CardContent>
                </Card>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <AnimatedSection>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Why Go Digital?
                </h2>
                <p className="text-[#4a4a4a] text-lg mb-8">
                  Your digital card unlocks a world of possibilities that your physical card simply can't match.
                </p>
                
                <div className="space-y-4">
                  {[
                    "Never lose your card again - it's always in your phone",
                    "Real-time rewards tracking and notifications", 
                    "Exclusive digital-only promotions and offers",
                    "Seamless integration with our mobile app",
                    "Instant access to your loyalty benefits anywhere",
                    "Enhanced security with blockchain technology"
                  ].map((benefit, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-[#4a4a4a]">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={0.2}>
              <div className="relative">
                <div className="bg-gradient-to-br from-[#3A56FF]/10 to-[#6366f1]/10 rounded-2xl p-8">
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png"
                    alt="Digital VIP Card"
                    width={400}
                    height={250}
                    className="w-full max-w-md mx-auto rounded-xl shadow-2xl"
                  />
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-[#3A56FF] to-[#6366f1]">
        <div className="container mx-auto px-4 text-center">
          <AnimatedSection>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to Make the Switch?
            </h2>
            <p className="text-white/90 text-lg mb-8 max-w-2xl mx-auto">
              Join thousands of satisfied customers who have already made the switch to digital. 
              Your loyalty rewards are waiting!
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleGetStarted}
                className="bg-white text-[#3A56FF] hover:bg-gray-100 px-8 py-4 text-lg font-semibold rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                {user ? "Go to Dashboard" : "Get Started Now"}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>

              <Link href="/upgrade">
                <Button
                  variant="outline"
                  className="border-white bg-white/10 text-white hover:bg-white hover:text-[#3A56FF] px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200"
                >
                  View VIP Cards
                </Button>
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </PublicRoute>
  );
}
