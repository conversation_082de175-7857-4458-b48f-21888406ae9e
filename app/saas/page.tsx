"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { ArrowRight, CheckCircle2, <PERSON><PERSON>s, BarChart, Users, Zap, Cloud, Rocket, Star, Shield } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON>Header } from "@/components/page-header"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function SaaSPage() {
  const [selectedTier, setSelectedTier] = useState("business")

  const saasTools = [
    {
      name: "Business Automation",
      description: "Automate workflows and streamline operations",
      icon: <Settings className="w-8 h-8 text-[#FFD93D]" />,
      fuseCost: "30 FUSE",
      features: ["Workflow automation", "Task scheduling", "Process optimization"]
    },
    {
      name: "Analytics Suite",
      description: "Advanced reporting and business intelligence",
      icon: <BarChart className="w-8 h-8 text-[#FFD93D]" />,
      fuseCost: "25 FUSE",
      features: ["Real-time dashboards", "Custom reports", "Data visualization"]
    },
    {
      name: "CRM Platform",
      description: "Comprehensive customer relationship management",
      icon: <Users className="w-8 h-8 text-[#FFD93D]" />,
      fuseCost: "35 FUSE",
      features: ["Contact management", "Sales pipeline", "Customer insights"]
    },
    {
      name: "Cloud Infrastructure",
      description: "Scalable cloud hosting and management",
      icon: <Cloud className="w-8 h-8 text-[#FFD93D]" />,
      fuseCost: "40 FUSE/month",
      features: ["Auto-scaling", "Load balancing", "99.9% uptime SLA"]
    }
  ]

  const tiers = [
    {
      id: "startup",
      name: "Startup",
      fuseCost: "60 FUSE",
      description: "Essential tools for growing businesses",
      features: [
        "Business automation tools",
        "Basic analytics dashboard",
        "CRM for up to 1,000 contacts",
        "Email support",
        "Standard integrations"
      ],
      popular: false
    },
    {
      id: "business",
      name: "Business", 
      fuseCost: "120 FUSE",
      description: "Complete business management solution",
      features: [
        "Everything in Startup",
        "Advanced analytics suite",
        "Unlimited CRM contacts",
        "Cloud infrastructure included",
        "Priority support",
        "Custom integrations"
      ],
      popular: true
    },
    {
      id: "enterprise",
      name: "Enterprise",
      fuseCost: "250 FUSE",
      description: "Enterprise-grade solutions at scale",
      features: [
        "Everything in Business",
        "White-label solutions",
        "Dedicated infrastructure",
        "24/7 phone support",
        "Custom development",
        "SLA guarantees"
      ],
      popular: false
    }
  ]

  return (
    <>
      <PageHeader
        title="SaaS Solutions with $FUSE"
        subtitle="AUTOMATE • ANALYZE • SCALE"
        description="Submit $FUSE tokens to access enterprise-grade Software as a Service tools, automation platforms, and business management systems."
      />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-[#FFD93D]/10 to-[#FFE066]/10">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-1/2">
              <Badge className="bg-[#FFD93D]/20 text-[#FFD93D] hover:bg-[#FFD93D]/30 mb-6">
                SaaS Solutions World
              </Badge>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Enterprise SaaS Tools with <span className="text-[#FFD93D]">$FUSE</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Access powerful business automation, analytics, and management tools that scale with your business - 
                all unlocked with $FUSE tokens.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-[#FFD93D] to-[#FFE066] hover:from-[#FFC93D] hover:to-[#FFD966] text-gray-900 font-bold"
                >
                  <Zap className="w-5 h-5 mr-2" />
                  Access SaaS Tools
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
                
                <Button variant="outline" size="lg">
                  <BarChart className="w-5 h-5 mr-2" />
                  View Demo
                </Button>
              </div>
            </div>

            <div className="lg:w-1/2">
              <div className="relative">
                <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <Badge className="bg-[#FFD93D]/20 text-[#FFD93D]">Enterprise</Badge>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Revenue</span>
                      <span className="text-lg font-bold text-green-600">+24.5%</span>
                    </div>
                    
                    <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-[#FFD93D] to-[#FFE066] rounded-full w-3/4"></div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 mt-6">
                      <div className="text-center p-3 bg-gradient-to-br from-[#FFD93D]/20 to-[#FFE066]/20 rounded-lg">
                        <Settings className="w-6 h-6 text-[#FFD93D] mx-auto mb-1" />
                        <div className="text-xs text-gray-600">Automation</div>
                      </div>
                      <div className="text-center p-3 bg-gradient-to-br from-[#FFD93D]/20 to-[#FFE066]/20 rounded-lg">
                        <BarChart className="w-6 h-6 text-[#FFD93D] mx-auto mb-1" />
                        <div className="text-xs text-gray-600">Analytics</div>
                      </div>
                      <div className="text-center p-3 bg-gradient-to-br from-[#FFD93D]/20 to-[#FFE066]/20 rounded-lg">
                        <Users className="w-6 h-6 text-[#FFD93D] mx-auto mb-1" />
                        <div className="text-xs text-gray-600">CRM</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-[#FFD93D] text-gray-900 px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                  AI-Powered
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* SaaS Tools Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Enterprise-Grade SaaS Tools
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Submit $FUSE tokens to unlock powerful business tools that help you automate, analyze, and scale your operations.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {saasTools.map((tool, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 border border-gray-100 hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-gradient-to-br from-[#FFD93D]/20 to-[#FFE066]/20 rounded-xl">
                    {tool.icon}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-xl font-bold text-gray-900">{tool.name}</h3>
                      <Badge className="bg-[#FFD93D]/20 text-[#FFD93D]">{tool.fuseCost}</Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{tool.description}</p>
                    
                    <div className="space-y-2">
                      {tool.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <CheckCircle2 className="w-4 h-4 text-[#FFD93D]" />
                          <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Tiers */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Choose Your SaaS Package
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Select the perfect package for your business needs. All packages are unlocked with $FUSE tokens.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {tiers.map((tier) => (
              <div
                key={tier.id}
                className={`relative bg-white rounded-2xl p-8 border-2 transition-all duration-300 ${
                  tier.popular 
                    ? 'border-[#FFD93D] shadow-lg scale-105' 
                    : 'border-gray-200 hover:border-[#FFD93D]/50'
                }`}
              >
                {tier.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-[#FFD93D] text-gray-900 px-4 py-1">Most Popular</Badge>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.name}</h3>
                  <div className="text-3xl font-bold text-[#FFD93D] mb-2">{tier.fuseCost}</div>
                  <p className="text-gray-600">{tier.description}</p>
                </div>

                <div className="space-y-4 mb-8">
                  {tier.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <CheckCircle2 className="w-5 h-5 text-[#FFD93D] flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <Button 
                  className={`w-full ${
                    tier.popular
                      ? 'bg-gradient-to-r from-[#FFD93D] to-[#FFE066] hover:from-[#FFC93D] hover:to-[#FFD966] text-gray-900 font-bold'
                      : 'bg-white border-2 border-[#FFD93D] text-[#FFD93D] hover:bg-[#FFD93D] hover:text-gray-900'
                  }`}
                  onClick={() => setSelectedTier(tier.id)}
                >
                  Submit {tier.fuseCost}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#FFD93D] to-[#FFE066] text-gray-900">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Scale Your Business?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Join thousands of businesses using enterprise-grade SaaS tools to automate operations and drive growth. 
            Start your transformation with $FUSE tokens today.
          </p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button size="lg" className="bg-gray-900 text-white hover:bg-gray-800">
              <Zap className="w-5 h-5 mr-2" />
              Get Started Now
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            
            <Link href="/fuse">
              <Button size="lg" variant="outline" className="border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white">
                <Settings className="w-5 h-5 mr-2" />
                Back to $FUSE Worlds
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </>
  )
}
