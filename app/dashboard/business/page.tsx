"use client";

import React, { useState, useEffect, useRef, Suspense } from "react";
import { PageHeader } from "@/components/page-header";
import { CtaSection } from "@/components/cta-section";
import { useAuth } from "@/contexts/auth-context";
import { getSupabaseClient } from "@/lib/supabase";
import { Building2, RefreshCw, Wifi, WifiOff } from "lucide-react";
import { BusinessForm } from "@/components/business/business-form";
// Removed unused import: BusinessOwnershipGuard
import { AdditionalApplicationForm } from "@/components/business/additional-application-form";
import { BusinessDashboardContent } from "@/components/business/business-dashboard-content";

// Business data interface
interface BusinessData {
  id: string;
  name: string;
  logo_url?: string;
  website?: string;
  category?: string;
  premium_discount?: string;
  is_active: boolean;
  user_id: string;
  business_address?: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  latitude?: number;
  longitude?: number;
  business_referral?: string;
  referring_business_id?: string;
  business_spotlight?: boolean;
  loyalty_reward_frequency?: string;
  logo_optimized_url?: string;
  logo_alt_text?: string;
  logo_width?: number;
  logo_height?: number;
  logo_file_size?: number;
  logo_mime_type?: string;
  created_at: string;
  updated_at: string;
  interaction_count?: number;
  referral_count?: number;
}

// Cache management
class BusinessCache {
  private static instance: BusinessCache;
  private cache = new Map<string, { data: BusinessData; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static getInstance(): BusinessCache {
    if (!BusinessCache.instance) {
      BusinessCache.instance = new BusinessCache();
    }
    return BusinessCache.instance;
  }

  get(userId: string): BusinessData | null {
    const cached = this.cache.get(userId);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    this.cache.delete(userId);
    return null;
  }

  set(userId: string, data: BusinessData): void {
    this.cache.set(userId, { data, timestamp: Date.now() });
  }

  invalidate(userId: string): void {
    this.cache.delete(userId);
  }

  clear(): void {
    this.cache.clear();
  }
}

// Component that uses useSearchParams - needs to be wrapped in Suspense
function BusinessDashboardPageContent() {
  const { user, profile, isLoading: authLoading } = useAuth();

  const [activeTab, setActiveTab] = useState("overview");
  const [showForm, setShowForm] = useState(false);
  const [showAdditionalForm, setShowAdditionalForm] = useState(false);
  const [loyaltyFrequency, setLoyaltyFrequency] = useState<'monthly' | 'quarterly' | 'annually'>('monthly');
  const [formLoading, setFormLoading] = useState(false);
  const [businessData, setBusinessData] = useState<BusinessData | null>(null);
  const [isOnline, setIsOnline] = useState(true);
  const [lastSync, setLastSync] = useState<Date | null>(null);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle');
  const [initialLoading, setInitialLoading] = useState(true);

  const cache = useRef(BusinessCache.getInstance());
  const realtimeSubscription = useRef<any>(null);

  // Online/offline detection
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Unified authentication and business data fetch
  const fetchBusinessData = async (useCache = true, retryCount = 0): Promise<BusinessData | null> => {
    if (!user?.id) return null;

    // Try cache first
    if (useCache) {
      const cached = cache.current.get(user.id);
      if (cached) {
        console.log('✅ Using cached business data');
        return cached;
      }
    }

    setSyncStatus('syncing');
    try {
      // Add delay on first attempt to ensure session is established
      if (retryCount === 0) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // API call with user ID parameter (similar to working endpoints)
      const response = await fetch(`/api/dashboard/business?userId=${user.id}`, {
        headers: {
          'Cache-Control': 'no-cache',
        },
        credentials: 'include', // Include authentication cookies
      });
      const result = await response.json();

      // Add detailed logging to see what's happening
      console.log('🔍 API Response:', {
        status: response.status,
        ok: response.ok,
        result: result,
        hasBusiness: !!result.business,
        userId: user?.id,
        attempt: retryCount + 1
      });

      if (response.ok && result.business) {
        const business = result.business as BusinessData;
        cache.current.set(user.id, business);
        setLastSync(new Date());
        setSyncStatus('idle');
        console.log('✅ Business data fetched and cached');
        return business;
      } else if (response.status === 401 && retryCount < 2) {
        // Retry authentication instead of immediate redirect
        console.log(`⏳ Auth not ready, retrying business data fetch (attempt ${retryCount + 1})...`);
        setSyncStatus('syncing');
        await new Promise(resolve => setTimeout(resolve, 1000));
        return fetchBusinessData(false, retryCount + 1);
      } else if (response.status === 401) {
        console.error('❌ Authentication failed after retries - redirecting to login');
        setSyncStatus('error');
        // Only redirect after retries are exhausted
        window.location.href = '/login';
        return null;
      } else {
        console.log('❌ No business found for user:', user?.id, 'Response:', result);
        setSyncStatus('idle');
        return null;
      }
    } catch (error) {
      if (retryCount < 2) {
        console.log(`⏳ Network error, retrying business data fetch (attempt ${retryCount + 1})...`);
        setSyncStatus('syncing');
        await new Promise(resolve => setTimeout(resolve, 1000));
        return fetchBusinessData(false, retryCount + 1);
      }
      console.error('❌ Error fetching business data after retries:', error);
      setSyncStatus('error');
      return null;
    }
  };

  // Initialize business data with unified authentication
  useEffect(() => {
    const initializeData = async () => {
      if (authLoading) {
        // Wait for auth to complete
        return;
      }

      if (!user?.id) {
        // No user - redirect to login after a brief delay to avoid flashing
        setTimeout(() => {
          window.location.href = '/login';
        }, 1000);
        return;
      }

      try {
        const data = await fetchBusinessData();
        setBusinessData(data);
      } finally {
        setInitialLoading(false);
      }
    };

    initializeData();
  }, [user?.id, authLoading]);

  // Setup real-time subscription
  useEffect(() => {
    const supabase = getSupabaseClient()
    if (!user?.id || !supabase) return;

    console.log('🔄 Setting up real-time subscription for business updates');

    // Subscribe to business changes
    realtimeSubscription.current = supabase
      .channel(`business_changes_${user.id}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'businesses',
          filter: `user_id=eq.${user.id}`,
        },
        async (payload) => {
          console.log('🔄 Real-time business update received:', payload);

          // Invalidate cache
          cache.current.invalidate(user.id);

          // Fetch fresh data
          const freshData = await fetchBusinessData(false);
          if (freshData) {
            setBusinessData(freshData);
            setLastSync(new Date());
          }
        }
      )
      .subscribe((status) => {
        console.log('Real-time subscription status:', status);
      });

    return () => {
      if (realtimeSubscription.current && supabase) {
        console.log('🔄 Cleaning up real-time subscription');
        supabase.removeChannel(realtimeSubscription.current);
      }
    };
  }, [user?.id]);

  // Manual refresh
  const handleRefresh = async () => {
    if (!user?.id) return;
    cache.current.invalidate(user.id);
    const freshData = await fetchBusinessData(false);
    setBusinessData(freshData);
  };



  // Enhanced form submission with optimistic updates and cache management
  const handleFormSubmit = async (formData: any, existingBusiness?: any) => {
    if (!user) return;

    setFormLoading(true);
    setSyncStatus('syncing');

    try {
      if (existingBusiness) {
        // Optimistic update - update UI immediately
        const optimisticUpdate = { ...existingBusiness, ...formData };
        setBusinessData(optimisticUpdate);

        // Update using API endpoint
        const response = await fetch('/api/dashboard/business', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
          },
          credentials: 'include',
          body: JSON.stringify({
            businessData: formData,
            userId: user.id
          })
        });

        const result = await response.json();

        if (!response.ok || result.error) {
          // Revert optimistic update on error
          setBusinessData(existingBusiness);
          console.error("Failed to update business:", result.error);
          alert(`Failed to update business: ${result.error}`);
          setSyncStatus('error');
        } else {
          const updatedBusiness = result.business as BusinessData;
          console.log("Business updated successfully:", updatedBusiness.name);

          // Update cache with fresh data
          cache.current.set(user.id, updatedBusiness);
          setBusinessData(updatedBusiness);
          setLastSync(new Date());
          setSyncStatus('idle');

          // Show success message
          const successDiv = document.createElement('div');
          successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse';
          successDiv.textContent = '✅ Business updated successfully!';
          document.body.appendChild(successDiv);
          setTimeout(() => successDiv.remove(), 3000);
        }
      } else {
        // Create new business - use Supabase client directly (this works fine)
        const supabase = getSupabaseClient();
        if (!supabase) {
          alert("Supabase client not available");
          setSyncStatus('error');
          return;
        }

        const { data, error } = await supabase
          .from("businesses")
          .insert({
            ...formData,
            user_id: user.id,
            is_active: true
          })
          .select()
          .single();

        if (error) {
          console.error("Failed to create business:", error);
          alert(`Failed to create business: ${error.message}`);
          setSyncStatus('error');
        } else {
          const newBusiness = data as unknown as BusinessData;
          console.log("Business created successfully:", newBusiness.name);

          // Update cache with new business
          cache.current.set(user.id, newBusiness);
          setBusinessData(newBusiness);
          setLastSync(new Date());
          setSyncStatus('idle');

          // Show success message
          const successDiv = document.createElement('div');
          successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse';
          successDiv.textContent = '✅ Business created successfully!';
          document.body.appendChild(successDiv);
          setTimeout(() => successDiv.remove(), 3000);
        }
      }
    } catch (err) {
      console.error("Exception during business form submission:", err);
      alert("An unexpected error occurred. Please try again.");
      setSyncStatus('error');
    } finally {
      setFormLoading(false);
      setShowForm(false);
    }
  };

  const handleLoyaltyFrequencyUpdate = async (newFrequency: 'monthly' | 'quarterly' | 'annually', businessId: string) => {
    if (!user || !businessData) return;

    setFormLoading(true);
    setSyncStatus('syncing');

    // Optimistic update
    const optimisticUpdate = { ...businessData, loyalty_reward_frequency: newFrequency };
    setBusinessData(optimisticUpdate);
    setLoyaltyFrequency(newFrequency);

    // Update using API endpoint
    const response = await fetch('/api/dashboard/business', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
      },
      credentials: 'include',
      body: JSON.stringify({
        businessData: { loyalty_reward_frequency: newFrequency },
        userId: user.id
      })
    });

    const result = await response.json();

    if (!response.ok || result.error) {
      // Revert optimistic update
      setBusinessData(businessData);
      setLoyaltyFrequency(businessData.loyalty_reward_frequency as any || 'monthly');
      console.error("Error updating loyalty frequency:", result.error);
      setSyncStatus('error');
    } else {
      // Update cache with fresh data
      const updatedBusiness = result.business as BusinessData;
      cache.current.set(user.id, updatedBusiness);
      setBusinessData(updatedBusiness);
      setLastSync(new Date());
      setSyncStatus('idle');
    }

    setFormLoading(false);
  };

  return (
    <>
      <PageHeader
        title="Business Dashboard"
        subtitle="MANAGE YOUR BUSINESS"
        description="Track performance, manage loyalty programs, and engage with your customers."
      />

      {/* Responsive Status Bar */}
      <div className="bg-gray-50 border-b">
        <div className="container mx-auto px-4 py-2">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 text-sm">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                {isOnline ? (
                  <Wifi className="h-4 w-4 text-green-500" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-500" />
                )}
                <span className={`font-medium ${isOnline ? 'text-green-600' : 'text-red-600'}`}>
                  {isOnline ? 'Online' : 'Offline'}
                </span>
              </div>
              
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${
                  syncStatus === 'idle' ? 'bg-green-500' :
                  syncStatus === 'syncing' ? 'bg-yellow-500 animate-pulse' :
                  'bg-red-500'
                }`}></div>
                <span className="text-gray-600">
                  {syncStatus === 'idle' ? 'Synced' :
                   syncStatus === 'syncing' ? 'Syncing...' :
                   'Sync Error'}
                </span>
              </div>
              
              {lastSync && (
                <span className="text-gray-500">
                  Last sync: {lastSync.toLocaleTimeString()}
                </span>
              )}
            </div>
            
            <button
              onClick={handleRefresh}
              disabled={syncStatus === 'syncing'}
              className="flex items-center gap-1 px-3 py-1 bg-white border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <RefreshCw className={`h-3 w-3 ${syncStatus === 'syncing' ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      <section className="py-8 lg:py-16">
        <div className="container mx-auto px-4">
          {/* Show loading state during initial authentication and data fetch */}
          {(authLoading || initialLoading) ? (
            <div className="text-center py-16">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-8 max-w-md mx-auto shadow-lg">
                <RefreshCw className="h-16 w-16 text-blue-500 mx-auto mb-6 animate-spin" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Loading Dashboard</h3>
                <p className="text-gray-600 mb-4">
                  {authLoading ? 'Authenticating...' : 'Loading business data...'}
                </p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{width: '60%'}}></div>
                </div>
              </div>
            </div>
          ) : /* Show additional application form if requested */
          showAdditionalForm ? (
            <AdditionalApplicationForm
              onSubmit={(success) => {
                if (success) {
                  setShowAdditionalForm(false);
                  handleRefresh(); // Refresh data after successful submission
                }
              }}
              onCancel={() => setShowAdditionalForm(false)}
            />
          ) : businessData ? (
            // Use cached business data instead of BusinessOwnershipGuard
            showForm ? (
              <div className="max-w-4xl mx-auto">
                <BusinessForm
                  initialData={businessData}
                  onSubmit={(formData) => handleFormSubmit(formData, businessData)}
                  onCancel={() => setShowForm(false)}
                />
              </div>
            ) : (
              <div className="max-w-6xl mx-auto">
                <BusinessDashboardContent
                  verifiedBusiness={businessData}
                  showForm={showForm}
                  setShowForm={setShowForm}
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                  loyaltyFrequency={loyaltyFrequency}
                  setLoyaltyFrequency={setLoyaltyFrequency}
                  formLoading={formLoading}
                  handleFormSubmit={handleFormSubmit}
                  handleLoyaltyFrequencyUpdate={handleLoyaltyFrequencyUpdate}
                  profile={profile}
                />
              </div>
            )
          ) : (
            // No business found - show registration form
            showForm ? (
              <div className="max-w-4xl mx-auto">
                <BusinessForm
                  initialData={null}
                  onSubmit={handleFormSubmit}
                  onCancel={() => setShowForm(false)}
                />
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-8 max-w-md mx-auto shadow-lg">
                  <Building2 className="h-16 w-16 text-blue-500 mx-auto mb-6" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">No Business Registered</h3>
                  <p className="text-gray-600 mb-6">Register your business to access the dashboard and start managing your customer relationships.</p>
                  <button
                    onClick={() => setShowForm(true)}
                    className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold"
                  >
                    Register Your Business
                  </button>
                </div>
              </div>
            )
          )}
        </div>
      </section>

      <CtaSection />
    </>
  );
}

// Main export component with Suspense boundary
export default function BusinessDashboardPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    }>
      <BusinessDashboardPageContent />
    </Suspense>
  );
}
