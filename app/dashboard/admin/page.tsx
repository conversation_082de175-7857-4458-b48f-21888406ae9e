"use client";

import React, { useState, useEffect } from "react";
import { PageHeader } from "@/components/page-header";
import { AnimatedSection } from "@/components/animated-section";
import { AnimatedCard } from "@/components/animated-card";

import { getSupabaseClient } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import {
  Building2,
  Users,
  Star,
  Globe,
  CheckCircle,
  BarChart3
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

interface AdminStats {
  totalBusinesses: number;
  activeBusinesses: number;
  inactiveBusinesses: number;
  spotlightBusinesses: number;
  totalUsers: number;
  cardHolders: number;
  recentBusinesses: any[];
}

export default function DashboardAdminPage() {
  const [stats, setStats] = useState<AdminStats>({
    totalBusinesses: 0,
    activeBusinesses: 0,
    inactiveBusinesses: 0,
    spotlightBusinesses: 0,
    totalUsers: 0,
    cardHolders: 0,
    recentBusinesses: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAdminStats();
  }, []);

  async function loadAdminStats() {
    try {
      setLoading(true);

      if (!supabase) {
        console.error("Supabase client not initialized");
        return;
      }

      // Load business statistics
      const { data: businesses, error: businessError } = await supabase
        .from("businesses")
        .select(`
          id,
          name,
          category,
          is_active,
          business_spotlight,
          logo_url,
          premium_discount,
          created_at,
          profiles:user_id(first_name, last_name, user_email)
        `)
        .order("created_at", { ascending: false });

      if (businessError) {
        console.error("Error loading businesses:", businessError);
        return;
      }

      // Load user statistics
      const { data: profiles, error: profilesError } = await supabase
        .from("profiles")
        .select("id, is_card_holder");

      if (profilesError) {
        console.error("Error loading profiles:", profilesError);
        return;
      }

      // Calculate statistics
      const totalBusinesses = businesses?.length || 0;
      const activeBusinesses = businesses?.filter(b => b.is_active).length || 0;
      const inactiveBusinesses = businesses?.filter(b => !b.is_active).length || 0;
      const spotlightBusinesses = businesses?.filter(b => b.business_spotlight).length || 0;
      const totalUsers = profiles?.length || 0;
      const cardHolders = profiles?.filter(p => p.is_card_holder).length || 0;
      const recentBusinesses = businesses?.slice(0, 5) || [];

      setStats({
        totalBusinesses,
        activeBusinesses,
        inactiveBusinesses,
        spotlightBusinesses,
        totalUsers,
        cardHolders,
        recentBusinesses
      });

    } catch (error) {
      console.error("Error loading admin stats:", error);
    } finally {
      setLoading(false);
    }
  }

  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: string } = {
      restaurant: '🍽️', retail: '🛍️', health: '🏥', beauty: '💄',
      automotive: '🚗', entertainment: '🎭', fitness: '💪', 
      technology: '💻', education: '📚', professional: '💼'
    };
    return icons[category] || '🏢';
  };

  return (
    <ProtectedRoute requiredRole="admin">
      <PageHeader
        title="Admin Dashboard"
        subtitle="MANAGEMENT PORTAL"
        description="Overview of the Fuse VIP network and business management tools."
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          {/* Quick Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <AnimatedSection delay={0}>
              <AnimatedCard className="bg-gradient-to-br from-blue-600 to-blue-700 p-6 rounded-lg text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">Total Businesses</p>
                    <p className="text-3xl font-bold">{stats.totalBusinesses}</p>
                  </div>
                  <Building2 className="h-8 w-8 text-blue-200" />
                </div>
              </AnimatedCard>
            </AnimatedSection>

            <AnimatedSection delay={0.1}>
              <AnimatedCard className="bg-gradient-to-br from-green-600 to-green-700 p-6 rounded-lg text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">Active Businesses</p>
                    <p className="text-3xl font-bold">{stats.activeBusinesses}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-200" />
                </div>
              </AnimatedCard>
            </AnimatedSection>

            <AnimatedSection delay={0.2}>
              <AnimatedCard className="bg-gradient-to-br from-yellow-600 to-yellow-700 p-6 rounded-lg text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-100 text-sm">Spotlight Businesses</p>
                    <p className="text-3xl font-bold">{stats.spotlightBusinesses}</p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-200" />
                </div>
              </AnimatedCard>
            </AnimatedSection>

            <AnimatedSection delay={0.3}>
              <AnimatedCard className="bg-gradient-to-br from-purple-600 to-purple-700 p-6 rounded-lg text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">VIP Card Holders</p>
                    <p className="text-3xl font-bold">{stats.cardHolders}</p>
                  </div>
                  <Users className="h-8 w-8 text-purple-200" />
                </div>
              </AnimatedCard>
            </AnimatedSection>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <AnimatedSection delay={0.4}>
              <Link href="/admin/businesses">
                <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-bold text-lg text-gray-900">Manage Businesses</h3>
                      <p className="text-gray-600 text-sm">Edit, activate, and manage all businesses</p>
                    </div>
                    <Building2 className="h-8 w-8 text-blue-600" />
                  </div>
                </AnimatedCard>
              </Link>
            </AnimatedSection>

            <AnimatedSection delay={0.5}>
              <Link href="/industry">
                <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-bold text-lg text-gray-900">View Directory</h3>
                      <p className="text-gray-600 text-sm">Browse the public business directory</p>
                    </div>
                    <Globe className="h-8 w-8 text-green-600" />
                  </div>
                </AnimatedCard>
              </Link>
            </AnimatedSection>

            <AnimatedSection delay={0.6}>
              <Link href="/admin">
                <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-bold text-lg text-gray-900">Full Admin Portal</h3>
                      <p className="text-gray-600 text-sm">Access all admin tools and features</p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-purple-600" />
                  </div>
                </AnimatedCard>
              </Link>
            </AnimatedSection>
          </div>

          {/* Recent Businesses */}
          <AnimatedSection delay={0.7}>
            <div className="bg-white rounded-lg shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-gray-900">Recent Businesses</h3>
                  <Link 
                    href="/admin/businesses"
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    View All →
                  </Link>
                </div>
              </div>

              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
                </div>
              ) : stats.recentBusinesses.length > 0 ? (
                <div className="divide-y divide-gray-200">
                  {stats.recentBusinesses.map((business) => (
                    <div key={business.id} className="p-6">
                      <div className="flex items-center space-x-4">
                        {/* Business Logo/Icon */}
                        <div className="relative flex-shrink-0">
                          {business.business_spotlight && (
                            <div className="absolute -top-1 -right-1 z-10">
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            </div>
                          )}
                          
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg relative bg-white p-1">
                            {business.logo_url ? (
                              <Image
                                src={business.logo_url}
                                alt={business.name}
                                fill
                                className="object-contain rounded-lg"
                                unoptimized={business.logo_url?.endsWith('.svg')}
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full">
                                <span className="text-lg">{getCategoryIcon(business.category)}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Business Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-semibold text-gray-900">{business.name}</h4>
                            <div className={`px-2 py-1 rounded-full text-xs font-bold ${
                              business.is_active 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {business.is_active ? 'Active' : 'Inactive'}
                            </div>
                          </div>
                          
                          <div className="text-sm text-gray-600">
                            <p>
                              <strong>Owner:</strong> {business.profiles?.first_name} {business.profiles?.last_name}
                            </p>
                            <p>
                              <strong>Category:</strong> {business.category.charAt(0).toUpperCase() + business.category.slice(1).replace('_', ' ')} • 
                              <strong> Discount:</strong> {business.premium_discount}%
                            </p>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex space-x-2">
                          <Link href="/admin/businesses">
                            <button className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                              Manage
                            </button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-6 text-center text-gray-500">
                  No businesses found.
                </div>
              )}
            </div>
          </AnimatedSection>

          {/* Network Health */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
            <AnimatedSection delay={0.8}>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Network Health</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Business Activation Rate</span>
                    <span className="font-semibold">
                      {stats.totalBusinesses > 0 
                        ? Math.round((stats.activeBusinesses / stats.totalBusinesses) * 100)
                        : 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ 
                        width: `${stats.totalBusinesses > 0 
                          ? (stats.activeBusinesses / stats.totalBusinesses) * 100 
                          : 0}%` 
                      }}
                    ></div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">VIP Card Adoption</span>
                    <span className="font-semibold">
                      {stats.totalUsers > 0 
                        ? Math.round((stats.cardHolders / stats.totalUsers) * 100)
                        : 0}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-purple-600 h-2 rounded-full" 
                      style={{ 
                        width: `${stats.totalUsers > 0 
                          ? (stats.cardHolders / stats.totalUsers) * 100 
                          : 0}%` 
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={0.9}>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">Quick Stats</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between py-2">
                    <span className="text-gray-600">Total Users</span>
                    <span className="font-semibold text-lg">{stats.totalUsers}</span>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span className="text-gray-600">Inactive Businesses</span>
                    <span className="font-semibold text-lg text-orange-600">{stats.inactiveBusinesses}</span>
                  </div>
                  <div className="flex items-center justify-between py-2">
                    <span className="text-gray-600">Featured Businesses</span>
                    <span className="font-semibold text-lg text-yellow-600">{stats.spotlightBusinesses}</span>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>
    </ProtectedRoute>
  );
}