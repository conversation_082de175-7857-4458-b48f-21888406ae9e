"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { getSupabaseClient } from "@/lib/supabase";
import { ProtectedRoute } from "@/components/protected-route";
import { BusinessQRCode } from "@/components/business-qr-code";
import { Button } from "@/components/ui/button";
import { ArrowLeft, BarChart3, Users, Calendar, TrendingUp } from "lucide-react";

interface BusinessInfo {
  id: string;
  name: string;
  category: string;
  logo_url?: string;
}

interface VisitStats {
  totalVisits: number;
  uniqueVisitors: number;
  visitsThisWeek: number;
  visitsThisMonth: number;
  fuseDistributed: number;
}

export default function BusinessQRPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [business, setBusiness] = useState<BusinessInfo | null>(null);
  const [visitStats, setVisitStats] = useState<VisitStats>({
    totalVisits: 0,
    uniqueVisitors: 0,
    visitsThisWeek: 0,
    visitsThisMonth: 0,
    fuseDistributed: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) return;

    const fetchBusinessData = async () => {
      const supabase = getSupabaseClient();
      if (!supabase) return;

      try {
        // Fetch business information
        const { data: businessData, error: businessError } = await supabase
          .from('businesses')
          .select('id, name, category, logo_url')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .single();

        if (businessError) {
          console.error('Error fetching business:', businessError);
          return;
        }

        if (!businessData) {
          // No approved business found
          return;
        }

        setBusiness(businessData);

        // Fetch visit statistics
        const { data: visits, error: visitsError } = await supabase
          .from('business_visits')
          .select('user_id, scanned_at')
          .eq('business_id', businessData.id);

        if (visitsError) {
          console.error('Error fetching visits:', visitsError);
          return;
        }

        // Calculate statistics
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

        const totalVisits = visits?.length || 0;
        const uniqueVisitors = new Set(visits?.map(v => v.user_id)).size;
        const visitsThisWeek = visits?.filter(v => 
          new Date(v.scanned_at) >= oneWeekAgo
        ).length || 0;
        const visitsThisMonth = visits?.filter(v => 
          new Date(v.scanned_at) >= oneMonthAgo
        ).length || 0;

        setVisitStats({
          totalVisits,
          uniqueVisitors,
          visitsThisWeek,
          visitsThisMonth,
          fuseDistributed: totalVisits * 100
        });

      } catch (error) {
        console.error('Error fetching business data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBusinessData();
  }, [user]);

  const handleGoBack = () => {
    router.push('/dashboard');
  };

  const handleViewFullAnalytics = () => {
    router.push('/dashboard/business-analytics');
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!business) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center mb-8">
              <Button
                onClick={handleGoBack}
                variant="ghost"
                className="text-white hover:bg-white/10 mr-4"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Dashboard
              </Button>
            </div>

            <div className="max-w-2xl mx-auto text-center">
              <div className="bg-white rounded-xl p-8 shadow-xl">
                <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                  No Approved Business Found
                </h1>
                <p className="text-gray-600 mb-6">
                  You need an approved business account to access QR codes and analytics. 
                  Please apply for business verification or wait for approval.
                </p>
                <Button 
                  onClick={() => router.push('/dashboard/business')}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3"
                >
                  Manage Business Application
                </Button>
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={handleGoBack}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <div className="max-w-6xl mx-auto">
            {/* Page Title */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-white mb-2">
                Business QR Code & Analytics
              </h1>
              <p className="text-blue-200">
                Manage your QR code and track customer visits
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* QR Code Section */}
              <div className="space-y-6">
                <div className="bg-white rounded-xl p-6 shadow-xl">
                  <h2 className="text-xl font-bold text-gray-900 mb-4">Your QR Code</h2>
                  <BusinessQRCode 
                    businessId={business.id}
                    businessName={business.name}
                    size={300}
                    className="border-0 shadow-none"
                  />
                </div>

                <div className="bg-white/10 rounded-xl p-6">
                  <h3 className="text-white font-semibold mb-3">Display Instructions</h3>
                  <ul className="text-gray-300 text-sm space-y-2">
                    <li>• Print and display prominently in your business</li>
                    <li>• Place near entrance or checkout counter</li>
                    <li>• Encourage VIP customers to scan for FUSE rewards</li>
                    <li>• Each customer can scan once per day</li>
                  </ul>
                </div>
              </div>

              {/* Analytics Section */}
              <div className="space-y-6">
                <div className="bg-white rounded-xl p-6 shadow-xl">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-bold text-gray-900">Visit Analytics</h2>
                    <Button 
                      onClick={handleViewFullAnalytics}
                      variant="outline"
                      size="sm"
                    >
                      View Details
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900">{visitStats.totalVisits}</div>
                      <div className="text-gray-600 text-sm">Total Visits</div>
                    </div>

                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900">{visitStats.uniqueVisitors}</div>
                      <div className="text-gray-600 text-sm">Unique Visitors</div>
                    </div>

                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <Calendar className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900">{visitStats.visitsThisWeek}</div>
                      <div className="text-gray-600 text-sm">This Week</div>
                    </div>

                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <TrendingUp className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900">{visitStats.visitsThisMonth}</div>
                      <div className="text-gray-600 text-sm">This Month</div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-xl">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">FUSE Rewards Impact</h3>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-green-600 mb-2">
                      {visitStats.fuseDistributed.toLocaleString()}
                    </div>
                    <div className="text-gray-600">Total FUSE Distributed</div>
                    <p className="text-sm text-gray-500 mt-2">
                      Your business has helped customers earn {visitStats.fuseDistributed.toLocaleString()} FUSE tokens
                    </p>
                  </div>
                </div>

                <div className="bg-white/5 rounded-xl p-6">
                  <h3 className="text-white font-semibold mb-3">Benefits for Your Business</h3>
                  <ul className="text-gray-300 text-sm space-y-2">
                    <li>• Attract VIP customers with FUSE rewards</li>
                    <li>• Increase customer loyalty and repeat visits</li>
                    <li>• Track customer engagement analytics</li>
                    <li>• Join the Fuse.VIP partner network</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
