"use client";

import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AnimatedSection } from "@/components/animated-section";
import { useAuth } from "@/contexts/auth-context";
import { OnboardingPanel } from "@/components/dashboard/onboarding-panel";
import { RedeemCodeModal } from "@/components/redeem-code-modal";
import { DashboardProfileSummary } from "@/components/dashboard/dashboard-profile-summary";
import { WalletProfileSection } from "@/components/dashboard/wallet-profile-section";
import { DashboardDataProvider } from "@/components/containers/dashboard-data-container";
import { EnhancedBusinessDisplay } from "@/components/dashboard/enhanced-business-display";
import { QRCodeModal } from "@/components/dashboard/qr-code-modal";
import { SkyBackground } from "@/components/sky-background";
import ThemeToggle from "@/components/theme-toggle";
import { useTheme } from "next-themes";
import { getSupabaseClient } from "@/lib/supabase";
import { profileCache } from "@/lib/profile-cache";
import { authStateManager } from "@/lib/auth-state-manager";
import { walletService } from "@/lib/wallet-service";

import { Loader2, Gift, LogOut, QrCode, Users, Coins, Settings } from "lucide-react";

import Image from "next/image";
import Link from "next/link";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { BusinessDebugPanel } from "@/components/debug/business-debug-panel";


function DashboardContent() {
  const { user, profile, isLoading, signOut, isAdmin } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isRedeemModalOpen, setIsRedeemModalOpen] = useState(false);
  const [isQRModalOpen, setIsQRModalOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [oauthProcessing, setOauthProcessing] = useState(false);
  const { theme, setTheme } = useTheme();

  // Theme toggle function
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // Card tiers data (matching upgrade page)
  const cardTiers = [
    {
      name: "Premium Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/premium-card-jvnxFn2cckvmHmXc3LEiYxIXgsVH9k.png",
      rewards: { referral: "20%", affiliate: "1%" },
      color: "from-blue-600 via-purple-600 to-indigo-700"
    },
    {
      name: "Gold Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/gold-card-bflkHLVolhMSi3L5RexoL9fh7wsqBq.png",
      rewards: { referral: "25%", affiliate: "2%" },
      color: "from-yellow-600 via-yellow-500 to-orange-600"
    },
    {
      name: "Platinum Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/platinum-card-IQekSEB3CMuXes5qUTxZc55ZeKUVOy.png",
      rewards: { referral: "30%", affiliate: "3%" },
      color: "from-gray-400 via-gray-300 to-gray-500"
    },
    {
      name: "Diamond Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/diamond-card-I9uaKzc6KKTRSgl9mrz4SP2Y2myuiF.png",
      rewards: { referral: "35%", affiliate: "4%" },
      color: "from-cyan-400 via-blue-300 to-indigo-400"
    },
    {
      name: "Obsidian Card",
      cardImage: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/obsidian-card-sZV0uG2g9pJ0BiQRLvn14MJIFGvzDn.png",
      rewards: { referral: "40%", affiliate: "5%" },
      color: "from-black via-gray-800 to-gray-900"
    }
  ];

  // Helper function to get card tier data
  const getUserCardTier = () => {
    if (!profile?.card_tier) return null;
    return cardTiers.find(card =>
      card.name.toLowerCase().includes(profile.card_tier!.toLowerCase())
    );
  };


  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await signOut();
      // Clear any stored user data
      localStorage.removeItem('user_id');
      localStorage.removeItem('user_email');
      // Redirect to home page
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Check for pending wallet connections when user auth state changes
  useEffect(() => {
    // Dashboard is ready when user is authenticated
    if (user && profile) {
      console.log("Dashboard ready for user:", user.email);
      
      // Check for pending wallet address from previous OAuth callback
      const pendingWalletAddress = localStorage.getItem('pending_wallet_address');
      const pendingWalletTimestamp = localStorage.getItem('pending_wallet_timestamp');
      
      if (pendingWalletAddress && pendingWalletTimestamp) {
        const timestamp = new Date(pendingWalletTimestamp);
        const ageMinutes = (Date.now() - timestamp.getTime()) / (1000 * 60);
        
        // Only process if pending wallet is less than 10 minutes old
        if (ageMinutes < 10) {
          console.log('🔄 [Dashboard] Processing pending wallet connection:', {
            walletAddress: `${pendingWalletAddress.substring(0, 8)}...`,
            ageMinutes: Math.round(ageMinutes * 100) / 100
          });
          
          // Process pending wallet using walletService directly
          walletService.connectWallet(user.id, pendingWalletAddress, 'xrp')
            .then((result: any) => {
              console.log('✅ [Dashboard] Pending wallet connection processed:', result);
              if (result.success) {
                // Clean up pending data
                localStorage.removeItem('pending_wallet_address');
                localStorage.removeItem('pending_wallet_timestamp');
              }
            })
            .catch((error: any) => {
              console.error('❌ [Dashboard] Failed to process pending wallet:', error);
            });
        } else {
          console.log('⏰ [Dashboard] Pending wallet address expired, cleaning up');
          localStorage.removeItem('pending_wallet_address');
          localStorage.removeItem('pending_wallet_timestamp');
        }
      }
    }
  }, [user, profile]);

  // Handle OAuth callback parameters and wallet returns for Xaman
  useEffect(() => {
    const handleOAuthCallback = async () => {
      // Check for wallet return first
      const walletReturn = authStateManager.isWalletReturn();
      
      if (walletReturn.isReturn && walletReturn.sessionId) {
        console.log('🔄 Wallet return detected:', walletReturn);
        setOauthProcessing(true);

        try {
          // Try to restore authentication state
          const restoration = authStateManager.restoreAuthState(walletReturn.sessionId);
          
          if (restoration.success && restoration.state) {
            console.log('✅ Auth state restored successfully:', {
              userId: restoration.state.userId,
              targetRoute: restoration.state.targetRoute,
              walletContext: restoration.state.walletContext
            });

            // Check if user is currently authenticated
            if (!user?.id) {
              console.log('🔑 User not authenticated, attempting automatic sign-in...');
              
              // If the user from state matches what we expect, redirect to login with restoration
              // This handles the case where the session expired during wallet interaction
              const loginUrl = `/login?wallet_return=true&session_id=${walletReturn.sessionId}&redirect=${encodeURIComponent(restoration.state.targetRoute)}`;
              console.log('↩️ Redirecting to login for authentication restoration:', loginUrl);
              router.push(loginUrl);
              return;
            }

            // User is authenticated, continue with normal flow
            console.log('✅ User authenticated, continuing wallet return flow');
            
            // Clean up URL parameters and redirect to target route if different
            const currentRoute = window.location.pathname + window.location.search;
            if (restoration.state.targetRoute !== currentRoute && restoration.state.targetRoute !== window.location.pathname) {
              console.log('↩️ Redirecting to original target route:', restoration.state.targetRoute);
              authStateManager.cleanupAuthState(walletReturn.sessionId);
              router.push(restoration.state.targetRoute);
              return;
            }

            // Clean up the URL parameters
            const cleanUrl = window.location.pathname;
            window.history.replaceState({}, document.title, cleanUrl);
            authStateManager.cleanupAuthState(walletReturn.sessionId);
            
            console.log('🎉 Wallet return processed successfully');
          } else {
            console.warn('⚠️ Failed to restore auth state:', restoration.error);
            // Continue with normal OAuth flow as fallback
          }
        } catch (restoreError) {
          console.error('❌ Error during wallet return processing:', restoreError);
          // Continue with normal OAuth flow as fallback
        } finally {
          setOauthProcessing(false);
        }
      }

      // Standard OAuth callback handling (existing logic)
      const authCode = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');

      if (authCode || error) {
        console.log('🔐 OAuth callback detected:', { authCode: !!authCode, state, error });
        setOauthProcessing(true);

        try {
          if (error) {
            console.error('❌ OAuth error:', error);
            // Handle OAuth error - could show a notification
          } else if (authCode) {
            console.log('🔄 Processing OAuth authorization code...');

            // Exchange authorization code for access token via our API
            const { authenticatedFetch } = await import('@/lib/auth-client');
            const tokenResponse = await authenticatedFetch('/api/xaman/oauth/token', {
              method: 'POST',
              body: JSON.stringify({
                code: authCode,
                state: state,
                redirect_uri: window.location.origin + '/dashboard'
              }),
            });

            if (!tokenResponse.ok) {
              const errorData = await tokenResponse.json();
              throw new Error(`Token exchange failed: ${errorData.error}`);
            }

            const tokenData = await tokenResponse.json();
            console.log('✅ OAuth token exchange successful:', {
              hasAccessToken: !!tokenData.access_token,
              walletAddress: tokenData.user?.account
            });

            // Update user profile with wallet address if user is authenticated
            console.log('🔍 [Dashboard] OAuth callback auth state check:', {
              hasUser: !!user?.id,
              userId: user?.id,
              userEmail: user?.email,
              hasProfile: !!profile,
              profileId: profile?.id,
              hasWalletAddress: !!tokenData.user?.account,
              walletAddress: tokenData.user?.account,
              authIsLoading: isLoading
            });

            if (user?.id && tokenData.user?.account) {
              console.log('🔄 [Dashboard] User authenticated, updating wallet profile...');
              try {
                const result = await updateUserWalletProfile(user.id, tokenData.user.account);
                console.log('✅ [Dashboard] Wallet profile update result:', result);
              } catch (updateError) {
                console.error('❌ [Dashboard] Failed to update wallet profile:', updateError);
              }
            } else if (!user?.id && tokenData.user?.account) {
              console.warn('⚠️ [Dashboard] User not authenticated but wallet address available - storing temporarily');
              // Store wallet data temporarily for when user becomes authenticated
              localStorage.setItem('pending_wallet_address', tokenData.user.account);
              localStorage.setItem('pending_wallet_timestamp', new Date().toISOString());
              
              // Try to retry after a short delay when auth state might be ready
              setTimeout(async () => {
                console.log('🔄 [Dashboard] Retrying wallet update after auth delay...');
                if (user?.id) {
                  try {
                    const result = await updateUserWalletProfile(user.id, tokenData.user.account);
                    console.log('✅ [Dashboard] Delayed wallet profile update result:', result);
                    // Clean up temporary storage
                    localStorage.removeItem('pending_wallet_address');
                    localStorage.removeItem('pending_wallet_timestamp');
                  } catch (retryError) {
                    console.error('❌ [Dashboard] Delayed wallet update failed:', retryError);
                  }
                } else {
                  console.warn('⚠️ [Dashboard] User still not authenticated after delay');
                }
              }, 2000); // 2 second delay to allow auth state to stabilize
            } else {
              console.warn('⚠️ [Dashboard] Cannot update wallet profile - missing requirements:', {
                hasUser: !!user?.id,
                hasWalletAddress: !!tokenData.user?.account,
                userEmail: user?.email || 'none',
                walletAddress: tokenData.user?.account ? `${tokenData.user.account.substring(0, 8)}...` : 'none'
              });
            }

            // Store wallet connection state
            localStorage.setItem('fuse_wallet_address', tokenData.user.account);
            localStorage.setItem('fuse_wallet_connected', 'true');

            // Clean up URL parameters after processing
            const cleanUrl = window.location.pathname;
            window.history.replaceState({}, document.title, cleanUrl);

            console.log('🎉 OAuth callback processed successfully, URL cleaned');
          }
        } catch (callbackError) {
          console.error('❌ Error processing OAuth callback:', callbackError);
        } finally {
          setOauthProcessing(false);
        }
      }
    };

    // Helper function to update user profile with wallet address using centralized service
    const updateUserWalletProfile = async (userId: string, walletAddress: string) => {
      if (!userId) {
        console.warn('⚠️ Cannot update wallet profile: User ID not provided');
        return { success: false, error: 'User ID not provided' };
      }

      if (!walletAddress) {
        console.warn('⚠️ Cannot update wallet profile: Wallet address not provided');
        return { success: false, error: 'Wallet address not provided' };
      }

      console.log('💾 [Dashboard] Updating wallet profile using centralized service:', {
        userId,
        walletAddress: `${walletAddress.substring(0, 6)}...${walletAddress.substring(walletAddress.length - 4)}`
      });

      try {
        const result = await walletService.connectWallet(userId, walletAddress, 'xrp');
        
        if (result.success) {
          console.log('✅ [Dashboard] Wallet profile updated successfully via service');
          return { success: true, walletAddress };
        } else {
          console.error('❌ [Dashboard] Failed to update wallet profile:', result.error);
          return { success: false, error: result.error };
        }
      } catch (error) {
        console.error('❌ [Dashboard] Exception updating user wallet profile:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
      }
    };

    // Process OAuth callback with improved timing handling
    // We need to handle cases where searchParams exist but user might not be loaded yet
    if (searchParams) {
      // Add a delay to allow auth state to stabilize
      const timer = setTimeout(() => {
        handleOAuthCallback();
      }, 100); // Small delay to ensure auth state is stable
      
      return () => clearTimeout(timer);
    }
  }, [searchParams, user]);

  // Business data is now handled by the useBusinessData hook



  if (isLoading || oauthProcessing) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3A56FF]" />
          <p className="text-gray-600">
            {oauthProcessing ? 'Processing wallet connection...' : 'Loading your dashboard...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <DashboardDataProvider>
        {/* Header with user controls */}
        <div className="relative bg-gradient-to-br from-[#000814] via-[#001122] to-black">
        <div className="container mx-auto px-4 pt-8 pb-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Image
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png"
              width={40}
              height={40}
              alt="Fuse.vip Logo"
              className="w-10 h-10"
            />
            <div>
              <h1 className="text-white text-xl font-bold">Welcome back!</h1>
              <p className="text-white/70 text-sm">Ready to grow your business or save money?</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {isAdmin && (
              <Link href="/admin">
                <Button
                  variant="outline"
                  className="text-blue-400 border-blue-400 hover:bg-blue-400/10 flex items-center"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Admin
                </Button>
              </Link>
            )}
            <Button
              onClick={handleLogout}
              disabled={isLoggingOut}
              variant="outline"
              className="text-red-400 border-red-400 hover:bg-red-400/10 flex items-center"
            >
              {isLoggingOut ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Signing out...
                </>
              ) : (
                <>
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Condensed Hero Section */}
        <div className="container mx-auto px-4 py-8">
          <AnimatedSection>
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-6">
                <h2 className="text-2xl md:text-4xl font-bold text-white mb-2 leading-tight">
                  Your <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">Fuse</span> Dashboard
                </h2>
                <p className="text-sm md:text-base text-white/80 max-w-xl mx-auto">
                  Scan, connect, and earn rewards
                </p>
              </div>

              {/* VIP Card Section - Main Focus */}
              <div className="mb-8">
                <AnimatedSection delay={0.1}>
                  {!isLoading && profile?.is_card_holder && getUserCardTier() ? (
                    // Show user's card tier in a striking way
                    <div className="relative group">
                      {/* Dynamic background gradient based on card tier */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${getUserCardTier()?.color || 'from-emerald-600 via-teal-600 to-cyan-700'} rounded-3xl opacity-90 group-hover:opacity-100 transition-opacity duration-300`}></div>

                      {/* Content */}
                      <div className="relative bg-black/10 backdrop-blur-sm rounded-3xl p-8 lg:p-12 border border-white/20 h-full">
                        <div className="text-center text-white space-y-6">
                          {/* Badge */}
                          <div className="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-full text-sm font-semibold">
                            ✅ VIP Member
                          </div>

                          {/* Title */}
                          <h3 className="text-4xl lg:text-5xl font-bold leading-tight">
                            Your <span className="text-yellow-400">{getUserCardTier()?.name}</span>
                          </h3>

                          {/* Card Image */}
                          <div className="flex justify-center mb-6">
                            <div className="relative">
                              <Image
                                src={getUserCardTier()?.cardImage || "/placeholder.svg"}
                                width={300}
                                height={200}
                                alt={`${getUserCardTier()?.name} VIP Card`}
                                className="rounded-xl shadow-2xl transform hover:scale-105 transition-transform duration-300"
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl"></div>
                            </div>
                          </div>

                          {/* Rewards Info */}
                          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-sm mx-auto border border-white/20">
                            <div className="grid grid-cols-2 gap-4 text-center">
                              <div>
                                <div className="text-2xl font-bold text-yellow-400">{getUserCardTier()?.rewards.referral}</div>
                                <div className="text-white/80 text-sm">Referral Rewards</div>
                              </div>
                              <div>
                                <div className="text-2xl font-bold text-green-400">{getUserCardTier()?.rewards.affiliate}</div>
                                <div className="text-white/80 text-sm">Affiliate Rewards</div>
                              </div>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="space-y-4">
                            <div className="flex flex-col sm:flex-row gap-2">
                              <Link href="/dashboard/cards" className="flex-1">
                                <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold text-lg px-8 py-4 rounded-xl w-full border shadow-lg transform hover:scale-105 transition-all duration-200">
                                  View Card Dashboard
                                </Button>
                              </Link>
                              <Button
                                onClick={() => setIsQRModalOpen(true)}
                                className="bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 hover:text-blue-200 border border-blue-400/30 px-8 py-4 rounded-xl flex-1 text-lg font-semibold shadow-lg transform hover:scale-105 transition-all duration-200"
                              >
                                <QrCode className="h-5 w-5 mr-2" />
                                Open QR Center
                              </Button>
                            </div>
                            <div className="flex flex-col sm:flex-row gap-2">
                              <Button
                                onClick={() => router.push('/qr-connections')}
                                className="bg-white/20 hover:bg-white/30 text-white border border-white/30 px-4 py-3 rounded-xl flex-1 font-semibold"
                              >
                                <Users className="h-4 w-4 mr-2" />
                                View Connections
                              </Button>
                              <Link href="/industry" className="flex-1">
                                <Button className="bg-yellow-500 hover:bg-yellow-400 text-black font-bold px-4 py-3 rounded-xl w-full">
                                  Find Discounts
                                </Button>
                              </Link>
                              <Button
                                onClick={() => setIsRedeemModalOpen(true)}
                                className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-4 py-3 rounded-xl flex-1 border"
                              >
                                Redeem Code
                              </Button>
                            </div>
                          </div>

                          <p className="text-white/60 text-sm">
                            🎉 VIP Member • 💰 Earning rewards • 🔥 Exclusive access
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    // Show VIP Card CTA for non-card holders
                    <div className="relative group">
                      {/* Background gradient */}
                      <div className="absolute inset-0 bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-700 rounded-3xl opacity-90 group-hover:opacity-100 transition-opacity duration-300"></div>

                      {/* Content */}
                      <div className="relative bg-black/10 backdrop-blur-sm rounded-3xl p-8 lg:p-12 border border-white/20 h-full">
                        <div className="text-center text-white space-y-6">
                          {/* Badge */}
                          <div className="inline-flex items-center px-4 py-2 bg-orange-500 text-white rounded-full text-sm font-semibold">
                            💎 For Smart Shoppers
                          </div>

                          {/* Title */}
                          <h3 className="text-4xl lg:text-5xl font-bold leading-tight">
                            Get Your <span className="text-orange-400">VIP Card</span>
                          </h3>

                          {/* Description */}
                          <p className="text-xl text-white/90 max-w-md mx-auto">
                            Unlock exclusive discounts and rewards at hundreds of businesses nationwide.
                          </p>

                          {/* Pricing Options */}
                          <div className="space-y-4 max-w-lg mx-auto">
                            {/* Monthly Option - Most Prominent */}
                            <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-2xl p-6 border-2 border-green-400/50 relative">
                              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                <span className="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                                  🔥 MOST POPULAR
                                </span>
                              </div>
                              <div className="text-center pt-2">
                                <div className="text-5xl font-bold text-green-400 mb-2">$9.99</div>
                                <div className="text-white/90 text-lg font-semibold">per month</div>
                                <div className="text-white/70 text-sm mt-1">Cancel anytime • No commitment</div>
                              </div>
                            </div>

                            {/* Annual Option */}
                            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                              <div className="text-center">
                                <div className="text-white/70 text-sm line-through">$200/year</div>
                                <div className="text-2xl font-bold text-orange-400">$100/year</div>
                                <div className="text-white/80 text-sm">Save $20 vs monthly</div>
                              </div>
                            </div>
                          </div>

                          {/* Benefits */}
                          <div className="space-y-4 text-left max-w-sm mx-auto">
                            <div className="flex items-center">
                              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                <span className="text-white text-xs">✓</span>
                              </div>
                              <span className="text-white/90">Save 10-50% at partner businesses</span>
                            </div>
                            <div className="flex items-center">
                              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                <span className="text-white text-xs">✓</span>
                              </div>
                              <span className="text-white/90">Earn cashback and rewards</span>
                            </div>
                            <div className="flex items-center">
                              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                <span className="text-white text-xs">✓</span>
                              </div>
                              <span className="text-white/90">Access to exclusive events</span>
                            </div>
                            <div className="flex items-center">
                              <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                <span className="text-white text-xs">✓</span>
                              </div>
                              <span className="text-white/90">Priority customer support</span>
                            </div>
                          </div>

                          {/* CTA Buttons */}
                          <div className="space-y-4">
                            {/* Primary CTA - Monthly */}
                            <Link href="/upgrade">
                              <Button className="bg-green-500 hover:bg-green-400 text-white font-bold text-lg px-8 py-4 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200 w-full">
                                🚀 Start Monthly - $9.99/mo
                              </Button>
                            </Link>

                            {/* Secondary CTA - Annual */}
                            <Link href="/upgrade">
                              <Button className="bg-orange-500 hover:bg-orange-400 text-white font-bold text-base px-6 py-3 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200 w-full">
                                💳 Get Annual Card - $100/year
                              </Button>
                            </Link>

                            <div className="flex flex-col sm:flex-row gap-2">
                              <Link href="/dashboard/cards" className="flex-1">
                                <Button className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-4 py-3 rounded-xl w-full border">
                                  View Card Dashboard
                                </Button>
                              </Link>
                              <Button
                                onClick={() => setIsQRModalOpen(true)}
                                className="bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 hover:text-blue-200 border border-blue-400/30 px-4 py-3 rounded-xl flex-1 font-semibold"
                              >
                                <QrCode className="h-4 w-4 mr-2" />
                                Open QR Center
                              </Button>
                            </div>
                            <div className="flex flex-col sm:flex-row gap-2">
                              <Button
                                onClick={() => router.push('/qr-connections')}
                                className="bg-white/20 hover:bg-white/30 text-white border border-white/30 px-4 py-3 rounded-xl flex-1 font-semibold"
                              >
                                <Users className="h-4 w-4 mr-2" />
                                View Connections
                              </Button>
                              <Button
                                onClick={() => setIsRedeemModalOpen(true)}
                                className="bg-white/20 border-white/40 text-white hover:bg-white/30 font-semibold px-4 py-3 rounded-xl flex-1 border"
                              >
                                Redeem Code
                              </Button>
                            </div>
                          </div>

                          <p className="text-white/60 text-sm">
                            💳 Instant activation • 🔒 Cancel anytime • 📱 Mobile app access
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </AnimatedSection>
              </div>

              {/* Quick Actions Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-8">
                {/* Quick Actions */}
                <AnimatedSection delay={0.2}>
                  <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
                    <h3 className="text-lg font-bold text-white mb-3">Quick Actions</h3>
                    <div className="bg-white/5 rounded-xl p-4 mb-4 border border-white/20">
                      <Gift className="h-16 w-16 text-white/50 mx-auto mb-3" />
                      <p className="text-white/70 text-sm mb-2">
                        Redeem codes and scan QR codes
                      </p>
                      <p className="text-white/60 text-xs">
                        Enter promo codes • Scan to earn rewards
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Button
                        onClick={() => setIsRedeemModalOpen(true)}
                        className="bg-green-600/20 hover:bg-green-600/30 border border-green-400/30 text-green-300 hover:text-green-200 px-4 py-2 rounded-lg w-full text-sm"
                      >
                        <Gift className="h-4 w-4 mr-2" />
                        Redeem Code
                      </Button>
                      <Button
                        onClick={() => setIsQRModalOpen(true)}
                        className="bg-purple-600/20 hover:bg-purple-600/30 border border-purple-400/30 text-purple-300 hover:text-purple-200 px-4 py-2 rounded-lg w-full text-sm"
                      >
                        <QrCode className="h-4 w-4 mr-2" />
                        Scan QR Code
                      </Button>
                    </div>
                  </div>
                </AnimatedSection>

                {/* FUSE Rewards Info */}
                {profile?.is_card_holder && (
                  <AnimatedSection delay={0.3}>
                    <div className="bg-gradient-to-br from-green-600/20 to-blue-600/20 backdrop-blur-sm rounded-2xl p-4 border border-green-400/30 text-center">
                      <Coins className="h-8 w-8 text-green-400 mx-auto mb-2" />
                      <h3 className="text-lg font-bold text-white mb-3">FUSE Rewards</h3>
                      <div className="space-y-2 text-white/90 text-xs">
                        <div className="bg-white/10 rounded-lg p-2 border border-white/20">
                          <div className="font-semibold text-green-400">100 FUSE per scan</div>
                          <div className="text-white/70 text-xs">Business QR codes (daily)</div>
                        </div>
                      </div>
                    </div>
                  </AnimatedSection>
                )}
              </div>

              {/* Quick Stats */}
              <div className="flex flex-row gap-6 justify-center items-center text-center text-xs">
                <div className="flex items-center text-white/70">
                  <span className="text-green-400 mr-1">✓</span>
                  <span>70+ Partners</span>
                </div>
                <div className="flex items-center text-white/70">
                  <span className="text-green-400 mr-1">✓</span>
                  <span>Instant Rewards</span>
                </div>
                <div className="flex items-center text-white/70">
                  <span className="text-green-400 mr-1">✓</span>
                  <span>50+ Cities</span>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </div>

      {/* Compressed Card & Business Section */}
      <section className="py-6 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          {/* Onboarding Panel - will only show if onboarding is not complete */}
          <OnboardingPanel />

          {/* Profile Summary */}
          <div className="mb-4">
            <DashboardProfileSummary />
          </div>

          {/* Wallet Management Section */}
          <div className="mb-4">
            <WalletProfileSection />
          </div>

          {/* Enhanced Business Card - Direct API Integration */}
          <div className="max-w-6xl mx-auto">
            <EnhancedBusinessDisplay />
          </div>
        </div>
      </section>



        {/* Add the modals at the end */}
        <RedeemCodeModal
          isOpen={isRedeemModalOpen}
          onClose={() => setIsRedeemModalOpen(false)}
        />

        <QRCodeModal
          isOpen={isQRModalOpen}
          onClose={() => setIsQRModalOpen(false)}
          onRedeemClick={() => {
            setIsQRModalOpen(false);
            setIsRedeemModalOpen(true);
          }}
        />

        {/* Debug Panel - Remove this in production */}
        {process.env.NODE_ENV === 'development' && (
          <div className="container mx-auto px-4 py-4">
            <BusinessDebugPanel />
          </div>
        )}
      </DashboardDataProvider>
    </ProtectedRoute>
  );
}

export default function DashboardPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#3A56FF]" />
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    }>
      <DashboardContent />
    </Suspense>
  );
}
