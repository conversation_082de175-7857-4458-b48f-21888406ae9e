"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AnimatedSection } from "@/components/animated-section";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useAuth } from "@/contexts/auth-context";
import { RedeemCodeModal } from "@/components/redeem-code-modal";
import { PhysicalCardRedeemModal } from "@/components/physical-card-redeem-modal";
import { ObsidianGame } from "@/components/games/obsidian-game";

export default function CardDashboard() {
  const router = useRouter();
  const { user, profile, isLoading: profileLoading } = useAuth();
  const [isRedeemModalOpen, setIsRedeemModalOpen] = useState(false);
  const [isPhysicalCardModalOpen, setIsPhysicalCardModalOpen] = useState(false);
  const [isObsidianGameOpen, setIsObsidianGameOpen] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  } | null>(null);


  useEffect(() => {
    if (profile) {
      calculateTimeRemaining();
    }
  }, [profile, profileLoading, user]);

  // Timer effect for live countdown
  useEffect(() => {
    if (isPremiumOrHigher() && profile?.membership_end_date) {
      const timer = setInterval(() => {
        calculateTimeRemaining();
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [profile?.membership_end_date, profile?.card_tier]);

  // Helper function to check if user has premium or higher tier
  const isPremiumOrHigher = () => {
    const tier = profile?.card_tier?.toLowerCase();
    return tier && ['premium', 'gold', 'platinum', 'diamond', 'obsidian'].includes(tier);
  };

  // Helper function to check if user has obsidian tier
  const isObsidianTier = () => {
    const tier = profile?.card_tier?.toLowerCase();
    return tier === 'obsidian';
  };

  // Calculate time remaining until membership expires
  const calculateTimeRemaining = () => {
    if (!profile?.membership_end_date) {
      setTimeRemaining(null);
      return;
    }

    const now = new Date().getTime();
    const endDate = new Date(profile.membership_end_date).getTime();
    const difference = endDate - now;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      setTimeRemaining({ days, hours, minutes, seconds });
    } else {
      setTimeRemaining(null);
    }
  };





  if (profileLoading || !profile) {
    return <div className="text-center py-16 text-white">Loading your profile...</div>;
  }

  return (
    <div className="relative w-full min-h-screen bg-gradient-to-b from-[#000814] via-[#000d1a] to-black overflow-hidden text-white px-4 sm:px-8 py-20">
      {/* Background Image Overlay */}
      <div className="absolute inset-0 z-0 after:content-[''] after:absolute after:inset-0 after:bg-radial-gradient after:from-transparent after:to-black/70">
        <Image
          src="/images/landing-bckgrnd-2.png"
          alt="Fuse.vip Background"
          width={1600}
          height={900}
          className="object-cover w-full h-full opacity-30"
          priority
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-4xl mx-auto space-y-12">

        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-white">
            {profile.is_card_holder
              ? (profile.card_tier ? `${profile.card_tier.charAt(0).toUpperCase() + profile.card_tier.slice(1).toLowerCase()} Card` : "Your VIP Card")
              : "Get Your VIP Card"
            }
          </h1>

          {profile.is_card_holder && isPremiumOrHigher() && timeRemaining && (
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-4 mx-auto max-w-md">
              <p className="text-sm font-medium text-white mb-2">Membership Expires In:</p>
              <div className="grid grid-cols-4 gap-2 text-center">
                <div className="bg-white/20 rounded-md p-2">
                  <div className="text-2xl font-bold text-white">{timeRemaining.days}</div>
                  <div className="text-xs text-white/80">Days</div>
                </div>
                <div className="bg-white/20 rounded-md p-2">
                  <div className="text-2xl font-bold text-white">{timeRemaining.hours}</div>
                  <div className="text-xs text-white/80">Hours</div>
                </div>
                <div className="bg-white/20 rounded-md p-2">
                  <div className="text-2xl font-bold text-white">{timeRemaining.minutes}</div>
                  <div className="text-xs text-white/80">Minutes</div>
                </div>
                <div className="bg-white/20 rounded-md p-2">
                  <div className="text-2xl font-bold text-white">{timeRemaining.seconds}</div>
                  <div className="text-xs text-white/80">Seconds</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Membership Card */}
        <AnimatedSection>
          <div className="relative bg-[#1c1f2a]/80 backdrop-blur-sm border border-white/10 rounded-2xl px-8 py-10 shadow-2xl">
            <div className="flex flex-col md:flex-row items-center justify-center gap-8">
              {/* VIP Card Image */}
              <div className="flex-shrink-0">
                <div className="relative">
                  <Image
                    src="/images/premium-card.png"
                    alt="Premium Fuse Card"
                    width={280}
                    height={180}
                    className={`rounded-md shadow-lg transition-all duration-300 ${
                      !profile.is_card_holder ? "opacity-50 grayscale" : ""
                    }`}
                  />
                  {!profile.is_card_holder && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/60 rounded-md">
                      <div className="text-center">
                        <div className="text-2xl mb-2">🔒</div>
                        <div className="text-white font-semibold text-sm">Get Your VIP Card</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Profile Info */}
              <div className="text-center md:text-left space-y-4">
                <h2 className="text-2xl font-semibold text-white">
                  Welcome, {profile.first_name} {profile.last_name}!
                </h2>

                {/* User Profile Details */}
                <div className="bg-white/5 rounded-lg p-4 space-y-3">
                  <div className="grid grid-cols-1 gap-3 text-sm">
                    <div>
                      <span className="font-medium text-white">Name:</span>{" "}
                      <span className="text-white/80">{profile.first_name} {profile.last_name}</span>
                    </div>
                    <div>
                      <span className="font-medium text-white">Email:</span>{" "}
                      <span className="text-white/80">{profile.user_email}</span>
                    </div>
                    {profile.membership_start_date && (
                      <div>
                        <span className="font-medium text-white">Membership Start:</span>{" "}
                        <span className="text-white/80">
                          {new Date(profile.membership_start_date).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                    {profile.membership_end_date && (
                      <div>
                        <span className="font-medium text-white">Membership End:</span>{" "}
                        <span className="text-white/80">
                          {new Date(profile.membership_end_date).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                    {profile.card_tier && (
                      <div>
                        <span className="font-medium text-white">Card Tier:</span>{" "}
                        <span className="text-blue-400 font-semibold">
                          {profile.card_tier.charAt(0).toUpperCase() + profile.card_tier.slice(1).toLowerCase()}
                        </span>
                      </div>
                    )}
                    {profile.xrp_wallet_address && (
                      <div>
                        <span className="font-medium text-white">XRP Wallet:</span>{" "}
                        <span className="text-green-400 font-mono text-xs break-all">
                          {profile.xrp_wallet_address}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="pt-4 space-y-2">
                  {profile.is_card_holder ? (
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Button
                        onClick={() => setIsRedeemModalOpen(true)}
                        className="bg-blue-600 hover:bg-blue-700 text-white text-sm px-6 py-2 rounded-md shadow-md"
                      >
                        Redeem VIP Code
                      </Button>
                      <Button
                        onClick={() => setIsPhysicalCardModalOpen(true)}
                        variant="outline"
                        className="border-blue-600 text-blue-600 hover:bg-blue-50 text-sm px-6 py-2 rounded-md"
                      >
                        Redeem Physical Card
                      </Button>
                      {isObsidianTier() && (
                        <Button
                          onClick={() => setIsObsidianGameOpen(true)}
                          className="bg-gradient-to-r from-purple-600 to-black hover:from-purple-700 hover:to-gray-900 text-white text-sm px-6 py-2 rounded-md shadow-md border border-purple-400"
                        >
                          ⚫ Play Obsidian Game
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Button
                        onClick={() => router.push("/upgrade")}
                        className="bg-yellow-500 hover:bg-yellow-400 text-black font-semibold text-sm px-6 py-2 rounded-md shadow-md"
                      >
                        🚀 Get VIP Card - $100
                      </Button>
                      <Button
                        onClick={() => setIsRedeemModalOpen(true)}
                        variant="outline"
                        className="border-white/30 text-white hover:bg-white/10 text-sm px-6 py-2 rounded-md"
                      >
                        Redeem Code
                      </Button>
                      <Button
                        onClick={() => setIsPhysicalCardModalOpen(true)}
                        variant="outline"
                        className="border-white/30 text-white hover:bg-white/10 text-sm px-6 py-2 rounded-md"
                      >
                        Redeem Physical Card
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Obsidian Tier Exclusive Section */}
        {profile.is_card_holder && isObsidianTier() && (
          <AnimatedSection delay={0.15}>
            <div className="text-center bg-gradient-to-r from-purple-600/20 to-black/50 border border-purple-400/40 rounded-lg p-6">
              <h2 className="text-2xl font-bold text-white mb-3">
                ⚫ <span className="text-purple-400">Obsidian Tier Exclusive</span> ⚫
              </h2>
              <p className="text-lg text-purple-200 mb-4">
                Access the exclusive Obsidian Gem Collector game!
              </p>
              <p className="text-sm text-purple-300/80 mb-4">
                Collect precious gems while avoiding obstacles. Test your skills and achieve high scores!
              </p>
              <Button
                onClick={() => setIsObsidianGameOpen(true)}
                className="bg-gradient-to-r from-purple-600 to-black hover:from-purple-700 hover:to-gray-900 text-white font-bold text-lg px-8 py-3 rounded-xl shadow-lg border border-purple-400"
              >
                ⚫ Launch Obsidian Game ⚫
              </Button>
            </div>
          </AnimatedSection>
        )}

        {/* Simple CTA for Non-Card Holders */}
        {!profile.is_card_holder && (
          <AnimatedSection delay={0.2}>
            <div className="text-center bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-8">
              <h2 className="text-3xl font-bold text-white mb-4">
                Get Your <span className="text-yellow-400">VIP Card</span> Now!
              </h2>
              <p className="text-lg text-white/80 mb-6">
                Unlock exclusive discounts and rewards at hundreds of businesses
              </p>
              <div className="text-center mb-6">
                <div className="text-white/70 text-sm line-through">$200</div>
                <div className="text-4xl font-bold text-yellow-400">$100</div>
                <div className="text-white/80 text-sm">One-time payment • Valid for 1 year</div>
              </div>
              <Button
                onClick={() => router.push("/upgrade")}
                className="bg-yellow-500 hover:bg-yellow-400 text-black font-bold text-lg px-8 py-4 rounded-xl shadow-lg"
              >
                🚀 Get Your VIP Card Now
              </Button>
            </div>
          </AnimatedSection>
        )}



        {/* Modals */}
        <RedeemCodeModal
          isOpen={isRedeemModalOpen}
          onClose={() => setIsRedeemModalOpen(false)}
        />
        <PhysicalCardRedeemModal
          isOpen={isPhysicalCardModalOpen}
          onClose={() => setIsPhysicalCardModalOpen(false)}
        />
        <ObsidianGame
          isOpen={isObsidianGameOpen}
          onClose={() => setIsObsidianGameOpen(false)}
        />
      </div>
    </div>
  );
}