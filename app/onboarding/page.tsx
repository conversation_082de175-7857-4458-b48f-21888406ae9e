"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { OnboardingProvider, useOnboarding } from "@/contexts/onboarding-context"
import { OnboardingContainer } from "@/components/onboarding/onboarding-container"

function OnboardingContent() {
  const { user } = useAuth()
  const { isOnboardingComplete } = useOnboarding()
  const router = useRouter()
  const searchParams = useSearchParams()
  const isRequired = searchParams.get('required') === 'true'
  const reason = searchParams.get('reason')
  const isMissingProfile = reason === 'missing_profile' || reason === 'profile_creation_failed' || reason === 'profile_exception'
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!user) {
        // If no user, redirect to login
        router.push("/")
        return
      }

      // Check if onboarding was already completed or skipped
      const onboardingComplete = localStorage.getItem(`onboarding_complete_${user.id}`) === "true"
      const onboardingSkipped = localStorage.getItem(`onboarding_skipped_${user.id}`) === "true"
      
      // If missing profile or onboarding is required (for VIP card purchase), don't allow skipping
      if (isRequired || isMissingProfile) {
        console.log(isMissingProfile ? "Profile creation required due to missing profile" : "Profile completion required for VIP card purchase")
        if (onboardingComplete && !isMissingProfile) {
          console.log("Profile already complete, redirecting to dashboard")
          router.push("/dashboard")
          return
        }
        // Force onboarding even if previously skipped, especially for missing profiles
        if (isMissingProfile) {
          // Clear any cached onboarding status if profile is missing
          localStorage.removeItem(`onboarding_complete_${user.id}`)
          localStorage.removeItem(`onboarding_skipped_${user.id}`)
          console.log("Missing profile detected - forcing onboarding")
        }
        setIsLoading(false)
        return
      }
      
      if (onboardingComplete || onboardingSkipped) {
        console.log("User has already completed or skipped onboarding, redirecting to dashboard")
        router.push("/dashboard")
        return
      }

      // Allow onboarding to proceed
      setIsLoading(false)
    }

    // Give a small delay to allow auth context to settle
    const timeoutId = setTimeout(checkOnboardingStatus, 1000)
    
    return () => clearTimeout(timeoutId)
  }, [user, router])

  // Also redirect if onboarding gets completed
  useEffect(() => {
    if (isOnboardingComplete) {
      router.push("/dashboard")
    }
  }, [isOnboardingComplete, router])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#000814] via-[#001122] to-black flex items-center justify-center">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg">Preparing your onboarding experience...</p>
          <p className="text-sm text-gray-400 mt-2">This gives our servers time to set up your account</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#000814] via-[#001122] to-black">
      <OnboardingContainer />
    </div>
  )
}

function OnboardingLoadingFallback() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#000814] via-[#001122] to-black flex items-center justify-center">
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-lg">Loading onboarding...</p>
      </div>
    </div>
  )
}

export default function OnboardingPage() {
  return (
    <OnboardingProvider>
      <Suspense fallback={<OnboardingLoadingFallback />}>
        <OnboardingContent />
      </Suspense>
    </OnboardingProvider>
  )
}
