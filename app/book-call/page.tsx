import { PageHeader } from "@/components/page-header"
import { GoogleCalendarButton } from "@/components/google-calendar-button"
import Image from "next/image"
import { Linkedin } from "lucide-react"

export default function BookCallPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <PageHeader
        title="Book a Call"
        description="Schedule a time to speak with our team about how Fuse.Vip can help your business grow."
      />

      {/* Team Introduction Section */}
      <div className="max-w-4xl mx-auto mt-12 mb-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Meet Our Leadership</h2>
          <p className="text-[#4a4a4a] max-w-2xl mx-auto">
            Our diverse team of experts brings together a wealth of experience in blockchain, loyalty programs, and
            customer engagement.
          </p>
        </div>

        {/* Raul <PERSON> */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden max-w-2xl mx-auto">
          <div className="md:flex">
            <div className="md:w-1/2">
              <div className="h-80 md:h-full overflow-hidden bg-gray-50">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/headshot-r2koYBigCOYP9UVZeK8ifY8FxKm7BH.jpeg"
                  alt="Raul Salazar"
                  width={400}
                  height={400}
                  className="w-full h-full object-cover object-top"
                />
              </div>
            </div>
            <div className="md:w-1/2 p-8">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="font-bold text-2xl mb-2">Raul Salazar</h3>
                  <p className="text-[#3A56FF] font-semibold text-lg">Founder</p>
                </div>
                <a
                  href="#"
                  className="text-[#3A56FF] hover:text-[#3A56FF]/80 transition-colors"
                  aria-label="Raul Salazar's LinkedIn profile"
                >
                  <Linkedin className="h-6 w-6" />
                </a>
              </div>
              <p className="text-[#4a4a4a] text-sm leading-relaxed">
                Raul Salazar, Founder and CEO of Fuse.vip. With a background in Communication and Media Studies
                from Pepperdine University, Fresno St., and Cal St. San Bernardino, Raul combines his expertise in
                digital marketing, blockchain technology, and customer engagement to revolutionize loyalty
                programs. His passion for innovation and commitment to creating value for businesses drives the company's mission forward. Recently, Raul signed his first book deal to explain the mind that made this revolutionary idea.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-3xl mx-auto mt-12 bg-white p-8 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-6 text-center">Select a Time That Works for You</h2>

        <p className="text-gray-600 mb-8 text-center">
          Our team is ready to answer your questions and help you get started with Fuse.Vip. Choose a convenient time
          from our calendar below.
        </p>

        <div className="flex justify-center mb-8">
          <GoogleCalendarButton label="Book an appointment" color="#3A56FF" className="inline-block" />
        </div>

        <div className="text-sm text-gray-500 text-center">
          <p>
            Can't find a suitable time? Email us at{" "}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
