'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle, Loader2, CreditCard, Wallet, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';

function PaymentSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing payment...');
  const [paymentDetails, setPaymentDetails] = useState<{
    amount?: string;
    destination?: string;
    txid?: string;
    type?: 'xrp' | 'fuse';
    currency?: string;
  }>({});

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get URL parameters
        const txid = searchParams.get('txid');
        const result = searchParams.get('result');
        const amount = searchParams.get('amount');
        const destination = searchParams.get('destination');
        const type = searchParams.get('type') as 'xrp' | 'fuse' || 'xrp';
        const currency = type === 'fuse' ? 'FUSE' : 'XRP';

        setPaymentDetails({ amount, destination, txid, type, currency });

        if (result === 'tesSUCCESS' && txid) {
          setStatus('success');
          setMessage(`Payment of ${amount} ${currency} completed successfully!`);
          
          // Redirect to dashboard after a short delay
          setTimeout(() => {
            if (type === 'fuse') {
              router.push('/dashboard/rewards');
            } else {
              router.push('/dashboard/cards');
            }
          }, 3000);
        } else if (result === 'rejected') {
          setStatus('error');
          setMessage('Payment was rejected. Please try again.');
        } else if (result === 'cancelled') {
          setStatus('error');
          setMessage('Payment was cancelled. Please try again.');
        } else {
          setStatus('error');
          setMessage('Payment failed. Please try again.');
        }
      } catch (error) {
        console.error('Payment callback processing error:', error);
        setStatus('error');
        setMessage('An error occurred while processing the payment.');
      }
    };

    handleCallback();
  }, [searchParams, router]);

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-12 w-12 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-12 w-12 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-12 w-12 text-red-500" />;
    }
  };

  const getButtonText = () => {
    switch (status) {
      case 'success':
        return 'Go to Dashboard';
      case 'error':
        return 'Try Again';
      default:
        return 'Please wait...';
    }
  };

  const handleButtonClick = () => {
    if (status === 'success') {
      if (paymentDetails.type === 'fuse') {
        router.push('/dashboard/rewards');
      } else {
        router.push('/dashboard/cards');
      }
    } else if (status === 'error') {
      router.push('/dashboard');
    }
  };

  const handleViewTransaction = () => {
    if (paymentDetails.txid) {
      window.open(`https://livenet.xrpl.org/transactions/${paymentDetails.txid}`, '_blank');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm rounded-2xl border border-gray-700 text-white">
          <CardHeader className="text-center">
            <motion.div
              className="flex justify-center mb-4"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
            >
              {getIcon()}
            </motion.div>
            
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <CardTitle className="text-2xl text-white">
                {status === 'loading' && 'Processing Payment...'}
                {status === 'success' && 'Payment Successful!'}
                {status === 'error' && 'Payment Failed'}
              </CardTitle>
              <CardDescription className="text-lg text-gray-300 mt-2">
                {message}
              </CardDescription>
            </motion.div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {paymentDetails.amount && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
              >
                <div className="flex items-center gap-2 mb-3">
                  <CreditCard className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium text-white">Payment Details</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Amount:</span>
                    <span className="font-mono font-medium text-white">
                      {paymentDetails.amount} {paymentDetails.currency}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Type:</span>
                    <span className="font-medium text-white capitalize">
                      {paymentDetails.type === 'xrp' ? 'XRP Payment' : 'FUSE Token Payment'}
                    </span>
                  </div>
                  {paymentDetails.destination && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">To:</span>
                      <span className="font-mono text-xs text-gray-300">
                        {paymentDetails.destination.slice(0, 8)}...{paymentDetails.destination.slice(-8)}
                      </span>
                    </div>
                  )}
                  {paymentDetails.txid && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Transaction:</span>
                      <button
                        onClick={handleViewTransaction}
                        className="font-mono text-xs text-blue-400 hover:text-blue-300 underline"
                      >
                        {paymentDetails.txid.slice(0, 8)}...{paymentDetails.txid.slice(-8)}
                      </button>
                    </div>
                  )}
                </div>
              </motion.div>
            )}

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="flex items-center justify-center gap-2 text-sm text-gray-400"
            >
              <Wallet className="h-4 w-4" />
              <span>$Fuse Rewards Payment System</span>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="flex gap-3"
            >
              {status === 'success' && paymentDetails.txid && (
                <Button 
                  onClick={handleViewTransaction}
                  variant="outline"
                  className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  View Transaction
                </Button>
              )}
              
              <Button 
                onClick={handleButtonClick}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                variant={status === 'error' ? 'outline' : 'default'}
              >
                {status === 'success' ? (
                  <>
                    Continue
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                ) : status === 'error' ? (
                  'Try Again'
                ) : (
                  'Please wait...'
                )}
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

function PaymentSuccessLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-black flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm rounded-2xl border border-gray-700 text-white">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
          </div>
          <CardTitle className="text-2xl text-white">Processing Payment...</CardTitle>
          <CardDescription className="text-lg text-gray-300">
            Please wait while we process your payment...
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
            <Wallet className="h-4 w-4" />
            <span>$Fuse Rewards Payment System</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<PaymentSuccessLoading />}>
      <PaymentSuccessContent />
    </Suspense>
  );
}
