"use client"

import { <PERSON>Header } from "@/components/page-header"
import { CtaSection } from "@/components/cta-section"
import { useState } from "react"
import { ChevronDown, Building2, Users, Play, HelpCircle, ExternalLink, Layers } from "lucide-react"
import { AnimatedSection } from "@/components/animated-section"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"
import Link from "next/link"
import { CardStackTrigger } from "@/components/ui/card-stack-modal"
import { DotLottieReact } from "@lottiefiles/dotlottie-react"

export default function ResourcesPage() {
  const [showFAQs, setShowFAQs] = useState(false)

  return (
    <>
      <PageHeader
        title="Resources"
        subtitle="LEARN ABOUT FUSE"
        description="Discover how Fu<PERSON> transforms business relationships and customer loyalty through our comprehensive resources."
      />

      {/* Video Resources Section */}
      <section className="py-20 bg-gradient-to-br from-[#000814] via-[#001122] to-black relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-1/2 h-1/2 bg-[#3A56FF] opacity-20 blur-[100px] rounded-full" />
          <div className="absolute bottom-0 right-1/4 w-1/2 h-1/2 bg-[#FF914D] opacity-20 blur-[100px] rounded-full" />
        </div>

        <div className="container mx-auto px-4 relative z-20">
          <AnimatedSection>
            <div className="text-center mb-16">
              <Badge className="bg-gradient-to-r from-[#3A56FF] to-[#6366f1] text-white mb-6 text-lg px-6 py-3">
                Our Resources
              </Badge>
              <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-[#FF914D] to-white bg-clip-text text-transparent">
                Understanding Fuse
              </h2>
              <p className="text-xl text-white/80 max-w-3xl mx-auto">
                Want to understand how Fuse creates value for businesses and customers? Check this out.
              </p>
            </div>
          </AnimatedSection>

          {/* Video Grid */}
          <AnimatedSection delay={0.2}>
            <div className="relative">
              {/* Floating Lottie Animation */}
              <motion.div
                className="absolute -top-8 left-4 w-20 h-20 z-10 pointer-events-none"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1, duration: 0.8, type: "spring" }}
              >
                <DotLottieReact
                  src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
                  loop
                  autoplay
                  className="w-full h-full opacity-80"
                />
              </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto mb-16">
              {/* Why Businesses Fuse Interactive Cards */}
              <CardStackTrigger className="cursor-pointer">
                <motion.div
                  className="group bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl overflow-hidden"
                  whileHover={{ y: -10, scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="aspect-video bg-gradient-to-br from-[#3A56FF]/20 to-[#6366f1]/20 relative flex items-center justify-center">
                    <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-all duration-300" />
                    <Layers className="h-16 w-16 text-white group-hover:scale-110 transition-transform duration-300" />
                    <Badge className="absolute top-4 right-4 bg-[#FF914D] text-white">Interactive</Badge>
                  </div>
                  <div className="p-8">
                    <div className="w-16 h-16 bg-gradient-to-r from-[#3A56FF] to-[#6366f1] rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                      <Building2 className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">Why Businesses Fuse</h3>
                    <p className="text-white/80 mb-6">
                      Discover how local businesses leverage Fuse to increase customer retention, build loyalty, and grow their revenue through our innovative reward system.
                    </p>
                    <ul className="space-y-2 text-white/70 text-sm">
                      <li>• Increase customer retention by 40%</li>
                      <li>• Build lasting customer relationships</li>
                      <li>• Earn cryptocurrency rewards</li>
                      <li>• Simple setup and management</li>
                    </ul>
                    <div className="mt-6 flex items-center justify-center">
                      <Button
                        size="sm"
                        className="bg-gradient-to-r from-[#FF914D] to-[#FF6B35] hover:from-[#FF6B35] hover:to-[#FF914D] text-black font-bold"
                      >
                        <Layers className="mr-2 h-4 w-4" />
                        Explore Interactive Story
                      </Button>
                    </div>
                  </div>
                </motion.div>
              </CardStackTrigger>

              {/* Why Customers Fuse Video */}
              <motion.div
                className="group bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl overflow-hidden"
                whileHover={{ y: -10, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="aspect-video bg-gradient-to-br from-[#FF914D]/20 to-[#FF6B35]/20 relative flex items-center justify-center">
                  <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-all duration-300" />
                  <Play className="h-16 w-16 text-white group-hover:scale-110 transition-transform duration-300" />
                  <Badge className="absolute top-4 right-4 bg-[#FF914D] text-white">Coming Soon</Badge>
                </div>
                <div className="p-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-[#FF914D] to-[#FF6B35] rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Why Customers Fuse</h3>
                  <p className="text-white/80 mb-6">
                    Learn how customers benefit from exclusive discounts, rewards, and a seamless loyalty experience when they join the Fuse network.
                  </p>
                  <ul className="space-y-2 text-white/70 text-sm">
                    <li>• Exclusive discounts at partner businesses</li>
                    <li>• Earn rewards with every purchase</li>
                    <li>• Support local businesses you love</li>
                    <li>• Simple VIP card system</li>
                  </ul>
                </div>
              </motion.div>
            </div>
            </div>
          </AnimatedSection>

          {/* Call to Action */}
          <AnimatedSection delay={0.4}>
            <div className="text-center">
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Link href="/register">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-[#FF914D] to-[#FF6B35] hover:from-[#FF6B35] hover:to-[#FF914D] text-black font-bold text-lg px-10 py-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 w-full sm:w-auto"
                  >
                    <Building2 className="mr-2 h-6 w-6" />
                    Register Your Business
                  </Button>
                </Link>

                <Link href="/industry">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-[#3A56FF] to-[#6366f1] hover:from-[#6366f1] hover:to-[#3A56FF] text-white font-bold text-lg px-10 py-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-300 w-full sm:w-auto"
                  >
                    <Users className="mr-2 h-6 w-6" />
                    Get VIP Card
                  </Button>
                </Link>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <AnimatedSection>
              <div className="text-center mb-12">
                <Badge className="bg-gradient-to-r from-[#3A56FF] to-[#6366f1] text-white mb-6 text-lg px-6 py-3">
                  <HelpCircle className="mr-2 h-5 w-5" />
                  Frequently Asked Questions
                </Badge>
                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
                  Common Questions About Fuse
                </h2>
                <p className="text-gray-600 text-lg">
                  Get answers to the most frequently asked questions about our platform.
                </p>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={0.2}>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => setShowFAQs(!showFAQs)}
                  className="w-full flex justify-between items-center p-6 bg-gray-50 hover:bg-gray-100 transition-colors"
                  aria-expanded={showFAQs}
                >
                  <span className="font-medium text-lg text-gray-900">View Frequently Asked Questions</span>
                  <ChevronDown
                    className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${
                      showFAQs ? "transform rotate-180" : ""
                    }`}
                  />
                </button>

                {showFAQs && (
                  <div className="p-6 bg-white border-t border-gray-200">
                    <div className="space-y-6">
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2">How does Fuse work for businesses?</h3>
                        <p className="text-gray-600">
                          Businesses register with Fuse, offer discounts to VIP cardholders, and earn cryptocurrency rewards based on customer engagement and loyalty program participation.
                        </p>
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2">What are VIP cards?</h3>
                        <p className="text-gray-600">
                          VIP cards are digital loyalty cards that customers can purchase to access exclusive discounts and rewards at participating Fuse partner businesses.
                        </p>
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2">How do customers benefit?</h3>
                        <p className="text-gray-600">
                          Customers with VIP cards receive exclusive discounts at partner businesses, earn rewards with purchases, and support local businesses they love.
                        </p>
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2">Is there a cost to join as a business?</h3>
                        <p className="text-gray-600">
                          Business registration is free. Businesses earn money through increased customer retention and cryptocurrency rewards from the Fuse network.
                        </p>
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2">What cryptocurrencies does Fuse support?</h3>
                        <p className="text-gray-600">
                          Fuse supports multiple cryptocurrencies including XRP, Bitcoin, Ethereum, USDC, and our native FUSE tokens, giving businesses flexibility in how they receive rewards.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </AnimatedSection>

            {/* External Links */}
            <AnimatedSection delay={0.3}>
              <div className="mt-8 space-y-4">
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <a
                    href="https://warm-libra-173.notion.site/Fuse-Vip-1de5aebd6632807fab05d1b0378b7cc5"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full flex justify-between items-center p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
                  >
                    <span className="font-medium text-lg text-gray-900">Privacy Policy & Terms of Service</span>
                    <ExternalLink className="h-5 w-5 text-gray-500" />
                  </a>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      <CtaSection />
    </>
  )
}