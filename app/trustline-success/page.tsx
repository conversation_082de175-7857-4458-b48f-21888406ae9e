'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { CheckCircle, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TrustlineSuccessPage() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          router.push('/wallet');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-800 border-gray-700">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold text-white">
            Trustline Set Successfully!
          </CardTitle>
          <CardDescription className="text-gray-300">
            Your FUSE token trustline has been established
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-center space-y-2">
            <p className="text-gray-300">
              You can now receive and hold FUSE tokens in your wallet.
            </p>
            <p className="text-sm text-gray-400">
              Currency: <span className="font-mono text-orange-400">FUSE</span>
            </p>
            <p className="text-sm text-gray-400">
              Issuer: <span className="font-mono text-orange-400">rs2G9J95qwL3yw241JTRdgms2hhcLouVHo</span>
            </p>
          </div>

          <div className="space-y-3">
            <Button
              onClick={() => router.push('/wallet')}
              className="w-full bg-orange-600 hover:bg-orange-700"
            >
              Return to Wallet
            </Button>
            
            <Button
              onClick={() => window.open('https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet&currency=FUSE&issuer=rs2G9J95qwL3yw241JTRdgms2hhcLouVHo', '_blank')}
              variant="outline"
              className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Trade FUSE on xMagnetic DEX
              <ExternalLink className="ml-2 h-4 w-4" />
            </Button>
          </div>

          <div className="text-center text-sm text-gray-400">
            Redirecting to wallet in {countdown} seconds...
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
