import Link from 'next/link'
import { Suspense } from 'react'

function ErrorContent({ searchParams }: { searchParams: { message?: string } }) {
  const errorMessage = searchParams.message ||
    "Sorry, there was an error with your authentication. This could be due to an expired link or invalid credentials."

  const isServiceUnavailable = errorMessage.includes('temporarily unavailable') ||
    errorMessage.includes('503') ||
    errorMessage.includes('Service Unavailable')

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {isServiceUnavailable ? 'Service Temporarily Unavailable' : 'Authentication Error'}
          </h1>
          <p className="text-gray-600 mb-6">
            {errorMessage}
          </p>

          {isServiceUnavailable && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
              <p className="text-sm text-yellow-800">
                Our authentication service is experiencing temporary issues. Please try again in a few minutes.
              </p>
            </div>
          )}
          <div className="space-y-3">
            <Link
              href="/login"
              className="block w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Try Logging In Again
            </Link>
            <Link
              href="/register"
              className="block w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
            >
              Create New Account
            </Link>
            <Link
              href="/"
              className="block w-full text-blue-600 hover:text-blue-800 transition-colors"
            >
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ErrorPage({ searchParams }: { searchParams: { message?: string } }) {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">Loading...</div>
      </div>
    }>
      <ErrorContent searchParams={searchParams} />
    </Suspense>
  )
}
