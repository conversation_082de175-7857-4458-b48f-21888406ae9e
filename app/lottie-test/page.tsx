"use client"

import { DotLottieWCAnimation } from "@/components/dotlottie-wc-animation"
import { LottieIframe, YourLottieAnimation } from "@/components/lottie-iframe"

export default function LottieTestPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Lottie Animation Test
          </h1>
          <p className="text-lg text-gray-600">
            Testing the new dotlottie-wc implementation with various animations
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Your specific animation - Iframe (Most Reliable) */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Your Animation (Iframe)</h3>
            <div className="flex justify-center">
              <YourLottieAnimation />
            </div>
            <p className="text-sm text-gray-600 mt-2 text-center">✅ Most reliable approach</p>
          </div>

          {/* Your specific animation - Web Component */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Your Animation (Web Component)</h3>
            <div className="flex justify-center">
              <DotLottieWCAnimation
                src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
                width="300px"
                height="300px"
                speed="1"
                autoplay={true}
                loop={true}
              />
            </div>
            <p className="text-sm text-gray-600 mt-2 text-center">⚡ Falls back to iframe if needed</p>
          </div>

          {/* Mission animation */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Mission Animation</h3>
            <div className="flex justify-center">
              <DotLottieWCAnimation
                src="https://lottie.host/9b3b5a23-79cb-449b-a523-7cab48ff6aa2/SnOFt96zlp.lottie"
                width="300px"
                height="300px"
                speed="1"
                autoplay={true}
                loop={true}
              />
            </div>
          </div>

          {/* Industry animation */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Industry Animation</h3>
            <div className="flex justify-center">
              <DotLottieWCAnimation
                src="https://lottie.host/bff023a8-ddc5-4788-adf5-e8ef59e1e088/e749q9gbhS.lottie"
                width="300px"
                height="300px"
                speed="1"
                autoplay={true}
                loop={true}
              />
            </div>
          </div>

          {/* Bubble animation */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Bubble Animation</h3>
            <div className="flex justify-center">
              <DotLottieWCAnimation
                src="https://lottie.host/4d1f0743-dbdc-44b6-a8ce-2ccd0741c428/upqyw8Z5Y3.lottie"
                width="300px"
                height="300px"
                speed="1"
                autoplay={true}
                loop={true}
              />
            </div>
          </div>

          {/* Small version test */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Small Size Test</h3>
            <div className="flex justify-center">
              <DotLottieWCAnimation
                src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
                width="100px"
                height="100px"
                speed="2"
                autoplay={true}
                loop={true}
              />
            </div>
          </div>

          {/* Custom styling test */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Custom Style Test</h3>
            <div className="flex justify-center">
              <DotLottieWCAnimation
                src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
                width="200px"
                height="200px"
                speed="0.5"
                autoplay={true}
                loop={true}
                style={{
                  border: "2px solid #3A56FF",
                  borderRadius: "12px",
                  padding: "10px"
                }}
              />
            </div>
          </div>
        </div>

        <div className="mt-12 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold mb-4">Implementation Details</h2>
          <div className="space-y-4 text-gray-700">
            <p>
              <strong>Script Used:</strong> https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js
            </p>
            <p>
              <strong>Web Component:</strong> &lt;dotlottie-wc&gt;
            </p>
            <p>
              <strong>CSP Updated:</strong> Added support for unpkg.com and lottie.host domains
            </p>
            <p>
              <strong>Features:</strong> Autoplay, loop, custom speed, responsive sizing, error handling
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
