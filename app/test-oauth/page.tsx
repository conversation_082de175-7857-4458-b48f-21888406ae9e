'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

export default function TestOAuthPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testTokenExchange = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      // Test with dummy data to verify API route works
      const response = await fetch('/api/xaman/oauth/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: 'test-authorization-code',
          state: 'test-state',
          redirect_uri: window.location.origin + '/dashboard'
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'API request failed');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  const initiateOAuth = () => {
    // Redirect to Xaman OAuth2 authorization
    const clientId = process.env.NEXT_PUBLIC_XAMAN_API_KEY;
    const redirectUri = window.location.origin + '/dashboard';
    const state = 'test-state-' + Date.now();
    
    const authUrl = `https://oauth2.xumm.app/auth/authorize?` +
      `response_type=code&` +
      `client_id=${clientId}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `state=${state}&` +
      `scope=openid`;

    window.location.href = authUrl;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4 max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>Xaman OAuth2 Flow Test</CardTitle>
            <CardDescription>
              Test the OAuth2 integration with Xaman wallet
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            
            {/* Environment Check */}
            <div className="space-y-2">
              <h3 className="font-semibold">Environment Check</h3>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  {process.env.NEXT_PUBLIC_XAMAN_API_KEY ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  )}
                  <span>
                    NEXT_PUBLIC_XAMAN_API_KEY: {
                      process.env.NEXT_PUBLIC_XAMAN_API_KEY ? 
                      `${process.env.NEXT_PUBLIC_XAMAN_API_KEY.substring(0, 8)}...` : 
                      'Missing'
                    }
                  </span>
                </div>
                <div className="text-gray-600">
                  Redirect URI: {typeof window !== 'undefined' ? window.location.origin + '/dashboard' : 'N/A'}
                </div>
              </div>
            </div>

            {/* Test Buttons */}
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">1. Test API Route</h3>
                <Button 
                  onClick={testTokenExchange}
                  disabled={isLoading}
                  variant="outline"
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Testing...
                    </>
                  ) : (
                    'Test Token Exchange API'
                  )}
                </Button>
              </div>

              <div>
                <h3 className="font-semibold mb-2">2. Test Full OAuth Flow</h3>
                <Button 
                  onClick={initiateOAuth}
                  disabled={!process.env.NEXT_PUBLIC_XAMAN_API_KEY}
                  className="w-full"
                >
                  Start OAuth2 Flow
                </Button>
                <p className="text-xs text-gray-600 mt-1">
                  This will redirect you to Xaman for authorization, then back to /dashboard
                </p>
              </div>
            </div>

            {/* Results */}
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="font-semibold text-red-700">Error</span>
                </div>
                <pre className="text-sm text-red-600 whitespace-pre-wrap">{error}</pre>
              </div>
            )}

            {result && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="font-semibold text-green-700">Success</span>
                </div>
                <pre className="text-sm text-green-600 whitespace-pre-wrap">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}

            {/* Instructions */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-700 mb-2">Testing Instructions</h4>
              <ol className="text-sm text-blue-600 space-y-1 list-decimal list-inside">
                <li>First test the API route to ensure server-side token exchange works</li>
                <li>Then test the full OAuth flow by clicking "Start OAuth2 Flow"</li>
                <li>You'll be redirected to Xaman to sign a transaction</li>
                <li>After signing, you'll return to /dashboard with OAuth parameters</li>
                <li>The dashboard will automatically process the callback</li>
              </ol>
            </div>

          </CardContent>
        </Card>
      </div>
    </div>
  );
}
