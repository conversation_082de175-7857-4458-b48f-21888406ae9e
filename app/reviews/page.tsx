"use client"

import { <PERSON>Header } from "@/components/page-header"
import { CtaSection } from "@/components/cta-section"
import { Star } from "lucide-react"
import { motion } from "framer-motion"
import { DotLottieReact } from "@lottiefiles/dotlottie-react"

export default function ReviewsPage() {
  const testimonials = [
    {
      name: "<PERSON>",
      company: "The PrincipalED Leader, LLC",
      rating: 5,
      text: "Fuse.vip has transformed how we connect with our community. The platform is intuitive and the results speak for themselves."
    },
    {
      name: "<PERSON>",
      company: "<PERSON>",
      rating: 5,
      text: "Outstanding service and support. Our patient engagement has increased significantly since joining Fuse.vip."
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      company: "<PERSON><PERSON>h Patel DDS Inc.",
      rating: 4,
      text: "Great platform for building customer loyalty. The VIP rewards system has been a game-changer for our practice."
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      company: "@livehoneybelle",
      rating: 5,
      text: "Love the seamless integration and the way it helps us reward our most loyal customers. Highly recommended!"
    },
    {
      name: "<PERSON><PERSON>",
      company: "Protein Plus",
      rating: 5,
      text: "The customer support is exceptional and the platform delivers exactly what was promised. Five stars!"
    },
    {
      name: "<PERSON>",
      company: "The Art of Pitching Corp",
      rating: 5,
      text: "Fuse.vip has helped us build stronger relationships with our clients. The ROI has been fantastic."
    },
  ]

  return (
    <>
      <PageHeader
        title="Client Reviews"
        subtitle="SUCCESS STORIES"
        description="Don't just take our word for it. See what our clients have to say about working with Fuse.vip."
      />

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Testimonials</h2>
            <p className="text-[#4a4a4a] max-w-2xl mx-auto">
              We're proud of the relationships we've built with our clients and the results we've helped them achieve.
            </p>
          </div>

          <div className="relative">
            {/* Floating Lottie Animation */}
            <motion.div
              className="absolute -top-6 right-4 w-16 h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 z-10 pointer-events-none"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8, duration: 0.8, type: "spring" }}
            >
              <DotLottieReact
                src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
                loop
                autoplay
                className="w-full h-full opacity-80"
              />
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
                <div className="flex items-center mb-4">
                  <div>
                    <h3 className="font-bold">{testimonial.name}</h3>
                    <p className="text-sm text-[#4a4a4a]">{testimonial.company}</p>
                  </div>
                </div>
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < testimonial.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <p className="text-[#4a4a4a]">{testimonial.text}</p>
              </div>
            ))}
            </div>
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4 text-center">
          <div className="relative">
            <h2 className="text-3xl font-bold mb-6">Our Client Satisfaction</h2>

            {/* Second Lottie Animation */}
            <motion.div
              className="absolute -top-4 left-4 w-12 h-12 md:w-16 md:h-16 lg:w-20 lg:h-20 z-10 pointer-events-none"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 1.2, duration: 0.8, type: "spring" }}
            >
              <DotLottieReact
                src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
                loop
                autoplay
                className="w-full h-full opacity-70"
              />
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-4xl font-bold text-[#316bff] mb-2">98%</div>
              <p className="text-[#4a4a4a]">Client Satisfaction Rate</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-4xl font-bold text-[#316bff] mb-2">250+</div>
              <p className="text-[#4a4a4a]">VIP's Fusing</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-4xl font-bold text-[#316bff] mb-2">100%</div>
              <p className="text-[#4a4a4a]">Repeat Business Rate</p>
            </div>
          </div>
        </div>
      </section>

      <CtaSection />
    </>
  )
}
