'use client';

import { useEffect, useState } from 'react';
import { PageHeader } from '@/components/page-header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ExternalLink, Wallet, TrendingUp } from 'lucide-react';
import { AnimatedSection } from '@/components/animated-section';
import { XAMAN_CONFIG } from '@/lib/xaman-config';

declare global {
  interface Window {
    Xumm: any;
    xumm: any;
  }
}

export default function XamanDemoPage() {
  const [accountAddress, setAccountAddress] = useState('...');
  const [isReady, setIsReady] = useState(false);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const loadXummSDK = () => {
      if (typeof window === 'undefined' || window.xumm) return;

      const script = document.createElement('script');
      script.src = 'https://xumm.app/assets/cdn/xumm.min.js';
      script.async = true;

      script.onload = () => {
        if (!XAMAN_CONFIG.API_KEY) {
          console.error('Missing Xaman API key');
          return;
        }

        window.xumm = new window.Xumm(XAMAN_CONFIG.API_KEY);

        window.xumm.on('ready', () => {
          console.log('Ready (e.g. hide loading state of page)');
          setIsReady(true);
        });

        // We rely on promises in the `success` event: fired again if a user
        // logs out and logs back in again (resets all promises)
        window.xumm.on('success', async () => {
          try {
            const account = await window.xumm.user.account;
            setAccountAddress(account);
            setIsConnected(true);
          } catch (error) {
            console.error('Error getting account:', error);
          }
        });

        window.xumm.on('logout', async () => {
          setAccountAddress('...');
          setIsConnected(false);
        });
      };

      document.body.appendChild(script);
    };

    loadXummSDK();
  }, []);

  const handleLogin = () => {
    if (window.xumm) {
      window.xumm.authorize();
    }
  };

  const handleLogout = () => {
    if (window.xumm) {
      window.xumm.logout();
    }
  };

  const openFuseDEX = () => {
    window.open(
      'https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet&currency=FUSE&issuer=rs2G9J95qwL3yw241JTRdgms2hhcLouVHo',
      '_blank'
    );
  };

  return (
    <>
      <PageHeader
        title="Xaman Wallet Demo"
        subtitle="XUMM SDK INTEGRATION"
        description="Demonstration of Xaman wallet connection and FUSE token trustline functionality."
      />

      <section className="py-12 bg-[#121212]">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Wallet Connection Demo */}
            <AnimatedSection>
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Wallet className="h-5 w-5" />
                    Wallet Connection
                  </CardTitle>
                  <CardDescription className="text-gray-300">
                    Connect your Xaman wallet using the XUMM SDK
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 bg-gray-900 rounded-md">
                    <p className="text-sm text-gray-400 mb-2">Account Address:</p>
                    <p className="font-mono text-sm text-white break-all">
                      {accountAddress}
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      onClick={handleLogin}
                      disabled={!isReady || isConnected}
                      className="flex-1 bg-orange-600 hover:bg-orange-700"
                    >
                      {!isReady ? 'Loading...' : isConnected ? 'Connected' : 'Login'}
                    </Button>
                    <Button
                      onClick={handleLogout}
                      disabled={!isReady || !isConnected}
                      variant="outline"
                      className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      Logout
                    </Button>
                  </div>

                  <div className="text-xs text-gray-500">
                    Status: {!isReady ? 'Loading SDK...' : isConnected ? 'Connected' : 'Disconnected'}
                  </div>
                </CardContent>
              </Card>
            </AnimatedSection>

            {/* FUSE Token Info */}
            <AnimatedSection>
              <Card className="bg-gray-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    FUSE Token
                  </CardTitle>
                  <CardDescription className="text-gray-300">
                    Set trustline and trade FUSE tokens
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="p-3 bg-gray-900 rounded-md">
                      <p className="text-xs text-gray-400">Currency</p>
                      <p className="font-mono text-orange-400">FUSE</p>
                    </div>
                    <div className="p-3 bg-gray-900 rounded-md">
                      <p className="text-xs text-gray-400">Issuer</p>
                      <p className="font-mono text-xs text-orange-400 break-all">
                        rs2G9J95qwL3yw241JTRdgms2hhcLouVHo
                      </p>
                    </div>
                  </div>

                  <Button
                    onClick={openFuseDEX}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                  >
                    Trade on xMagnetic DEX
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </Button>

                  <div className="text-xs text-gray-500 text-center">
                    Connect wallet first, then use the main wallet page to set trustline
                  </div>
                </CardContent>
              </Card>
            </AnimatedSection>
          </div>

          {/* Instructions */}
          <AnimatedSection>
            <Card className="mt-6 bg-gray-800 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">How to Use</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-gray-300">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-orange-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    1
                  </div>
                  <div>
                    <p className="font-semibold">Connect Your Wallet</p>
                    <p className="text-sm text-gray-400">
                      Click "Login" to connect your Xaman wallet using the XUMM SDK
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-orange-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    2
                  </div>
                  <div>
                    <p className="font-semibold">Set FUSE Trustline</p>
                    <p className="text-sm text-gray-400">
                      Go to the main wallet page and click "Set FUSE Trustline" to enable FUSE token transactions
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-orange-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    3
                  </div>
                  <div>
                    <p className="font-semibold">Trade FUSE Tokens</p>
                    <p className="text-sm text-gray-400">
                      Use the xMagnetic DEX to trade FUSE tokens with other cryptocurrencies
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AnimatedSection>
        </div>
      </section>
    </>
  );
}
