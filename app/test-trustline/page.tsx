"use client"

import { TrustlineSetup } from '@/components/fuse/trustline-setup'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useXamanUserInfoCache } from '@/lib/xaman-userinfo-cache'
import { useAuth } from '@/contexts/auth-context'
import { useState } from 'react'
import { toast } from 'sonner'

/**
 * Test page for FUSE Trustline Setup with OAuth2 Userinfo Caching
 * 
 * This page demonstrates the enhanced trustline setup flow that:
 * 1. Caches user account info using https://oauth2.xumm.app/userinfo
 * 2. Brings users back to the site with wallet connected and trustline set
 * 3. Prompts users to visit /upgrade or get VIP cards after setup
 */
export default function TestTrustlinePage() {
  const { user } = useAuth()
  const [cacheStats, setCacheStats] = useState<any>(null)
  
  const {
    getCachedByUserId,
    clearAllCache,
    getCacheStats
  } = useXamanUserInfoCache(user?.id)

  const handleCheckCache = () => {
    if (user?.id) {
      const cached = getCachedByUserId()
      if (cached) {
        toast.success(`Found cached info for wallet: ${cached.account}`)
        console.log('Cached user info:', cached)
      } else {
        toast.info('No cached user info found')
      }
    } else {
      toast.error('Please log in to check cache')
    }
  }

  const handleClearCache = () => {
    clearAllCache()
    toast.success('All cached user info cleared')
  }

  const handleGetStats = () => {
    const stats = getCacheStats()
    setCacheStats(stats)
    toast.info(`Cache stats: ${stats.totalEntries} total, ${stats.expiredEntries} expired`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-black p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">
              🧪 FUSE Trustline Setup Test
            </CardTitle>
            <CardDescription className="text-center">
              Testing OAuth2 userinfo caching and enhanced trustline setup flow
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button onClick={handleCheckCache} variant="outline">
                Check Cache
              </Button>
              <Button onClick={handleGetStats} variant="outline">
                Get Stats
              </Button>
              <Button onClick={handleClearCache} variant="destructive">
                Clear Cache
              </Button>
            </div>
            
            {cacheStats && (
              <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <h4 className="font-semibold mb-2">Cache Statistics:</h4>
                <div className="text-sm space-y-1">
                  <div>Total Entries: {cacheStats.totalEntries}</div>
                  <div>Expired Entries: {cacheStats.expiredEntries}</div>
                  <div>Valid Entries: {cacheStats.totalEntries - cacheStats.expiredEntries}</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* User Status */}
        <Card>
          <CardHeader>
            <CardTitle>User Status</CardTitle>
          </CardHeader>
          <CardContent>
            {user ? (
              <div className="space-y-2">
                <div className="text-sm">
                  <strong>User ID:</strong> {user.id}
                </div>
                <div className="text-sm">
                  <strong>Email:</strong> {user.email}
                </div>
                <div className="text-green-600 font-semibold">✅ Authenticated</div>
              </div>
            ) : (
              <div className="text-amber-600 font-semibold">
                ⚠️ Not authenticated - Please log in to test full functionality
              </div>
            )}
          </CardContent>
        </Card>

        {/* Flow Description */}
        <Card>
          <CardHeader>
            <CardTitle>Expected Flow</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="flex items-start gap-3">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">1</span>
                <div>
                  <strong>User sets up trustline</strong> using one of the methods below
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">2</span>
                <div>
                  <strong>Account info is cached</strong> using https://oauth2.xumm.app/userinfo
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">3</span>
                <div>
                  <strong>User returns to site</strong> with wallet connected and trustline set
                </div>
              </div>
              <div className="flex items-start gap-3">
                <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">4</span>
                <div>
                  <strong>Success actions appear</strong> prompting to visit /upgrade or get FUSE tokens
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Trustline Setup Component */}
        <TrustlineSetup />

        {/* Technical Details */}
        <Card>
          <CardHeader>
            <CardTitle>Technical Implementation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <strong>OAuth2 Userinfo Endpoint:</strong> https://oauth2.xumm.app/userinfo
              </div>
              <div>
                <strong>Cache Storage:</strong> localStorage + Supabase profiles table
              </div>
              <div>
                <strong>Cache Expiry:</strong> 24 hours
              </div>
              <div>
                <strong>Success Actions:</strong> Route to /upgrade (VIP Card) or xmagnetic.org (FUSE Tokens)
              </div>
              <div>
                <strong>Welcome Back Detection:</strong> Automatic for returning users with cached info
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
