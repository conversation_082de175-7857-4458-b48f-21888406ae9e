"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { getSupabaseClient } from "@/lib/supabase";
import ImprovedQRScanner from "@/components/improved-qr-scanner";
import { parseQRData } from "@/lib/qr-code-generator";
import { ProtectedRoute } from "@/components/protected-route";
import { Button } from "@/components/ui/button";
import { ArrowLeft, CheckCircle, AlertCircle, QrCode, Camera, Smartphone, Shield, Zap } from "lucide-react";

function ScanPageContent() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [scanResult, setScanResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [cameraPermission, setCameraPermission] = useState<'granted' | 'denied' | 'prompt' | 'checking'>('checking');
  const [showScanner, setShowScanner] = useState(false);

  // Check camera permission on mount
  useEffect(() => {
    checkCameraPermission();
  }, []);

  // Handle test QR from URL
  useEffect(() => {
    const testQR = searchParams.get('test_qr');
    if (testQR && user) {
      try {
        const decodedQR = decodeURIComponent(testQR);
        console.log('Processing test QR:', decodedQR);
        handleScanSuccess(decodedQR);
      } catch (error) {
        console.error('Failed to process test QR:', error);
      }
    }
  }, [searchParams, user]);

  const checkCameraPermission = async () => {
    try {
      const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
      setCameraPermission(permission.state);

      permission.addEventListener('change', () => {
        setCameraPermission(permission.state);
      });
    } catch (error) {
      console.log('Permission API not supported, will request on scanner start');
      setCameraPermission('prompt');
    }
  };

  const requestCameraPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      stream.getTracks().forEach(track => track.stop());
      setCameraPermission('granted');
      setShowScanner(true);
    } catch (error) {
      setCameraPermission('denied');
      setScanResult({
        success: false,
        message: "Camera access is required to scan QR codes. Please allow camera permissions in your browser settings."
      });
    }
  };

  const handleScanSuccess = async (decodedText: string) => {
    if (isProcessing) return; // Prevent multiple scans

    setIsProcessing(true);

    try {
      console.log("Raw QR data:", decodedText); // Debug log

      // Parse QR code data to determine type
      const qrData = parseQRData(decodedText.trim());
      console.log("Parsed QR data:", qrData); // Debug log

      if (!qrData) {
        throw new Error(`Invalid QR Code format. Scanned data: ${decodedText.substring(0, 100)}...`);
      }

      if (qrData.userId === user?.id) {
        throw new Error("You cannot scan your own QR code");
      }

      let successMessage = "";
      let interactionType: 'user_scan' | 'business_scan' = 'user_scan';

      // Check if user is a cardholder for business scans
      if (qrData.type === 'business') {
        const supabase = getSupabaseClient();
        if (!supabase) {
          throw new Error('Database connection not available');
        }

        const { data: profile } = await supabase
          .from('profiles')
          .select('is_card_holder')
          .eq('id', user?.id)
          .single();

        if (!profile?.is_card_holder) {
          throw new Error("Only VIP cardholders can earn FUSE rewards by scanning business QR codes. Upgrade to VIP to start earning!");
        }

        // Check for cooldown (1 scan per business per day)
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const { data: existingInteraction } = await supabase
          .from('qr_interactions')
          .select('id')
          .eq('scanner_user_id', user?.id)
          .eq('scanned_business_id', qrData.userId)
          .eq('interaction_type', 'business_scan')
          .gte('created_at', today.toISOString())
          .lt('created_at', tomorrow.toISOString())
          .single();

        if (existingInteraction) {
          throw new Error("You've already scanned this business today. Come back tomorrow to earn more FUSE!");
        }

        // Check if business exists first
        console.log(`Looking for business with ID: ${qrData.userId}`);

        let { data: business, error: businessError } = await supabase
          .from('businesses')
          .select('id, name, category, is_active')
          .eq('id', qrData.userId)
          .single();

        console.log('Business query result:', { business, businessError });

        // Check for specific error types
        if (businessError) {
          console.error('Business query error:', businessError);

          // If it's a "not found" error (PGRST116), the business doesn't exist
          if (businessError.code === 'PGRST116') {
            console.log(`Business ${qrData.userId} not found in database - attempting auto-creation`);

            // Try to create a placeholder business for FUSE Network businesses
            const { data: newBusiness, error: createError } = await supabase
              .from('businesses')
              .insert({
                id: qrData.userId,
                name: `FUSE Network Business`,
                category: 'Business Services',
                is_active: true, // Auto-activate FUSE Network businesses
                user_id: null, // No specific owner
                website: 'https://fuse.vip',
                contact_phone: 'Contact via FUSE Network',
                contact_email: '<EMAIL>',
                business_address: 'FUSE Network Partner Location'
              })
              .select('id, name, category, is_active')
              .single();

            if (createError) {
              console.error('Failed to create business:', createError);
              throw new Error(`Business not found in database (ID: ${qrData.userId.slice(0, 8)}...). This appears to be a FUSE Network business that hasn't been synced to your local database yet. Please contact support to sync FUSE Network businesses.`);
            }

            console.log('Auto-created FUSE Network business:', newBusiness);
            business = newBusiness;
          } else {
            // Other error (like RLS policy violation)
            throw new Error(`Database error when looking up business: ${businessError.message}. Please try again or contact support.`);
          }
        }

        if (!business) {
          throw new Error(`Business data is null. Please try again or contact support.`);
        }

        if (business.is_active === false) {
          throw new Error(`This business (${business.name}) is not currently active for FUSE rewards.`);
        }

        // Log the business interaction and award 100 FUSE tokens
        const { error: interactionError } = await supabase.from("qr_interactions").insert({
          scanner_user_id: user?.id,
          scanned_business_id: qrData.userId,
          interaction_type: 'business_scan',
          qr_data: decodedText.trim(),
          fuse_tokens_awarded: 100
        });

        if (interactionError) {
          console.error("Business interaction error:", interactionError);
          throw new Error(`Failed to log business interaction: ${interactionError.message}. Please try again.`);
        }

        // Success! Business interaction logged with 100 FUSE tokens awarded
        console.log(`Successfully logged business scan for ${business.name} - 100 FUSE tokens awarded`);

        successMessage = `🎉 Success! You earned 100 FUSE tokens at ${business.name}!`;

        // Redirect to success page with business info
        router.push(`/scan/success?business_id=${qrData.userId}&reward=100`);
        return;
      } else {
        // Handle user QR code scan
        interactionType = 'user_scan';

        const supabase = getSupabaseClient();
        if (!supabase) {
          throw new Error('Database connection not available');
        }

        // Log the user interaction
        const { error: interactionError } = await supabase.from("qr_interactions").insert({
          scanner_user_id: user?.id,
          scanned_user_id: qrData.userId,
          interaction_type: 'user_scan',
          qr_data: decodedText.trim(),
        });

        if (interactionError) {
          throw interactionError;
        }

        // Get scanned user's profile for personalized message
        const { data: scannedProfile } = await supabase
          .from('profiles')
          .select('first_name, last_name, is_card_holder')
          .eq('id', qrData.userId)
          .single();

        const userName = scannedProfile
          ? `${scannedProfile.first_name} ${scannedProfile.last_name}`
          : 'another user';

        const vipStatus = scannedProfile?.is_card_holder ? ' (VIP Member)' : '';

        successMessage = `Successfully connected with ${userName}${vipStatus}! Check your interactions to see the connection.`;
      }

      setScanResult({
        success: true,
        message: successMessage
      });

      // Auto-redirect after 3 seconds
      setTimeout(() => {
        router.push('/dashboard');
      }, 3000);

    } catch (error) {
      console.error("QR Scan Error:", error);
      setScanResult({
        success: false,
        message: error instanceof Error ? error.message : "Failed to process QR code. Please try again."
      });

      // Allow retry after 2 seconds
      setTimeout(() => {
        setIsProcessing(false);
        setScanResult(null);
      }, 2000);
    } finally {
      // Always reset processing state
      if (!scanResult?.success) {
        setIsProcessing(false);
      }
    }
  };

  const handleScanError = (error: string) => {
    // Only show critical errors to user
    if (error.includes('Camera') || error.includes('permission')) {
      setScanResult({
        success: false,
        message: "Camera access is required to scan QR codes. Please allow camera permissions and try again."
      });
    }
  };

  const handleGoBack = () => {
    router.push('/dashboard');
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center mb-8">
            <Button
              onClick={handleGoBack}
              variant="ghost"
              className="text-white hover:bg-white/10 mr-4"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Button>
          </div>

          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">
                Scan QR Code
              </h1>
              <p className="text-gray-300">
                Scan QR codes to connect with other users or log business visits
              </p>
            </div>

            {/* Scanner or Result */}
            <div className="bg-white rounded-xl p-6 shadow-xl">
              {scanResult ? (
                <div className="flex flex-col items-center space-y-4 text-center">
                  {scanResult.success ? (
                    <>
                      <div className="relative">
                        <CheckCircle className="text-green-500 w-20 h-20" />
                        <div className="absolute inset-0 animate-ping">
                          <CheckCircle className="text-green-400 w-20 h-20 opacity-75" />
                        </div>
                      </div>
                      <h2 className="text-2xl font-bold text-green-600">Success!</h2>
                      <p className="text-gray-700 text-lg">{scanResult.message}</p>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
                        <span>Redirecting to dashboard...</span>
                      </div>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="text-red-500 w-20 h-20" />
                      <h2 className="text-2xl font-bold text-red-600">Oops!</h2>
                      <p className="text-gray-700 text-lg">{scanResult.message}</p>
                      {!scanResult.message.includes('Camera') && (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                          <span>Retrying in a moment...</span>
                        </div>
                      )}
                      {scanResult.message.includes('Camera') && (
                        <Button
                          onClick={() => setScanResult(null)}
                          className="mt-4"
                          variant="outline"
                        >
                          Try Again
                        </Button>
                      )}
                    </>
                  )}
                </div>
              ) : !showScanner ? (
                <div className="text-center space-y-6">
                  {cameraPermission === 'checking' && (
                    <div className="space-y-4">
                      <div className="animate-pulse">
                        <Camera className="w-16 h-16 mx-auto text-blue-500" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-800">Checking Camera Access...</h3>
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    </div>
                  )}

                  {cameraPermission === 'prompt' && (
                    <div className="space-y-6">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                        <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 rounded-full p-6 w-24 h-24 mx-auto flex items-center justify-center">
                          <Camera className="w-12 h-12 text-white" />
                        </div>
                      </div>
                      <div className="space-y-3">
                        <h3 className="text-2xl font-bold text-gray-800">Camera Access Required</h3>
                        <p className="text-gray-600 max-w-md mx-auto">
                          To scan QR codes, we need access to your camera. Click the button below to grant permission.
                        </p>
                      </div>
                      <Button
                        onClick={requestCameraPermission}
                        className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                      >
                        <Shield className="w-5 h-5 mr-2" />
                        Enable Camera Access
                      </Button>
                    </div>
                  )}

                  {cameraPermission === 'granted' && (
                    <div className="space-y-6">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-blue-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                        <div className="relative bg-gradient-to-r from-green-500 to-blue-600 rounded-full p-6 w-24 h-24 mx-auto flex items-center justify-center">
                          <Zap className="w-12 h-12 text-white" />
                        </div>
                      </div>
                      <div className="space-y-3">
                        <h3 className="text-2xl font-bold text-gray-800">Ready to Scan!</h3>
                        <p className="text-gray-600">
                          Camera access granted. Click below to start scanning QR codes.
                        </p>
                      </div>
                      <Button
                        onClick={() => setShowScanner(true)}
                        className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                      >
                        <QrCode className="w-5 h-5 mr-2" />
                        Start Scanning
                      </Button>
                    </div>
                  )}

                  {cameraPermission === 'denied' && (
                    <div className="space-y-6">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-orange-500 rounded-full blur-xl opacity-30"></div>
                        <div className="relative bg-gradient-to-r from-red-500 to-orange-600 rounded-full p-6 w-24 h-24 mx-auto flex items-center justify-center">
                          <AlertCircle className="w-12 h-12 text-white" />
                        </div>
                      </div>
                      <div className="space-y-3">
                        <h3 className="text-2xl font-bold text-red-600">Camera Access Denied</h3>
                        <p className="text-gray-600 max-w-md mx-auto">
                          Camera access is required to scan QR codes. Please enable camera permissions in your browser settings and refresh the page.
                        </p>
                      </div>
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-w-md mx-auto">
                        <p className="text-sm text-red-700">
                          <strong>How to enable:</strong> Look for the camera icon in your browser's address bar and click "Allow"
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div>
                  <ImprovedQRScanner
                    onScanSuccess={handleScanSuccess}
                    onScanError={handleScanError}
                  />

                  {isProcessing && (
                    <div className="mt-6 text-center">
                      <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
                        <span className="text-blue-700 font-medium">Processing your scan...</span>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Instructions */}
            {!scanResult && showScanner && (
              <div className="mt-6 text-center">
                <div className="bg-white/10 rounded-lg p-4">
                  <h3 className="text-white font-semibold mb-2">Scanning Tips:</h3>
                  <ul className="text-gray-300 text-sm space-y-1">
                    <li>• Point your camera at the QR code</li>
                    <li>• Keep the code within the scanning frame</li>
                    <li>• Hold steady until the scan completes</li>
                    <li>• Ensure good lighting for best results</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

export default function ScanPage() {
  return (
    <Suspense fallback={
      <ProtectedRoute>
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    }>
      <ScanPageContent />
    </Suspense>
  );
}
