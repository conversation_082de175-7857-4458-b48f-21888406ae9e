"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { ArrowRight, CheckCircle2, Server, Code, Palette, Zap, Globe, Rocket, Star, Users } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { PageHeader } from "@/components/page-header"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function WebBuildingPage() {
  const [selectedTier, setSelectedTier] = useState("starter")

  const webTools = [
    {
      name: "AI Website Builder",
      description: "Create stunning websites with AI-powered design assistance",
      icon: <Palette className="w-8 h-8 text-[#FF6B6B]" />,
      fuseCost: "10 FUSE",
      features: ["AI-generated layouts", "Smart content suggestions", "Auto-responsive design"]
    },
    {
      name: "Premium Templates",
      description: "Access exclusive, professionally designed templates",
      icon: <Code className="w-8 h-8 text-[#FF6B6B]" />,
      fuseCost: "5 FUSE",
      features: ["50+ premium templates", "Industry-specific designs", "Customizable components"]
    },
    {
      name: "Advanced Hosting",
      description: "High-performance hosting with global CDN",
      icon: <Server className="w-8 h-8 text-[#FF6B6B]" />,
      fuseCost: "15 FUSE/month",
      features: ["99.9% uptime guarantee", "Global CDN", "SSL certificates included"]
    },
    {
      name: "SEO Optimization",
      description: "Boost your search rankings with advanced SEO tools",
      icon: <Rocket className="w-8 h-8 text-[#FF6B6B]" />,
      fuseCost: "8 FUSE",
      features: ["Keyword optimization", "Meta tag generation", "Performance analytics"]
    }
  ]

  const tiers = [
    {
      id: "starter",
      name: "Starter",
      fuseCost: "25 FUSE",
      description: "Perfect for personal websites and small projects",
      features: [
        "AI Website Builder access",
        "5 premium templates",
        "Basic hosting (1 site)",
        "SSL certificate",
        "Email support"
      ],
      popular: false
    },
    {
      id: "professional",
      name: "Professional", 
      fuseCost: "50 FUSE",
      description: "Ideal for businesses and professional portfolios",
      features: [
        "Everything in Starter",
        "All premium templates",
        "Advanced hosting (5 sites)",
        "SEO optimization tools",
        "Priority support",
        "Custom domain included"
      ],
      popular: true
    },
    {
      id: "enterprise",
      name: "Enterprise",
      fuseCost: "100 FUSE",
      description: "For agencies and large-scale projects",
      features: [
        "Everything in Professional",
        "Unlimited sites",
        "White-label solutions",
        "API access",
        "Dedicated account manager",
        "Custom integrations"
      ],
      popular: false
    }
  ]

  return (
    <>
      <PageHeader
        title="Web Building with $FUSE"
        subtitle="CREATE • DESIGN • DEPLOY"
        description="Submit $FUSE tokens to access premium web development tools, AI-powered builders, and professional hosting solutions."
      />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-[#FF6B6B]/10 to-[#FF8E8E]/10">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-1/2">
              <Badge className="bg-[#FF6B6B]/20 text-[#FF6B6B] hover:bg-[#FF6B6B]/30 mb-6">
                Web Building World
              </Badge>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Build Professional Websites with <span className="text-[#FF6B6B]">$FUSE</span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Transform your ideas into stunning websites using AI-powered tools, premium templates, 
                and enterprise-grade hosting - all unlocked with $FUSE tokens.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg"
                  className="bg-gradient-to-r from-[#FF6B6B] to-[#FF8E8E] hover:from-[#FF5A5A] hover:to-[#FF7D7D] text-white"
                >
                  <Zap className="w-5 h-5 mr-2" />
                  Start Building Now
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
                
                <Button variant="outline" size="lg">
                  <Globe className="w-5 h-5 mr-2" />
                  View Examples
                </Button>
              </div>
            </div>

            <div className="lg:w-1/2">
              <div className="relative">
                <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <Badge className="bg-[#FF6B6B]/20 text-[#FF6B6B]">AI-Powered</Badge>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="h-4 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E8E] rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-full"></div>
                    <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                    
                    <div className="grid grid-cols-2 gap-4 mt-6">
                      <div className="h-20 bg-gradient-to-br from-[#FF6B6B]/20 to-[#FF8E8E]/20 rounded-lg flex items-center justify-center">
                        <Palette className="w-8 h-8 text-[#FF6B6B]" />
                      </div>
                      <div className="h-20 bg-gradient-to-br from-[#FF6B6B]/20 to-[#FF8E8E]/20 rounded-lg flex items-center justify-center">
                        <Code className="w-8 h-8 text-[#FF6B6B]" />
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-[#FF6B6B] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                  AI-Powered
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Web Tools Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Premium Web Building Tools
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Submit $FUSE tokens to unlock professional-grade tools that make web development fast, easy, and powerful.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {webTools.map((tool, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 border border-gray-100 hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-gradient-to-br from-[#FF6B6B]/20 to-[#FF8E8E]/20 rounded-xl">
                    {tool.icon}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-xl font-bold text-gray-900">{tool.name}</h3>
                      <Badge className="bg-[#FF6B6B]/20 text-[#FF6B6B]">{tool.fuseCost}</Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{tool.description}</p>
                    
                    <div className="space-y-2">
                      {tool.features.map((feature, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <CheckCircle2 className="w-4 h-4 text-[#FF6B6B]" />
                          <span className="text-sm text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Tiers */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Choose Your Web Building Package
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Select the perfect package for your needs. All packages are unlocked with $FUSE tokens.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {tiers.map((tier) => (
              <div
                key={tier.id}
                className={`relative bg-white rounded-2xl p-8 border-2 transition-all duration-300 ${
                  tier.popular 
                    ? 'border-[#FF6B6B] shadow-lg scale-105' 
                    : 'border-gray-200 hover:border-[#FF6B6B]/50'
                }`}
              >
                {tier.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-[#FF6B6B] text-white px-4 py-1">Most Popular</Badge>
                  </div>
                )}
                
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.name}</h3>
                  <div className="text-3xl font-bold text-[#FF6B6B] mb-2">{tier.fuseCost}</div>
                  <p className="text-gray-600">{tier.description}</p>
                </div>

                <div className="space-y-4 mb-8">
                  {tier.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-3">
                      <CheckCircle2 className="w-5 h-5 text-[#FF6B6B] flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                <Button 
                  className={`w-full ${
                    tier.popular
                      ? 'bg-gradient-to-r from-[#FF6B6B] to-[#FF8E8E] hover:from-[#FF5A5A] hover:to-[#FF7D7D] text-white'
                      : 'bg-white border-2 border-[#FF6B6B] text-[#FF6B6B] hover:bg-[#FF6B6B] hover:text-white'
                  }`}
                  onClick={() => setSelectedTier(tier.id)}
                >
                  Submit {tier.fuseCost}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E8E] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Build Your Dream Website?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Join thousands of creators who are building amazing websites with $FUSE tokens. 
            Start your web building journey today.
          </p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button size="lg" className="bg-white text-[#FF6B6B] hover:bg-gray-100">
              <Zap className="w-5 h-5 mr-2" />
              Get Started Now
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            
            <Link href="/fuse">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <Globe className="w-5 h-5 mr-2" />
                Back to $FUSE Worlds
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </>
  )
}
