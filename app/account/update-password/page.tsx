"use client";

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getSupabaseClient } from '@/lib/supabase';
import { useAuth } from '@/contexts/auth-context';

function UpdatePasswordForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const [loading, setLoading] = useState(false);
  const [hasSession, setHasSession] = useState(false);
  const [isRecoveryFlow, setIsRecoveryFlow] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { session, user } = useAuth();

  // Check for recovery flow and session on component mount
  useEffect(() => {
    const checkSessionAndRecovery = async () => {
      // Check if this is a recovery flow from email link
      const tokenHash = searchParams.get('token_hash');
      const type = searchParams.get('type');
      
      if (tokenHash && type === 'recovery') {
        setIsRecoveryFlow(true);
        console.log('Recovery flow detected with token:', tokenHash);
      }

      if (session || user) {
        setHasSession(true);
        // If user is logged in, pre-fill email
        if (user?.email) {
          setEmail(user.email);
        }
      } else {
        // Check Supabase session directly
        const supabase = getSupabaseClient();
        if (supabase) {
          const { data: { session: currentSession } } = await supabase.auth.getSession();
          if (currentSession?.user) {
            setHasSession(true);
            setEmail(currentSession.user.email || '');
          }
        }
      }
    };

    checkSessionAndRecovery();
  }, [session, user, searchParams]);

  // Password validation state
  const getPasswordValidation = (password: string) => {
    return {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
  };

  const passwordValidation = getPasswordValidation(password);

  const validatePassword = (password: string) => {
    if (password.length < 8) return 'Password must be at least 8 characters long';
    if (!/[A-Z]/.test(password)) return 'Password must include an uppercase letter';
    if (!/[a-z]/.test(password)) return 'Password must include a lowercase letter';
    if (!/[0-9]/.test(password)) return 'Password must include a number';
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) return 'Password must include a special character';
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMsg('');
    setSuccessMsg('');
    setLoading(true);

    try {
      if (password !== confirmPassword) {
        throw new Error('Passwords do not match');
      }

      const passwordError = validatePassword(password);
      if (passwordError) {
        throw new Error(passwordError);
      }

      // Determine email to use - from session or user input
      const emailToUse = (hasSession || isRecoveryFlow) && user?.email ? user.email : email;

      if (!emailToUse) {
        throw new Error('Email is required');
      }

      console.log('Attempting to update password via API...');

      // Always use the admin API endpoint for consistent behavior and proper privileges
      const response = await fetch('/api/auth/update-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: emailToUse,
          password
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Password update failed');
      }

      console.log('Password updated successfully via admin API');

      if (hasSession || isRecoveryFlow) {
        // User had an active session or was in recovery flow, redirect to dashboard
        setSuccessMsg('Password updated successfully! Redirecting to dashboard...');
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        // No active session, redirect to login
        setSuccessMsg('Password updated successfully! Redirecting to login...');
        setTimeout(async () => {
          const supabase = getSupabaseClient();
          if (supabase) {
            await supabase.auth.signOut();
          }
          router.push('/login');
        }, 2000);
      }

    } catch (error: any) {
      console.error('Password update failed:', error);
      setErrorMsg(error.message || 'Password update failed');
    } finally {
      setLoading(false);
    }
  };

  const styles = {
    container: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      padding: '20px',
      backgroundColor: '#f5f5f5',
    },
    formBox: {
      backgroundColor: 'white',
      padding: '40px',
      borderRadius: '8px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      width: '100%',
      maxWidth: '400px',
    },
    heading: {
      textAlign: 'center' as const,
      marginBottom: '30px',
      color: '#333',
      fontSize: '24px',
      fontWeight: 'bold',
    },
    form: {
      display: 'flex',
      flexDirection: 'column' as const,
      gap: '20px',
    },
    field: {
      display: 'flex',
      flexDirection: 'column' as const,
    },
    label: {
      marginBottom: '8px',
      color: '#555',
      fontSize: '14px',
      fontWeight: '500',
    },
    input: {
      padding: '12px',
      border: '1px solid #ddd',
      borderRadius: '4px',
      fontSize: '16px',
      transition: 'border-color 0.2s',
      outline: 'none',
    },
    button: {
      padding: '12px',
      backgroundColor: loading ? '#ccc' : '#007bff',
      color: 'white',
      border: 'none',
      borderRadius: '4px',
      fontSize: '16px',
      fontWeight: '500',
      cursor: loading ? 'not-allowed' : 'pointer',
      transition: 'background-color 0.2s',
    },
    error: {
      color: '#dc3545',
      fontSize: '14px',
      marginBottom: '20px',
      textAlign: 'center' as const,
    },
    success: {
      color: '#28a745',
      fontSize: '14px',
      marginBottom: '20px',
      textAlign: 'center' as const,
    },
  };

  return (
    <div style={styles.container}>
      <div style={styles.formBox}>
        <h2 style={styles.heading}>Update Password</h2>

        <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: hasSession || isRecoveryFlow ? '#e7f3ff' : '#f8f9fa', borderRadius: '4px', border: `1px solid ${hasSession || isRecoveryFlow ? '#b3d9ff' : '#e9ecef'}` }}>
          <p style={{ margin: '0 0 10px 0', fontSize: '14px', color: hasSession || isRecoveryFlow ? '#0066cc' : '#6c757d', textAlign: 'center' }}>
            {hasSession || isRecoveryFlow
              ? '✓ You have an active session. Your password will be updated immediately.'
              : 'Enter your email and new password. If you have a valid reset session, your password will be updated immediately. Otherwise, we\'ll send you a new reset link.'
            }
          </p>
          {isRecoveryFlow && (
            <p style={{ margin: '10px 0 0 0', fontSize: '12px', color: '#0066cc', textAlign: 'center', fontStyle: 'italic' }}>
              Password recovery mode - you can update your password directly.
            </p>
          )}
        </div>

        <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#e7f3ff', borderRadius: '4px', border: '1px solid #b3d9ff' }}>
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px', color: '#0066cc', fontWeight: 'bold' }}>
            Password Requirements:
          </h4>
          <ul style={{ margin: 0, paddingLeft: '20px', fontSize: '13px', color: '#0066cc' }}>
            <li>At least 8 characters long</li>
            <li>One uppercase letter (A-Z)</li>
            <li>One lowercase letter (a-z)</li>
            <li>One number (0-9)</li>
            <li>One special character (!@#$%^&*(),.?":{}|&lt;&gt;)</li>
          </ul>
        </div>

        {errorMsg && <p style={styles.error}>{errorMsg}</p>}
        {successMsg && <p style={styles.success}>{successMsg}</p>}

        <form onSubmit={handleSubmit} style={styles.form}>
          {!hasSession && !isRecoveryFlow && (
            <div style={styles.field}>
              <label style={styles.label}>
                Email Address
                <input
                  type="email"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  style={styles.input}
                  placeholder="Enter your email address"
                  required
                  disabled={loading}
                />
              </label>
            </div>
          )}

          {(hasSession || isRecoveryFlow) && email && (
            <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#e7f3ff', borderRadius: '4px', border: '1px solid #b3d9ff' }}>
              <p style={{ margin: 0, fontSize: '14px', color: '#0066cc' }}>
                <strong>Updating password for:</strong> {email}
              </p>
            </div>
          )}

          <div style={styles.field}>
            <label style={styles.label}>
              New Password
              <input
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                style={styles.input}
                placeholder="Enter new password"
                required
                disabled={loading}
              />
            </label>

            {password && (
              <div style={{ marginTop: '8px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px', fontSize: '12px' }}>
                <div style={{ marginBottom: '5px', fontWeight: 'bold', color: '#495057' }}>Password Requirements:</div>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                  <div style={{ color: passwordValidation.length ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.length ? '✓' : '✗'} At least 8 characters
                  </div>
                  <div style={{ color: passwordValidation.uppercase ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.uppercase ? '✓' : '✗'} One uppercase letter
                  </div>
                  <div style={{ color: passwordValidation.lowercase ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.lowercase ? '✓' : '✗'} One lowercase letter
                  </div>
                  <div style={{ color: passwordValidation.number ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.number ? '✓' : '✗'} One number
                  </div>
                  <div style={{ color: passwordValidation.special ? '#28a745' : '#dc3545' }}>
                    {passwordValidation.special ? '✓' : '✗'} One special character
                  </div>
                </div>
              </div>
            )}
          </div>

          <div style={styles.field}>
            <label style={styles.label}>
              Confirm Password
              <input
                type="password"
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                style={styles.input}
                placeholder="Confirm new password"
                required
                disabled={loading}
              />
            </label>

            {confirmPassword && (
              <div style={{ marginTop: '8px', fontSize: '12px' }}>
                <div style={{ color: password === confirmPassword ? '#28a745' : '#dc3545' }}>
                  {password === confirmPassword ? '✓ Passwords match' : '✗ Passwords do not match'}
                </div>
              </div>
            )}
          </div>

          <button
            type="submit"
            style={{
              ...styles.button,
              backgroundColor: loading || !Object.values(passwordValidation).every(Boolean) || password !== confirmPassword || (!hasSession && !isRecoveryFlow && !email && !user?.email)
                ? '#ccc'
                : '#007bff',
              cursor: loading || !Object.values(passwordValidation).every(Boolean) || password !== confirmPassword || (!hasSession && !isRecoveryFlow && !email && !user?.email)
                ? 'not-allowed'
                : 'pointer'
            }}
            disabled={loading || !Object.values(passwordValidation).every(Boolean) || password !== confirmPassword || (!hasSession && !isRecoveryFlow && !email && !user?.email)}
          >
            {loading ? 'Processing...' : 'Update Password'}
          </button>
        </form>
      </div>
    </div>
  );
}

export default function UpdatePassword() {
  return (
    <Suspense fallback={
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5'
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          Loading...
        </div>
      </div>
    }>
      <UpdatePasswordForm />
    </Suspense>
  );
}
