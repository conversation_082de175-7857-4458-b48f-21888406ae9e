"use client"

import { LottieReactAnimation, YourLottieReactAnimation, LottieWithControls } from "@/components/lottie-react-animation"

export default function LottieReactTestPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Lottie React Implementation
          </h1>
          <p className="text-lg text-gray-600">
            Using lottie-react library as recommended by the Medium article
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Your specific animation */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Your Animation</h3>
            <div className="flex justify-center">
              <YourLottieReactAnimation />
            </div>
            <p className="text-sm text-gray-600 mt-2 text-center">✅ Using lottie-react</p>
          </div>

          {/* Mission animation */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Mission Animation</h3>
            <div className="flex justify-center">
              <LottieReactAnimation
                src="https://lottie.host/9b3b5a23-79cb-449b-a523-7cab48ff6aa2/SnOFt96zlp.lottie"
                width={300}
                height={300}
                loop={true}
                autoplay={true}
                speed={1}
              />
            </div>
          </div>

          {/* Industry animation */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Industry Animation</h3>
            <div className="flex justify-center">
              <LottieReactAnimation
                src="https://lottie.host/bff023a8-ddc5-4788-adf5-e8ef59e1e088/e749q9gbhS.lottie"
                width={300}
                height={300}
                loop={true}
                autoplay={true}
                speed={1}
              />
            </div>
          </div>

          {/* Bubble animation */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Bubble Animation</h3>
            <div className="flex justify-center">
              <LottieReactAnimation
                src="https://lottie.host/4d1f0743-dbdc-44b6-a8ce-2ccd0741c428/upqyw8Z5Y3.lottie"
                width={300}
                height={300}
                loop={true}
                autoplay={true}
                speed={1}
              />
            </div>
          </div>

          {/* Small version */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Small Size</h3>
            <div className="flex justify-center">
              <LottieReactAnimation
                src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
                width={150}
                height={150}
                loop={true}
                autoplay={true}
                speed={2}
              />
            </div>
          </div>

          {/* With controls */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">With Controls</h3>
            <div className="flex justify-center">
              <LottieWithControls
                src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
                width={200}
                height={200}
                loop={true}
                autoplay={true}
                speed={1}
              />
            </div>
          </div>
        </div>

        {/* Implementation guide */}
        <div className="mt-12 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold mb-4">Implementation Guide</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">1. Install lottie-react</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
                <code>npm install lottie-react</code>
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">2. Basic Usage</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
{`import { LottieReactAnimation } from "@/components/lottie-react-animation"

<LottieReactAnimation
  src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
  width={300}
  height={300}
  loop={true}
  autoplay={true}
  speed={1}
/>`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">3. Features</h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                <li>✅ Works perfectly with Next.js</li>
                <li>✅ No CSP issues</li>
                <li>✅ Automatic error handling</li>
                <li>✅ Loading states</li>
                <li>✅ Playback controls</li>
                <li>✅ Responsive sizing</li>
                <li>✅ TypeScript support</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">4. Why lottie-react?</h3>
              <div className="text-gray-700 space-y-2">
                <p>Based on the Medium article reference, lottie-react is preferred over react-lottie because:</p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>No babel-runtime dependency issues</li>
                  <li>Better Next.js compatibility</li>
                  <li>More reliable and maintained</li>
                  <li>Simpler API</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
