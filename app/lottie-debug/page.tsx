"use client"

import { useEffect, useState } from "react"

export default function LottieDebugPage() {
  const [scriptLoaded, setScriptLoaded] = useState(false)
  const [scriptError, setScriptError] = useState<string | null>(null)
  const [consoleErrors, setConsoleErrors] = useState<string[]>([])

  useEffect(() => {
    // Capture console errors
    const originalError = console.error
    const errors: string[] = []
    
    console.error = (...args) => {
      errors.push(args.join(' '))
      setConsoleErrors([...errors])
      originalError(...args)
    }

    return () => {
      console.error = originalError
    }
  }, [])

  const testScriptLoad = () => {
    const script = document.createElement("script")
    script.src = "https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js"
    script.type = "module"
    
    script.onload = () => {
      setScriptLoaded(true)
      setScriptError(null)
    }
    
    script.onerror = (error) => {
      setScriptError(`Failed to load script: ${error}`)
      setScriptLoaded(false)
    }

    document.head.appendChild(script)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <h1 className="text-4xl font-bold text-center mb-8">Lottie Debug Page</h1>
        
        {/* Script Loading Test */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-bold mb-4">Script Loading Test</h2>
          <button 
            onClick={testScriptLoad}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mb-4"
          >
            Test Script Load
          </button>
          <div className="space-y-2">
            <p>Script Loaded: <span className={scriptLoaded ? "text-green-600" : "text-red-600"}>{scriptLoaded ? "✓ Yes" : "✗ No"}</span></p>
            {scriptError && <p className="text-red-600">Error: {scriptError}</p>}
          </div>
        </div>

        {/* Iframe Approach (Known Working) */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-bold mb-4">Iframe Approach (Should Work)</h2>
          <div className="flex justify-center">
            <iframe
              src="https://lottie.host/embed/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.html"
              width="300"
              height="300"
              className="border-0"
              title="Lottie Animation via Iframe"
            />
          </div>
        </div>

        {/* Direct HTML Test */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-bold mb-4">Direct HTML Test</h2>
          <div className="flex justify-center">
            <div 
              dangerouslySetInnerHTML={{
                __html: `
                  <script src="https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js" type="module"></script>
                  <dotlottie-wc 
                    src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie" 
                    style="width: 300px;height: 300px" 
                    speed="1" 
                    autoplay 
                    loop>
                  </dotlottie-wc>
                `
              }}
            />
          </div>
        </div>

        {/* Console Errors */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-bold mb-4">Console Errors</h2>
          {consoleErrors.length > 0 ? (
            <div className="space-y-2">
              {consoleErrors.map((error, index) => (
                <p key={index} className="text-red-600 text-sm font-mono bg-red-50 p-2 rounded">
                  {error}
                </p>
              ))}
            </div>
          ) : (
            <p className="text-green-600">No console errors detected</p>
          )}
        </div>

        {/* CSP Information */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold mb-4">CSP Information</h2>
          <div className="text-sm space-y-2">
            <p><strong>Script Sources:</strong> 'self', 'unsafe-inline', 'unsafe-eval', unpkg.com</p>
            <p><strong>Connect Sources:</strong> lottie.host, unpkg.com</p>
            <p><strong>Frame Sources:</strong> lottie.host</p>
            <p><strong>Image Sources:</strong> https: (all HTTPS sources allowed)</p>
          </div>
        </div>
      </div>
    </div>
  )
}
