import { NextRequest, NextResponse } from 'next/server'
import { profilesCache } from '@/lib/profiles-cache'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const minimal = searchParams.get('minimal') === 'true'
    const checkBusinessOwner = searchParams.get('checkBusinessOwner') === 'true'

    if (!userId) {
      return NextResponse.json(
        { error: 'userId parameter is required' },
        { status: 400 }
      )
    }

    console.log(`🔄 Fetching ${minimal ? 'minimal' : 'full'} cached profile for user: ${userId}`)

    let profile: any
    let isBusinessOwner: boolean | undefined

    if (minimal) {
      // Get minimal profile data
      profile = await profilesCache.getProfileMinimal(userId)
    } else {
      // Get full profile data
      profile = await profilesCache.getProfile(userId)
    }

    if (!profile) {
      console.log(`ℹ️ No profile found for user: ${userId}`)
      return NextResponse.json({
        error: 'Profile not found',
        userId
      }, { status: 404 })
    }

    // Check business owner status if requested
    if (checkBusinessOwner) {
      isBusinessOwner = await profilesCache.checkUserIsBusinessOwner(userId)
    }

    const totalDuration = Date.now() - startTime
    console.log(`✅ Successfully fetched cached profile for user ${userId} in ${totalDuration}ms`)

    const response: any = {
      success: true,
      profile,
      performance: {
        totalTime: totalDuration,
        cached: true,
        source: 'server_cache',
        minimal
      },
      cache: profilesCache.getStats(),
      timestamp: new Date().toISOString()
    }

    if (checkBusinessOwner) {
      response.isBusinessOwner = isBusinessOwner
    }

    return NextResponse.json(response)

  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Cached profile API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message,
        cached: false
      },
      { status: 500 }
    )
  }
}

// POST endpoint for cache management and bulk operations
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const body = await request.json()
    const { action, userIds, userId, walletAddress } = body
    
    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'preload':
        if (!userIds || !Array.isArray(userIds)) {
          return NextResponse.json(
            { error: 'userIds array is required for preload action' },
            { status: 400 }
          )
        }

        console.log(`🔄 Preloading profiles for ${userIds.length} users`)
        await profilesCache.preloadProfiles(userIds)
        
        const statsAfterPreload = profilesCache.getStats()
        
        return NextResponse.json({
          success: true,
          message: `Preloaded profiles for ${userIds.length} users`,
          stats: statsAfterPreload
        })

      case 'refresh':
        console.log('🔄 Refreshing profile cache')
        await profilesCache.refresh()
        
        return NextResponse.json({
          success: true,
          message: 'Profile cache refreshed successfully',
          stats: profilesCache.getStats()
        })

      case 'clear':
        console.log('🧹 Clearing profile cache')
        profilesCache.clear()
        
        return NextResponse.json({
          success: true,
          message: 'Profile cache cleared successfully'
        })

      case 'invalidate':
        if (userIds && Array.isArray(userIds)) {
          userIds.forEach(userId => profilesCache.invalidateProfile(userId))
          
          return NextResponse.json({
            success: true,
            message: `Invalidated cache for ${userIds.length} profiles`
          })
        } else if (userId) {
          profilesCache.invalidateProfile(userId)
          
          return NextResponse.json({
            success: true,
            message: `Invalidated cache for user ${userId}`
          })
        } else {
          profilesCache.invalidateAll()
          
          return NextResponse.json({
            success: true,
            message: 'Invalidated all profile cache'
          })
        }

      case 'updateWallet':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required for updateWallet action' },
            { status: 400 }
          )
        }

        profilesCache.updateWalletInCache(userId, walletAddress || null)
        
        return NextResponse.json({
          success: true,
          message: `Updated wallet address in cache for user ${userId}`
        })

      case 'checkBusinessOwner':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required for checkBusinessOwner action' },
            { status: 400 }
          )
        }

        const isBusinessOwner = await profilesCache.checkUserIsBusinessOwner(userId)
        
        return NextResponse.json({
          success: true,
          userId,
          isBusinessOwner,
          cached: true
        })

      case 'bulk-check-business-owners':
        if (!userIds || !Array.isArray(userIds)) {
          return NextResponse.json(
            { error: 'userIds array is required for bulk-check-business-owners action' },
            { status: 400 }
          )
        }

        const businessOwnerResults = await Promise.all(
          userIds.map(async (userId) => ({
            userId,
            isBusinessOwner: await profilesCache.checkUserIsBusinessOwner(userId)
          }))
        )
        
        return NextResponse.json({
          success: true,
          results: businessOwnerResults,
          cached: true
        })

      case 'stats':
        return NextResponse.json({
          success: true,
          stats: profilesCache.getStats()
        })

      case 'cache-info':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required for cache-info action' },
            { status: 400 }
          )
        }

        const cacheInfo = profilesCache.getCacheInfo(userId)
        
        return NextResponse.json({
          success: true,
          userId,
          cacheInfo
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: preload, refresh, clear, invalidate, updateWallet, checkBusinessOwner, bulk-check-business-owners, stats, cache-info' },
          { status: 400 }
        )
    }

  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('Profile cache operation error:', error)
    
    return NextResponse.json(
      { error: 'Cache operation failed', details: error.message },
      { status: 500 }
    )
  }
}