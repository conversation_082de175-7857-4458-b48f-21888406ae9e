import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase/server'

// API key for profile access - in production, store this securely
const PROFILES_API_KEY = process.env.PROFILES_API_KEY || 'fuse-profiles-secure-2024'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const apiKey = searchParams.get('apiKey') || request.headers.get('x-api-key')
    const userId = searchParams.get('userId')

    // Check API key
    if (apiKey !== PROFILES_API_KEY) {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401 }
      )
    }

    // Create admin client with retry logic
    const supabase = createAdminClient()

    // Validate userId if provided (basic UUID format check)
    if (userId && !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID format' },
        { status: 400 }
      )
    }

    // If userId is provided, get specific profile
    if (userId) {
      let retryCount = 0
      const maxRetries = 3

      while (retryCount < maxRetries) {
        try {
          const { data: profile, error } = await supabase
            .from('profiles')
            .select('id, first_name, last_name, phone, user_email, is_card_holder, is_business_applicant, card_tier, referring_business_id, membership_start_date, membership_end_date, xrp_wallet_address, created_at')
            .eq('id', userId)
            .single()

          if (error) {
            if (error.message.includes('schema cache') && retryCount < maxRetries - 1) {
              retryCount++
              console.log(`Schema cache error, retrying... (${retryCount}/${maxRetries})`)
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
              continue
            }
            throw error
          }

          return NextResponse.json({ profile })
        } catch (err) {
          if (retryCount === maxRetries - 1) {
            throw err
          }
          retryCount++
        }
      }
    }

    // Otherwise, get all profiles (with pagination and retry logic)
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10')))
    const offset = (page - 1) * limit

    let retryCount = 0
    const maxRetries = 3

    while (retryCount < maxRetries) {
      try {
        const { data: profiles, error, count } = await supabase
          .from('profiles')
          .select('*', { count: 'exact' })
          .range(offset, offset + limit - 1)
          .order('created_at', { ascending: false })

        if (error) {
          if (error.message.includes('schema cache') && retryCount < maxRetries - 1) {
            retryCount++
            console.log(`Schema cache error, retrying... (${retryCount}/${maxRetries})`)
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
            continue
          }
          throw error
        }

        return NextResponse.json({
          profiles,
          pagination: {
            page,
            limit,
            total: count || 0,
            totalPages: Math.ceil((count || 0) / limit)
          },
          retries: retryCount
        })
      } catch (err) {
        if (retryCount === maxRetries - 1) {
          throw err
        }
        retryCount++
      }
    }

  } catch (error) {
    console.error('Profiles API error:', error)

    // Better error serialization
    let errorDetails = 'Unknown error'
    if (error instanceof Error) {
      errorDetails = error.message
    } else if (typeof error === 'object' && error !== null) {
      errorDetails = JSON.stringify(error, null, 2)
    } else {
      errorDetails = String(error)
    }

    return NextResponse.json(
      { error: 'Failed to fetch profiles', details: errorDetails },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const apiKey = request.headers.get('x-api-key')
    
    // Check API key
    if (apiKey !== PROFILES_API_KEY) {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const supabase = createAdminClient()

    const { data: profile, error } = await supabase
      .from('profiles')
      .insert(body)
      .select()
      .single()

    if (error) {
      return NextResponse.json(
        { error: 'Failed to create profile', details: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json({ profile }, { status: 201 })

  } catch (error) {
    console.error('Profile creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const apiKey = searchParams.get('apiKey') || request.headers.get('x-api-key')
    const userId = searchParams.get('userId')
    
    // Check API key
    if (apiKey !== PROFILES_API_KEY) {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401 }
      )
    }

    if (!userId) {
      return NextResponse.json(
        { error: 'userId is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const supabase = createAdminClient()

    const { data: profile, error } = await supabase
      .from('profiles')
      .update(body)
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      return NextResponse.json(
        { error: 'Failed to update profile', details: error.message },
        { status: 400 }
      )
    }

    // Invalidate cache after successful profile update
    cacheInvalidation.invalidateProfile(userId);

    return NextResponse.json({ profile })

  } catch (error) {
    console.error('Profile update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
