import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('🏥 Health check starting...')

    const supabase = await createClient()

    // Test 1: Basic connection
    console.log('Test 1: Basic Supabase connection')
    const { data: connectionTest, error: connectionError } = await supabase
      .from('businesses')
      .select('count(*)')
      .limit(1)

    if (connectionError) {
      console.error('❌ Connection test failed:', connectionError)
      return NextResponse.json({
        status: 'unhealthy',
        error: 'Database connection failed',
        details: connectionError
      }, { status: 500 })
    }

    // Test 2: RLS policies
    console.log('Test 2: RLS policies test')
    const { data: businessCount, error: businessError } = await supabase
      .from('businesses')
      .select('id')
      .eq('is_active', true)
      .limit(1)

    const { data: profileCount, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1)

    // Test 3: Environment variables
    const envCheck = {
      supabase_url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      supabase_anon_key: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      supabase_service_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      db_password: !!process.env.SUPABASE_DB_PASSWORD
    }

    console.log('✅ Health check completed')

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      tests: {
        connection: {
          success: !connectionError,
          error: connectionError?.message || null
        },
        businesses: {
          success: !businessError,
          accessible: !!businessCount,
          error: businessError?.message || null
        },
        profiles: {
          success: !profileError,
          accessible: !!profileCount,
          error: profileError?.message || null
        },
        environment: envCheck
      },
      message: 'All systems operational'
    })

  } catch (error: any) {
    console.error('❌ Health check failed:', error)
    
    return NextResponse.json({
      status: 'unhealthy',
      error: 'Health check failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
