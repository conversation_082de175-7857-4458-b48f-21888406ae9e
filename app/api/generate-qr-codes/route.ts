import { NextRequest, NextResponse } from 'next/server';
import { createAdminSupabaseClient } from '@/lib/supabase/server-only';
import { generateUniqueUserQRId, generateQRCodeDataURL } from '@/lib/qr-code-generator';
import { qrCodeCache } from '@/lib/qr-code-cache';
import { getUserQRCodeDirect, upsertUserQRCodeDirect } from '@/lib/database-direct';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, forceRegenerate = false } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log(`🔄 QR code request for user ${userId}, forceRegenerate: ${forceRegenerate}`);

    // Try cache first if not forcing regeneration
    if (!forceRegenerate) {
      const cachedQR = await qrCodeCache.getQRCode(userId);
      if (cachedQR) {
        console.log(`🎯 Returning cached QR code for user ${userId}`);
        return NextResponse.json({
          success: true,
          qrCodeUrl: cachedQR.qrCodeUrl,
          message: 'QR code from cache'
        });
      }
    }

    // If forced regeneration, invalidate cache
    if (forceRegenerate) {
      qrCodeCache.invalidateUser(userId);
    }

    // Try direct database access first
    let existingQR = null;
    if (!forceRegenerate) {
      existingQR = await getUserQRCodeDirect(userId);
      if (existingQR?.qr_code_url) {
        console.log(`📋 Found existing QR code via direct database for user ${userId}`);
        return NextResponse.json({
          success: true,
          qrCodeUrl: existingQR.qr_code_url,
          message: 'QR code already exists'
        });
      }
    }

    // Need to generate new QR code - get user profile
    const supabase = createAdminSupabaseClient();
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, user_email, first_name, last_name')
      .eq('id', userId)
      .maybeSingle();

    if (profileError || !profile) {
      console.error('Profile fetch error:', profileError);
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Generate new QR code
    const qrData = generateUniqueUserQRId(userId, profile.user_email);
    const qrCodeDataUrl = await generateQRCodeDataURL(qrData, {
      size: 256,
      color: {
        dark: '#1f2937', // gray-800
        light: '#ffffff'
      }
    });

    // Save to database using direct connection first
    const directResult = await upsertUserQRCodeDirect(userId, qrData, qrCodeDataUrl);
    
    if (!directResult) {
      // Fallback to Supabase client if direct fails
      console.warn('Direct QR code save failed, falling back to Supabase client');
      const { error: upsertError } = await supabase
        .from('user_qr_codes')
        .upsert({
          user_id: userId,
          qr_data: qrData,
          qr_code_url: qrCodeDataUrl,
          is_active: true,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (upsertError) {
        console.error('Error saving QR code via Supabase client:', upsertError);
        return NextResponse.json(
          { error: 'Failed to save QR code' },
          { status: 500 }
        );
      }
    }

    // Invalidate cache to ensure fresh data on next request
    qrCodeCache.invalidateUser(userId);

    return NextResponse.json({
      success: true,
      qrCodeUrl: qrCodeDataUrl,
      message: forceRegenerate ? 'QR code regenerated successfully' : 'QR code generated successfully'
    });

  } catch (error) {
    console.error('QR Code API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Bulk generate QR codes for all users without them
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const adminKey = searchParams.get('adminKey');

    // Simple admin key check (in production, use proper authentication)
    if (adminKey !== 'fuse-admin-2024') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use admin client for bulk operations
    const supabase = createAdminSupabaseClient();

    // Get all users and filter out those who already have QR codes
    const { data: allUsers, error: allUsersError } = await supabase
      .from('profiles')
      .select('id, user_email');

    if (allUsersError) {
      console.error('Error fetching all users:', allUsersError);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }

    // Get users who already have QR codes
    const { data: usersWithQR } = await supabase
      .from('user_qr_codes')
      .select('user_id')
      .eq('is_active', true);

    const userIdsWithQR = new Set(usersWithQR?.map(u => u.user_id) || []);

    // Filter out users who already have QR codes
    const usersWithoutQR = allUsers?.filter(user => !userIdsWithQR.has(user.id)) || [];
    const usersError = null;

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      );
    }

    if (!usersWithoutQR || usersWithoutQR.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All users already have QR codes',
        generated: 0
      });
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    // Generate QR codes for each user
    for (const user of usersWithoutQR) {
      try {
        const qrData = generateUniqueUserQRId(user.id, user.user_email);
        const qrCodeDataUrl = await generateQRCodeDataURL(qrData, { 
          size: 256,
          color: {
            dark: '#1f2937',
            light: '#ffffff'
          }
        });

        const { error } = await supabase
          .from('user_qr_codes')
          .insert({
            user_id: user.id,
            qr_data: qrData,
            qr_code_url: qrCodeDataUrl,
            is_active: true
          });

        if (error) {
          console.error(`Error saving QR code for user ${user.id}:`, error);
          results.push({ userId: user.id, success: false, error: error.message });
          errorCount++;
        } else {
          results.push({ userId: user.id, success: true });
          successCount++;
        }
      } catch (error) {
        console.error(`Error generating QR code for user ${user.id}:`, error);
        results.push({ 
          userId: user.id, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
        errorCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `Generated QR codes for ${successCount} users`,
      generated: successCount,
      errors: errorCount,
      results: results
    });

  } catch (error) {
    console.error('Bulk generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
