import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Try multiple potential API endpoints for FUSE/XRP rate
    const endpoints = [
      'https://api.xmagnetic.org/v1/ticker/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP',
      'https://api.xmagnetic.org/dex/ticker/FUSE+rs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP+XRP',
      'https://xmagnetic.org/api/ticker/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP'
    ]

    let rateData = null
    let sourceEndpoint = null

    // Try each endpoint until we get valid data
    for (const endpoint of endpoints) {
      try {
        console.log(`Trying endpoint: ${endpoint}`)
        
        const response = await fetch(endpoint, {
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Fuse-VIP/1.0'
          },
          next: { revalidate: 300 } // Cache for 5 minutes
        })

        if (response.ok) {
          const data = await response.json()
          console.log(`Response from ${endpoint}:`, data)
          
          // Check for various possible data structures
          if (data.last_price || data.price || data.rate || data.close || data.last) {
            rateData = {
              price: data.last_price || data.price || data.rate || data.close || data.last,
              volume: data.volume || data.volume_24h || data.baseVolume,
              high: data.high || data.high_24h,
              low: data.low || data.low_24h,
              timestamp: data.timestamp || Date.now()
            }
            sourceEndpoint = endpoint
            break
          }
        }
      } catch (error) {
        console.warn(`Failed to fetch from ${endpoint}:`, error)
        continue
      }
    }

    // If no rate found from DEX, use fallback calculation
    if (!rateData) {
      console.log('No DEX data found, using fallback calculation')
      
      // Get XRP price from CoinGecko
      let xrpUsd = 2.4 // Fallback
      try {
        const xrpResponse = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=ripple&vs_currencies=usd')
        const xrpData = await xrpResponse.json()
        if (xrpData.ripple?.usd) {
          xrpUsd = xrpData.ripple.usd
        }
      } catch (error) {
        console.warn('Failed to fetch XRP price:', error)
      }

      // Use fallback rate based on provided example: 10 XRP = 13941 FUSE
      const fallbackFusePerXrp = 13941 / 10 // 1394.1 FUSE per XRP
      
      rateData = {
        price: fallbackFusePerXrp,
        volume: 0,
        high: fallbackFusePerXrp * 1.05,
        low: fallbackFusePerXrp * 0.95,
        timestamp: Date.now(),
        fallback: true
      }
      sourceEndpoint = 'fallback'
    }

    return NextResponse.json({
      success: true,
      data: rateData,
      source: sourceEndpoint,
      timestamp: Date.now(),
      pair: 'FUSE/XRP',
      description: 'FUSE token (rs2G9J95qwL3yw241JTRdgms2hhcLouVHo) to XRP exchange rate'
    })

  } catch (error: any) {
    console.error('DEX rate fetch error:', error)
    
    // Return fallback data even on error
    return NextResponse.json({
      success: true,
      data: {
        price: 1394.1, // Fallback: 13941 FUSE per 10 XRP
        volume: 0,
        high: 1394.1 * 1.05,
        low: 1394.1 * 0.95,
        timestamp: Date.now(),
        fallback: true
      },
      source: 'error_fallback',
      timestamp: Date.now(),
      pair: 'FUSE/XRP',
      error: error.message
    })
  }
}