import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const uuid = searchParams.get('uuid');
    
    if (!uuid) {
      return NextResponse.json({ error: 'UUID is required' }, { status: 400 });
    }

    const apiKey = process.env.NEXT_PUBLIC_XUMM_API_KEY;
    const apiSecret = process.env.XUMM_API_SECRET;

    if (!apiKey || !apiSecret) {
      return NextResponse.json({ error: 'XUMM API credentials not configured' }, { status: 500 });
    }

    const response = await fetch(`https://xumm.app/api/v1/platform/payload/${uuid}`, {
      headers: {
        'X-API-Key': apiKey,
        'X-API-Secret': apiSecret,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`XUMM API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Extract wallet address from the response
    let walletAddress = null;
    if (data.response?.account) {
      walletAddress = data.response.account;
    }

    return NextResponse.json({
      success: true,
      walletAddress,
      payloadData: data,
    });
  } catch (error) {
    console.error('Error fetching XUMM payload:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payload data' },
      { status: 500 }
    );
  }
}