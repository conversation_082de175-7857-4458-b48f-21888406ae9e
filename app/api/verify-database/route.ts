import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-direct'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Starting comprehensive database verification...')
    
    const results: any = {
      timestamp: new Date().toISOString(),
      connection: false,
      tables: {},
      summary: {
        total_tables: 0,
        accessible_tables: 0,
        failed_tables: 0,
        total_records: 0
      }
    }

    // Test basic connection
    try {
      await executeQuery('SELECT 1 as test')
      results.connection = true
      console.log('✅ Database connection successful')
    } catch (error) {
      console.error('❌ Database connection failed:', error)
      return NextResponse.json({
        success: false,
        error: 'Database connection failed',
        details: error instanceof Error ? error.message : String(error)
      }, { status: 500 })
    }

    // List of all main tables to verify
    const tablesToCheck = [
      'profiles',
      'businesses', 
      'network_applications',
      'business_discount_codes',
      'user_qr_codes',
      'qr_interactions',
      'business_visits',
      'purchases',
      'physical_cards',
      'vip_codes',
      'vip_redemptions',
      'admin_users',
      'portal_roles'
    ]

    // Check each table
    for (const tableName of tablesToCheck) {
      results.summary.total_tables++
      
      try {
        // Check if table exists and get basic info
        const tableInfo = await executeQuery(`
          SELECT 
            COUNT(*) as record_count,
            pg_size_pretty(pg_total_relation_size('${tableName}')) as table_size
          FROM ${tableName}
        `)

        if (tableInfo.data && tableInfo.data[0]) {
          const recordCount = parseInt(tableInfo.data[0].record_count)
          results.tables[tableName] = {
            accessible: true,
            record_count: recordCount,
            table_size: tableInfo.data[0].table_size,
            error: null
          }
          results.summary.accessible_tables++
          results.summary.total_records += recordCount
          console.log(`✅ ${tableName}: ${recordCount} records`)
        } else {
          throw new Error('No data returned')
        }
      } catch (error) {
        results.tables[tableName] = {
          accessible: false,
          record_count: 0,
          table_size: 'N/A',
          error: error instanceof Error ? error.message : String(error)
        }
        results.summary.failed_tables++
        console.error(`❌ ${tableName}: ${error}`)
      }
    }

    // Additional verification queries
    try {
      // Check auth.users table (special case)
      const authUsers = await executeQuery('SELECT COUNT(*) as count FROM auth.users')
      if (authUsers.data && authUsers.data[0]) {
        results.tables['auth.users'] = {
          accessible: true,
          record_count: parseInt(authUsers.data[0].count),
          table_size: 'N/A',
          error: null
        }
        console.log(`✅ auth.users: ${authUsers.data[0].count} records`)
      }
    } catch (error) {
      results.tables['auth.users'] = {
        accessible: false,
        record_count: 0,
        table_size: 'N/A', 
        error: error instanceof Error ? error.message : String(error)
      }
      console.error(`❌ auth.users: ${error}`)
    }

    // Test some key relationships
    try {
      const relationshipTest = await executeQuery(`
        SELECT 
          p.id as profile_id,
          p.user_email,
          p.is_card_holder,
          COUNT(b.id) as business_count,
          COUNT(qi.id) as interaction_count
        FROM profiles p
        LEFT JOIN businesses b ON p.id = b.user_id
        LEFT JOIN qr_interactions qi ON p.id = qi.scanner_user_id
        GROUP BY p.id, p.user_email, p.is_card_holder
        LIMIT 5
      `)
      
      results.relationship_test = {
        success: true,
        sample_data: relationshipTest.data || []
      }
      console.log('✅ Relationship queries working')
    } catch (error) {
      results.relationship_test = {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
      console.error('❌ Relationship test failed:', error)
    }

    console.log(`📊 Database verification complete: ${results.summary.accessible_tables}/${results.summary.total_tables} tables accessible`)

    return NextResponse.json({
      success: true,
      message: `Database verification complete: ${results.summary.accessible_tables}/${results.summary.total_tables} tables accessible`,
      data: results
    })

  } catch (error) {
    console.error('💥 Database verification failed:', error)
    return NextResponse.json({
      success: false,
      error: 'Database verification failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
