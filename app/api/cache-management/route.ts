import { NextRequest, NextResponse } from 'next/server'
import { businessCache } from '@/lib/business-cache'
import { profilesCache } from '@/lib/profiles-cache'
import { qrCodeCache } from '@/lib/qr-code-cache'

interface CacheHealthStatus {
  service: string;
  status: 'healthy' | 'warning' | 'critical';
  stats: any;
  recommendations?: string[];
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'health'

    switch (action) {
      case 'health':
        return getCacheHealth()
      
      case 'stats':
        return getCacheStats()
      
      case 'summary':
        return getCacheSummary()
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: health, stats, summary' },
          { status: 400 }
        )
    }

  } catch (error: any) {
    console.error('Cache management GET error:', error)
    
    return NextResponse.json(
      { error: 'Cache management operation failed', details: error.message },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, services } = body
    
    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'refresh-all':
        return await refreshAllCaches(services)
      
      case 'clear-all':
        return clearAllCaches(services)
      
      case 'warm-up':
        return await warmUpCaches()
      
      case 'optimize':
        return await optimizeCaches()
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: refresh-all, clear-all, warm-up, optimize' },
          { status: 400 }
        )
    }

  } catch (error: any) {
    console.error('Cache management POST error:', error)
    
    return NextResponse.json(
      { error: 'Cache management operation failed', details: error.message },
      { status: 500 }
    )
  }
}

async function getCacheHealth(): Promise<NextResponse> {
  const businessStats = businessCache.getStats()
  const profilesStats = profilesCache.getStats()
  const qrStats = qrCodeCache.getStats()

  const healthChecks: CacheHealthStatus[] = [
    analyzeBusinessCacheHealth(businessStats),
    analyzeProfilesCacheHealth(profilesStats),
    analyzeQRCacheHealth(qrStats)
  ]

  const overallStatus = healthChecks.some(h => h.status === 'critical') ? 'critical' :
                       healthChecks.some(h => h.status === 'warning') ? 'warning' : 'healthy'

  return NextResponse.json({
    success: true,
    overallStatus,
    services: healthChecks,
    timestamp: new Date().toISOString(),
    recommendations: generateOverallRecommendations(healthChecks)
  })
}

async function getCacheStats(): Promise<NextResponse> {
  return NextResponse.json({
    success: true,
    stats: {
      business: businessCache.getStats(),
      profiles: profilesCache.getStats(),
      qrCodes: qrCodeCache.getStats()
    },
    timestamp: new Date().toISOString()
  })
}

async function getCacheSummary(): Promise<NextResponse> {
  const businessStats = businessCache.getStats()
  const profilesStats = profilesCache.getStats()
  const qrStats = qrCodeCache.getStats()

  const totalEntries = businessStats.totalEntries + profilesStats.totalEntries + qrStats.totalEntries
  const totalHits = businessStats.hitCount + profilesStats.hitCount + qrStats.hitCount
  const totalMisses = businessStats.missCount + profilesStats.missCount + qrStats.missCount
  const totalRequests = totalHits + totalMisses
  const overallHitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0

  return NextResponse.json({
    success: true,
    summary: {
      totalEntries,
      totalRequests,
      overallHitRate: Math.round(overallHitRate * 100) / 100,
      services: {
        business: {
          entries: businessStats.totalEntries,
          hitRate: Math.round(businessStats.hitRate * 100) / 100,
          lastRefresh: businessStats.lastRefresh
        },
        profiles: {
          entries: profilesStats.totalEntries,
          hitRate: Math.round(profilesStats.hitRate * 100) / 100,
          lastRefresh: profilesStats.lastRefresh
        },
        qrCodes: {
          entries: qrStats.totalEntries,
          hitRate: Math.round(qrStats.hitRate * 100) / 100
        }
      }
    },
    timestamp: new Date().toISOString()
  })
}

async function refreshAllCaches(services?: string[]): Promise<NextResponse> {
  const servicesToRefresh = services || ['business', 'profiles']
  const results: any = {}

  try {
    if (servicesToRefresh.includes('business')) {
      console.log('🔄 Refreshing business cache...')
      await businessCache.refresh()
      results.business = { success: true, stats: businessCache.getStats() }
    }

    if (servicesToRefresh.includes('profiles')) {
      console.log('🔄 Refreshing profiles cache...')
      await profilesCache.refresh()
      results.profiles = { success: true, stats: profilesCache.getStats() }
    }

    if (servicesToRefresh.includes('qrCodes')) {
      console.log('🔄 Clearing QR cache (no refresh method)...')
      qrCodeCache.clear()
      results.qrCodes = { success: true, stats: qrCodeCache.getStats() }
    }

    return NextResponse.json({
      success: true,
      message: `Refreshed ${servicesToRefresh.join(', ')} cache(s)`,
      results,
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Cache refresh failed',
      details: error.message,
      results
    }, { status: 500 })
  }
}

function clearAllCaches(services?: string[]): NextResponse {
  const servicesToClear = services || ['business', 'profiles', 'qrCodes']
  const results: any = {}

  try {
    if (servicesToClear.includes('business')) {
      businessCache.clear()
      results.business = { success: true, message: 'Business cache cleared' }
    }

    if (servicesToClear.includes('profiles')) {
      profilesCache.clear()
      results.profiles = { success: true, message: 'Profiles cache cleared' }
    }

    if (servicesToClear.includes('qrCodes')) {
      qrCodeCache.clear()
      results.qrCodes = { success: true, message: 'QR codes cache cleared' }
    }

    return NextResponse.json({
      success: true,
      message: `Cleared ${servicesToClear.join(', ')} cache(s)`,
      results,
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Cache clear failed',
      details: error.message,
      results
    }, { status: 500 })
  }
}

async function warmUpCaches(): Promise<NextResponse> {
  try {
    console.log('🔥 Warming up caches...')
    
    // Warm up business cache by fetching all businesses
    await businessCache.getAllBusinesses()
    
    // Note: Profiles cache warms up on-demand since we don't want to load all user profiles
    
    return NextResponse.json({
      success: true,
      message: 'Cache warm-up completed',
      stats: {
        business: businessCache.getStats(),
        profiles: profilesCache.getStats(),
        qrCodes: qrCodeCache.getStats()
      },
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Cache warm-up failed',
      details: error.message
    }, { status: 500 })
  }
}

async function optimizeCaches(): Promise<NextResponse> {
  try {
    console.log('⚡ Optimizing caches...')
    
    const actions = []
    
    // Check business cache performance
    const businessStats = businessCache.getStats()
    if (businessStats.hitRate < 50) {
      await businessCache.refresh()
      actions.push('Refreshed business cache due to low hit rate')
    }
    
    // Check profiles cache performance
    const profilesStats = profilesCache.getStats()
    if (profilesStats.hitRate < 30) {
      // For profiles, we don't refresh all, but we note the issue
      actions.push('Profiles cache has low hit rate - consider warming up frequently used profiles')
    }
    
    // Check QR cache
    const qrStats = qrCodeCache.getStats()
    if (qrStats.hitRate < 40) {
      actions.push('QR cache has low hit rate - operating normally for on-demand loading')
    }
    
    return NextResponse.json({
      success: true,
      message: 'Cache optimization completed',
      actions,
      stats: {
        business: businessCache.getStats(),
        profiles: profilesCache.getStats(),
        qrCodes: qrCodeCache.getStats()
      },
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Cache optimization failed',
      details: error.message
    }, { status: 500 })
  }
}

function analyzeBusinessCacheHealth(stats: any): CacheHealthStatus {
  const recommendations = []
  let status: 'healthy' | 'warning' | 'critical' = 'healthy'

  if (stats.hitRate < 50) {
    status = 'warning'
    recommendations.push('Hit rate below 50% - consider refreshing cache or checking query patterns')
  }

  if (stats.hitRate < 25) {
    status = 'critical'
    recommendations.push('Critical: Hit rate below 25% - cache may not be working effectively')
  }

  if (stats.totalEntries === 0) {
    status = 'warning'
    recommendations.push('No cached entries - cache needs to be warmed up')
  }

  return {
    service: 'business',
    status,
    stats,
    recommendations: recommendations.length > 0 ? recommendations : undefined
  }
}

function analyzeProfilesCacheHealth(stats: any): CacheHealthStatus {
  const recommendations = []
  let status: 'healthy' | 'warning' | 'critical' = 'healthy'

  if (stats.hitRate < 30) {
    status = 'warning'
    recommendations.push('Hit rate below 30% - profiles cache is working on-demand, consider preloading frequent users')
  }

  if (stats.hitRate < 15) {
    status = 'critical'
    recommendations.push('Critical: Hit rate below 15% - check if profiles cache is functioning properly')
  }

  return {
    service: 'profiles',
    status,
    stats,
    recommendations: recommendations.length > 0 ? recommendations : undefined
  }
}

function analyzeQRCacheHealth(stats: any): CacheHealthStatus {
  const recommendations = []
  let status: 'healthy' | 'warning' | 'critical' = 'healthy'

  if (stats.hitRate < 40) {
    status = 'warning'
    recommendations.push('Hit rate below 40% - QR cache is demand-driven, this may be normal')
  }

  return {
    service: 'qrCodes',
    status,
    stats,
    recommendations: recommendations.length > 0 ? recommendations : undefined
  }
}

function generateOverallRecommendations(healthChecks: CacheHealthStatus[]): string[] {
  const recommendations = []
  
  const criticalServices = healthChecks.filter(h => h.status === 'critical')
  const warningServices = healthChecks.filter(h => h.status === 'warning')
  
  if (criticalServices.length > 0) {
    recommendations.push(`Critical issues detected in: ${criticalServices.map(s => s.service).join(', ')}`)
  }
  
  if (warningServices.length > 0) {
    recommendations.push(`Performance warnings in: ${warningServices.map(s => s.service).join(', ')}`)
  }
  
  if (criticalServices.length === 0 && warningServices.length === 0) {
    recommendations.push('All cache services are operating optimally')
  }
  
  return recommendations
}