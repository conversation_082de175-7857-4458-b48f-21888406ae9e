import { NextRequest, NextResponse } from 'next/server';
import { directStorageService } from '@/lib/direct-storage-service';

export async function POST(request: NextRequest) {
  try {
    console.log('📤 Processing direct storage upload...');

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const bucket = formData.get('bucket') as string || 'business-assets';
    const pathPrefix = formData.get('pathPrefix') as string || 'uploads';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size must be less than 5MB' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'File must be an image (JPEG, PNG, WebP, or SVG)' },
        { status: 400 }
      );
    }

    // Generate unique filename
    const timestamp = Date.now();
    const fileExt = file.name.split('.').pop();
    const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const fileName = `${pathPrefix}/${timestamp}_${sanitizedName}`;

    console.log(`🔄 Uploading file: ${fileName} to bucket: ${bucket}`);

    // Upload using direct storage service
    const { data, error } = await directStorageService.uploadFile(
      bucket,
      fileName,
      file,
      {
        contentType: file.type,
        upsert: true,
        cacheControl: '3600'
      }
    );

    if (error) {
      console.error('❌ Upload failed:', error);
      return NextResponse.json(
        { error: error },
        { status: 500 }
      );
    }

    // Get public URL
    const publicUrl = directStorageService.getPublicUrl(bucket, fileName);

    console.log('✅ Upload successful:', publicUrl);

    return NextResponse.json({
      success: true,
      data: {
        url: publicUrl,
        path: fileName,
        bucket: bucket,
        size: file.size,
        type: file.type
      }
    });

  } catch (error: any) {
    console.error('❌ Storage upload API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Checking storage service health...');

    // Health check
    const isHealthy = await directStorageService.healthCheck();
    const stats = directStorageService.getStats();

    if (!isHealthy) {
      return NextResponse.json(
        { 
          error: 'Storage service unhealthy',
          stats
        },
        { status: 503 }
      );
    }

    // Try to list buckets
    const { data: buckets, error } = await directStorageService.listBuckets();

    return NextResponse.json({
      success: true,
      healthy: isHealthy,
      stats,
      buckets: buckets || [],
      bucketsError: error
    });

  } catch (error: any) {
    console.error('❌ Storage health check error:', error);
    
    return NextResponse.json(
      { 
        error: 'Health check failed',
        details: error.message 
      },
      { status: 500 }
    );
  }
}