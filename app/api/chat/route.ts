import { NextRequest, NextResponse } from 'next/server';
import { PerplexityClient, checkRateLimit } from '@/lib/perplexity';
import { getContextualPrompt } from '@/lib/fuse-context';
import { getAuthenticatedUser } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import { ConversationManager } from '@/lib/conversation-manager';
import { SequentialThinking } from '@/lib/sequential-thinking';
import { chatMonitor } from '@/lib/monitoring';
import DOMPurify from 'isomorphic-dompurify';

// Utility function to sanitize content from AI responses
function sanitizeAIResponse(content: string): string {
  return DOMPurify.sanitize(content, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li', 'code', 'pre', 'blockquote', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
    ALLOWED_ATTR: ['class'],
    ALLOW_DATA_ATTR: false,
    FORBID_SCRIPT: true,
    FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'textarea', 'select', 'button', 'iframe', 'frame'],
    FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur', 'onchange', 'onsubmit', 'href', 'src'],
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_DOM_IMPORT: false
  });
}

// Utility function to validate and sanitize user input
function sanitizeUserInput(input: string): string {
  if (!input || typeof input !== 'string') {
    throw new Error('Invalid input');
  }

  // Remove any HTML tags from user input
  const sanitized = DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  });

  // Additional validation
  if (sanitized.length > 4000) {
    throw new Error('Input too long');
  }

  return sanitized.trim();
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Get user IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';

    // Check rate limit
    if (!checkRateLimit(ip)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      );
    }

    const { message, sessionId, conversationHistory = [] } = await request.json();

    // Validate and sanitize user input
    let sanitizedMessage: string;
    try {
      sanitizedMessage = sanitizeUserInput(message);
    } catch (error) {
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Invalid message format' },
        { status: 400 }
      );
    }

    // Generate session ID if not provided
    const currentSessionId = sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Initialize conversation manager
    const conversationManager = ConversationManager.getInstance();

    // Get Perplexity API key
    const apiKey = process.env.PERPLEXITY_API_KEY;
    if (!apiKey) {
      console.error('Perplexity API key not configured');
      return NextResponse.json(
        { error: 'Chat service not configured' },
        { status: 500 }
      );
    }

    // Initialize Perplexity client and sequential thinking
    const perplexity = new PerplexityClient(apiKey);
    const sequentialThinking = new SequentialThinking(apiKey);

    // Analyze user intent using sanitized message
    const intentAnalysis = await conversationManager.analyzeIntent(sanitizedMessage);
    const isComplex = await sequentialThinking.isComplexQuery(sanitizedMessage);
    
    // Get user context if authenticated
    let userContext = '';
    let userId: string | undefined;
    try {
      const user = await getAuthenticatedUser();
      if (user) {
        userId = user.id;
        userContext = `The user is authenticated with email: ${user.email}. You can provide personalized responses and mention their account status.`;
      }
    } catch (error) {
      // User not authenticated, continue without user context
      console.log('User not authenticated for chat');
    }

    // Get conversation history
    const history = await conversationManager.getConversationHistory(currentSessionId);
    
    // Add user message to conversation (using sanitized message)
    await conversationManager.addMessage(currentSessionId, {
      role: 'user',
      content: sanitizedMessage,
      timestamp: new Date(),
      metadata: { intent: intentAnalysis.intent, confidence: intentAnalysis.confidence }
    });

    let response;
    let assistantMessage: string;

    // Use sequential thinking for complex queries
    if (isComplex) {
      const fuseContext = getContextualPrompt(sanitizedMessage, userContext, history);
      const thinkingProcess = await sequentialThinking.processComplexQuery(sanitizedMessage, fuseContext, history);
      assistantMessage = thinkingProcess.finalConclusion;
      response = { id: `thinking_${Date.now()}`, usage: { total_tokens: 0, prompt_tokens: 0, completion_tokens: 0 } };
    } else {
      // Standard response for simple queries
      const systemPrompt = getContextualPrompt(sanitizedMessage, userContext, history);
      const messages = [
        {
          role: 'system' as const,
          content: systemPrompt
        },
        {
          role: 'user' as const,
          content: sanitizedMessage
        }
      ];

      response = await perplexity.chat(messages);
      assistantMessage = response.choices[0].message.content;
    }

    if (!isComplex && (!response.choices || response.choices.length === 0)) {
      throw new Error('No response from Perplexity API');
    }

    // Track conversation metrics
    const responseTime = Date.now() - startTime;
    const tokenUsage = response.usage?.total_tokens || 0;
    
    chatMonitor.trackConversation(currentSessionId, intentAnalysis.intent, tokenUsage, isComplex);
    
    // Log critical support requests
    if (intentAnalysis.requiresEscalation || assistantMessage.includes('<EMAIL>')) {
      chatMonitor.trackEscalation(currentSessionId, userId, message, intentAnalysis.intent);
    }

    // Add assistant response to conversation
    await conversationManager.addMessage(currentSessionId, {
      role: 'assistant',
      content: assistantMessage,
      timestamp: new Date(),
      metadata: { 
        perplexityId: response.id,
        usage: response.usage,
        intent: intentAnalysis.intent,
        escalationRequired: intentAnalysis.requiresEscalation,
        usedSequentialThinking: isComplex
      }
    });

    return NextResponse.json({
      message: assistantMessage,
      usage: response.usage,
      conversationId: response.id,
      sessionId: currentSessionId,
      intent: intentAnalysis.intent,
      requiresEscalation: intentAnalysis.requiresEscalation,
      usedSequentialThinking: isComplex
    });

  } catch (error) {
    console.error('Chat API error:', error);
    
    // Return a helpful fallback response
    const fallbackMessage = `I'm experiencing technical difficulties, but I'm excited to help you discover the future of loyalty rewards!

🚀 **Fuse.vip is revolutionizing customer loyalty** with blockchain technology on the XRP Ledger. Our $FUSE token is now live, opening unprecedented opportunities for businesses and customers alike.

**For immediate assistance:**
- Email: <EMAIL>
- Discord: https://discord.gg/n9d7PEbm  
- Twitter: @fuserewards

**Why the future is bright with Fuse.vip:**
- Cross-business rewards network
- Token-gated VIP experiences
- Blockchain-secured loyalty programs
- Enterprise-grade automation for small businesses

Join us in shaping the future of customer engagement! 🌟`;

    return NextResponse.json({
      message: fallbackMessage,
      error: 'Service temporarily unavailable'
    }, { status: 200 }); // Return 200 to show fallback message
  }
}

export async function GET() {
  return NextResponse.json({
    status: 'Chat API is running',
    endpoints: {
      POST: '/api/chat - Send a chat message',
    },
    rateLimit: '10 requests per minute per IP'
  });
}
