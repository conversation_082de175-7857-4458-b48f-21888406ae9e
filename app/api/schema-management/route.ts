import { NextRequest, NextResponse } from 'next/server'
import { 
  reloadPostgRESTSchema, 
  checkSchemaHealth, 
  autoReloadSchema,
  emergencySchemaReset,
  monitorAndMaintainSchema
} from '@/lib/schema-management'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)

// Rate limiting for schema operations
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(ip: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const userLimit = rateLimitMap.get(ip)
  
  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (userLimit.count >= maxRequests) {
    return false
  }
  
  userLimit.count++
  return true
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'health'
    
    // Get client IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 'unknown'
    
    if (!checkRateLimit(ip, 20, 60000)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    switch (action) {
      case 'health':
        const healthCheck = await checkSchemaHealth()
        return NextResponse.json({
          success: true,
          action: 'health_check',
          data: healthCheck,
          timestamp: new Date().toISOString()
        })

      case 'monitor':
        const { data: monitorData, error: monitorError } = await supabaseAdmin
          .rpc('monitor_schema_issues')
        
        if (monitorError) {
          throw new Error(`Monitor failed: ${monitorError.message}`)
        }

        return NextResponse.json({
          success: true,
          action: 'monitor',
          data: monitorData,
          timestamp: new Date().toISOString()
        })

      case 'logs':
        const { data: logs, error: logsError } = await supabaseAdmin
          .from('schema_reload_log')
          .select('*')
          .order('triggered_at', { ascending: false })
          .limit(50)
        
        if (logsError) {
          throw new Error(`Failed to fetch logs: ${logsError.message}`)
        }

        return NextResponse.json({
          success: true,
          action: 'logs',
          data: logs,
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: health, monitor, or logs' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Schema management GET error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, force = false } = await request.json()
    
    // Get client IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 'unknown'
    
    if (!checkRateLimit(ip, 5, 60000)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded for POST operations' },
        { status: 429 }
      )
    }

    console.log(`🔧 Schema management action: ${action}`)

    switch (action) {
      case 'reload':
        const reloadResult = await reloadPostgRESTSchema()
        return NextResponse.json({
          success: reloadResult.success,
          action: 'reload',
          data: reloadResult,
          timestamp: new Date().toISOString()
        })

      case 'auto-reload':
        const autoResult = await autoReloadSchema()
        return NextResponse.json({
          success: autoResult.success,
          action: 'auto-reload',
          data: autoResult,
          timestamp: new Date().toISOString()
        })

      case 'emergency-reset':
        if (!force) {
          return NextResponse.json(
            { error: 'Emergency reset requires force=true parameter' },
            { status: 400 }
          )
        }
        
        const emergencyResult = await emergencySchemaReset()
        return NextResponse.json({
          success: emergencyResult.success,
          action: 'emergency-reset',
          data: emergencyResult,
          timestamp: new Date().toISOString()
        })

      case 'maintain':
        await monitorAndMaintainSchema()
        return NextResponse.json({
          success: true,
          action: 'maintain',
          message: 'Schema maintenance completed',
          timestamp: new Date().toISOString()
        })

      case 'notify-direct':
        // Direct NOTIFY command execution
        const { error: notifyError } = await supabaseAdmin
          .rpc('notify_schema_reload')
        
        if (notifyError) {
          throw new Error(`Direct notify failed: ${notifyError.message}`)
        }

        return NextResponse.json({
          success: true,
          action: 'notify-direct',
          message: 'Direct NOTIFY command executed',
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: reload, auto-reload, emergency-reset, maintain, or notify-direct' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Schema management POST error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Handle PATCH for configuration updates
export async function PATCH(request: NextRequest) {
  try {
    const { exposed_schemas } = await request.json()
    
    // This would ideally update Supabase configuration
    // For now, we'll log the request and provide guidance
    console.log('🔧 Schema configuration update requested:', { exposed_schemas })
    
    if (exposed_schemas && Array.isArray(exposed_schemas)) {
      if (exposed_schemas.length === 1 && exposed_schemas[0] === 'public') {
        return NextResponse.json({
          success: true,
          message: 'Configuration update requested: expose only public schema',
          recommendation: 'This is the optimal configuration for fixing PGRST002 errors',
          next_steps: [
            'Go to Supabase Dashboard → Settings → API',
            'Update "Exposed schemas" to only include "public"',
            'Save configuration',
            'Wait 2-3 minutes for changes to propagate',
            'Call POST /api/schema-management with action=reload'
          ],
          timestamp: new Date().toISOString()
        })
      } else {
        return NextResponse.json({
          success: false,
          message: 'Suboptimal configuration detected',
          warning: `Exposing ${exposed_schemas.length} schemas may cause PGRST002 errors`,
          recommendation: 'Use only ["public"] for optimal performance',
          current_request: exposed_schemas,
          timestamp: new Date().toISOString()
        })
      }
    }

    return NextResponse.json(
      { error: 'Invalid configuration. Provide exposed_schemas array.' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Schema configuration PATCH error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
