import { NextRequest, NextResponse } from 'next/server'
import { superUserService } from '@/lib/superuser-service'

/**
 * SuperUser API Route for Profiles
 * Uses service role key to bypass RLS and fetch all profiles
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const cardHoldersOnly = searchParams.get('cardHolders') === 'true'
    const businessApplicantsOnly = searchParams.get('businessApplicants') === 'true'
    const useCache = searchParams.get('cache') !== 'false'

    console.log('🔐 SuperUser API: Fetching profiles...', {
      cardHoldersOnly,
      businessApplicantsOnly,
      useCache
    })

    let profiles

    if (cardHoldersOnly) {
      profiles = await superUserService.getCardHolders(useCache)
    } else if (businessApplicantsOnly) {
      profiles = await superUserService.getBusinessApplicants(useCache)
    } else {
      profiles = await superUserService.getAllProfiles(useCache)
    }

    console.log(`✅ SuperUser API: Successfully fetched ${profiles.length} profiles`)

    return NextResponse.json({
      success: true,
      data: profiles,
      count: profiles.length,
      source: 'superuser_service',
      timestamp: new Date().toISOString(),
      cache: useCache ? 'enabled' : 'disabled'
    })

  } catch (error) {
    console.error('❌ SuperUser API: Error fetching profiles:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch profiles',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * Clear profiles cache
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const clearAll = searchParams.get('all') === 'true'

    if (clearAll) {
      superUserService.clearCache()
      console.log('🗑️ SuperUser API: All cache cleared')
    } else {
      superUserService.clearCacheEntry('all_profiles')
      console.log('🗑️ SuperUser API: Profiles cache cleared')
    }

    return NextResponse.json({
      success: true,
      message: clearAll ? 'All cache cleared' : 'Profiles cache cleared',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ SuperUser API: Error clearing cache:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to clear cache',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
