import { NextRequest, NextResponse } from 'next/server'
import { superUserService } from '@/lib/superuser-service'

/**
 * SuperUser API Route for All Data
 * Fetches both businesses and profiles in a single request
 * Uses service role key to bypass RLS
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const useCache = searchParams.get('cache') !== 'false'
    const activeBusinessesOnly = searchParams.get('activeBusinesses') === 'true'
    const cardHoldersOnly = searchParams.get('cardHolders') === 'true'

    console.log('🔐 SuperUser API: Fetching all data...', {
      useCache,
      activeBusinessesOnly,
      cardHoldersOnly
    })

    // Fetch businesses and profiles concurrently
    const [businesses, profiles] = await Promise.all([
      activeBusinessesOnly 
        ? superUserService.getActiveBusinesses(useCache)
        : superUserService.getAllBusinesses(useCache),
      cardHoldersOnly
        ? superUserService.getCardHolders(useCache)
        : superUserService.getAllProfiles(useCache)
    ])

    console.log(`✅ SuperUser API: Successfully fetched ${businesses.length} businesses and ${profiles.length} profiles`)

    // Create summary statistics
    const stats = {
      businesses: {
        total: businesses.length,
        active: businesses.filter(b => b.is_active).length,
        inactive: businesses.filter(b => !b.is_active).length
      },
      profiles: {
        total: profiles.length,
        cardHolders: profiles.filter(p => p.is_card_holder).length,
        businessApplicants: profiles.filter(p => p.is_business_applicant).length
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        businesses,
        profiles
      },
      stats,
      source: 'superuser_service',
      timestamp: new Date().toISOString(),
      cache: useCache ? 'enabled' : 'disabled'
    })

  } catch (error) {
    console.error('❌ SuperUser API: Error fetching all data:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch all data',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * Get cache statistics
 */
export async function POST(request: NextRequest) {
  try {
    const cacheStats = superUserService.getCacheStats()

    return NextResponse.json({
      success: true,
      cache: cacheStats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ SuperUser API: Error getting cache stats:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to get cache stats',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
