import { NextRequest, NextResponse } from 'next/server'
import { superUserService } from '@/lib/superuser-service'

/**
 * SuperUser API Route for Businesses
 * Uses service role key to bypass RLS and fetch all businesses
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const activeOnly = searchParams.get('active') === 'true'
    const withOwners = searchParams.get('withOwners') === 'true'
    const useCache = searchParams.get('cache') !== 'false'

    console.log('🔐 SuperUser API: Fetching businesses...', {
      activeOnly,
      withOwners,
      useCache
    })

    let businesses

    if (withOwners) {
      businesses = await superUserService.getBusinessesWithOwners(useCache)
    } else if (activeOnly) {
      businesses = await superUserService.getActiveBusinesses(useCache)
    } else {
      businesses = await superUserService.getAllBusinesses(useCache)
    }

    console.log(`✅ SuperUser API: Successfully fetched ${businesses.length} businesses`)

    return NextResponse.json({
      success: true,
      data: businesses,
      count: businesses.length,
      source: 'superuser_service',
      timestamp: new Date().toISOString(),
      cache: useCache ? 'enabled' : 'disabled'
    })

  } catch (error) {
    console.error('❌ SuperUser API: Error fetching businesses:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch businesses',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * Clear businesses cache
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const clearAll = searchParams.get('all') === 'true'

    if (clearAll) {
      superUserService.clearCache()
      console.log('🗑️ SuperUser API: All cache cleared')
    } else {
      superUserService.clearCacheEntry('all_businesses')
      superUserService.clearCacheEntry('businesses_with_owners')
      console.log('🗑️ SuperUser API: Business cache entries cleared')
    }

    return NextResponse.json({
      success: true,
      message: clearAll ? 'All cache cleared' : 'Business cache cleared',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ SuperUser API: Error clearing cache:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to clear cache',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
