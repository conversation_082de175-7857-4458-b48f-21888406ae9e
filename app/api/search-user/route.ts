import { NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-direct'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    const userId = searchParams.get('user_id')
    
    if (!email && !userId) {
      return NextResponse.json(
        { error: 'Please provide either email or user_id parameter' },
        { status: 400 }
      )
    }
    
    let query = `
      SELECT 
        id,
        first_name,
        last_name,
        user_email,
        is_card_holder,
        is_business_applicant,
        phone,
        xrp_wallet_address,
        card_tier,
        membership_start_date,
        membership_end_date,
        referring_business_id,
        created_at
      FROM profiles 
    `
    
    let params = []
    
    if (email) {
      query += ` WHERE user_email ILIKE $1`
      params = [`%${email}%`]
    } else if (userId) {
      query += ` WHERE id = $1`
      params = [userId]
    }
    
    query += ` ORDER BY created_at DESC`
    
    const { data: profiles, error } = await executeQuery(query, params)
    
    if (error) {
      throw new Error(`Error searching for user: ${error}`)
    }
    
    return NextResponse.json({
      success: true,
      profiles: profiles || [],
      searchCriteria: email ? `email: ${email}` : `user_id: ${userId}`,
      message: profiles && profiles.length > 0 
        ? `Found ${profiles.length} user(s)` 
        : 'No users found matching the search criteria'
    })
    
  } catch (error) {
    console.error('❌ Error searching for user:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to search for user', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    )
  }
}