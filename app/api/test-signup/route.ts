import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { email, password, metadata } = await request.json()

    console.log('🔍 Test signup API called:', { email, metadata })

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    // Test sign up with email confirmation disabled for testing
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata || {},
        emailRedirectTo: undefined // Disable email confirmation for testing
      }
    })

    console.log('📊 Supabase signup result:', { 
      success: !error, 
      userId: data?.user?.id,
      userEmail: data?.user?.email,
      error: error?.message 
    })

    if (error) {
      console.error('❌ Test signup error:', error)
      return NextResponse.json({
        success: false,
        error: error.message,
        details: {
          code: error.status,
          message: error.message
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: data.user?.id,
          email: data.user?.email,
          created_at: data.user?.created_at
        },
        session: data.session ? {
          access_token: data.session.access_token ? 'present' : 'missing'
        } : null
      }
    })

  } catch (error: any) {
    console.error('❌ Test signup exception:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error.message
    }, { status: 500 })
  }
}
