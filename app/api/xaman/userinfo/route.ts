import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

/**
 * XAMAN OAuth2 Userinfo API Endpoint
 * 
 * Provides server-side access to cached user account information
 * and handles syncing with user profiles in the database.
 * 
 * This complements the client-side caching service and provides
 * a reliable way to store and retrieve user account info.
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const walletAddress = searchParams.get('walletAddress')

    if (!userId && !walletAddress) {
      return NextResponse.json(
        { error: 'Either userId or walletAddress is required' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    let profile = null

    if (userId) {
      // Get user profile by user ID
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = not found
        console.error('❌ Error fetching user profile:', error)
        return NextResponse.json(
          { error: 'Failed to fetch user profile' },
          { status: 500 }
        )
      }

      profile = data
    } else if (walletAddress) {
      // Get user profile by wallet address
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('xrp_wallet_address', walletAddress)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = not found
        console.error('❌ Error fetching user profile by wallet:', error)
        return NextResponse.json(
          { error: 'Failed to fetch user profile' },
          { status: 500 }
        )
      }

      profile = data
    }

    if (!profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      )
    }

    // Return cached user info from profile
    const userInfo = {
      userId: profile.id,
      account: profile.xrp_wallet_address,
      walletConnectedAt: profile.wallet_connected_at,
      walletLastUsed: profile.wallet_last_used,
      xamanUserToken: profile.xaman_user_token,
      xamanTokenExpiresAt: profile.xaman_user_token_expires_at,
      xamanLastSuccessfulSign: profile.xaman_last_successful_sign
    }

    return NextResponse.json({
      success: true,
      userInfo
    })

  } catch (error) {
    console.error('❌ Error in userinfo API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      userId, 
      userInfo, 
      action = 'cache' // 'cache', 'sync_to_profile'
    } = body

    console.log('📝 Userinfo cache request:', { userId, action, hasUserInfo: !!userInfo })

    if (!userId) {
      return NextResponse.json(
        { error: 'userId is required' },
        { status: 400 }
      )
    }

    if (action === 'cache' && !userInfo) {
      return NextResponse.json(
        { error: 'userInfo is required for cache action' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    switch (action) {
      case 'cache':
        return await handleCacheUserInfo(supabase, userId, userInfo)
      case 'sync_to_profile':
        return await handleSyncToProfile(supabase, userId)
      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be: cache, sync_to_profile' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Error in userinfo POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function handleCacheUserInfo(supabase: any, userId: string, userInfo: any) {
  try {
    // Update user profile with cached info
    const updateData: any = {
      wallet_last_used: new Date().toISOString()
    }

    // Update wallet address if provided
    if (userInfo.account) {
      updateData.xrp_wallet_address = userInfo.account
      if (!updateData.wallet_connected_at) {
        updateData.wallet_connected_at = new Date().toISOString()
      }
    }

    // Update XAMAN user token if provided
    if (userInfo.sub) {
      // Store the XAMAN user ID as a reference
      updateData.xaman_user_token = userInfo.sub
      updateData.xaman_user_token_expires_at = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      updateData.xaman_last_successful_sign = new Date().toISOString()
    }

    const { data, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      console.error('❌ Error updating profile with cached info:', error)
      return NextResponse.json(
        { error: 'Failed to cache user info' },
        { status: 500 }
      )
    }

    console.log('✅ User info cached successfully in profile')
    return NextResponse.json({
      success: true,
      message: 'User info cached successfully',
      profile: data
    })

  } catch (error) {
    console.error('❌ Error caching user info:', error)
    return NextResponse.json(
      { error: 'Failed to cache user info' },
      { status: 500 }
    )
  }
}

async function handleSyncToProfile(supabase: any, userId: string) {
  try {
    // This would typically fetch fresh userinfo from OAuth2 endpoint
    // and sync it to the profile. For now, we'll just update the last used timestamp
    
    const { data, error } = await supabase
      .from('profiles')
      .update({
        wallet_last_used: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      console.error('❌ Error syncing to profile:', error)
      return NextResponse.json(
        { error: 'Failed to sync to profile' },
        { status: 500 }
      )
    }

    console.log('✅ Profile synced successfully')
    return NextResponse.json({
      success: true,
      message: 'Profile synced successfully',
      profile: data
    })

  } catch (error) {
    console.error('❌ Error syncing to profile:', error)
    return NextResponse.json(
      { error: 'Failed to sync to profile' },
      { status: 500 }
    )
  }
}
