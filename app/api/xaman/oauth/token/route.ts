import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { code, state, redirect_uri } = body;

    console.log('🔐 OAuth token exchange request:', { 
      hasCode: !!code, 
      state, 
      redirect_uri 
    });

    // Validate required parameters
    if (!code) {
      return NextResponse.json(
        { error: 'Missing authorization code' },
        { status: 400 }
      );
    }

    // Get API credentials from environment
    const clientId = process.env.NEXT_PUBLIC_XAMAN_API_KEY;
    const clientSecret = process.env.XUMM_API_SECRET;

    if (!clientId || !clientSecret) {
      console.error('❌ Missing Xaman API credentials');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    console.log('🔧 Using client ID:', clientId.substring(0, 8) + '...');

    // Exchange authorization code for access token
    const tokenResponse = await fetch('https://oauth2.xumm.app/auth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: clientId,
        client_secret: clientSecret,
        code: code,
        redirect_uri: redirect_uri || `${process.env.NEXT_PUBLIC_SITE_URL || 'https://fuse.vip'}/dashboard`,
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('❌ Token exchange failed:', {
        status: tokenResponse.status,
        statusText: tokenResponse.statusText,
        error: errorText
      });
      
      return NextResponse.json(
        { error: 'Token exchange failed', details: errorText },
        { status: tokenResponse.status }
      );
    }

    const tokenData = await tokenResponse.json();
    console.log('✅ Token exchange successful:', {
      hasAccessToken: !!tokenData.access_token,
      tokenType: tokenData.token_type,
      expiresIn: tokenData.expires_in
    });

    // Get user info using the access token
    const userInfoResponse = await fetch('https://oauth2.xumm.app/auth/userinfo', {
      headers: {
        'Authorization': `${tokenData.token_type} ${tokenData.access_token}`,
        'Accept': 'application/json',
      },
    });

    if (!userInfoResponse.ok) {
      console.error('❌ Failed to get user info:', userInfoResponse.status);
      return NextResponse.json(
        { error: 'Failed to get user info' },
        { status: userInfoResponse.status }
      );
    }

    const userInfo = await userInfoResponse.json();
    console.log('✅ User info retrieved:', {
      sub: userInfo.sub,
      account: userInfo.account,
      network: userInfo.network
    });

    // Return the token data and user info
    return NextResponse.json({
      success: true,
      access_token: tokenData.access_token,
      token_type: tokenData.token_type,
      expires_in: tokenData.expires_in,
      user: {
        sub: userInfo.sub,
        account: userInfo.account,
        network: userInfo.network,
        iat: userInfo.iat,
        exp: userInfo.exp
      }
    });

  } catch (error) {
    console.error('❌ OAuth token exchange error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
