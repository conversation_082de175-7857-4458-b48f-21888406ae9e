import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Verify webhook signature (recommended)
    // const signature = request.headers.get('x-xaman-signature');
    
    console.log('Xaman webhook received:', JSON.stringify(body, null, 2));
    
    // Process the webhook data based on the structure from user's example
    if (body.meta && body.payloadResponse && body.userToken) {
      await processXamanWebhook(body);
    } else {
      // Handle legacy webhook format if needed
      await processLegacyWebhook(body);
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function processXamanWebhook(webhookData: any) {
  try {
    const { meta, payloadResponse, userToken, custom_meta } = webhookData;
    
    console.log('Processing Xaman webhook:', {
      payloadId: meta.payload_uuidv4,
      signed: payloadResponse.signed,
      userToken: userToken.user_token,
      txid: payloadResponse.txid,
      instruction: custom_meta.instruction
    });

    // Only process if the payload was signed
    if (!payloadResponse.signed) {
      console.log('Payload not signed, skipping processing');
      return;
    }

    // Create Supabase client with service role
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Find user by Xaman user token
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('xaman_user_token', userToken.user_token)
      .single();

    if (profileError || !profile) {
      console.error('User not found for Xaman user token:', userToken.user_token);
      return;
    }

    // Update user profile with Xaman activity
    const updateData: any = {
      xaman_last_successful_sign: new Date().toISOString(),
      xaman_user_token_expires_at: new Date(userToken.token_expiration * 1000).toISOString()
    };

    // If this is a sign-in event, we can extract wallet information
    if (custom_meta.instruction?.includes('Sign in')) {
      // For sign-in events, we need to get the account from the transaction
      if (payloadResponse.txid) {
        // This will be handled by the transaction verification function
        console.log('Sign-in transaction detected, will verify on ledger');
      }
    }

    // Update the profile
    const { error: updateError } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', profile.id);

    if (updateError) {
      console.error('Profile update error:', updateError);
      return;
    }

    console.log('✅ Successfully processed Xaman webhook for user:', profile.id);

    // If there's a transaction ID, queue it for verification
    if (payloadResponse.txid) {
      await queueTransactionVerification(payloadResponse.txid, profile.id, meta.payload_uuidv4);
    }

  } catch (error) {
    console.error('Error processing Xaman webhook:', error);
  }
}

async function processLegacyWebhook(body: any) {
  // Handle legacy webhook format
  switch (body.type) {
    case 'transaction':
      console.log('Legacy transaction update:', body.data);
      break;
    case 'signin':
      console.log('Legacy sign-in event:', body.data);
      break;
    default:
      console.log('Unknown webhook type:', body.type);
  }
}

async function queueTransactionVerification(txid: string, userId: string, payloadId: string) {
  try {
    // Import the transaction verification function
    const { verifyXamanTransaction } = await import('@/lib/xaman-transaction-verification');
    
    console.log('Starting transaction verification:', {
      txid,
      userId,
      payloadId
    });

    // Perform immediate verification (could be moved to a queue system for production)
    const verification = await verifyXamanTransaction(txid, userId, payloadId);
    
    console.log('Transaction verification result:', {
      txid,
      isValid: verification.isValid,
      networkType: verification.networkType,
      account: verification.account,
      deliveredAmount: verification.deliveredAmount,
      errors: verification.errors
    });

    // If verification failed, log the errors
    if (!verification.isValid) {
      console.error('Transaction verification failed:', verification.errors);
    }

    return verification;
    
  } catch (error) {
    console.error('Error in transaction verification:', error);
    return null;
  }
}
