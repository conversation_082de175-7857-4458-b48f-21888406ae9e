import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { executeQuery } from '@/lib/database-direct'
import { performanceMonitor } from '@/lib/performance-monitor'

export async function GET(request: NextRequest) {
  const startTime = Date.now()

  try {
    // Create Supabase client with proper request context
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // In API routes, we can't set cookies, so we'll just log them
            cookiesToSet.forEach(({ name, value, options }) => {
              console.log(`Cookie would be set: ${name}=${value}`)
            })
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Authentication failed:', authError?.message)
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    console.log(`🔄 Checking optimized business access for authenticated user: ${user.id}`)

    const queryStartTime = Date.now()

    // Use optimized business access function with authenticated user's ID
    const { data, error } = await executeQuery(
      'SELECT has_business_access($1) as has_business_access, is_admin($1) as is_admin',
      [user.id]
    )

    const queryDuration = Date.now() - queryStartTime

    if (error) {
      console.error('❌ Failed to check business access for user', user.id, ':', error)

      // Track failed query
      performanceMonitor.trackDatabaseQuery(
        'has_business_access',
        'businesses',
        'SELECT',
        queryDuration,
        0
      )

      return NextResponse.json(
        { error: 'Failed to check business access', details: error },
        { status: 500 }
      )
    }

    const accessData = data && data.length > 0 ? data[0] : { has_business_access: false, is_admin: false }

    const totalDuration = Date.now() - startTime

    // Track performance
    performanceMonitor.trackDatabaseQuery(
      'has_business_access',
      'businesses',
      'SELECT',
      queryDuration,
      1
    )

    performanceMonitor.trackAPICall(
      '/api/business-access-optimized',
      'GET',
      totalDuration,
      200,
      user.id,
      false
    )

    console.log(`✅ Business access check completed for user ${user.id} in ${totalDuration}ms (Query: ${queryDuration}ms)`)

    return NextResponse.json({
      success: true,
      data: {
        has_business_access: accessData.has_business_access,
        is_admin: accessData.is_admin
      },
      performance: {
        queryTime: queryDuration,
        totalTime: totalDuration,
        cached: false
      },
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Business access API error:', error)

    // Track failed API call
    performanceMonitor.trackAPICall(
      '/api/business-access-optimized',
      'GET',
      totalDuration,
      500
    )

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    )
  }
}