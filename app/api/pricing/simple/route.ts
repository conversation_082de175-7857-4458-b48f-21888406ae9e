import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Simple, fast pricing with immediate fallbacks
    let xrpUsd = 2.4 // Fallback
    let fuseXrp = 1394.1 // Fallback: 13,941 FUSE per 10 XRP = 1,394.1 per XRP
    
    // Quick XRP price fetch (with timeout)
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 3000) // 3 second timeout
      
      const xrpResponse = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=ripple&vs_currencies=usd', {
        signal: controller.signal
      })
      clearTimeout(timeoutId)
      
      if (xrpResponse.ok) {
        const xrpData = await xrpResponse.json()
        if (xrpData.ripple?.usd) {
          xrpUsd = xrpData.ripple.usd
        }
      }
    } catch (error) {
      console.warn('Using fallback XRP price:', error)
    }
    
    // Calculate derived rates
    const fuseUsd = xrpUsd / fuseXrp
    const xrpFuse = 1 / fuseXrp
    
    // VIP card prices
    const cards = [
      { name: 'Monthly VIP Card', usdPrice: 9.99 },
      { name: 'Premium Card', usdPrice: 100 },
      { name: 'Gold Card', usdPrice: 250 },
      { name: 'Platinum Card', usdPrice: 500 },
      { name: 'Diamond Card', usdPrice: 1000 },
      { name: 'Obsidian Card', usdPrice: 1500 }
    ]
    
    const cardPrices = cards.map(card => ({
      ...card,
      xrpPrice: (card.usdPrice / xrpUsd).toFixed(6),
      fusePrice: Math.round(card.usdPrice / fuseUsd).toLocaleString(),
      rates: {
        xrpUsd,
        fuseUsd,
        fuseXrp,
        xrpFuse
      }
    }))
    
    return NextResponse.json({
      success: true,
      cards: cardPrices,
      rates: {
        xrpUsd,
        fuseUsd,
        fuseXrp,
        xrpFuse,
        lastUpdated: Date.now()
      },
      source: 'simple_api',
      timestamp: new Date().toISOString()
    })
    
  } catch (error: any) {
    console.error('Simple pricing API error:', error)
    
    // Even on error, return fallback data
    const fallbackRates = {
      xrpUsd: 2.4,
      fuseUsd: 2.4 / 1394.1,
      fuseXrp: 1394.1,
      xrpFuse: 1 / 1394.1
    }
    
    const cards = [
      { name: 'Monthly VIP Card', usdPrice: 9.99 },
      { name: 'Premium Card', usdPrice: 100 },
      { name: 'Gold Card', usdPrice: 250 },
      { name: 'Platinum Card', usdPrice: 500 },
      { name: 'Diamond Card', usdPrice: 1000 },
      { name: 'Obsidian Card', usdPrice: 1500 }
    ]
    
    const cardPrices = cards.map(card => ({
      ...card,
      xrpPrice: (card.usdPrice / fallbackRates.xrpUsd).toFixed(6),
      fusePrice: Math.round(card.usdPrice / fallbackRates.fuseUsd).toLocaleString(),
      rates: fallbackRates
    }))
    
    return NextResponse.json({
      success: true,
      cards: cardPrices,
      rates: fallbackRates,
      source: 'fallback',
      error: error.message,
      timestamp: new Date().toISOString()
    })
  }
}