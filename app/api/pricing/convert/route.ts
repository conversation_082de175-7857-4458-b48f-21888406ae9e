import { NextRequest, NextResponse } from 'next/server'
import { pricingService } from '@/lib/pricing-service'

export async function POST(request: NextRequest) {
  try {
    const { amount, fromCurrency, toCurrency } = await request.json()
    
    if (!amount || !fromCurrency || !toCurrency) {
      return NextResponse.json(
        { error: 'Missing required parameters: amount, fromCurrency, toCurrency' },
        { status: 400 }
      )
    }

    const validCurrencies = ['USD', 'XRP', 'FUSE']
    if (!validCurrencies.includes(fromCurrency) || !validCurrencies.includes(toCurrency)) {
      return NextResponse.json(
        { error: 'Invalid currency. Supported: USD, XRP, FUSE' },
        { status: 400 }
      )
    }

    let convertedAmount: string
    const prices = await pricingService.getCurrentPrices()

    // Perform conversion based on currency pair
    if (fromCurrency === 'USD') {
      if (toCurrency === 'XRP') {
        convertedAmount = await pricingService.calculateXRPAmount(amount)
      } else if (toCurrency === 'FUSE') {
        convertedAmount = await pricingService.calculateFUSEAmount(amount)
      } else {
        convertedAmount = amount.toString() // USD to USD
      }
    } else if (fromCurrency === 'XRP') {
      if (toCurrency === 'FUSE') {
        convertedAmount = await pricingService.convertXRPToFUSE(amount)
      } else if (toCurrency === 'USD') {
        const xrpValue = typeof amount === 'string' ? parseFloat(amount) : amount
        convertedAmount = (xrpValue * prices.xrpUsd).toFixed(2)
      } else {
        convertedAmount = amount.toString() // XRP to XRP
      }
    } else if (fromCurrency === 'FUSE') {
      if (toCurrency === 'XRP') {
        convertedAmount = await pricingService.convertFUSEToXRP(amount)
      } else if (toCurrency === 'USD') {
        const fuseValue = typeof amount === 'string' ? parseFloat(amount) : amount
        convertedAmount = (fuseValue * prices.fuseUsd).toFixed(2)
      } else {
        convertedAmount = amount.toString() // FUSE to FUSE
      }
    } else {
      return NextResponse.json(
        { error: 'Unsupported conversion' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      conversion: {
        from: {
          amount: amount.toString(),
          currency: fromCurrency
        },
        to: {
          amount: convertedAmount,
          currency: toCurrency
        },
        rate: {
          [`${fromCurrency}_${toCurrency}`]: 
            fromCurrency === 'USD' && toCurrency === 'XRP' ? (1 / prices.xrpUsd).toFixed(6) :
            fromCurrency === 'USD' && toCurrency === 'FUSE' ? (1 / prices.fuseUsd).toFixed(2) :
            fromCurrency === 'XRP' && toCurrency === 'USD' ? prices.xrpUsd.toFixed(2) :
            fromCurrency === 'FUSE' && toCurrency === 'USD' ? prices.fuseUsd.toFixed(6) :
            fromCurrency === 'XRP' && toCurrency === 'FUSE' ? prices.fuseXrp.toFixed(2) :
            fromCurrency === 'FUSE' && toCurrency === 'XRP' ? prices.xrpFuse.toFixed(6) :
            '1'
        },
        timestamp: prices.lastUpdated
      }
    })
  } catch (error: any) {
    console.error('Currency conversion error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to convert currency' },
      { status: 500 }
    )
  }
}