import { NextRequest, NextResponse } from 'next/server'
import { pricingService } from '@/lib/pricing-service'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const refresh = searchParams.get('refresh') === 'true'
    
    // Get current prices (force refresh if requested)
    const prices = refresh 
      ? await pricingService.refreshPrices()
      : await pricingService.getCurrentPrices()
    
    // Get VIP card prices
    const cardPrices = await pricingService.getVIPCardPrices()
    
    // Get cache status
    const cacheStatus = pricingService.getCacheStatus()
    
    return NextResponse.json({
      success: true,
      prices,
      cardPrices: cardPrices.cards,
      rates: cardPrices.rates,
      cache: cacheStatus,
      lastUpdated: cardPrices.lastUpdated
    })
  } catch (error: any) {
    console.error('Pricing API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'Failed to fetch pricing data' 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()
    
    if (action === 'refresh') {
      // Force refresh prices
      const prices = await pricingService.refreshPrices()
      const cardPrices = await pricingService.getVIPCardPrices()
      
      return NextResponse.json({
        success: true,
        message: 'Prices refreshed successfully',
        prices,
        cardPrices: cardPrices.cards,
        rates: cardPrices.rates,
        lastUpdated: cardPrices.lastUpdated
      })
    }
    
    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error: any) {
    console.error('Pricing API POST error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to process request' },
      { status: 500 }
    )
  }
}