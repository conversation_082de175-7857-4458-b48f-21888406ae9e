import { NextResponse } from 'next/server'
import { executeQuery, getActiveBusinessesDirect, getUserBusinessesDirect } from '@/lib/database-direct'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const includeProfile = searchParams.get('include_profile') === 'true'
    const isPublic = searchParams.get('public') === 'true'
    const fields = searchParams.get('fields') || 'full' // minimal, standard, full
    const limit = parseInt(searchParams.get('limit') || '100')

    console.log('🔄 Fetching businesses with consolidated endpoint...', { fields, limit })

    if (userId) {
      // Get businesses for specific user
      console.log(`👤 Fetching businesses for user: ${userId}`)
      
      let businesses
      if (includeProfile) {
        // Enhanced query with profile joins
        const query = `
          SELECT 
            b.*,
            p.first_name,
            p.last_name,
            p.user_email,
            p.is_card_holder,
            p.card_tier,
            p.membership_start_date,
            p.membership_end_date,
            ur.role_id,
            pr.name as role_name,
            pr.permissions
          FROM businesses b
          LEFT JOIN profiles p ON b.user_id = p.id
          LEFT JOIN user_roles ur ON p.id = ur.user_id
          LEFT JOIN portal_roles pr ON ur.role_id = pr.id
          WHERE b.user_id = $1 AND b.is_active = true
          ORDER BY b.created_at DESC
        `
        
        const { data, error } = await executeQuery(query, [userId])
        if (error) throw new Error(error)
        businesses = data || []
      } else {
        // Standard business fetch
        businesses = await getUserBusinessesDirect(userId)
      }

      return NextResponse.json({
        data: businesses,
        count: businesses?.length || 0,
        source: 'direct_database_with_profile',
        timestamp: new Date().toISOString()
      })
    } else {
      // Get all active businesses with field options
      console.log('🏢 Fetching all active businesses...')
      
      let query = ''
      
      if (isPublic || fields === 'minimal') {
        // Public/minimal fields only
        query = `
          SELECT
            b.id,
            b.name,
            b.logo_url,
            b.website,
            b.category,
            b.premium_discount,
            b.is_active,
            b.business_spotlight,
            b.business_address,
            b.contact_name,
            b.contact_email,
            b.contact_phone,
            b.contact_info,
            b.created_at
          FROM businesses b
          WHERE b.is_active = true
          ORDER BY b.display_order ASC NULLS LAST, b.created_at DESC
          LIMIT $1
        `
      } else if (fields === 'standard') {
        // Standard fields with basic stats
        query = `
          SELECT
            b.*,
            p.first_name as owner_first_name,
            p.last_name as owner_last_name,
            p.user_email as owner_email,
            COUNT(qr.id) as qr_scan_count
          FROM businesses b
          LEFT JOIN profiles p ON b.user_id = p.id
          LEFT JOIN qr_interactions qr ON b.id = qr.scanned_business_id
          WHERE b.is_active = true
          GROUP BY b.id, p.id
          ORDER BY b.display_order ASC NULLS LAST, b.created_at DESC
          LIMIT $1
        `
      } else {
        // Full fields with all data
        query = `
          SELECT
            b.*,
            p.first_name as owner_first_name,
            p.last_name as owner_last_name,
            p.user_email as owner_email,
            p.is_card_holder as owner_is_card_holder,
            p.card_tier as owner_card_tier,
            COUNT(qr.id) as qr_scan_count,
            COUNT(DISTINCT ref.id) as referral_count
          FROM businesses b
          LEFT JOIN profiles p ON b.user_id = p.id
          LEFT JOIN qr_interactions qr ON b.id = qr.scanned_business_id
          LEFT JOIN profiles ref ON b.id = ref.referring_business_id
          WHERE b.is_active = true
          GROUP BY b.id, p.id
          ORDER BY b.display_order ASC NULLS LAST, b.created_at DESC
          LIMIT $1
        `
      }

      console.log(`📊 Executing query for ${fields} fields with limit ${limit}`)

      const { data, error } = await executeQuery(query, [limit])
      if (error) throw new Error(error)

      const businesses = data || []
      console.log(`✅ Found ${businesses.length} businesses with ${fields} data`)

      return NextResponse.json({
        success: true,
        data: businesses,
        count: businesses.length,
        source: 'direct_database_consolidated',
        fields: fields,
        timestamp: new Date().toISOString()
      })
    }
  } catch (error) {
    console.error('❌ Error in consolidated businesses API:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch businesses',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    console.log('📝 Creating new business with enhanced profile integration...')

    if (!body.user_id) {
      return NextResponse.json(
        { error: 'user_id is required' },
        { status: 400 }
      )
    }

    // Enhanced business creation with profile validation
    const createQuery = `
      INSERT INTO businesses (
        user_id, name, category, website, contact_email, contact_phone,
        contact_name, business_address, premium_discount, logo_url,
        description, is_active, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), NOW())
      RETURNING *
    `

    const params = [
      body.user_id,
      body.name,
      body.category || null,
      body.website || null,
      body.contact_email || null,
      body.contact_phone || null,
      body.contact_name || null,
      body.business_address || null,
      body.premium_discount || null,
      body.logo_url || null,
      body.description || null,
      body.is_active !== undefined ? body.is_active : true
    ]

    const { data, error } = await executeQuery(createQuery, params)
    if (error) throw new Error(error)

    if (data && data.length > 0) {
      console.log(`✅ Business created successfully: ${data[0].id}`)
      
      // Update profile to mark as business applicant if not already
      const updateProfileQuery = `
        UPDATE profiles 
        SET is_business_applicant = true, updated_at = NOW()
        WHERE id = $1
      `
      await executeQuery(updateProfileQuery, [body.user_id])

      return NextResponse.json({
        data: data[0],
        message: 'Business created successfully',
        timestamp: new Date().toISOString()
      })
    }

    throw new Error('Failed to create business')
  } catch (error) {
    console.error('❌ Error creating business:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create business', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    )
  }
}
