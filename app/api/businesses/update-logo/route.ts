import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    console.log('📝 Processing business logo update...');

    const body = await request.json();
    const { 
      businessId, 
      logoUrl, 
      logoFileSize, 
      logoMimeType, 
      logoWidth, 
      logoHeight 
    } = body;

    if (!businessId || !logoUrl) {
      return NextResponse.json(
        { error: 'businessId and logoUrl are required' },
        { status: 400 }
      );
    }

    console.log(`🔄 Updating logo for business: ${businessId}`);
    console.log(`🔗 Logo URL: ${logoUrl}`);

    // Update business record with logo information
    const { data: updatedBusiness, error } = await supabase
      .from('businesses')
      .update({
        logo_url: logoUrl,
        logo_optimized_url: logoUrl,
        logo_file_size: logoFileSize || null,
        logo_mime_type: logoMimeType || null,
        logo_width: logoWidth || null,
        logo_height: logoHeight || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', businessId)
      .select('id, name, logo_url, logo_file_size, logo_mime_type')
      .single();

    if (error) {
      console.error('❌ Database update failed:', error);
      return NextResponse.json(
        { 
          error: 'Failed to update business logo',
          details: error.message 
        },
        { status: 500 }
      );
    }

    if (!updatedBusiness) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      );
    }

    console.log('✅ Business logo updated successfully:', updatedBusiness);

    return NextResponse.json({
      success: true,
      data: updatedBusiness,
      message: 'Business logo updated successfully'
    });

  } catch (error: any) {
    console.error('❌ Logo update API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
