import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    console.log('Testing authentication for:', email)

    const supabase = await createClient()

    // Test sign in
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.error('Test login error:', error)
      return NextResponse.json({
        success: false,
        error: error.message,
        details: {
          code: error.status,
          message: error.message
        }
      })
    }

    console.log('Test login successful for user:', data.user?.id)

    // Test getting user session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error('Session error:', sessionError)
    }

    return NextResponse.json({
      success: true,
      message: 'Authentication test successful',
      user: {
        id: data.user?.id,
        email: data.user?.email,
        email_confirmed_at: data.user?.email_confirmed_at,
        last_sign_in_at: data.user?.last_sign_in_at
      },
      session: {
        access_token: data.session?.access_token ? 'present' : 'missing',
        refresh_token: data.session?.refresh_token ? 'present' : 'missing',
        expires_at: data.session?.expires_at
      },
      sessionCheck: {
        hasSession: !!sessionData.session,
        sessionError: sessionError?.message || null
      }
    })

  } catch (error) {
    console.error('Test API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Test basic connection
    const { data: { user }, error } = await supabase.auth.getUser()
    
    return NextResponse.json({
      success: true,
      message: 'Supabase connection test',
      hasUser: !!user,
      userId: user?.id || null,
      error: error?.message || null
    })
  } catch (error) {
    console.error('Connection test error:', error)
    return NextResponse.json({
      success: false,
      error: 'Connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
