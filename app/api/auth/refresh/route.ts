import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { withSecurity } from '@/lib/api-security'

export async function POST(request: NextRequest) {
  return withSecurity(
    request,
    async (req, { supabase }) => {
      try {
        const supabaseClient = supabase || await createClient()

        const { data, error } = await supabaseClient.auth.refreshSession()

        if (error) {
          console.error('Refresh error:', error)
          return NextResponse.json(
            { error: error.message },
            { status: 401 }
          )
        }

        return NextResponse.json({
          success: true,
          user: data.user,
          session: data.session
        })

      } catch (error) {
        console.error('Refresh API error:', error)
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        )
      }
    },
    {
      rateLimit: { windowMs: 60000, maxRequests: 30 }, // 30 refresh attempts per minute
      logRequest: true
    }
  )
}
