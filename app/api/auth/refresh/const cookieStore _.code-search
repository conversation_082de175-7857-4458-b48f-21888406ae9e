# Query: const cookieStore =
# ContextLines: 1

10 results - 7 files

app/api/auth/login/route.ts:
  16  
  17:     const cookieStore = await cookies() // ✅ Await is necessary
  18      const supabase = await createClient(cookieStore) // ✅ Await createClient

app/api/auth/logout/route.ts:
  7    try {
  8:     const cookieStore = await cookies();
  9      const supabase = await createClient(cookieStore); // ✅ Await the async function

app/api/auth/me/route.ts:
  6    try {
  7:     const cookieStore = await cookies()
  8  

app/api/auth/refresh/route.ts:
  14    try {
  15:     const cookieStore = await cookies()
  16  

app/api/chat/route.ts:
  47      // try {
  48:     //   const cookieStore = await cookies();
  49      //   const user = await getAuthenticatedUser(cookieStore);

lib/auth-server.ts:
   26    try {
   27:     const cookieStore = await cookies()
   28      

  119    try {
  120:     const cookieStore = await cookies()
  121      const { createClient } = await import('@/lib/supabase/server')

  156    try {
  157:     const cookieStore = await cookies()
  158      const { createClient } = await import('@/lib/supabase/server')

lib/supabase/server.ts:
   7  export const createClient = async () => {
   8:   const cookieStore = await getCookies();
   9  

  63  export async function getAuthenticatedUser(): Promise<JWTPayload | null> {
  64:   const cookieStore = await getCookies();
  65    const token = await getTokenFromCookies(cookieStore, 'access');
