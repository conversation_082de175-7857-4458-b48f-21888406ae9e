import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server-only';
import { ensureUserProfileDirect, createProfileDirect } from '@/lib/database-direct';

interface ProfileData {
  userId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  referringBusinessId?: string;
  isBusinessApplicant?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Creating profile after signup...');
    
    const body = await request.json();
    const { 
      userId, 
      email, 
      firstName, 
      lastName, 
      phone, 
      referringBusinessId, 
      isBusinessApplicant = false 
    }: ProfileData = body;

    if (!userId || !email) {
      return NextResponse.json(
        { error: 'User ID and email are required' },
        { status: 400 }
      );
    }

    // Try direct database profile creation first (fastest)
    console.log(`💾 Creating profile directly for user: ${userId}`);
    
    const profileData = {
      id: userId,
      user_email: email,
      first_name: firstName || null,
      last_name: lastName || null,
      phone: phone || null,
      referring_business_id: referringBusinessId || null,
      is_business_applicant: isBusinessApplicant,
      is_card_holder: false,
      card_tier: 'Premium',
      created_at: new Date().toISOString()
    };

    const directProfile = await createProfileDirect(profileData);
    
    if (directProfile) {
      console.log('✅ Profile created successfully via direct database');
      
      // Cache the profile immediately for faster auth context loading
      const cacheKey = `profile_${userId}`;
      const timestampKey = `profile_timestamp_${userId}`;
      
      const cachedProfile = {
        id: directProfile.id,
        user_email: directProfile.user_email,
        first_name: directProfile.first_name || '',
        last_name: directProfile.last_name || '',
        phone: directProfile.phone || '',
        is_card_holder: directProfile.is_card_holder || false,
        is_business_applicant: directProfile.is_business_applicant || false,
        card_tier: directProfile.card_tier || 'Premium',
        xrp_wallet_address: directProfile.xrp_wallet_address || '',
        created_at: directProfile.created_at
      };

      // Note: We can't directly access localStorage on server side, 
      // but we'll return cache data for client-side caching
      
      return NextResponse.json({
        success: true,
        profile: directProfile,
        cacheData: {
          profile: cachedProfile,
          timestamp: Date.now()
        },
        message: 'Profile created and cached successfully via direct database'
      });
    }

    // Fallback to Supabase client if direct method fails
    console.warn('📋 Direct profile creation failed, falling back to Supabase client...');
    
    const supabase = await createServerSupabaseClient();
    
    // First check if profile already exists
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('id, user_email, first_name, last_name, phone, is_card_holder, is_business_applicant')
      .eq('id', userId)
      .maybeSingle();

    if (existingProfile) {
      console.log('✅ Profile already exists, updating with new data');
      
      // Update existing profile
      const { data: updatedProfile, error: updateError } = await supabase
        .from('profiles')
        .update({
          first_name: firstName || existingProfile.first_name,
          last_name: lastName || existingProfile.last_name,
          phone: phone || existingProfile.phone,
          referring_business_id: referringBusinessId || null,
          is_business_applicant: isBusinessApplicant
        })
        .eq('id', userId)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating profile:', updateError);
        return NextResponse.json(
          { error: 'Failed to update profile' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        profile: updatedProfile,
        cacheData: {
          profile: {
            id: updatedProfile.id,
            user_email: updatedProfile.user_email,
            first_name: updatedProfile.first_name || '',
            last_name: updatedProfile.last_name || '',
            phone: updatedProfile.phone || '',
            is_card_holder: updatedProfile.is_card_holder || false,
            is_business_applicant: updatedProfile.is_business_applicant || false,
            card_tier: 'Premium',
            xrp_wallet_address: '',
            created_at: updatedProfile.created_at || new Date().toISOString()
          },
          timestamp: Date.now()
        },
        message: 'Profile updated successfully via Supabase client'
      });
    }

    // Create new profile via Supabase
    const { data: newProfile, error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: userId,
        user_email: email,
        first_name: firstName || null,
        last_name: lastName || null,
        phone: phone || null,
        referring_business_id: referringBusinessId || null,
        is_business_applicant: isBusinessApplicant,
        is_card_holder: false,
        card_tier: 'Premium'
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating profile via Supabase:', insertError);
      return NextResponse.json(
        { error: 'Failed to create profile' },
        { status: 500 }
      );
    }

    console.log('✅ Profile created successfully via Supabase client');

    return NextResponse.json({
      success: true,
      profile: newProfile,
      cacheData: {
        profile: {
          id: newProfile.id,
          user_email: newProfile.user_email,
          first_name: newProfile.first_name || '',
          last_name: newProfile.last_name || '',
          phone: newProfile.phone || '',
          is_card_holder: newProfile.is_card_holder || false,
          is_business_applicant: newProfile.is_business_applicant || false,
          card_tier: newProfile.card_tier || 'Premium',
          xrp_wallet_address: '',
          created_at: newProfile.created_at
        },
        timestamp: Date.now()
      },
      message: 'Profile created successfully via Supabase client'
    });

  } catch (error) {
    console.error('Profile creation API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}