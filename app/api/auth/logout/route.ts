import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { withSecurity } from '@/lib/api-security'

export async function POST(request: NextRequest) {
  return withSecurity(
    request,
    async (req, { supabase }) => {
      try {
        const supabaseClient = supabase || await createClient()

        const { error } = await supabaseClient.auth.signOut()

        if (error) {
          console.error('Logout error:', error)
          return NextResponse.json(
            { error: error.message },
            { status: 400 }
          )
        }

        return NextResponse.json({
          success: true,
          message: 'Logged out successfully'
        })

      } catch (error) {
        console.error('Logout API error:', error)
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        )
      }
    },
    {
      rateLimit: { windowMs: 60000, maxRequests: 20 }, // 20 logout attempts per minute
      logRequest: true
    }
  )
}
