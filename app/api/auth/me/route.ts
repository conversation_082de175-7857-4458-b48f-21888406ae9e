import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { withSecurity } from '@/lib/api-security'

export async function GET(request: NextRequest) {
  return withSecurity(
    request,
    async (req, { user, supabase }) => {
      try {
        if (!user) {
          return NextResponse.json(
            { error: 'Not authenticated' },
            { status: 401 }
          )
        }

        const supabaseClient = supabase || await createClient()

        // Get user profile data
        const { data: profile, error: profileError } = await supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()

        if (profileError && profileError.code !== 'PGRST116') {
          console.error('Profile fetch error:', profileError)
        }

        // Get user business data if exists
        const { data: business, error: businessError } = await supabaseClient
          .from('businesses')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (businessError && businessError.code !== 'PGRST116') {
          console.error('Business fetch error:', businessError)
        }

        return NextResponse.json({
          user: {
            id: user.id,
            email: user.email,
            email_confirmed_at: user.email_confirmed_at,
            created_at: user.created_at,
            last_sign_in_at: user.last_sign_in_at
          },
          profile: profile || null,
          business: business || null
        })

      } catch (error) {
        console.error('Me API error:', error)
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        )
      }
    },
    {
      requireAuth: true,
      rateLimit: { windowMs: 60000, maxRequests: 60 }, // 60 requests per minute
      logRequest: true
    }
  )
}
