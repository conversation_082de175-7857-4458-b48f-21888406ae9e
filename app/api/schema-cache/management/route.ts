import { NextRequest, NextResponse } from 'next/server'
import { directSchemaCache } from '@/lib/schema-cache-direct'
import { ensureCacheReady, refreshCache, getCacheHealth, isCacheReady } from '@/lib/cache-initializer'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'health'

    switch (action) {
      case 'health':
        return getCacheHealthStatus()
      
      case 'stats':
        return getCacheStatistics()
      
      case 'status':
        return getCacheStatus()
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: health, stats, status' },
          { status: 400 }
        )
    }

  } catch (error: any) {
    console.error('Schema cache management GET error:', error)
    
    return NextResponse.json(
      { error: 'Cache management operation failed', details: error.message },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, force } = body
    
    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'initialize':
        return await initializeCache(force)
      
      case 'refresh':
        return await refreshSchemaCache()
      
      case 'clear':
        return clearSchemaCache()
      
      case 'warm-up':
        return await warmUpCache()
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: initialize, refresh, clear, warm-up' },
          { status: 400 }
        )
    }

  } catch (error: any) {
    console.error('Schema cache management POST error:', error)
    
    return NextResponse.json(
      { error: 'Cache management operation failed', details: error.message },
      { status: 500 }
    )
  }
}

async function getCacheHealthStatus(): Promise<NextResponse> {
  const health = getCacheHealth()
  const stats = directSchemaCache.getStats()
  
  // Analyze cache health
  let overallStatus: 'healthy' | 'warning' | 'critical' = 'healthy'
  const issues: string[] = []
  const recommendations: string[] = []
  
  if (!health.isInitialized || !health.isReady) {
    overallStatus = 'critical'
    issues.push('Cache not initialized or ready')
    recommendations.push('Initialize the cache using POST /api/schema-cache/management with action: initialize')
  }
  
  if (stats.hitRate < 50 && (stats.hitCount + stats.missCount) > 100) {
    if (overallStatus !== 'critical') overallStatus = 'warning'
    issues.push('Low cache hit rate')
    recommendations.push('Cache may need refresh or there may be data inconsistencies')
  }
  
  if (stats.lastRefresh) {
    const timeSinceRefresh = Date.now() - stats.lastRefresh.getTime()
    if (timeSinceRefresh > 2 * 60 * 60 * 1000) { // 2 hours
      if (overallStatus !== 'critical') overallStatus = 'warning'
      issues.push('Cache data may be stale')
      recommendations.push('Consider refreshing the cache')
    }
  }
  
  return NextResponse.json({
    success: true,
    overallStatus,
    cache: {
      initialized: health.isInitialized,
      ready: health.isReady,
      stats,
      health: health.health
    },
    issues: issues.length > 0 ? issues : undefined,
    recommendations: recommendations.length > 0 ? recommendations : undefined,
    timestamp: new Date().toISOString()
  })
}

async function getCacheStatistics(): Promise<NextResponse> {
  try {
    await ensureCacheReady()
    
    const stats = directSchemaCache.getStats()
    const health = getCacheHealth()
    
    return NextResponse.json({
      success: true,
      statistics: {
        ...stats,
        initialized: health.isInitialized,
        ready: health.isReady,
        hitRateFormatted: `${Math.round(stats.hitRate * 100) / 100}%`,
        lastRefreshFormatted: stats.lastRefresh ? stats.lastRefresh.toISOString() : 'Never',
        refreshDurationFormatted: stats.refreshDuration ? `${stats.refreshDuration}ms` : 'N/A'
      },
      timestamp: new Date().toISOString()
    })
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to get cache statistics', details: error.message },
      { status: 500 }
    )
  }
}

async function getCacheStatus(): Promise<NextResponse> {
  const health = getCacheHealth()
  
  return NextResponse.json({
    success: true,
    status: {
      initialized: health.isInitialized,
      ready: health.isReady,
      cacheReady: isCacheReady(),
      businessesLoaded: directSchemaCache.getStats().businessesCount > 0,
      profilesLoaded: directSchemaCache.getStats().profilesCount > 0
    },
    timestamp: new Date().toISOString()
  })
}

async function initializeCache(force: boolean = false): Promise<NextResponse> {
  try {
    const health = getCacheHealth()
    
    if (health.isInitialized && health.isReady && !force) {
      return NextResponse.json({
        success: true,
        message: 'Cache already initialized',
        stats: directSchemaCache.getStats(),
        skipped: true
      })
    }
    
    console.log(`🚀 ${force ? 'Force ' : ''}initializing schema cache...`)
    
    const startTime = Date.now()
    await ensureCacheReady()
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: `Schema cache ${force ? 'force ' : ''}initialized successfully`,
      duration,
      stats: directSchemaCache.getStats(),
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Cache initialization failed',
      details: error.message
    }, { status: 500 })
  }
}

async function refreshSchemaCache(): Promise<NextResponse> {
  try {
    console.log('🔄 Refreshing schema cache...')
    
    const startTime = Date.now()
    await refreshCache()
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: 'Schema cache refreshed successfully',
      duration,
      stats: directSchemaCache.getStats(),
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Cache refresh failed',
      details: error.message
    }, { status: 500 })
  }
}

function clearSchemaCache(): NextResponse {
  try {
    console.log('🧹 Clearing schema cache...')
    
    directSchemaCache.clear()
    
    return NextResponse.json({
      success: true,
      message: 'Schema cache cleared successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Cache clear failed',
      details: error.message
    }, { status: 500 })
  }
}

async function warmUpCache(): Promise<NextResponse> {
  try {
    console.log('🔥 Warming up schema cache...')
    
    const startTime = Date.now()
    
    // Ensure cache is initialized
    await ensureCacheReady()
    
    // Perform some warm-up operations
    const businesses = await directSchemaCache.getAllBusinesses()
    const spotlightBusinesses = await directSchemaCache.getSpotlightBusinesses()
    const cardHolders = await directSchemaCache.getCardHolders()
    
    const duration = Date.now() - startTime
    
    return NextResponse.json({
      success: true,
      message: 'Schema cache warmed up successfully',
      duration,
      warmUpOperations: {
        businessesLoaded: businesses.length,
        spotlightBusinessesLoaded: spotlightBusinesses.length,
        cardHoldersLoaded: cardHolders.length
      },
      stats: directSchemaCache.getStats(),
      timestamp: new Date().toISOString()
    })

  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: 'Cache warm-up failed',
      details: error.message
    }, { status: 500 })
  }
}