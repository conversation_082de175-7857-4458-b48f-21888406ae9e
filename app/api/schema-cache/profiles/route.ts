import { NextRequest, NextResponse } from 'next/server'
import { directSchemaCache } from '@/lib/schema-cache-direct'
import { ensureCacheReady } from '@/lib/cache-initializer'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // Ensure cache is ready
    await ensureCacheReady()
    
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const cardHoldersOnly = searchParams.get('cardHolders') === 'true'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
    
    if (userId) {
      // Get specific user profile
      console.log(`🔍 Getting profile ${userId} from schema cache`)
      
      const profile = await directSchemaCache.getProfile(userId)
      
      if (!profile) {
        return NextResponse.json(
          { error: 'Profile not found', userId },
          { status: 404 }
        )
      }
      
      const totalDuration = Date.now() - startTime
      console.log(`✅ Schema cache served profile ${userId} in ${totalDuration}ms`)
      
      return NextResponse.json({
        success: true,
        profile,
        performance: {
          totalTime: totalDuration,
          cached: true,
          source: 'schema_cache_direct_sql'
        },
        cache: directSchemaCache.getStats(),
        timestamp: new Date().toISOString()
      })
      
    } else if (cardHoldersOnly) {
      // Get all card holders
      console.log('🔍 Getting all card holders from schema cache')
      
      const cardHolders = await directSchemaCache.getCardHolders()
      
      // Apply pagination
      const offset = (page - 1) * limit
      const totalCount = cardHolders.length
      const totalPages = Math.ceil(totalCount / limit)
      const paginatedCardHolders = cardHolders.slice(offset, offset + limit)
      
      const totalDuration = Date.now() - startTime
      console.log(`✅ Schema cache served ${paginatedCardHolders.length} card holders in ${totalDuration}ms`)
      
      return NextResponse.json({
        success: true,
        profiles: paginatedCardHolders,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        },
        performance: {
          totalTime: totalDuration,
          cached: true,
          source: 'schema_cache_direct_sql'
        },
        cache: directSchemaCache.getStats(),
        timestamp: new Date().toISOString()
      })
      
    } else {
      // Get all profiles with pagination
      console.log(`🔍 Getting all profiles from schema cache - Page: ${page}, Limit: ${limit}`)
      
      const offset = (page - 1) * limit
      const { profiles, total } = await directSchemaCache.getAllProfiles(offset, limit)
      
      const totalPages = Math.ceil(total / limit)
      
      const totalDuration = Date.now() - startTime
      console.log(`✅ Schema cache served ${profiles.length} profiles in ${totalDuration}ms`)
      
      return NextResponse.json({
        success: true,
        profiles,
        pagination: {
          page,
          limit,
          totalCount: total,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        },
        performance: {
          totalTime: totalDuration,
          cached: true,
          source: 'schema_cache_direct_sql'
        },
        cache: directSchemaCache.getStats(),
        timestamp: new Date().toISOString()
      })
    }
    
  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Schema cache profiles API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Schema cache error',
        details: error.message,
        performance: {
          totalTime: totalDuration,
          cached: false
        }
      },
      { status: 500 }
    )
  }
}

// POST for specific operations
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    await ensureCacheReady()
    
    const body = await request.json()
    const { action, userId } = body
    
    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }
    
    switch (action) {
      case 'checkBusinessOwner':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required for checkBusinessOwner action' },
            { status: 400 }
          )
        }
        
        console.log(`🔍 Checking business owner status for ${userId} from schema cache`)
        
        const isBusinessOwner = await directSchemaCache.isBusinessOwner(userId)
        
        const totalDuration = Date.now() - startTime
        console.log(`✅ Schema cache served business owner check for ${userId} in ${totalDuration}ms: ${isBusinessOwner}`)
        
        return NextResponse.json({
          success: true,
          userId,
          isBusinessOwner,
          performance: {
            totalTime: totalDuration,
            cached: true,
            source: 'schema_cache_direct_sql'
          },
          cache: directSchemaCache.getStats(),
          timestamp: new Date().toISOString()
        })
        
      case 'getProfile':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required for getProfile action' },
            { status: 400 }
          )
        }
        
        console.log(`🔍 Getting profile ${userId} from schema cache via POST`)
        
        const profile = await directSchemaCache.getProfile(userId)
        
        if (!profile) {
          return NextResponse.json(
            { error: 'Profile not found', userId },
            { status: 404 }
          )
        }
        
        const profileDuration = Date.now() - startTime
        console.log(`✅ Schema cache served profile ${userId} in ${profileDuration}ms`)
        
        return NextResponse.json({
          success: true,
          profile,
          performance: {
            totalTime: profileDuration,
            cached: true,
            source: 'schema_cache_direct_sql'
          },
          cache: directSchemaCache.getStats(),
          timestamp: new Date().toISOString()
        })
        
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: checkBusinessOwner, getProfile' },
          { status: 400 }
        )
    }
    
  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Schema cache profiles POST error:', error)
    
    return NextResponse.json(
      { 
        error: 'Schema cache error',
        details: error.message,
        performance: {
          totalTime: totalDuration,
          cached: false
        }
      },
      { status: 500 }
    )
  }
}