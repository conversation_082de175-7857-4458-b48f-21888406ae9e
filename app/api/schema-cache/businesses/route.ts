import { NextRequest, NextResponse } from 'next/server'
import { directSchemaCache } from '@/lib/schema-cache-direct'
import { ensureCacheReady } from '@/lib/cache-initializer'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // Ensure cache is ready
    await ensureCacheReady()
    
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const spotlightOnly = searchParams.get('spotlight') === 'true'
    const userId = searchParams.get('userId') // For user's businesses
    
    console.log(`🔄 Fetching businesses from schema cache - Page: ${page}, Limit: ${limit}`)
    
    let businesses: any[] = []
    
    if (userId) {
      // Get businesses for specific user
      businesses = await directSchemaCache.getBusinessesByUserId(userId)
    } else if (search) {
      // Search businesses
      businesses = await directSchemaCache.searchBusinesses(search)
    } else if (spotlightOnly) {
      // Get spotlight businesses
      businesses = await directSchemaCache.getSpotlightBusinesses()
    } else if (category && category !== 'all') {
      // Get businesses by category
      businesses = await directSchemaCache.getBusinessesByCategory(category)
    } else {
      // Get all active businesses
      businesses = await directSchemaCache.getAllBusinesses()
    }
    
    // Apply pagination
    const offset = (page - 1) * limit
    const totalCount = businesses.length
    const totalPages = Math.ceil(totalCount / limit)
    const paginatedBusinesses = businesses.slice(offset, offset + limit)
    
    const totalDuration = Date.now() - startTime
    
    console.log(`✅ Schema cache served ${paginatedBusinesses.length} businesses in ${totalDuration}ms`)
    
    return NextResponse.json({
      success: true,
      data: paginatedBusinesses,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      },
      filters: {
        category,
        search,
        spotlightOnly,
        userId
      },
      performance: {
        totalTime: totalDuration,
        cached: true,
        source: 'schema_cache_direct_sql'
      },
      cache: directSchemaCache.getStats(),
      timestamp: new Date().toISOString()
    })
    
  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Schema cache businesses API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Schema cache error',
        details: error.message,
        performance: {
          totalTime: totalDuration,
          cached: false
        }
      },
      { status: 500 }
    )
  }
}

// GET specific business by ID
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    await ensureCacheReady()
    
    const body = await request.json()
    const { businessId } = body
    
    if (!businessId) {
      return NextResponse.json(
        { error: 'businessId is required' },
        { status: 400 }
      )
    }
    
    console.log(`🔍 Getting business ${businessId} from schema cache`)
    
    const business = await directSchemaCache.getBusiness(businessId)
    
    if (!business) {
      return NextResponse.json(
        { error: 'Business not found', businessId },
        { status: 404 }
      )
    }
    
    const totalDuration = Date.now() - startTime
    
    console.log(`✅ Schema cache served business ${businessId} in ${totalDuration}ms`)
    
    return NextResponse.json({
      success: true,
      business,
      performance: {
        totalTime: totalDuration,
        cached: true,
        source: 'schema_cache_direct_sql'
      },
      cache: directSchemaCache.getStats(),
      timestamp: new Date().toISOString()
    })
    
  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Schema cache business lookup error:', error)
    
    return NextResponse.json(
      { 
        error: 'Schema cache error',
        details: error.message,
        performance: {
          totalTime: totalDuration,
          cached: false
        }
      },
      { status: 500 }
    )
  }
}