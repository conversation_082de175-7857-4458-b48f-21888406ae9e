import { NextRequest, NextResponse } from 'next/server'

// Temporarily disabled for build
export async function POST(request: NextRequest) {
  return NextResponse.json({ error: 'Service temporarily unavailable' }, { status: 503 })
}

/*
import { getUserByEmailDirect, verifyPasswordDirect, createUserDirect, updateUserPasswordDirect } from '@/lib/db-direct'
import { sign } from 'jsonwebtoken'
import { serverEnv } from '@/lib/env'

// Helper to create JWT token
function createJWT(user: any) {
  const payload = {
    sub: user.id,
    email: user.email,
    role: user.role || 'authenticated',
    aud: 'authenticated',
    exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24), // 24 hours
    iat: Math.floor(Date.now() / 1000),
    iss: 'supabase'
  }
  
  return sign(payload, serverEnv.SUPABASE_JWT_SECRET)
}

// Helper to hash password
async function hashPassword(password: string): Promise<string> {
  const bcrypt = await import('bcrypt')
  return bcrypt.hash(password, 12)
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, email, password, newPassword, userData } = body

    console.log(`🔄 Direct auth request: ${action} for ${email}`)

    switch (action) {
      case 'sign_in': {
        if (!email || !password) {
          return NextResponse.json(
            { error: 'Email and password are required' },
            { status: 400 }
          )
        }

        const { valid, user, error } = await verifyPasswordDirect(email, password)
        
        if (!valid || !user) {
          return NextResponse.json(
            { error: 'Invalid email or password' },
            { status: 401 }
          )
        }

        const accessToken = createJWT(user)
        
        return NextResponse.json({
          access_token: accessToken,
          token_type: 'bearer',
          expires_in: 86400,
          user: {
            id: user.id,
            email: user.email,
            email_confirmed_at: user.email_confirmed_at,
            last_sign_in_at: user.last_sign_in_at,
            role: user.role || 'authenticated',
            user_metadata: user.raw_user_meta_data || {},
            app_metadata: user.raw_app_meta_data || {}
          }
        })
      }

      case 'sign_up': {
        if (!email || !password) {
          return NextResponse.json(
            { error: 'Email and password are required' },
            { status: 400 }
          )
        }

        // Check if user already exists
        const { data: existingUser } = await getUserByEmailDirect(email)
        if (existingUser) {
          return NextResponse.json(
            { error: 'User already exists' },
            { status: 409 }
          )
        }

        const hashedPassword = await hashPassword(password)
        const { data: newUser, error } = await createUserDirect(email, hashedPassword, userData)
        
        if (error || !newUser) {
          console.error('User creation failed:', error)
          return NextResponse.json(
            { error: 'Failed to create user' },
            { status: 500 }
          )
        }

        const accessToken = createJWT(newUser)
        
        return NextResponse.json({
          access_token: accessToken,
          token_type: 'bearer',
          expires_in: 86400,
          user: {
            id: newUser.id,
            email: newUser.email,
            email_confirmed_at: newUser.email_confirmed_at,
            created_at: newUser.created_at,
            role: 'authenticated',
            user_metadata: userData || {},
            app_metadata: { provider: 'email', providers: ['email'] }
          }
        })
      }

      case 'update_password': {
        const authHeader = request.headers.get('authorization')
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return NextResponse.json(
            { error: 'Authorization required' },
            { status: 401 }
          )
        }

        if (!newPassword) {
          return NextResponse.json(
            { error: 'New password is required' },
            { status: 400 }
          )
        }

        try {
          const token = authHeader.replace('Bearer ', '')
          const jwt = await import('jsonwebtoken')
          const decoded = jwt.verify(token, serverEnv.SUPABASE_JWT_SECRET) as any
          
          const hashedPassword = await hashPassword(newPassword)
          const { data: updatedUser, error } = await updateUserPasswordDirect(decoded.sub, hashedPassword)
          
          if (error || !updatedUser) {
            return NextResponse.json(
              { error: 'Failed to update password' },
              { status: 500 }
            )
          }

          return NextResponse.json({
            message: 'Password updated successfully',
            user: {
              id: updatedUser.id,
              email: updatedUser.email,
              updated_at: updatedUser.updated_at
            }
          })
        } catch (jwtError) {
          return NextResponse.json(
            { error: 'Invalid token' },
            { status: 401 }
          )
        }
      }

      case 'get_user': {
        if (!email) {
          return NextResponse.json(
            { error: 'Email is required' },
            { status: 400 }
          )
        }

        const { data: user, error } = await getUserByEmailDirect(email)
        
        if (error) {
          return NextResponse.json(
            { error: 'Failed to fetch user' },
            { status: 500 }
          )
        }

        if (!user) {
          return NextResponse.json(
            { error: 'User not found' },
            { status: 404 }
          )
        }

        return NextResponse.json({
          user: {
            id: user.id,
            email: user.email,
            email_confirmed_at: user.email_confirmed_at,
            last_sign_in_at: user.last_sign_in_at,
            created_at: user.created_at,
            role: user.role || 'authenticated',
            user_metadata: user.raw_user_meta_data || {},
            app_metadata: user.raw_app_meta_data || {}
          }
        })
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('❌ Direct auth error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
*/
