import { NextRequest, NextResponse } from 'next/server'
import { directSchemaCache } from '@/lib/schema-cache-direct'
import { ensureCacheReady } from '@/lib/cache-initializer'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)

    console.log('👤 Fetching profiles via direct SQL schema cache', userId ? `for user: ${userId}` : '(paginated)')

    // Ensure cache is ready
    await ensureCacheReady()

    if (userId) {
      // Get specific user profile from direct SQL cache
      const profile = await directSchemaCache.getProfile(userId)

      if (!profile) {
        console.log(`ℹ️ No profile found for user: ${userId}`)
        return NextResponse.json({
          error: 'Profile not found',
          userId
        }, { status: 404 })
      }

      const totalDuration = Date.now() - startTime
      console.log(`✅ Found cached profile for user: ${userId} in ${totalDuration}ms`)

      return NextResponse.json({
        success: true,
        profile,
        message: 'Profile retrieved successfully',
        performance: {
          totalTime: totalDuration,
          cached: true,
          source: 'schema_cache_direct_sql'
        },
        cache: directSchemaCache.getStats(),
        timestamp: new Date().toISOString()
      })
    } else {
      // Get all profiles with pagination from direct SQL cache
      const offset = (page - 1) * limit
      const { profiles, total } = await directSchemaCache.getAllProfiles(offset, limit)
      
      const totalPages = Math.ceil(total / limit)
      const totalDuration = Date.now() - startTime
      
      console.log(`✅ Found ${profiles.length} cached profiles in ${totalDuration}ms`)

      return NextResponse.json({
        success: true,
        profiles,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        },
        message: 'Profiles retrieved successfully',
        performance: {
          totalTime: totalDuration,
          cached: true,
          source: 'schema_cache_direct_sql'
        },
        cache: directSchemaCache.getStats(),
        timestamp: new Date().toISOString()
      })
    }

  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Simple profiles API error:', error)
    
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message,
      performance: {
        totalTime: totalDuration,
        cached: false
      }
    }, { status: 500 })
  }
}
