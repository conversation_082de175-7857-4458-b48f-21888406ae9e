import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/client'
import { createClient as createServerClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  const testResults = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    tests: {} as any
  }

  // Test 1: Environment Variables
  testResults.tests.environmentVariables = {
    supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    supabaseAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    supabaseServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    urlValue: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + '...',
    anonKeyPrefix: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20) + '...'
  }

  // Test 2: Client-side Supabase Connection
  try {
    const clientSupabase = createClient()
    const { data: clientData, error: clientError } = await clientSupabase
      .from('businesses')
      .select('id, name, is_active')
      .eq('is_active', true)
      .limit(5)

    testResults.tests.clientConnection = {
      success: !clientError,
      error: clientError?.message,
      dataCount: clientData?.length || 0,
      sampleData: clientData?.slice(0, 2)
    }
  } catch (error) {
    testResults.tests.clientConnection = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Test 3: Server-side Supabase Connection
  try {
    const serverSupabase = await createServerClient()
    const { data: serverData, error: serverError } = await serverSupabase
      .from('businesses')
      .select('id, name, is_active')
      .eq('is_active', true)
      .limit(5)

    testResults.tests.serverConnection = {
      success: !serverError,
      error: serverError?.message,
      dataCount: serverData?.length || 0,
      sampleData: serverData?.slice(0, 2)
    }
  } catch (error) {
    testResults.tests.serverConnection = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Test 4: Auth Status
  try {
    const serverSupabase = await createServerClient()
    const { data: { user }, error: authError } = await serverSupabase.auth.getUser()
    
    testResults.tests.authStatus = {
      success: !authError,
      error: authError?.message,
      hasUser: !!user,
      userId: user?.id?.substring(0, 8) + '...' || null
    }
  } catch (error) {
    testResults.tests.authStatus = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Test 5: RLS Policy Test
  try {
    const serverSupabase = await createServerClient()
    const { data: rlsData, error: rlsError } = await serverSupabase
      .from('businesses')
      .select('count')
      .eq('is_active', true)

    testResults.tests.rlsPolicies = {
      success: !rlsError,
      error: rlsError?.message,
      canAccessPublicData: !!rlsData
    }
  } catch (error) {
    testResults.tests.rlsPolicies = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Overall status
  const allTests = Object.values(testResults.tests)
  const successfulTests = allTests.filter((test: any) => test.success).length
  const totalTests = allTests.length

  testResults.summary = {
    overallSuccess: successfulTests === totalTests,
    successfulTests,
    totalTests,
    successRate: `${Math.round((successfulTests / totalTests) * 100)}%`
  }

  return NextResponse.json(testResults, { 
    status: testResults.summary.overallSuccess ? 200 : 500,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
}
