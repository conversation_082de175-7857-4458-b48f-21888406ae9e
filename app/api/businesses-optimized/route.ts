import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-direct'
import { performanceMonitor } from '@/lib/performance-monitor'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100) // Max 100 per page
    const category = searchParams.get('category') || null
    const state = searchParams.get('state') || null
    const search = searchParams.get('search') || null
    const spotlightOnly = searchParams.get('spotlight') === 'true'
    const source = searchParams.get('source') || 'industry' // 'industry' or 'dashboard'
    
    const offset = (page - 1) * limit
    
    console.log(`🔄 Fetching optimized businesses - Page: ${page}, Limit: ${limit}, Source: ${source}`)
    
    let query: string
    let params: any[]
    
    if (source === 'dashboard') {
      // Use dashboard materialized view
      query = `SELECT * FROM get_dashboard_businesses_paginated($1, $2, $3, $4)`
      params = [limit, offset, category, null] // has_discount filter can be added later
    } else {
      // Use industry materialized view
      query = `SELECT * FROM get_industry_businesses_paginated($1, $2, $3, $4, $5, $6)`
      params = [limit, offset, category, state, search, spotlightOnly]
    }
    
    const queryStartTime = Date.now()
    const { data, error } = await executeQuery(query, params)
    const queryDuration = Date.now() - queryStartTime
    
    if (error) {
      console.error('❌ Optimized businesses query error:', error)
      
      // Track failed query
      performanceMonitor.trackDatabaseQuery(
        query,
        source === 'dashboard' ? 'dashboard_businesses' : 'industry_businesses',
        'SELECT',
        queryDuration,
        0
      )
      
      return NextResponse.json(
        { error: 'Failed to fetch businesses', details: error },
        { status: 500 }
      )
    }
    
    const businesses = data || []
    const totalCount = businesses.length > 0 ? businesses[0].total_count : 0
    const totalPages = Math.ceil(totalCount / limit)
    
    // Log performance metrics
    const totalDuration = Date.now() - startTime
    performanceMonitor.trackDatabaseQuery(
      query,
      source === 'dashboard' ? 'dashboard_businesses' : 'industry_businesses',
      'SELECT',
      queryDuration,
      businesses.length
    )
    
    performanceMonitor.trackAPICall(
      '/api/businesses-optimized',
      'GET',
      totalDuration,
      200,
      undefined,
      true // Using materialized view = cached
    )
    
    console.log(`✅ Successfully fetched ${businesses.length} businesses in ${totalDuration}ms (Query: ${queryDuration}ms)`)
    
    return NextResponse.json({
      success: true,
      data: businesses,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      },
      filters: {
        category,
        state,
        search,
        spotlightOnly,
        source
      },
      performance: {
        queryTime: queryDuration,
        totalTime: totalDuration,
        cached: true
      },
      timestamp: new Date().toISOString()
    })
    
  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Optimized businesses API error:', error)
    
    // Track failed API call
    performanceMonitor.trackAPICall(
      '/api/businesses-optimized',
      'GET',
      totalDuration,
      500
    )
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}

// POST endpoint to refresh materialized views
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const body = await request.json()
    const { action } = body
    
    if (action !== 'refresh_views') {
      return NextResponse.json(
        { error: 'Invalid action. Use "refresh_views"' },
        { status: 400 }
      )
    }
    
    console.log('🔄 Refreshing materialized views...')
    
    const queryStartTime = Date.now()
    const { error } = await executeQuery('SELECT refresh_all_business_views()', [])
    const queryDuration = Date.now() - queryStartTime
    
    if (error) {
      console.error('❌ Failed to refresh views:', error)
      return NextResponse.json(
        { error: 'Failed to refresh views', details: error },
        { status: 500 }
      )
    }
    
    const totalDuration = Date.now() - startTime
    
    // Log performance
    performanceMonitor.trackDatabaseQuery(
      'refresh_all_business_views',
      'materialized_views',
      'REFRESH',
      queryDuration
    )
    
    console.log(`✅ Materialized views refreshed in ${totalDuration}ms`)
    
    return NextResponse.json({
      success: true,
      message: 'Materialized views refreshed successfully',
      performance: {
        refreshTime: queryDuration,
        totalTime: totalDuration
      },
      timestamp: new Date().toISOString()
    })
    
  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ View refresh error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
