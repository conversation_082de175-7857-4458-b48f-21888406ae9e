import { NextRequest, NextResponse } from 'next/server';
import { qrCodeCache } from '@/lib/qr-code-cache';

export async function GET() {
  try {
    const stats = qrCodeCache.getStats();
    
    return NextResponse.json({
      success: true,
      cache: {
        stats,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('QR Cache stats error:', error);
    return NextResponse.json(
      { error: 'Failed to get cache stats' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userIds, action } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'preload':
        if (!userIds || !Array.isArray(userIds)) {
          return NextResponse.json(
            { error: 'userIds array is required for preload action' },
            { status: 400 }
          );
        }

        console.log(`🔄 Preloading QR codes for ${userIds.length} users`);
        await qrCodeCache.preloadQRCodes(userIds);
        
        const statsAfterPreload = qrCodeCache.getStats();
        
        return NextResponse.json({
          success: true,
          message: `Preloaded QR codes for ${userIds.length} users`,
          stats: statsAfterPreload
        });

      case 'clear':
        console.log('🧹 Clearing QR code cache');
        qrCodeCache.clear();
        
        return NextResponse.json({
          success: true,
          message: 'Cache cleared successfully'
        });

      case 'invalidate':
        if (!userIds || !Array.isArray(userIds)) {
          return NextResponse.json(
            { error: 'userIds array is required for invalidate action' },
            { status: 400 }
          );
        }

        userIds.forEach(userId => qrCodeCache.invalidateUser(userId));
        
        return NextResponse.json({
          success: true,
          message: `Invalidated cache for ${userIds.length} users`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: preload, clear, invalidate' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('QR Cache operation error:', error);
    return NextResponse.json(
      { error: 'Cache operation failed' },
      { status: 500 }
    );
  }
}