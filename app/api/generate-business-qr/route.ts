import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createClient } from '@/lib/supabase/server';
import { generateBusinessQRData, generateQRCodeDataURL } from '@/lib/qr-code-generator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { businessId, size = 256 } = body;

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Verify the business exists
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id, name, category')
      .eq('id', businessId)
      .single();

    if (businessError || !business) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      );
    }

    // Generate QR code data for the business
    const qrData = generateBusinessQRData(businessId);

    // Generate QR code image
    const qrCodeUrl = await generateQRCodeDataURL(qrData, {
      size,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    return NextResponse.json({
      success: true,
      qrCodeUrl,
      qrData,
      business: {
        id: business.id,
        name: business.name,
        category: business.category
      }
    });

  } catch (error) {
    console.error('Error generating business QR code:', error);
    return NextResponse.json(
      { error: 'Failed to generate QR code' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const businessId = searchParams.get('businessId');
    const size = parseInt(searchParams.get('size') || '256');

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Verify the business exists
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id, name, category')
      .eq('id', businessId)
      .single();

    if (businessError || !business) {
      return NextResponse.json(
        { error: 'Business not found' },
        { status: 404 }
      );
    }

    // Generate QR code data for the business
    const qrData = generateBusinessQRData(businessId);

    // Generate QR code image
    const qrCodeUrl = await generateQRCodeDataURL(qrData, {
      size,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    return NextResponse.json({
      success: true,
      qrCodeUrl,
      qrData,
      business: {
        id: business.id,
        name: business.name,
        category: business.category
      }
    });

  } catch (error) {
    console.error('Error generating business QR code:', error);
    return NextResponse.json(
      { error: 'Failed to generate QR code' },
      { status: 500 }
    );
  }
}
