import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/api-security'
import { logger } from '@/lib/monitoring'
import { executeQuery } from '@/lib/database-direct'

export async function GET(request: NextRequest) {
  return withSecurity(
    request,
    async (req, { user, supabase }) => {
      const { searchParams } = new URL(req.url)
      const type = searchParams.get('type') || 'overview'
      const since = searchParams.get('since')
      const limit = parseInt(searchParams.get('limit') || '100')

      try {
        switch (type) {
          case 'overview':
            return NextResponse.json(await getSystemOverview())
          
          case 'security':
            const securityLogs = logger.getLogs({
              level: 'security',
              since: since ? new Date(since) : undefined,
              limit
            })
            return NextResponse.json({ logs: securityLogs })
          
          case 'performance':
            const performanceLogs = logger.getLogs({
              category: 'performance',
              since: since ? new Date(since) : undefined,
              limit
            })
            return NextResponse.json({ logs: performanceLogs })
          
          case 'errors':
            const errorLogs = logger.getLogs({
              level: 'error',
              since: since ? new Date(since) : undefined,
              limit
            })
            return NextResponse.json({ logs: errorLogs })
          
          case 'health':
            return NextResponse.json(await getHealthStatus())
          
          default:
            return NextResponse.json(
              { error: 'Invalid monitoring type' },
              { status: 400 }
            )
        }
      } catch (error) {
        console.error('Monitoring API error:', error)
        return NextResponse.json(
          { error: 'Failed to fetch monitoring data' },
          { status: 500 }
        )
      }
    },
    {
      requireAuth: true,
      requireRole: 'admin',
      rateLimit: { windowMs: 60000, maxRequests: 60 }, // 60 requests per minute
      logRequest: true
    }
  )
}

async function getSystemOverview() {
  const now = new Date()
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  const recentLogs = logger.getLogs({ since: oneHourAgo })
  const dailyLogs = logger.getLogs({ since: oneDayAgo })

  const securityEvents = recentLogs.filter(log => log.level === 'security')
  const errors = recentLogs.filter(log => log.level === 'error')
  const warnings = recentLogs.filter(log => log.level === 'warn')

  return {
    timestamp: now.toISOString(),
    summary: {
      totalLogsLastHour: recentLogs.length,
      totalLogsLast24Hours: dailyLogs.length,
      securityEventsLastHour: securityEvents.length,
      errorsLastHour: errors.length,
      warningsLastHour: warnings.length
    },
    recentSecurityEvents: securityEvents.slice(-10),
    recentErrors: errors.slice(-10),
    systemHealth: await getHealthStatus()
  }
}

async function getHealthStatus() {
  // Simple database health check using executeQuery
  let databaseHealth = false
  try {
    await executeQuery('SELECT 1 as test', [])
    databaseHealth = true
  } catch (error) {
    console.error('Database health check failed:', error)
  }

  const checks = {
    database: databaseHealth,
    timestamp: new Date().toISOString()
  }

  // Add more health checks as needed
  const overallHealth = Object.values(checks).every(check => 
    typeof check === 'boolean' ? check : true
  )

  return {
    status: overallHealth ? 'healthy' : 'unhealthy',
    checks
  }
}

// Webhook endpoint for external monitoring services
export async function POST(request: NextRequest) {
  return withSecurity(
    request,
    async (req, { user }) => {
      try {
        const body = await req.json()
        const { type, data } = body

        switch (type) {
          case 'alert':
            logger.warn('external_monitoring', `External alert: ${data.message}`, data)
            break
          
          case 'metric':
            logger.info('external_monitoring', `External metric: ${data.name}`, data)
            break
          
          default:
            return NextResponse.json(
              { error: 'Invalid webhook type' },
              { status: 400 }
            )
        }

        return NextResponse.json({ success: true })
      } catch (error) {
        console.error('Monitoring webhook error:', error)
        return NextResponse.json(
          { error: 'Failed to process webhook' },
          { status: 500 }
        )
      }
    },
    {
      requireAuth: true,
      rateLimit: { windowMs: 60000, maxRequests: 100 },
      validateInput: (body) => {
        const errors: string[] = []
        
        if (!body.type || typeof body.type !== 'string') {
          errors.push('Type is required and must be a string')
        }
        
        if (!body.data || typeof body.data !== 'object') {
          errors.push('Data is required and must be an object')
        }
        
        return {
          isValid: errors.length === 0,
          errors: errors.length > 0 ? errors : undefined
        }
      },
      logRequest: true
    }
  )
}
