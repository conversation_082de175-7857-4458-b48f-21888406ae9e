import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-direct'
import { performanceMonitor } from '@/lib/performance-monitor'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('range') || '1h' // 1h, 6h, 24h, 7d
    const includeDetails = searchParams.get('details') === 'true'
    
    console.log(`🔄 Fetching monitoring dashboard data for range: ${timeRange}`)
    
    // Calculate time range in minutes
    const timeRangeMinutes = {
      '1h': 60,
      '6h': 360,
      '24h': 1440,
      '7d': 10080
    }[timeRange] || 60
    
    const dashboardData: any = {
      timestamp: new Date().toISOString(),
      timeRange,
      summary: {},
      performance: {},
      database: {},
      errors: {},
      system: {}
    }
    
    // =============================================
    // SYSTEM HEALTH SUMMARY
    // =============================================
    
    // Get system metrics
    const memUsage = process.memoryUsage()
    dashboardData.system = {
      uptime: Math.floor(process.uptime()),
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      },
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    }
    
    // =============================================
    // DATABASE PERFORMANCE METRICS
    // =============================================
    
    try {
      // Query performance logs
      const { data: perfData, error: perfError } = await executeQuery(`
        SELECT 
          query_name,
          COUNT(*) as total_queries,
          AVG(execution_time_ms) as avg_time,
          MIN(execution_time_ms) as min_time,
          MAX(execution_time_ms) as max_time,
          PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY execution_time_ms) as p95_time,
          SUM(CASE WHEN execution_time_ms > 1000 THEN 1 ELSE 0 END) as slow_queries
        FROM query_performance_log 
        WHERE executed_at >= NOW() - INTERVAL '${timeRangeMinutes} minutes'
        GROUP BY query_name
        ORDER BY avg_time DESC
        LIMIT 20
      `, [])
      
      if (!perfError && perfData) {
        dashboardData.database.queryPerformance = perfData
        dashboardData.database.totalQueries = perfData.reduce((sum, q) => sum + q.total_queries, 0)
        dashboardData.database.avgResponseTime = perfData.length > 0 
          ? Math.round(perfData.reduce((sum, q) => sum + q.avg_time, 0) / perfData.length)
          : 0
        dashboardData.database.slowQueries = perfData.reduce((sum, q) => sum + q.slow_queries, 0)
      }
      
      // Database connection health
      const { data: healthData, error: healthError } = await executeQuery('SELECT 1 as health', [])
      dashboardData.database.connectionHealth = !healthError ? 'healthy' : 'unhealthy'
      
      // Active businesses count
      const { data: businessData, error: businessError } = await executeQuery(
        'SELECT COUNT(*) as count FROM businesses WHERE is_active = true',
        []
      )
      if (!businessError && businessData) {
        dashboardData.summary.activeBusinesses = businessData[0].count
      }
      
      // Active users count (profiles with recent activity)
      const { data: userData, error: userError } = await executeQuery(`
        SELECT COUNT(*) as count 
        FROM profiles 
        WHERE updated_at >= NOW() - INTERVAL '7 days'
      `, [])
      if (!userError && userData) {
        dashboardData.summary.activeUsers = userData[0].count
      }
      
    } catch (dbError) {
      console.error('Database metrics error:', dbError)
      dashboardData.database.error = 'Failed to fetch database metrics'
    }
    
    // =============================================
    // APPLICATION PERFORMANCE METRICS
    // =============================================
    
    // Get performance metrics from monitor
    const metrics = performanceMonitor.getMetrics()
    
    // API performance summary
    const apiMetrics = metrics.filter(m => m.name === 'api_call')
    if (apiMetrics.length > 0) {
      dashboardData.performance.api = {
        totalRequests: apiMetrics.length,
        avgResponseTime: Math.round(apiMetrics.reduce((sum, m) => sum + m.duration, 0) / apiMetrics.length),
        errorRate: (apiMetrics.filter(m => m.status >= 400).length / apiMetrics.length * 100).toFixed(2),
        slowRequests: apiMetrics.filter(m => m.duration > 2000).length
      }
      
      // Top endpoints by request count
      const endpointCounts = apiMetrics.reduce((acc, m) => {
        acc[m.endpoint] = (acc[m.endpoint] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      dashboardData.performance.topEndpoints = Object.entries(endpointCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([endpoint, count]) => ({ endpoint, count }))
    }
    
    // Database query performance from monitor
    const dbMetrics = metrics.filter(m => m.name === 'database_query')
    if (dbMetrics.length > 0) {
      dashboardData.performance.database = {
        totalQueries: dbMetrics.length,
        avgQueryTime: Math.round(dbMetrics.reduce((sum, m) => sum + m.duration, 0) / dbMetrics.length),
        slowQueries: dbMetrics.filter(m => m.duration > 1000).length
      }
    }
    
    // Frontend performance from monitor
    const frontendMetrics = metrics.filter(m => m.name === 'component_render')
    if (frontendMetrics.length > 0) {
      dashboardData.performance.frontend = {
        totalRenders: frontendMetrics.length,
        avgRenderTime: Math.round(frontendMetrics.reduce((sum, m) => sum + m.duration, 0) / frontendMetrics.length),
        slowRenders: frontendMetrics.filter(m => m.duration > 100).length
      }
    }
    
    // =============================================
    // ERROR TRACKING
    // =============================================
    
    const errorMetrics = apiMetrics.filter(m => m.status >= 400)
    if (errorMetrics.length > 0) {
      dashboardData.errors = {
        totalErrors: errorMetrics.length,
        errorRate: (errorMetrics.length / apiMetrics.length * 100).toFixed(2),
        errorsByStatus: errorMetrics.reduce((acc, m) => {
          acc[m.status] = (acc[m.status] || 0) + 1
          return acc
        }, {} as Record<number, number>)
      }
    }
    
    // =============================================
    // PERFORMANCE SUMMARY
    // =============================================
    
    dashboardData.summary.status = 'healthy'
    dashboardData.summary.alerts = []
    
    // Check for performance issues
    if (dashboardData.database.avgResponseTime > 300) {
      dashboardData.summary.status = 'warning'
      dashboardData.summary.alerts.push('Database response time above 300ms')
    }
    
    if (dashboardData.performance.api?.errorRate > 5) {
      dashboardData.summary.status = 'critical'
      dashboardData.summary.alerts.push(`API error rate at ${dashboardData.performance.api.errorRate}%`)
    }
    
    if (dashboardData.database.slowQueries > 10) {
      dashboardData.summary.status = 'warning'
      dashboardData.summary.alerts.push(`${dashboardData.database.slowQueries} slow database queries detected`)
    }
    
    const totalDuration = Date.now() - startTime
    
    // Track this monitoring request
    performanceMonitor.trackAPICall(
      '/api/monitoring/dashboard',
      'GET',
      totalDuration,
      200
    )
    
    console.log(`✅ Monitoring dashboard data compiled in ${totalDuration}ms`)
    
    return NextResponse.json({
      success: true,
      data: dashboardData,
      performance: {
        compilationTime: totalDuration
      }
    })
    
  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Monitoring dashboard error:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to compile monitoring dashboard',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
