import { NextRequest, NextResponse } from 'next/server';
import { runConnectionDiagnostics } from '@/lib/dns-test';

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Starting connection diagnostics...');
    
    // Run comprehensive diagnostics
    const diagnostics = await runConnectionDiagnostics();
    
    // Test direct database connection
    let dbConnectionTest = null;
    try {
      const { getUserProfileMinimal } = await import('@/lib/database-direct');
      // Test with a dummy UUID to see if connection works
      const testResult = await getUserProfileMinimal('00000000-0000-0000-0000-000000000000');
      dbConnectionTest = {
        success: true,
        message: 'Database connection test completed (no user found is expected)'
      };
    } catch (error: any) {
      dbConnectionTest = {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
    
    // Test Supabase REST API
    let supabaseApiTest = null;
    try {
      const { createClient } = await import('@/utils/supabase/server');
      const supabase = await createClient();
      const { data, error } = await supabase.from('profiles').select('id').limit(1);
      
      supabaseApiTest = {
        success: !error,
        message: error ? error.message : 'Supabase API connection successful',
        dataReceived: !!data
      };
    } catch (error: any) {
      supabaseApiTest = {
        success: false,
        error: error.message
      };
    }
    
    const response = {
      timestamp: new Date().toISOString(),
      diagnostics,
      tests: {
        directDatabase: dbConnectionTest,
        supabaseApi: supabaseApiTest
      },
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasDbPassword: !!process.env.SUPABASE_DB_PASSWORD,
        hasServiceRole: !!process.env.SUPABASE_SERVICE_ROLE_KEY
      }
    };
    
    console.log('🔧 Connection diagnostics completed');
    
    return NextResponse.json(response, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
  } catch (error: any) {
    console.error('❌ Connection diagnostics failed:', error);
    
    return NextResponse.json({
      error: 'Connection diagnostics failed',
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;
    
    if (action === 'test-profile-query') {
      const { userId } = body;
      
      if (!userId) {
        return NextResponse.json({
          error: 'userId is required for profile query test'
        }, { status: 400 });
      }
      
      // Test profile query with provided user ID
      const { getUserProfileMinimal } = await import('@/lib/database-direct');
      const profile = await getUserProfileMinimal(userId);
      
      return NextResponse.json({
        success: !!profile,
        profile,
        timestamp: new Date().toISOString()
      });
    }
    
    return NextResponse.json({
      error: 'Unknown action'
    }, { status: 400 });
    
  } catch (error: any) {
    return NextResponse.json({
      error: 'Test failed',
      message: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
