import { NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-direct'

export async function GET(request: Request) {
  try {
    console.log('🔍 Searching for "The Art of Pitching" business...')
    
    // Find the business
    const businessQuery = `
      SELECT 
        id,
        name,
        user_id,
        category,
        website,
        contact_email,
        contact_name,
        contact_phone,
        business_address,
        premium_discount,
        logo_url,
        is_active,
        created_at,
        updated_at
      FROM businesses 
      WHERE name ILIKE $1
      ORDER BY created_at DESC
    `
    
    const { data: businesses, error: businessError } = await executeQuery(businessQuery, ['%The Art of Pitching%'])
    
    if (businessError) {
      throw new Error(`Error finding business: ${businessError}`)
    }
    
    // Get some user profiles for reference
    const profileQuery = `
      SELECT 
        id,
        first_name,
        last_name,
        user_email,
        is_card_holder,
        is_business_applicant,
        created_at
      FROM profiles 
      ORDER BY created_at DESC 
      LIMIT 10
    `
    
    const { data: profiles, error: profileError } = await executeQuery(profileQuery, [])
    
    if (profileError) {
      console.warn('Warning: Could not fetch user profiles:', profileError)
    }
    
    return NextResponse.json({
      success: true,
      businesses: businesses || [],
      recentProfiles: profiles || [],
      message: businesses && businesses.length > 0 
        ? `Found ${businesses.length} business(es) matching "The Art of Pitching"` 
        : 'No business found with name containing "The Art of Pitching"'
    })
    
  } catch (error) {
    console.error('❌ Error in Art of Pitching lookup:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to search for business', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { businessId, newUserId } = body
    
    if (!businessId || !newUserId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required parameters: businessId and newUserId' 
        },
        { status: 400 }
      )
    }
    
    console.log(`🔄 Updating business ${businessId} to set user_id to ${newUserId}...`)
    
    // First verify the user exists
    const userCheckQuery = `
      SELECT id, first_name, last_name, user_email
      FROM profiles 
      WHERE id = $1
    `
    
    const { data: userCheck, error: userError } = await executeQuery(userCheckQuery, [newUserId])
    
    if (userError) {
      throw new Error(`Error checking user: ${userError}`)
    }
    
    if (!userCheck || userCheck.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: `User with ID ${newUserId} not found in profiles table` 
        },
        { status: 404 }
      )
    }
    
    // Update the business
    const updateQuery = `
      UPDATE businesses 
      SET 
        user_id = $1,
        updated_at = NOW()
      WHERE id = $2
      RETURNING 
        id,
        name,
        user_id,
        updated_at
    `
    
    const { data: updatedBusiness, error: updateError } = await executeQuery(updateQuery, [newUserId, businessId])
    
    if (updateError) {
      throw new Error(`Error updating business: ${updateError}`)
    }
    
    if (!updatedBusiness || updatedBusiness.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: `Business with ID ${businessId} not found or could not be updated` 
        },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      business: updatedBusiness[0],
      user: userCheck[0],
      message: `Successfully updated business "${updatedBusiness[0].name}" to be owned by user ${userCheck[0].first_name} ${userCheck[0].last_name} (${userCheck[0].user_email})`
    })
    
  } catch (error) {
    console.error('❌ Error updating business user_id:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update business user_id', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    )
  }
}