import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    // Handle FormData for file uploads
    const formData = await request.formData();

    // Extract form fields
    const userId = formData.get('user_id') as string;
    const businessName = formData.get('business_name') as string;
    const category = formData.get('category') as string;
    const website = formData.get('website') as string;
    const contactName = formData.get('contact_name') as string;
    const contactEmail = formData.get('contact_email') as string;
    const contactPhone = formData.get('contact_phone') as string;
    const businessAddress = formData.get('business_address') as string;
    const proposedDiscount = formData.get('proposed_discount') as string;
    const loyaltyRewardFrequency = formData.get('loyalty_reward_frequency') as string;
    const logoFile = formData.get('logo_file') as File | null;

    if (!userId || !businessName || !category || !contactName || !contactEmail || !proposedDiscount) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    console.log('Creating network application for user:', userId);

    // Handle logo file if provided
    let logoData = null;
    if (logoFile && logoFile.size > 0) {
      // Convert file to base64 for storage in database
      const bytes = await logoFile.arrayBuffer();
      const buffer = Buffer.from(bytes);
      logoData = {
        filename: logoFile.name,
        mimetype: logoFile.type,
        size: logoFile.size,
        data: buffer.toString('base64')
      };
      console.log('Logo file received:', logoFile.name, logoFile.size, 'bytes');
    }

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Insert the network application
    const applicationData = {
      user_id: userId,
      business_name: businessName,
      category: category,
      website: website || null,
      contact_name: contactName,
      contact_email: contactEmail,
      contact_phone: contactPhone || null,
      business_address: businessAddress || null,
      proposed_discount: proposedDiscount,
      loyalty_reward_frequency: loyaltyRewardFrequency || 'monthly',
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      // Store logo data as JSON if provided
      logo_data: logoData ? JSON.stringify(logoData) : null
    };

    const { data: application, error: applicationError } = await supabase
      .from('network_applications')
      .insert(applicationData)
      .select()
      .single();

    if (applicationError) {
      console.error('Network application creation error:', applicationError);
      
      // Try fallback with direct database access
      try {
        const { createBusinessApplicationDirect } = await import('@/lib/database-direct');
        const directApplication = await createBusinessApplicationDirect({
          user_id: userId,
          business_name: businessName,
          category: category,
          website: website,
          contact_name: contactName,
          contact_email: contactEmail,
          contact_phone: contactPhone,
          business_address: businessAddress,
          proposed_discount: proposedDiscount,
          loyalty_reward_frequency: loyaltyRewardFrequency,
          logo_data: logoData ? JSON.stringify(logoData) : null
        });

        if (directApplication) {
          console.log('Successfully created application via direct database access');

          // Update profile to mark as business applicant (fallback path)
          try {
            const { error: profileError } = await supabase
              .from('profiles')
              .update({
                is_business_applicant: true
              })
              .eq('id', userId);

            if (profileError) {
              console.error('Warning: Failed to update profile is_business_applicant (fallback):', profileError);
            } else {
              console.log('✅ Successfully updated profile is_business_applicant to true (fallback) for user:', userId);
            }
          } catch (profileUpdateError) {
            console.error('Warning: Exception updating profile is_business_applicant (fallback):', profileUpdateError);
          }

          return NextResponse.json({
            success: true,
            data: directApplication,
            message: 'Network application submitted successfully (fallback)'
          });
        }
      } catch (fallbackError) {
        console.error('Fallback database access also failed:', fallbackError);
      }

      return NextResponse.json(
        { 
          error: 'Failed to submit network application', 
          details: applicationError.message,
          code: applicationError.code 
        },
        { status: 500 }
      );
    }

    console.log('Successfully created network application:', application.id);

    // Update profile to mark as business applicant
    try {
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_business_applicant: true
        })
        .eq('id', userId);

      if (profileError) {
        console.error('Warning: Failed to update profile is_business_applicant:', profileError);
        // Don't fail the entire request if profile update fails
      } else {
        console.log('✅ Successfully updated profile is_business_applicant to true for user:', userId);
      }
    } catch (profileUpdateError) {
      console.error('Warning: Exception updating profile is_business_applicant:', profileUpdateError);
      // Don't fail the entire request if profile update fails
    }

    return NextResponse.json({
      success: true,
      data: application,
      message: 'Network application submitted successfully'
    });

  } catch (error: any) {
    console.error('Network applications API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }

    console.log('Fetching network applications for user:', userId);

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Get user's network applications
    const { data: applications, error: applicationsError } = await supabase
      .from('network_applications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (applicationsError) {
      console.error('Network applications query error:', applicationsError);
      return NextResponse.json(
        { 
          error: 'Failed to fetch network applications', 
          details: applicationsError.message,
          code: applicationsError.code 
        },
        { status: 500 }
      );
    }

    console.log(`Successfully fetched ${applications?.length || 0} network applications for user:`, userId);

    return NextResponse.json({
      applications: applications || [],
      message: 'Network applications retrieved successfully'
    });

  } catch (error: any) {
    console.error('Network applications GET API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}
