import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, applications } = body;

    if (!userId || !applications || !Array.isArray(applications) || applications.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: userId and applications array' },
        { status: 400 }
      );
    }

    console.log(`Creating ${applications.length} network applications for user:`, userId);

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Prepare application data for bulk insert
    const applicationData = applications.map(app => ({
      user_id: userId,
      business_name: app.businessName,
      category: app.category,
      website: app.website || null,
      contact_name: app.contactName,
      contact_email: app.contactEmail,
      contact_phone: app.contactPhone || null,
      business_address: app.businessAddress || null,
      proposed_discount: app.proposedDiscount,
      loyalty_reward_frequency: app.loyaltyRewardFrequency || 'monthly',
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    // Insert all applications
    const { data: insertedApplications, error: applicationError } = await supabase
      .from('network_applications')
      .insert(applicationData)
      .select();

    if (applicationError) {
      console.error('Bulk network application creation error:', applicationError);
      
      // Try fallback with direct database access
      try {
        const { createBusinessApplicationDirect } = await import('@/lib/database-direct');
        const directApplications = [];
        
        for (const app of applications) {
          const directApplication = await createBusinessApplicationDirect({
            user_id: userId,
            business_name: app.businessName,
            category: app.category,
            website: app.website,
            contact_name: app.contactName,
            contact_email: app.contactEmail,
            contact_phone: app.contactPhone,
            business_address: app.businessAddress,
            proposed_discount: app.proposedDiscount,
            loyalty_reward_frequency: app.loyaltyRewardFrequency
          });
          
          if (directApplication) {
            directApplications.push(directApplication);
          }
        }

        if (directApplications.length > 0) {
          console.log(`Successfully created ${directApplications.length} applications via direct database access`);
          
          // Update profile to mark as business applicant (fallback path)
          try {
            const { error: profileError } = await supabase
              .from('profiles')
              .update({
                is_business_applicant: true
              })
              .eq('id', userId);

            if (profileError) {
              console.error('Warning: Failed to update profile is_business_applicant (bulk fallback):', profileError);
            } else {
              console.log('✅ Successfully updated profile is_business_applicant to true (bulk fallback) for user:', userId);
            }
          } catch (profileUpdateError) {
            console.error('Warning: Exception updating profile is_business_applicant (bulk fallback):', profileUpdateError);
          }
          
          return NextResponse.json({
            success: true,
            data: directApplications,
            message: `${directApplications.length} network applications submitted successfully (fallback)`
          });
        }
      } catch (fallbackError) {
        console.error('Bulk fallback database access also failed:', fallbackError);
      }

      return NextResponse.json(
        { 
          error: 'Failed to submit network applications', 
          details: applicationError.message,
          code: applicationError.code 
        },
        { status: 500 }
      );
    }

    console.log(`Successfully created ${insertedApplications.length} network applications`);

    // Update profile to mark as business applicant
    try {
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_business_applicant: true
        })
        .eq('id', userId);

      if (profileError) {
        console.error('Warning: Failed to update profile is_business_applicant (bulk):', profileError);
        // Don't fail the entire request if profile update fails
      } else {
        console.log('✅ Successfully updated profile is_business_applicant to true (bulk) for user:', userId);
      }
    } catch (profileUpdateError) {
      console.error('Warning: Exception updating profile is_business_applicant (bulk):', profileUpdateError);
      // Don't fail the entire request if profile update fails
    }

    return NextResponse.json({
      success: true,
      data: insertedApplications,
      message: `${insertedApplications.length} network applications submitted successfully`
    });

  } catch (error: any) {
    console.error('Bulk network applications API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}
