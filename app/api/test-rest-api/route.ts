import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const testResults = {
    timestamp: new Date().toISOString(),
    tests: {} as any
  }

  // Test 1: Direct REST API call
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/businesses?select=id,name,is_active&is_active=eq.true&limit=5`,
      {
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=minimal'
        }
      }
    )

    if (response.ok) {
      const data = await response.json()
      testResults.tests.restApi = {
        success: true,
        status: response.status,
        dataCount: data.length,
        sampleData: data.slice(0, 2)
      }
    } else {
      const errorText = await response.text()
      testResults.tests.restApi = {
        success: false,
        status: response.status,
        error: errorText
      }
    }
  } catch (error) {
    testResults.tests.restApi = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Test 2: Check PostgREST schema endpoint
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`,
      {
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
        }
      }
    )

    testResults.tests.postgrestSchema = {
      success: response.ok,
      status: response.status,
      contentType: response.headers.get('content-type')
    }

    if (!response.ok) {
      const errorText = await response.text()
      testResults.tests.postgrestSchema.error = errorText
    }
  } catch (error) {
    testResults.tests.postgrestSchema = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Test 3: Test with service role key
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/businesses?select=id,name&is_active=eq.true&limit=3`,
      {
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY!}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.ok) {
      const data = await response.json()
      testResults.tests.serviceRoleApi = {
        success: true,
        status: response.status,
        dataCount: data.length
      }
    } else {
      const errorText = await response.text()
      testResults.tests.serviceRoleApi = {
        success: false,
        status: response.status,
        error: errorText
      }
    }
  } catch (error) {
    testResults.tests.serviceRoleApi = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Overall status
  const allTests = Object.values(testResults.tests)
  const successfulTests = allTests.filter((test: any) => test.success).length
  const totalTests = allTests.length

  testResults.summary = {
    overallSuccess: successfulTests === totalTests,
    successfulTests,
    totalTests,
    successRate: `${Math.round((successfulTests / totalTests) * 100)}%`
  }

  return NextResponse.json(testResults, { 
    status: testResults.summary.overallSuccess ? 200 : 500 
  })
}
