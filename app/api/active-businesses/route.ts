import { NextRequest, NextResponse } from 'next/server'
import { getActiveBusinessesDirect } from '@/lib/database-direct'

export async function GET(request: NextRequest) {
  try {
    console.log('🏢 Fetching active businesses')

    // Get active businesses using direct database access
    const businesses = await getActiveBusinessesDirect()

    if (!businesses) {
      return NextResponse.json(
        { error: 'Failed to fetch active businesses' },
        { status: 500 }
      )
    }

    console.log(`✅ Found ${businesses.length} active businesses`)

    return NextResponse.json({
      success: true,
      data: businesses,
      message: 'Active businesses retrieved successfully'
    })

  } catch (error: any) {
    console.error('❌ Active businesses API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
