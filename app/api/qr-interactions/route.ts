import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { parseQRData } from '@/lib/qr-code-generator';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const type = searchParams.get('type'); // 'stats' or 'interactions'

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    if (type === 'stats') {
      // Fetch stats directly from database
      const [userConnectionsResult, businessVisitsResult] = await Promise.all([
        supabase
          .from('qr_interactions')
          .select('*', { count: 'exact', head: true })
          .or(`scanner_user_id.eq.${userId},scanned_user_id.eq.${userId}`)
          .eq('interaction_type', 'user_scan'),
        supabase
          .from('qr_interactions')
          .select('*', { count: 'exact', head: true })
          .eq('scanner_user_id', userId)
          .eq('interaction_type', 'business_scan')
      ]);

      if (userConnectionsResult.error || businessVisitsResult.error) {
        throw new Error('Failed to fetch statistics from database');
      }

      const stats = {
        userConnections: userConnectionsResult.count || 0,
        businessVisits: businessVisitsResult.count || 0,
      };

      return NextResponse.json(stats);
    }

    // Default: return interactions
    const { data: interactions, error } = await supabase
      .from('qr_interactions')
      .select(`
        id,
        scanner_user_id,
        scanned_user_id,
        scanned_business_id,
        interaction_type,
        created_at
      `)
      .or(`scanner_user_id.eq.${userId},scanned_user_id.eq.${userId}`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching interactions:', error);
      return NextResponse.json(
        { error: 'Failed to fetch interactions' },
        { status: 500 }
      );
    }

    return NextResponse.json({ interactions: interactions || [] });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { scannerUserId, qrData, locationData, metadata } = body;

    if (!scannerUserId || !qrData) {
      return NextResponse.json(
        { error: 'Scanner user ID and QR data are required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Parse QR code data
    const parsedQRData = parseQRData(qrData);
    if (!parsedQRData) {
      return NextResponse.json(
        { error: 'Invalid QR code format' },
        { status: 400 }
      );
    }

    // Prevent self-scanning
    if (parsedQRData.userId === scannerUserId) {
      return NextResponse.json(
        { error: 'Cannot scan your own QR code' },
        { status: 400 }
      );
    }

    // Prepare interaction data
    const interactionData: any = {
      scanner_user_id: scannerUserId,
      interaction_type: parsedQRData.type === 'business' ? 'business_scan' : 'user_scan',
      qr_data: qrData,
      location_data: locationData,
      interaction_metadata: metadata,
    };

    if (parsedQRData.type === 'business') {
      interactionData.scanned_business_id = parsedQRData.userId;
      
      // Also log to business_visits table for backward compatibility
      const { error: visitError } = await supabase
        .from('business_visits')
        .insert({
          user_id: scannerUserId,
          business_id: parsedQRData.userId,
          scanned_at: new Date().toISOString(),
        });

      if (visitError) {
        console.warn('Failed to log business visit:', visitError);
      }
    } else {
      interactionData.scanned_user_id = parsedQRData.userId;
    }

    // Insert the interaction
    const { data, error } = await supabase
      .from('qr_interactions')
      .insert(interactionData)
      .select()
      .single();

    if (error) {
      console.error('Error creating interaction:', error);
      return NextResponse.json(
        { error: 'Failed to create interaction' },
        { status: 500 }
      );
    }

    // Get additional data for response - optimize with single query
    let responseData: any = { interaction: data };

    if (parsedQRData.type === 'user') {
      // Get scanned user's profile
      const { data: scannedProfile, error: profileError } = await supabase
        .from('profiles')
        .select('first_name, last_name, is_card_holder')
        .eq('id', parsedQRData.userId)
        .single();

      if (!profileError) {
        responseData.scannedProfile = scannedProfile;
      }
    } else {
      // Get business information
      const { data: business, error: businessError } = await supabase
        .from('businesses')
        .select('name, category, logo_url')
        .eq('id', parsedQRData.userId)
        .single();

      if (!businessError) {
        responseData.business = business;
      }
    }

    // Cache system removed - no invalidation needed

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
