import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'

export async function GET(request: NextRequest) {
  const testResults = {
    timestamp: new Date().toISOString(),
    tests: {} as any
  }

  // Test 1: Direct PostgreSQL connection
  try {
    const pool = new Pool({
      connectionString: `postgresql://postgres:${process.env.SUPABASE_DB_PASSWORD}@db.haqbtbpmyadkocakqnew.supabase.co:5432/postgres`,
      ssl: { rejectUnauthorized: false }
    })

    const client = await pool.connect()
    
    // Test basic query
    const result = await client.query('SELECT COUNT(*) as count FROM businesses WHERE is_active = true')
    
    testResults.tests.directPostgres = {
      success: true,
      businessCount: result.rows[0].count,
      connectionWorking: true
    }

    client.release()
    await pool.end()
  } catch (error) {
    testResults.tests.directPostgres = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Test 2: Check if PostgRE<PERSON> is responding at all
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/`,
      {
        method: 'HEAD',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        }
      }
    )

    testResults.tests.postgrestHealth = {
      success: response.ok,
      status: response.status,
      headers: Object.fromEntries(response.headers.entries())
    }
  } catch (error) {
    testResults.tests.postgrestHealth = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Test 3: Try a simple table that should always work
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/schema_metadata?select=*`,
      {
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY!}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.ok) {
      const data = await response.json()
      testResults.tests.simpleTable = {
        success: true,
        status: response.status,
        dataCount: data.length
      }
    } else {
      const errorText = await response.text()
      testResults.tests.simpleTable = {
        success: false,
        status: response.status,
        error: errorText
      }
    }
  } catch (error) {
    testResults.tests.simpleTable = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Overall status
  const allTests = Object.values(testResults.tests)
  const successfulTests = allTests.filter((test: any) => test.success).length
  const totalTests = allTests.length

  testResults.summary = {
    overallSuccess: successfulTests > 0, // At least one test should pass
    successfulTests,
    totalTests,
    successRate: `${Math.round((successfulTests / totalTests) * 100)}%`,
    recommendation: successfulTests > 0 ? 
      "Direct database access works. PostgREST schema cache issue detected." :
      "All connections failing. Check environment variables and network."
  }

  return NextResponse.json(testResults, { 
    status: successfulTests > 0 ? 200 : 500 
  })
}
