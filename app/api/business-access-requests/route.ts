import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    const { user_id, business_id, message } = await request.json();

    if (!user_id || !business_id) {
      return NextResponse.json(
        { error: 'Missing required fields: user_id and business_id' },
        { status: 400 }
      );
    }

    console.log('Creating business access request for user:', user_id, 'business:', business_id);

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Check if user already has a pending request for this business
    const { data: existingRequest, error: checkError } = await supabase
      .from('business_access_requests')
      .select('id, status')
      .eq('user_id', user_id)
      .eq('business_id', business_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing requests:', checkError);
      return NextResponse.json(
        { error: 'Failed to check existing requests' },
        { status: 500 }
      );
    }

    if (existingRequest) {
      if (existingRequest.status === 'pending') {
        return NextResponse.json(
          { error: 'You already have a pending request for this business' },
          { status: 409 }
        );
      } else if (existingRequest.status === 'approved') {
        return NextResponse.json(
          { error: 'You already have access to this business' },
          { status: 409 }
        );
      }
    }

    // Insert the business access request
    const requestData = {
      user_id,
      business_id,
      message: message?.trim() || null,
      status: 'pending',
      requested_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: request_record, error: insertError } = await supabase
      .from('business_access_requests')
      .insert(requestData)
      .select()
      .single();

    if (insertError) {
      console.error('Business access request creation error:', insertError);
      
      if (insertError.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'You already have a request for this business' },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to submit business access request', 
          details: insertError.message,
          code: insertError.code 
        },
        { status: 500 }
      );
    }

    console.log('Successfully created business access request:', request_record.id);

    return NextResponse.json({
      success: true,
      data: request_record,
      message: 'Business access request submitted successfully'
    });

  } catch (error: any) {
    console.error('Business access requests API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }

    console.log('Fetching business access requests for user:', userId);

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Get user's business access requests with business details
    const { data: requests, error: requestsError } = await supabase
      .from('business_access_requests')
      .select(`
        *,
        businesses (
          id,
          name,
          business_address,
          contact_name,
          contact_email
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (requestsError) {
      console.error('Business access requests query error:', requestsError);
      return NextResponse.json(
        { 
          error: 'Failed to fetch business access requests', 
          details: requestsError.message,
          code: requestsError.code 
        },
        { status: 500 }
      );
    }

    console.log(`Successfully fetched ${requests?.length || 0} business access requests for user:`, userId);

    return NextResponse.json({
      requests: requests || [],
      message: 'Business access requests retrieved successfully'
    });

  } catch (error: any) {
    console.error('Business access requests GET API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}