import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const { user_id, business_name, contact_email, contact_name } = body
    
    if (!user_id || !business_name || !contact_email || !contact_name) {
      return NextResponse.json(
        { error: 'Missing required fields: user_id, business_name, contact_email, contact_name' },
        { status: 400 }
      )
    }

    console.log('📝 Processing business registration for user:', user_id)

    // Use Supabase instead of direct database connection to reduce connection pressure
    const supabase = await createClient()
    
    const applicationData = {
      user_id: body.user_id,
      business_name: body.business_name || body.businessName,
      website: body.website || body.business_website || null,
      category: body.category || body.business_category,
      contact_name: body.contact_name || body.contactName || null,
      contact_email: body.contact_email || body.contactEmail || null,
      contact_phone: body.contact_phone || body.contactPhone || null,
      proposed_discount: body.proposed_discount || body.proposedDiscount,
      business_address: body.business_address || body.businessAddress || null,
      logo_url: body.logo_url || body.logoUrl || null,
      loyalty_reward_frequency: body.loyalty_reward_frequency || body.loyaltyRewardFrequency || 'monthly',
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { data: result, error } = await supabase
      .from('network_applications')
      .insert(applicationData)
      .select()
      .single()

    if (error) {
      console.error('❌ Supabase error creating business application:', error)
      return NextResponse.json(
        { error: 'Failed to create business application', details: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Business application created successfully:', result.id)

    // Update profile to mark as business applicant
    try {
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_business_applicant: true
        })
        .eq('id', user_id);

      if (profileError) {
        console.error('Warning: Failed to update profile is_business_applicant:', profileError);
        // Don't fail the entire request if profile update fails
      } else {
        console.log('✅ Successfully updated profile is_business_applicant to true for user:', user_id);
      }
    } catch (profileUpdateError) {
      console.error('Warning: Exception updating profile is_business_applicant:', profileUpdateError);
      // Don't fail the entire request if profile update fails
    }

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Business application submitted successfully'
    })

  } catch (error: any) {
    console.error('❌ Business registration API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
