import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    const { user_id, tier, membership_duration_months, purchase_amount } = await request.json();

    if (!user_id || !tier) {
      return NextResponse.json(
        { error: 'Missing required fields: user_id and tier' },
        { status: 400 }
      );
    }

    console.log('Processing VIP purchase update for user:', user_id, 'Tier:', tier);

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Calculate membership dates
    const membershipStartDate = new Date();
    const membershipEndDate = new Date();
    membershipEndDate.setMonth(membershipEndDate.getMonth() + (membership_duration_months || 12));

    // Update profile with VIP card information
    const profileUpdates = {
      is_card_holder: true,
      card_tier: tier,
      membership_start_date: membershipStartDate.toISOString(),
      membership_end_date: membershipEndDate.toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .update(profileUpdates)
      .eq('id', user_id)
      .select()
      .single();

    if (profileError) {
      console.error('Profile update error:', profileError);
      return NextResponse.json(
        { 
          error: 'Failed to update profile', 
          details: profileError.message,
          code: profileError.code 
        },
        { status: 500 }
      );
    }

    // Record the purchase in purchases table
    const purchaseData = {
      user_id,
      amount: purchase_amount || 0,
      product_type: 'vip_card',
      product_details: {
        tier,
        membership_duration_months: membership_duration_months || 12,
        membership_start_date: membershipStartDate.toISOString(),
        membership_end_date: membershipEndDate.toISOString()
      },
      status: 'completed',
      created_at: new Date().toISOString()
    };

    const { data: purchase, error: purchaseError } = await supabase
      .from('purchases')
      .insert(purchaseData)
      .select()
      .single();

    if (purchaseError) {
      console.error('Purchase record error:', purchaseError);
      // Don't fail the entire operation if purchase recording fails
      console.warn('Warning: Failed to record purchase but profile was updated successfully');
    }

    console.log('Successfully updated VIP card status for user:', user_id);

    return NextResponse.json({
      success: true,
      data: {
        profile: profile,
        purchase: purchase,
        membership: {
          start_date: membershipStartDate.toISOString(),
          end_date: membershipEndDate.toISOString(),
          duration_months: membership_duration_months || 12,
          tier: tier
        }
      },
      message: 'VIP card purchase processed successfully'
    });

  } catch (error: any) {
    console.error('VIP purchase update API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }

    console.log('Fetching VIP status for user:', userId);

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Get user's VIP status
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, is_card_holder, card_tier, membership_start_date, membership_end_date')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('VIP status query error:', profileError);
      return NextResponse.json(
        { 
          error: 'Failed to fetch VIP status', 
          details: profileError.message,
          code: profileError.code 
        },
        { status: 500 }
      );
    }

    // Check if membership is active
    const now = new Date();
    const membershipEnd = profile.membership_end_date ? new Date(profile.membership_end_date) : null;
    const isActive = profile.is_card_holder && (!membershipEnd || membershipEnd > now);

    console.log(`Successfully fetched VIP status for user: ${userId}`);

    return NextResponse.json({
      success: true,
      data: {
        user_id: userId,
        is_card_holder: profile.is_card_holder,
        card_tier: profile.card_tier,
        membership_start_date: profile.membership_start_date,
        membership_end_date: profile.membership_end_date,
        is_active: isActive,
        days_remaining: membershipEnd ? Math.max(0, Math.ceil((membershipEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))) : null
      },
      message: 'VIP status retrieved successfully'
    });

  } catch (error: any) {
    console.error('VIP status GET API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}