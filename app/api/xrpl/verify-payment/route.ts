import { NextRequest, NextResponse } from 'next/server'
import { xrplService, XRPL_CONFIG } from '@/lib/xrpl'
import { getSupabaseClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { transactionHash, cardType, userId, userEmail } = await request.json()

    // Validate input
    if (!transactionHash || !cardType) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // Verify the transaction
    const verification = await xrplService.verifyTransaction(transactionHash)

    if (!verification.verified) {
      return NextResponse.json(
        { error: 'Transaction verification failed' },
        { status: 400 }
      )
    }

    // Check if payment was sent to correct destination
    if (verification.destination !== XRPL_CONFIG.DESTINATION_WALLET) {
      return NextResponse.json(
        { error: 'Payment sent to incorrect destination' },
        { status: 400 }
      )
    }

    // Check if this transaction has already been processed
    const supabase = getSupabaseClient();
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      )
    }

    const { data: existingPayment } = await supabase
      .from('xrpl_payments')
      .select('id')
      .eq('transaction_hash', transactionHash)
      .single()

    if (existingPayment) {
      return NextResponse.json(
        { error: 'Transaction already processed' },
        { status: 400 }
      )
    }

    // Record the payment
    const { error: paymentError } = await supabase
      .from('xrpl_payments')
      .insert({
        transaction_hash: transactionHash,
        user_id: userId,
        user_email: userEmail,
        card_type: cardType,
        amount: verification.amount,
        currency: verification.currency,
        sender_address: verification.sender,
        destination_address: verification.destination,
        verified_at: new Date().toISOString(),
        status: 'completed'
      })

    if (paymentError) {
      console.error('Failed to record payment:', paymentError)
      return NextResponse.json(
        { error: 'Failed to record payment' },
        { status: 500 }
      )
    }

    // Update user's VIP card status if userId is provided
    if (userId) {
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          vip_card_type: cardType,
          vip_card_active: true,
          vip_card_activated_at: new Date().toISOString(),
          vip_card_payment_method: 'XRPL'
        })
        .eq('id', userId)

      if (userUpdateError) {
        console.error('Failed to update user VIP status:', userUpdateError)
        // Don't fail the request, as payment was successful
      }
    }

    // For upgrade scenarios, check if user already has a card and this is an upgrade
    if (userId && cardType) {
      const { data: currentUser } = await supabase
        .from('users')
        .select('vip_card_type, vip_card_active')
        .eq('id', userId)
        .single()

      if (currentUser?.vip_card_active) {
        // This is an upgrade - record the upgrade transaction
        const { error: upgradeError } = await supabase
          .from('vip_card_upgrades')
          .insert({
            user_id: userId,
            from_card_type: currentUser.vip_card_type,
            to_card_type: cardType,
            transaction_hash: transactionHash,
            upgrade_date: new Date().toISOString()
          })

        if (upgradeError) {
          console.error('Failed to record upgrade:', upgradeError)
        }
      }
    }

    return NextResponse.json({
      success: true,
      transactionHash,
      cardType,
      amount: verification.amount,
      currency: verification.currency,
      verified: true
    })
  } catch (error: any) {
    console.error('Verify payment error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to verify payment' },
      { status: 500 }
    )
  }
}