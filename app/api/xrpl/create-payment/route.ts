import { NextRequest, NextResponse } from 'next/server'
import { xrplService, VIP_CARD_PRICES } from '@/lib/xrpl'
import { pricingService } from '@/lib/pricing-service'

export async function POST(request: NextRequest) {
  try {
    const { cardType, paymentMethod, userWallet } = await request.json()

    // Validate input
    if (!cardType || !paymentMethod || !userWallet) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    if (!['XRP', 'FUSE'].includes(paymentMethod)) {
      return NextResponse.json(
        { error: 'Invalid payment method' },
        { status: 400 }
      )
    }

    // Get card pricing
    const cardPricing = VIP_CARD_PRICES[cardType as keyof typeof VIP_CARD_PRICES]
    if (!cardPricing) {
      return NextResponse.json(
        { error: 'Invalid card type' },
        { status: 400 }
      )
    }

    let amount: string
    let currency: string

    if (paymentMethod === 'FUSE') {
      amount = await pricingService.calculateFUSEAmount(cardPricing.usd)
      currency = 'FUSE'
    } else {
      // Calculate XRP amount based on current USD price
      amount = await pricingService.calculateXRPAmount(cardPricing.usd)
      currency = 'XRP'
    }

    // Create payment transaction
    let paymentTx
    if (paymentMethod === 'XRP') {
      paymentTx = xrplService.createXRPPayment(userWallet, amount)
    } else {
      paymentTx = xrplService.createFUSEPayment(userWallet, amount)
    }

    // Get user's current balance
    const balance = await xrplService.getAccountBalance(userWallet)

    return NextResponse.json({
      success: true,
      paymentTransaction: paymentTx,
      amount,
      currency,
      balance,
      cardType,
      destination: paymentTx.Destination
    })
  } catch (error: any) {
    console.error('Create payment error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to create payment' },
      { status: 500 }
    )
  }
}