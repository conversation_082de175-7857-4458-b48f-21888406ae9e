import { NextRequest, NextResponse } from 'next/server'
import { xrplService } from '@/lib/xrpl'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const usdAmount = searchParams.get('usdAmount')

    // Get current XRP price
    const xrpPriceUSD = await xrplService.getXRPPriceUSD()

    let response: any = {
      xrpPriceUSD,
      timestamp: Date.now()
    }

    // If USD amount provided, calculate XRP equivalent
    if (usdAmount) {
      const xrpAmount = await xrplService.calculateXRPAmount(usdAmount)
      response.xrpAmount = xrpAmount
      response.usdAmount = usdAmount
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('Exchange rate error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to get exchange rate' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { amounts } = await request.json()

    if (!Array.isArray(amounts)) {
      return NextResponse.json(
        { error: 'Invalid amounts array' },
        { status: 400 }
      )
    }

    const xrpPriceUSD = await xrplService.getXRPPriceUSD()
    
    const conversions = await Promise.all(
      amounts.map(async (usdAmount: string) => ({
        usd: usdAmount,
        xrp: await xrplService.calculateXRPAmount(usdAmount)
      }))
    )

    return NextResponse.json({
      xrpPriceUSD,
      conversions,
      timestamp: Date.now()
    })
  } catch (error: any) {
    console.error('Batch exchange rate error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to calculate exchange rates' },
      { status: 500 }
    )
  }
}