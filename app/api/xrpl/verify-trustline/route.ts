import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'xrpl';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      transactionHash, 
      userId, 
      expectedCurrency, 
      expectedIssuer 
    } = body;

    console.log('Trustline verification request:', { 
      transactionHash, 
      userId, 
      expectedCurrency, 
      expectedIssuer 
    });

    // Validate required fields
    if (!transactionHash) {
      return NextResponse.json(
        { error: 'Missing required field: transactionHash' },
        { status: 400 }
      );
    }

    if (!transactionHash.match(/^[A-F0-9]{64}$/i)) {
      return NextResponse.json(
        { error: 'Invalid transaction hash format. Must be 64 hexadecimal characters.' },
        { status: 400 }
      );
    }

    // Initialize XRPL client
    const xrplClient = new Client('wss://xrplcluster.com');
    
    try {
      // Connect to XRPL
      await xrplClient.connect();
      console.log('✅ Connected to XRPL');

      // Fetch transaction by hash
      const txResponse = await xrplClient.request({
        command: 'tx',
        transaction: transactionHash,
        binary: false
      });

      if (!txResponse.result) {
        return NextResponse.json(
          { error: 'Transaction not found on XRPL ledger' },
          { status: 404 }
        );
      }

      const txData = txResponse.result;
      console.log('📄 Transaction data:', {
        type: txData.TransactionType,
        account: txData.Account,
        result: txData.meta?.TransactionResult
      });

      // Verify transaction was successful
      if (txData.meta?.TransactionResult !== 'tesSUCCESS') {
        return NextResponse.json(
          { error: `Transaction failed with result: ${txData.meta?.TransactionResult}` },
          { status: 400 }
        );
      }

      // Log the full transaction data for debugging
      console.log('Full transaction data:', JSON.stringify(txData, null, 2));

      // Extract wallet address (the account that initiated the transaction)
      // First try to get it from the transaction data directly
      let walletAddress = txData.Account;

      // If not found, try to get it from the transaction metadata
      if (!walletAddress && txData.meta?.TransactionMeta?.TransactionIndex) {
        walletAddress = txData.meta.TransactionMeta.Account;
      }

      // If still not found, try to get it from the Bithomp API
      if (!walletAddress) {
        try {
          // Use Bithomp API to get transaction details
          const bithompResponse = await fetch(`https://bithomp.com/api/v2/transaction/${transactionHash}`, {
            headers: {
              'Accept': 'application/json'
            }
          });

          if (bithompResponse.ok) {
            const bithompData = await bithompResponse.json();
            console.log('Bithomp data:', bithompData);

            // Extract account from Bithomp response
            if (bithompData.address) {
              walletAddress = bithompData.address;
            } else if (bithompData.account) {
              walletAddress = bithompData.account;
            } else if (bithompData.from) {
              walletAddress = bithompData.from;
            }
          }
        } catch (bithompError) {
          console.warn('⚠️ Error fetching from Bithomp:', bithompError);
          // Continue with other methods if Bithomp fails
        }
      }

      // If we still don't have a wallet address, check if it's in the transaction hash
      if (!walletAddress) {
        // Try to extract from affectedNodes in metadata
        try {
          const affectedNodes = txData.meta?.AffectedNodes || [];
          for (const node of affectedNodes) {
            if (node.ModifiedNode?.LedgerEntryType === 'RippleState') {
              const finalFields = node.ModifiedNode?.FinalFields;
              if (finalFields?.HighLimit?.issuer === expectedIssuer) {
                walletAddress = finalFields.LowLimit?.issuer;
                break;
              } else if (finalFields?.LowLimit?.issuer === expectedIssuer) {
                walletAddress = finalFields.HighLimit?.issuer;
                break;
              }
            }
          }
        } catch (metaError) {
          console.warn('⚠️ Error extracting from metadata:', metaError);
        }
      }

      // Final fallback - use the transaction hash to look up on XRPSCAN
      if (!walletAddress) {
        console.log('⚠️ Could not extract wallet address from transaction data, using fallback methods');

        // At this point, we need to ask the user to manually provide their wallet address
        return NextResponse.json(
          {
            error: 'Could not automatically extract wallet address from transaction. Please provide your wallet address manually.',
            transactionFound: true,
            needsManualWalletAddress: true
          },
          { status: 202 }  // 202 Accepted - transaction found but needs more info
        );
      }

      console.log('✅ Successfully extracted wallet address:', walletAddress);

      // Verify the transaction is related to the expected token if specified
      // Note: We're being more lenient here since we've already verified the transaction exists
      if (expectedCurrency && expectedIssuer) {
        const limitAmount = txData.LimitAmount;
        if (limitAmount && (limitAmount.currency !== expectedCurrency || limitAmount.issuer !== expectedIssuer)) {
          console.warn('⚠️ Trustline may be for a different token, but proceeding anyway');
        }
      }

      console.log('✅ Transaction verified successfully:', {
        walletAddress,
        currency: limitAmount?.currency,
        issuer: limitAmount?.issuer,
        limit: limitAmount?.value
      });

      // Initialize Supabase client
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      // Update user profile with wallet address if userId is provided
      if (userId) {
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            xrp_wallet_address: walletAddress,
            wallet_connected_at: new Date().toISOString(),
            wallet_last_used: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (updateError) {
          console.error('❌ Error updating user profile:', updateError);
          // Don't fail the request if profile update fails
          console.warn('⚠️ Continuing without updating user profile');
        } else {
          console.log('✅ User profile updated with wallet address:', walletAddress);
        }
      } else {
        console.log('ℹ️ No userId provided, skipping profile update');
      }

      // Log the successful verification for audit trail
      try {
        const logData: any = {
          transaction_hash: transactionHash,
          transaction_type: 'trustline_verification',
          wallet_address: walletAddress,
          status: 'verified',
          metadata: {
            currency: limitAmount?.currency,
            issuer: limitAmount?.issuer,
            limit: limitAmount?.value,
            ledger_index: txData.ledger_index,
            date: txData.date,
            user_authenticated: !!userId
          },
          verified_at: new Date().toISOString()
        };

        // Only include user_id if provided
        if (userId) {
          logData.user_id = userId;
        }

        const { error: logError } = await supabase
          .from('transaction_logs')
          .insert(logData);

        if (logError) {
          console.warn('⚠️ Could not log transaction verification:', logError);
          // Don't fail the request if logging fails
        } else {
          console.log('✅ Transaction verification logged successfully');
        }
      } catch (logError) {
        console.warn('⚠️ Error logging transaction verification:', logError);
        // Don't fail the request if logging fails
      }

      // Prepare response data
      const responseData = {
        success: true,
        walletAddress,
        transactionDetails: {
          hash: transactionHash,
          type: txData.TransactionType,
          result: txData.meta?.TransactionResult,
          currency: limitAmount?.currency,
          issuer: limitAmount?.issuer,
          limit: limitAmount?.value,
          ledgerIndex: txData.ledger_index,
          date: txData.date
        },
        userAuthenticated: !!userId
      };

      return NextResponse.json(responseData);

    } finally {
      // Always disconnect from XRPL
      if (xrplClient.isConnected()) {
        await xrplClient.disconnect();
        console.log('🔌 Disconnected from XRPL');
      }
    }

  } catch (error) {
    console.error('❌ Trustline verification error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error during transaction verification',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
