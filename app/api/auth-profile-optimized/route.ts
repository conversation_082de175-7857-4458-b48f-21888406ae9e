import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-direct'
import { performanceMonitor } from '@/lib/performance-monitor'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }
    
    console.log(`🔄 Loading optimized auth profile for user: ${userId}`)
    
    const queryStartTime = Date.now()
    
    // Use minimal profile query - only essential fields for auth
    const { data, error } = await executeQuery(
      `SELECT
        id,
        user_email,
        first_name,
        last_name,
        is_card_holder,
        is_business_applicant,
        card_tier,
        phone,
        xrp_wallet_address
      FROM profiles
      WHERE id = $1`,
      [userId]
    )
    
    const queryDuration = Date.now() - queryStartTime
    
    if (error) {
      console.error('❌ Failed to load auth profile:', error)
      
      // Track failed query
      performanceMonitor.trackDatabaseQuery(
        'get_auth_profile',
        'profiles',
        'SELECT',
        queryDuration,
        0
      )
      
      return NextResponse.json(
        { error: 'Failed to load user profile', details: error },
        { status: 500 }
      )
    }
    
    if (!data || data.length === 0) {
      console.log('📭 No profile found for user:', userId)
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      )
    }
    
    const profileData = data[0]
    
    // Check business access if user is a business applicant
    let hasBusinessAccess = false
    if (profileData.is_business_applicant) {
      const businessQueryStartTime = Date.now()
      const { data: businessData, error: businessError } = await executeQuery(
        'SELECT has_business_access($1) as has_access',
        [userId]
      )
      
      const businessQueryDuration = Date.now() - businessQueryStartTime
      
      if (!businessError && businessData && businessData.length > 0) {
        hasBusinessAccess = businessData[0].has_access
      }
      
      // Track business access query
      performanceMonitor.trackDatabaseQuery(
        'has_business_access',
        'businesses',
        'SELECT',
        businessQueryDuration,
        businessData?.length || 0
      )
    }
    
    // Construct optimized profile response with all essential fields
    const optimizedProfile = {
      id: profileData.id,
      user_email: profileData.user_email,
      first_name: profileData.first_name || '',
      last_name: profileData.last_name || '',
      is_card_holder: profileData.is_card_holder,
      is_business_applicant: profileData.is_business_applicant,
      card_tier: profileData.card_tier,
      phone: profileData.phone || '',
      xrp_wallet_address: profileData.xrp_wallet_address || '',
      has_business_access: hasBusinessAccess
    }
    
    const totalDuration = Date.now() - startTime
    
    // Track overall API performance
    performanceMonitor.trackAPICall(
      '/api/auth-profile-optimized',
      'GET',
      totalDuration,
      200,
      userId,
      false
    )
    
    // Track main profile query
    performanceMonitor.trackDatabaseQuery(
      'get_auth_profile',
      'profiles',
      'SELECT',
      queryDuration,
      1
    )
    
    console.log(`✅ Optimized auth profile loaded in ${totalDuration}ms (Query: ${queryDuration}ms)`)
    
    return NextResponse.json({
      success: true,
      data: optimizedProfile,
      performance: {
        queryTime: queryDuration,
        totalTime: totalDuration,
        cached: false
      },
      timestamp: new Date().toISOString()
    })
    
  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Auth profile API error:', error)
    
    // Track failed API call
    performanceMonitor.trackAPICall(
      '/api/auth-profile-optimized',
      'GET',
      totalDuration,
      500
    )
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
