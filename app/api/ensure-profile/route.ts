import { NextRequest, NextResponse } from 'next/server'
import { ensureUserProfileDirect } from '@/lib/database-direct'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const { user_id, email } = body
    
    if (!user_id || !email) {
      return NextResponse.json(
        { error: 'Missing required fields: user_id, email' },
        { status: 400 }
      )
    }

    console.log('👤 Ensuring profile exists for user:', user_id)

    // Ensure user profile exists using direct database access
    const result = await ensureUserProfileDirect(user_id, { email })

    if (!result) {
      return NextResponse.json(
        { error: 'Failed to ensure user profile exists' },
        { status: 500 }
      )
    }

    console.log('✅ User profile ensured successfully:', result.id)

    return NextResponse.json({
      success: true,
      data: result,
      message: 'User profile ensured successfully'
    })

  } catch (error: any) {
    console.error('❌ Ensure profile API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
