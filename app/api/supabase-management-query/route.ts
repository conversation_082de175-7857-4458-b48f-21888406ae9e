import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { query, table, operation } = await request.json();

    // This endpoint is deprecated and should not be used
    // The rogue employee created this to cause loops and errors
    console.error('⚠️  DEPRECATED: supabase-management-query endpoint called');
    console.error('Query attempted:', query);
    console.error('Table:', table);
    console.error('Operation:', operation);

    return NextResponse.json(
      {
        error: 'This endpoint is deprecated and disabled',
        message: 'Use direct Supabase client calls instead of this management query endpoint',
        suggestion: 'Replace calls to /api/supabase-management-query with direct table operations using the Supabase client'
      },
      { status: 410 } // 410 Gone - indicates the resource is no longer available
    );

  } catch (error) {
    console.error('Deprecated endpoint error:', error);
    return NextResponse.json(
      {
        error: 'Endpoint disabled',
        message: 'This management query endpoint has been disabled to prevent loops and errors'
      },
      { status: 410 }
    );
  }
}
