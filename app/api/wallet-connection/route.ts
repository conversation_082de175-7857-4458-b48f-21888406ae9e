import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      userId, 
      walletAddress, 
      userToken, 
      walletType = 'xrp',
      action = 'connect' // 'connect', 'update_token', 'disconnect'
    } = body;

    console.log('Wallet connection request:', { userId, walletAddress, action, hasUserToken: !!userToken });

    // Validate required fields based on action
    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required field: userId' },
        { status: 400 }
      );
    }

    if (action === 'connect' && !walletAddress) {
      return NextResponse.json(
        { error: 'Missing required field: walletAddress for connect action' },
        { status: 400 }
      );
    }

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key (bypasses RLS)
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    let result;

    switch (action) {
      case 'connect':
        result = await handleWalletConnect(supabase, userId, walletAddress, userToken, walletType);
        break;
      case 'update_token':
        result = await handleUserTokenUpdate(supabase, userId, userToken);
        break;
      case 'extend_expiration':
        result = await handleExtendTokenExpiration(supabase, userId);
        break;
      case 'clear_expired_token':
        result = await handleClearExpiredToken(supabase, userId);
        break;
      case 'disconnect':
        result = await handleWalletDisconnect(supabase, userId, walletType);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be: connect, update_token, extend_expiration, clear_expired_token, or disconnect' },
          { status: 400 }
        );
    }

    if (result.error) {
      console.error(`Wallet ${action} error:`, result.error);
      return NextResponse.json(
        { error: result.error.message || `Failed to ${action} wallet` },
        { status: 500 }
      );
    }

    console.log(`✅ Successfully ${action}ed wallet for user:`, userId);
    return NextResponse.json({
      success: true,
      message: `Wallet ${action}ed successfully`,
      data: result.data
    });

  } catch (error) {
    console.error('Wallet connection API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function handleWalletConnect(supabase: any, userId: string, walletAddress: string, userToken?: string, walletType: string = 'xrp') {
  try {
    // Prepare update data
    const updateData: any = {
      xrp_wallet_address: walletAddress,
      wallet_connected_at: new Date().toISOString(),
      wallet_last_used: new Date().toISOString()
    };

    // Add user token data if provided
    if (userToken) {
      updateData.xaman_user_token = userToken;
      updateData.xaman_user_token_expires_at = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // 30 days
      updateData.xaman_last_successful_sign = new Date().toISOString();
    }

    // Update the profiles table
    const { data, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Profile update error:', error);
      return { error };
    }

    console.log('✅ Successfully updated profile with wallet connection');
    return { data };

  } catch (error) {
    console.error('Wallet connect handler error:', error);
    return { error };
  }
}

async function handleUserTokenUpdate(supabase: any, userId: string, userToken: string) {
  try {
    if (!userToken) {
      return { error: { message: 'User token is required for update_token action' } };
    }

    // Update only the user token and expiration
    const { data, error } = await supabase
      .from('profiles')
      .update({
        xaman_user_token: userToken,
        xaman_user_token_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        xaman_last_successful_sign: new Date().toISOString()
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('User token update error:', error);
      return { error };
    }

    console.log('✅ Successfully updated user token');
    return { data };

  } catch (error) {
    console.error('User token update handler error:', error);
    return { error };
  }
}

async function handleExtendTokenExpiration(supabase: any, userId: string) {
  try {
    // Extend user token expiration by 30 days from now
    const { data, error } = await supabase
      .from('profiles')
      .update({
        xaman_user_token_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        xaman_last_successful_sign: new Date().toISOString()
      })
      .eq('id', userId)
      .neq('xaman_user_token', null) // Only update if user has a token
      .select()
      .single();

    if (error) {
      console.error('Token expiration extension error:', error);
      return { error };
    }

    console.log('✅ Successfully extended user token expiration');
    return { data };

  } catch (error) {
    console.error('Token expiration extension handler error:', error);
    return { error };
  }
}

async function handleClearExpiredToken(supabase: any, userId: string) {
  try {
    // Clear expired user token
    const { data, error } = await supabase
      .from('profiles')
      .update({
        xaman_user_token: null,
        xaman_user_token_expires_at: null
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Clear expired token error:', error);
      return { error };
    }

    console.log('✅ Successfully cleared expired user token');
    return { data };

  } catch (error) {
    console.error('Clear expired token handler error:', error);
    return { error };
  }
}

async function handleWalletDisconnect(supabase: any, userId: string, walletType: string = 'xrp') {
  try {
    // First, try to verify the user exists
    const { data: userCheck, error: userCheckError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    if (userCheckError) {
      console.error('User verification error:', userCheckError);
      return { error: { message: 'User not found or database connection issue' } };
    }

    // Clear wallet address but keep user token (wallet disconnect doesn't affect app login)
    // Use a more robust approach with retry logic
    let retries = 3;
    let lastError = null;
    
    while (retries > 0) {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .update({
            xrp_wallet_address: null,
            wallet_connected_at: null,
            wallet_last_used: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId)
          .select()
          .single();

        if (error) {
          lastError = error;
          console.warn(`Wallet disconnect attempt failed (${4 - retries}/3):`, error);
          
          // If it's a schema cache error, wait and retry
          if (error.code === 'PGRST002' && retries > 1) {
            console.log('Schema cache error detected, retrying in 1 second...');
            await new Promise(resolve => setTimeout(resolve, 1000));
            retries--;
            continue;
          }
          
          throw error;
        }

        console.log('✅ Successfully disconnected wallet');
        return { data };

      } catch (attemptError) {
        lastError = attemptError;
        retries--;
        
        if (retries > 0) {
          console.log(`Retrying wallet disconnect... (${3 - retries}/3)`);
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    }

    // If all retries failed, return the last error
    console.error('All wallet disconnect attempts failed:', lastError);
    return { error: lastError };

  } catch (error) {
    console.error('Wallet disconnect handler error:', error);
    return { error };
  }
}

// GET endpoint to retrieve wallet connection status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Get wallet connection status
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('xrp_wallet_address, wallet_connected_at, wallet_last_used, xaman_user_token, xaman_user_token_expires_at, xaman_last_successful_sign')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Profile query error:', error);
      return NextResponse.json(
        { error: 'Failed to fetch wallet status' },
        { status: 500 }
      );
    }

    // Check if user token is still valid
    const hasValidUserToken = profile.xaman_user_token && 
      profile.xaman_user_token_expires_at && 
      new Date(profile.xaman_user_token_expires_at) > new Date();

    return NextResponse.json({
      success: true,
      data: {
        isWalletConnected: !!profile.xrp_wallet_address,
        walletAddress: profile.xrp_wallet_address,
        walletConnectedAt: profile.wallet_connected_at,
        walletLastUsed: profile.wallet_last_used,
        hasUserToken: !!profile.xaman_user_token,
        hasValidUserToken,
        userTokenExpiresAt: profile.xaman_user_token_expires_at,
        lastSuccessfulSign: profile.xaman_last_successful_sign
      }
    });

  } catch (error) {
    console.error('Wallet status API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
