import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// API endpoint for cleaning up expired Xaman user tokens
export async function POST(request: NextRequest) {
  try {
    console.log('🧹 Starting Xaman user token cleanup job');

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Call the database function to clean up expired tokens
    const { data, error } = await supabase.rpc('cleanup_expired_xaman_tokens');

    if (error) {
      console.error('Token cleanup error:', error);
      return NextResponse.json(
        { 
          error: 'Failed to cleanup expired tokens',
          details: error.message 
        },
        { status: 500 }
      );
    }

    const cleanedCount = data || 0;
    console.log(`✅ Token cleanup completed. Cleaned ${cleanedCount} expired tokens`);

    return NextResponse.json({
      success: true,
      message: 'Token cleanup completed successfully',
      cleanedCount: cleanedCount,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Token cleanup API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check how many tokens would be cleaned up (dry run)
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Checking expired Xaman user tokens (dry run)');

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Count expired tokens without deleting them
    const { data: expiredTokens, error } = await supabase
      .from('profiles')
      .select('id, xaman_user_token_expires_at')
      .not('xaman_user_token', 'is', null)
      .not('xaman_user_token_expires_at', 'is', null)
      .lt('xaman_user_token_expires_at', new Date().toISOString());

    if (error) {
      console.error('Expired token query error:', error);
      return NextResponse.json(
        { 
          error: 'Failed to query expired tokens',
          details: error.message 
        },
        { status: 500 }
      );
    }

    const expiredCount = expiredTokens?.length || 0;
    console.log(`📊 Found ${expiredCount} expired tokens`);

    // Also get statistics about all tokens
    const { data: allTokens, error: allTokensError } = await supabase
      .from('profiles')
      .select('id, xaman_user_token_expires_at')
      .not('xaman_user_token', 'is', null)
      .not('xaman_user_token_expires_at', 'is', null);

    if (allTokensError) {
      console.error('All tokens query error:', allTokensError);
    }

    const totalTokens = allTokens?.length || 0;
    const validTokens = totalTokens - expiredCount;

    // Calculate tokens expiring soon (within 7 days)
    const soonExpiring = allTokens?.filter(token => {
      if (!token.xaman_user_token_expires_at) return false;
      const expiresAt = new Date(token.xaman_user_token_expires_at);
      const now = new Date();
      const daysUntilExpiration = Math.ceil((expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return daysUntilExpiration <= 7 && daysUntilExpiration > 0;
    })?.length || 0;

    return NextResponse.json({
      success: true,
      statistics: {
        totalTokens,
        validTokens,
        expiredTokens: expiredCount,
        expiringSoon: soonExpiring
      },
      wouldCleanup: expiredCount,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Token cleanup check API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
