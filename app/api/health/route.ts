import { NextResponse } from 'next/server'
import { performHealthCheck } from '@/utils/supabase/health-check'

export async function GET() {
  try {
    const health = await performHealthCheck()
    
    return NextResponse.json({
      status: health.overall ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      services: {
        auth: {
          status: health.auth.isHealthy ? 'healthy' : 'unhealthy',
          error: health.auth.error,
          responseTime: health.auth.responseTime
        },
        database: {
          status: health.database.isHealthy ? 'healthy' : 'unhealthy',
          error: health.database.error,
          responseTime: health.database.responseTime
        }
      }
    }, {
      status: health.overall ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  } catch (error) {
    console.error('Health check API error:', error)
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: 'Failed to perform health check'
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}
