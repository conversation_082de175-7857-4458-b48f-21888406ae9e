import { NextRequest, NextResponse } from 'next/server'
import { getUserProfileMinimal, ensureUserProfileDirect } from '@/lib/database-direct'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: user_id' },
        { status: 400 }
      )
    }

    console.log('👤 Fetching minimal profile for user:', userId)

    // Get minimal user profile using direct database access
    let profile = await getUserProfileMinimal(userId)

    // If profile doesn't exist, try to ensure it exists (this handles cases where the trigger didn't work)
    if (!profile) {
      console.log('📝 Profile not found, attempting to ensure profile exists...')

      try {
        // Try to get user email from auth.users table for profile creation
        const { executeQuery } = await import('@/lib/database-direct')
        const { data: userData } = await executeQuery(
          'SELECT email FROM auth.users WHERE id = $1',
          [userId]
        )

        if (userData && userData.length > 0) {
          const userEmail = userData[0].email
          profile = await ensureUserProfileDirect(userId, { email: userEmail })

          if (profile) {
            console.log('✅ Profile created successfully for user:', userId)
          }
        }
      } catch (ensureError) {
        console.error('❌ Failed to ensure profile exists:', ensureError)
      }
    }

    if (!profile) {
      console.log('❌ Profile still not found after ensure attempt for user:', userId)
      return NextResponse.json(
        {
          success: false,
          error: 'Profile not found and could not be created',
          details: 'User profile does not exist in the database'
        },
        { status: 404 }
      )
    }

    console.log('✅ Minimal profile retrieved successfully:', profile.id)

    return NextResponse.json({
      success: true,
      data: profile,
      message: 'Minimal profile retrieved successfully'
    })

  } catch (error: any) {
    console.error('❌ Profile minimal API error:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    )
  }
}
