import { NextRequest, NextResponse } from 'next/server'
import { PoolHealthMonitor, AdaptivePoolManager, getDatabaseConfig } from '@/lib/database-pool-config'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'health'
    const poolName = searchParams.get('pool') || 'all'

    switch (action) {
      case 'health':
        if (poolName === 'all') {
          const allHealth = PoolHealthMonitor.getAllPoolHealth()
          return NextResponse.json({
            success: true,
            action: 'health_check_all',
            data: allHealth,
            timestamp: new Date().toISOString(),
            summary: {
              total_pools: Object.keys(allHealth).length,
              healthy_pools: Object.values(allHealth).filter(h => h.status === 'healthy').length,
              warning_pools: Object.values(allHealth).filter(h => h.status === 'warning').length,
              critical_pools: Object.values(allHealth).filter(h => h.status === 'critical').length
            }
          })
        } else {
          const health = PoolHealthMonitor.getPoolHealth(poolName)
          return NextResponse.json({
            success: true,
            action: 'health_check_single',
            pool: poolName,
            data: health,
            timestamp: new Date().toISOString()
          })
        }

      case 'recommendations':
        const currentConfig = getDatabaseConfig('STANDARD')
        const recommendations = AdaptivePoolManager.getOptimalPoolSize(poolName, currentConfig)
        
        return NextResponse.json({
          success: true,
          action: 'recommendations',
          pool: poolName,
          current_config: currentConfig,
          recommendations,
          timestamp: new Date().toISOString()
        })

      case 'configs':
        const configs = {
          HIGH_PERFORMANCE: getDatabaseConfig('HIGH_PERFORMANCE'),
          STANDARD: getDatabaseConfig('STANDARD'),
          EMERGENCY: getDatabaseConfig('EMERGENCY'),
          BACKGROUND: getDatabaseConfig('BACKGROUND')
        }
        
        return NextResponse.json({
          success: true,
          action: 'configurations',
          data: configs,
          environment: process.env.NODE_ENV,
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: health, recommendations, or configs' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Pool health API error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, pool_name, metrics, load_data } = await request.json()

    switch (action) {
      case 'record_metrics':
        if (!pool_name || !metrics) {
          return NextResponse.json(
            { error: 'pool_name and metrics are required' },
            { status: 400 }
          )
        }

        PoolHealthMonitor.recordPoolStats(pool_name, metrics)
        
        return NextResponse.json({
          success: true,
          action: 'record_metrics',
          pool: pool_name,
          message: 'Metrics recorded successfully',
          timestamp: new Date().toISOString()
        })

      case 'record_load':
        if (!pool_name || typeof load_data !== 'number') {
          return NextResponse.json(
            { error: 'pool_name and load_data (number) are required' },
            { status: 400 }
          )
        }

        AdaptivePoolManager.recordLoad(pool_name, load_data)
        
        return NextResponse.json({
          success: true,
          action: 'record_load',
          pool: pool_name,
          load: load_data,
          message: 'Load data recorded successfully',
          timestamp: new Date().toISOString()
        })

      case 'optimize_pool':
        if (!pool_name) {
          return NextResponse.json(
            { error: 'pool_name is required' },
            { status: 400 }
          )
        }

        const currentConfig = getDatabaseConfig('STANDARD')
        const optimization = AdaptivePoolManager.getOptimalPoolSize(pool_name, currentConfig)
        
        // Log the optimization recommendation
        console.log(`🔧 Pool optimization for ${pool_name}:`, optimization)
        
        return NextResponse.json({
          success: true,
          action: 'optimize_pool',
          pool: pool_name,
          current_config: currentConfig,
          optimization,
          applied: false, // We don't auto-apply optimizations
          message: 'Optimization calculated - manual application required',
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: record_metrics, record_load, or optimize_pool' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Pool health POST error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// PATCH endpoint for emergency pool adjustments
export async function PATCH(request: NextRequest) {
  try {
    const { pool_name, emergency_config } = await request.json()

    if (!pool_name) {
      return NextResponse.json(
        { error: 'pool_name is required' },
        { status: 400 }
      )
    }

    // This would ideally trigger a pool reconfiguration
    // For now, we'll log the request and provide guidance
    console.log('🚨 Emergency pool reconfiguration requested:', {
      pool_name,
      emergency_config
    })

    const emergencyConfig = getDatabaseConfig('EMERGENCY', emergency_config)

    return NextResponse.json({
      success: true,
      action: 'emergency_reconfiguration',
      pool: pool_name,
      requested_config: emergency_config,
      recommended_config: emergencyConfig,
      message: 'Emergency configuration calculated',
      instructions: [
        'Restart the application with new pool configuration',
        'Monitor pool health after restart',
        'Revert to standard configuration once issues are resolved'
      ],
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Pool health PATCH error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
