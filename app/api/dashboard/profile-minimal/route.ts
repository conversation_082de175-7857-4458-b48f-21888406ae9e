import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-direct'

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }
    
    console.log(`🔄 Loading minimal dashboard profile for user: ${userId}`)
    
    const queryStartTime = Date.now()
    
    // Minimal query - only fields needed for dashboard and onboarding
    const { data, error } = await executeQuery(
      `SELECT 
        id, 
        user_email, 
        first_name,
        last_name,
        phone,
        is_card_holder, 
        card_tier,
        xrp_wallet_address,
        created_at
      FROM profiles 
      WHERE id = $1`,
      [userId]
    )
    
    const queryDuration = Date.now() - queryStartTime
    
    if (error) {
      console.error('❌ Failed to load minimal profile:', error)
      return NextResponse.json(
        { error: 'Failed to load user profile', details: error },
        { status: 500 }
      )
    }
    
    if (!data || data.length === 0) {
      console.log('📭 No profile found for user:', userId)
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      )
    }
    
    const profileData = data[0]
    const totalDuration = Date.now() - startTime
    
    // Check profile completeness for onboarding
    const isProfileComplete = !!(
      profileData.first_name && 
      profileData.last_name && 
      profileData.phone
    )
    
    // Construct minimal profile response
    const minimalProfile = {
      id: profileData.id,
      user_email: profileData.user_email,
      first_name: profileData.first_name || '',
      last_name: profileData.last_name || '',
      phone: profileData.phone || '',
      is_card_holder: profileData.is_card_holder || false,
      card_tier: profileData.card_tier || null,
      xrp_wallet_address: profileData.xrp_wallet_address || '',
      created_at: profileData.created_at,
      is_profile_complete: isProfileComplete
    }
    
    console.log(`✅ Minimal profile loaded in ${totalDuration}ms (Query: ${queryDuration}ms)`)
    
    return NextResponse.json({
      success: true,
      profile: minimalProfile,
      performance: {
        queryTime: queryDuration,
        totalTime: totalDuration
      },
      timestamp: new Date().toISOString()
    })
    
  } catch (error: any) {
    const totalDuration = Date.now() - startTime
    console.error('❌ Minimal profile API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { first_name, last_name, phone, xrp_wallet_address } = body

    console.log(`🔄 Updating profile for user: ${userId}`)

    // Update only the provided fields
    const updateFields = []
    const updateValues = []
    let paramIndex = 1

    if (first_name !== undefined) {
      updateFields.push(`first_name = $${paramIndex++}`)
      updateValues.push(first_name)
    }
    if (last_name !== undefined) {
      updateFields.push(`last_name = $${paramIndex++}`)
      updateValues.push(last_name)
    }
    if (phone !== undefined) {
      updateFields.push(`phone = $${paramIndex++}`)
      updateValues.push(phone)
    }
    if (xrp_wallet_address !== undefined) {
      updateFields.push(`xrp_wallet_address = $${paramIndex++}`)
      updateValues.push(xrp_wallet_address)
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No fields to update' },
        { status: 400 }
      )
    }

    // Add user ID as the last parameter
    updateValues.push(userId)

    const query = `
      UPDATE profiles 
      SET ${updateFields.join(', ')}, updated_at = NOW()
      WHERE id = $${paramIndex}
      RETURNING id, user_email, first_name, last_name, phone, is_card_holder, card_tier, xrp_wallet_address, created_at
    `

    const { data, error } = await executeQuery(query, updateValues)

    if (error) {
      console.error('❌ Failed to update profile:', error)
      return NextResponse.json(
        { error: 'Failed to update profile', details: error },
        { status: 500 }
      )
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'Profile not found or update failed' },
        { status: 404 }
      )
    }

    const updatedProfile = data[0]
    
    // Check profile completeness
    const isProfileComplete = !!(
      updatedProfile.first_name && 
      updatedProfile.last_name && 
      updatedProfile.phone
    )

    console.log('✅ Profile updated successfully')

    return NextResponse.json({
      success: true,
      profile: {
        ...updatedProfile,
        is_profile_complete: isProfileComplete
      },
      message: 'Profile updated successfully'
    })

  } catch (error: any) {
    console.error('❌ Profile update API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
