import { NextRequest, NextResponse } from 'next/server';
import { withSecurity } from '@/lib/api-security';
import { createClient } from '@/lib/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withSecurity(
    request,
    async (req, { user, supabase }) => {
      try {
        if (!user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }

        const businessId = params.id;

        if (!businessId) {
          return NextResponse.json(
            { error: 'Business ID is required' },
            { status: 400 }
          );
        }

        console.log(`Fetching business with ID: ${businessId} for user: ${user.id}`);

        // Use authenticated Supabase client (leverages RLS policies)
        const supabaseClient = supabase || await createClient();

        // Query business with ownership verification (RLS automatically enforces this)
        const { data: business, error } = await supabaseClient
          .from('businesses')
          .select('id, name, premium_discount, logo_url, website, user_id')
          .eq('id', businessId)
          .eq('user_id', user.id) // Ensure user owns this business
          .single();

        if (error) {
          console.error('Business query error for user', user.id, ':', error);
          return NextResponse.json(
            { error: 'Failed to fetch business data' },
            { status: 500 }
          );
        }

        if (!business) {
          return NextResponse.json(
            { error: 'Business not found or access denied' },
            { status: 404 }
          );
        }

        console.log(`Successfully fetched business: ${business.name} for user: ${user.id}`);

        return NextResponse.json({
          data: business
        });

      } catch (error) {
        console.error('API error for user', user?.id, ':', error);
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    },
    {
      requireAuth: true, // Enforce authentication
      rateLimit: { windowMs: 60000, maxRequests: 60 }, // 60 requests per minute
      logRequest: true // Log all requests for security monitoring
    }
  );
}
