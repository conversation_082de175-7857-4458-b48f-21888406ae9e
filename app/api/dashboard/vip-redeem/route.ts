import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create admin client with service role (follows the working pattern from business editing)
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    db: {
      schema: 'public'
    }
  }
);

// Validate VIP code format
function isValidVipCode(code: string): boolean {
  // Basic validation - adjust as needed for your code format
  return /^[A-Z0-9]{6,20}$/.test(code.toUpperCase());
}

// Calculate membership end date based on card tier
function calculateMembershipEndDate(cardTier: string = 'Premium'): Date {
  const endDate = new Date();
  
  // Different tiers get different membership durations
  switch (cardTier.toLowerCase()) {
    case 'premium':
      endDate.setFullYear(endDate.getFullYear() + 1);
      break;
    case 'gold':
      endDate.setFullYear(endDate.getFullYear() + 1);
      break;
    case 'platinum':
      endDate.setFullYear(endDate.getFullYear() + 2);
      break;
    case 'diamond':
      endDate.setFullYear(endDate.getFullYear() + 3);
      break;
    case 'obsidian':
      endDate.setFullYear(endDate.getFullYear() + 5);
      break;
    default:
      endDate.setFullYear(endDate.getFullYear() + 1);
  }
  
  return endDate;
}

export async function POST(request: NextRequest) {
  try {
    const { code, userId } = await request.json();

    // Validate input
    if (!code || !userId) {
      return NextResponse.json(
        { error: 'VIP code and user ID are required' },
        { status: 400 }
      );
    }

    // Validate code format
    if (!isValidVipCode(code)) {
      return NextResponse.json(
        { error: 'Invalid VIP code format' },
        { status: 400 }
      );
    }

    // Step 1: Check if VIP code exists and is valid
    const { data: vipCode, error: vipCodeError } = await supabaseAdmin
      .from('vip_codes')
      .select('*')
      .eq('code', code.toUpperCase())
      .maybeSingle();

    if (vipCodeError) {
      console.error('Error checking VIP code:', vipCodeError);
      return NextResponse.json(
        { error: 'Error validating VIP code' },
        { status: 500 }
      );
    }

    // Check if code exists
    if (!vipCode) {
      return NextResponse.json(
        { error: 'Invalid VIP code' },
        { status: 400 }
      );
    }

    // Check if code is active
    if (vipCode.is_active === false) {
      return NextResponse.json(
        { error: 'VIP code is no longer active' },
        { status: 400 }
      );
    }

    // Check if code has expired
    if (vipCode.expires_at && new Date(vipCode.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'VIP code has expired' },
        { status: 400 }
      );
    }

    // Check if code has remaining uses
    if (vipCode.remaining_uses !== null && vipCode.remaining_uses <= 0) {
      return NextResponse.json(
        { error: 'VIP code has no remaining uses' },
        { status: 400 }
      );
    }

    // Step 2: Check if user has already redeemed this code
    const { data: existingRedemption, error: redemptionCheckError } = await supabaseAdmin
      .from('vip_redemptions')
      .select('id')
      .eq('code', code.toUpperCase())
      .eq('redeemed_by', userId)
      .maybeSingle();

    if (redemptionCheckError) {
      console.error('Error checking existing redemption:', redemptionCheckError);
      return NextResponse.json(
        { error: 'Error checking redemption status' },
        { status: 500 }
      );
    }

    if (existingRedemption) {
      return NextResponse.json(
        { error: 'This VIP code has already been redeemed by this user' },
        { status: 400 }
      );
    }

    // Step 3: Begin redemption process
    const membershipStartDate = new Date();
    const membershipEndDate = calculateMembershipEndDate(vipCode.card_tier || 'Premium');

    // Create redemption record
    const { data: redemption, error: redemptionError } = await supabaseAdmin
      .from('vip_redemptions')
      .insert({
        code: code.toUpperCase(),
        redeemed_by: userId,
        redeemed_at: membershipStartDate.toISOString()
      })
      .select()
      .single();

    if (redemptionError) {
      console.error('Error creating redemption record:', redemptionError);
      return NextResponse.json(
        { error: 'Error processing VIP code redemption' },
        { status: 500 }
      );
    }

    // Step 4: Update user profile with VIP status (using service role to bypass RLS)
    const profileUpdateData = {
      is_card_holder: true,
      card_tier: vipCode.card_tier || 'Premium',
      membership_start_date: membershipStartDate.toISOString(),
      membership_end_date: membershipEndDate.toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: updatedProfile, error: profileUpdateError } = await supabaseAdmin
      .from('profiles')
      .update(profileUpdateData)
      .eq('id', userId)
      .select()
      .single();

    if (profileUpdateError) {
      console.error('Error updating profile:', profileUpdateError);
      
      // Try to cleanup the redemption record if profile update fails
      await supabaseAdmin
        .from('vip_redemptions')
        .delete()
        .eq('id', redemption.id);

      return NextResponse.json(
        { error: 'Error updating user profile' },
        { status: 500 }
      );
    }

    // Step 5: Update VIP code remaining uses if applicable
    if (vipCode.remaining_uses !== null) {
      const { error: updateUsesError } = await supabaseAdmin
        .from('vip_codes')
        .update({
          remaining_uses: vipCode.remaining_uses - 1
        })
        .eq('code', code.toUpperCase());

      if (updateUsesError) {
        console.error('Error updating VIP code uses:', updateUsesError);
        // Don't fail the request for this - the redemption was successful
      }
    }

    // Step 6: Return success response
    return NextResponse.json({
      success: true,
      message: 'VIP code redeemed successfully',
      data: {
        redemption_id: redemption.id,
        card_tier: vipCode.card_tier || 'Premium',
        membership_start_date: membershipStartDate.toISOString(),
        membership_end_date: membershipEndDate.toISOString(),
        profile: updatedProfile
      }
    });

  } catch (error) {
    console.error('Unexpected error in VIP code redemption:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json(
    { message: 'VIP code redemption endpoint. Use POST to redeem codes.' },
    { status: 200 }
  );
}