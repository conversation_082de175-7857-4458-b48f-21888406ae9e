import { NextRequest, NextResponse } from 'next/server';

async function executeQuery(query: string) {
  const response = await fetch(`https://supabase.com/api/v1/projects/haqbtbpmyadkocakqnew/database/query`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query })
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Database query failed: ${response.status} - ${errorText}`);
  }

  return await response.json();
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    console.log('Fetching comprehensive dashboard data using direct queries...');

    // Fetch all data in parallel for better performance
    const [businessesData, profilesData] = await Promise.all([
      // Fetch businesses with premium discounts
      executeQuery(`
        SELECT id, name, premium_discount, logo_url, website, is_active, user_id, created_at
        FROM businesses
        WHERE premium_discount IS NOT NULL
        AND is_active = true
        ORDER BY name;
      `),

      // Fetch all profiles (for admin/debugging purposes)
      executeQuery(`
        SELECT * FROM profiles
        ORDER BY created_at DESC;
      `)
    ]);

    const results: any = {
      businesses: {
        data: businessesData || [],
        error: null,
        count: businessesData?.length || 0
      },
      profiles: {
        data: profilesData || [],
        error: null,
        count: profilesData?.length || 0
      }
    };

    // If a specific user ID is provided, also fetch their specific data
    if (userId) {
      const [userProfileData, userBusinessData] = await Promise.all([
        // Fetch specific user profile
        executeQuery(`
          SELECT * FROM profiles
          WHERE id = '${userId}';
        `),

        // Fetch businesses owned by this user
        executeQuery(`
          SELECT * FROM businesses
          WHERE user_id = '${userId}';
        `)
      ]);

      results.userProfile = {
        data: userProfileData?.[0] || null,
        error: null
      };

      results.userBusinesses = {
        data: userBusinessData || [],
        error: null,
        count: userBusinessData?.length || 0
      };

      // If user has a referring business, fetch that too
      if (userProfileData?.[0]?.referring_business_id) {
        const referringBusinessData = await executeQuery(`
          SELECT id, name FROM businesses
          WHERE id = '${userProfileData[0].referring_business_id}';
        `);

        results.referringBusiness = {
          data: referringBusinessData?.[0] || null,
          error: null
        };
      }
    }

    console.log('Dashboard data summary:');
    console.log(`- Businesses: ${results.businesses.count}`);
    console.log(`- Profiles: ${results.profiles.count}`);
    if (userId) {
      console.log(`- User Profile: ${results.userProfile?.data ? 'Found' : 'Not found'}`);
      console.log(`- User Businesses: ${results.userBusinesses?.count || 0}`);
      console.log(`- Referring Business: ${results.referringBusiness?.data ? 'Found' : 'Not found'}`);
    }
    
    return NextResponse.json({
      success: true,
      ...results
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
