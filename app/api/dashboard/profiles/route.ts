import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }

    console.log('Fetching profile for user:', userId);

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Get user's profile with business access info
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        *,
        businesses!inner(
          id,
          name,
          category,
          is_active
        )
      `)
      .eq('id', userId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Profile query error:', profileError);
      return NextResponse.json(
        { 
          error: 'Failed to fetch profile', 
          details: profileError.message,
          code: profileError.code 
        },
        { status: 500 }
      );
    }

    console.log(`Successfully fetched profile for user: ${userId}`);

    return NextResponse.json({
      profile: profile || null,
      message: profile ? 'Profile found' : 'Profile not found'
    });

  } catch (error: any) {
    console.error('Profile GET API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, updates } = body;

    if (!userId || !updates) {
      return NextResponse.json(
        { error: 'User ID and updates are required' },
        { status: 400 }
      );
    }

    console.log('Updating profile for user:', userId, 'Updates:', Object.keys(updates));

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Sanitize updates object - only allow specific fields
    const allowedFields = [
      'first_name', 'last_name', 'phone', 'user_email', 'xrp_wallet_address',
      'is_card_holder', 'card_tier', 'membership_start_date', 'membership_end_date',
      'referring_business_id', 'is_business_applicant'
    ];

    const sanitizedUpdates = Object.entries(updates)
      .filter(([key]) => allowedFields.includes(key))
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    if (Object.keys(sanitizedUpdates).length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Add updated timestamp
    const updateData = {
      ...sanitizedUpdates,
      updated_at: new Date().toISOString()
    };

    // Update the profile
    const { data: profile, error: updateError } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single();

    if (updateError) {
      console.error('Profile update error:', updateError);
      return NextResponse.json(
        { 
          error: 'Failed to update profile', 
          details: updateError.message,
          code: updateError.code 
        },
        { status: 500 }
      );
    }

    console.log('Successfully updated profile for user:', userId);

    return NextResponse.json({
      success: true,
      profile: profile,
      message: 'Profile updated successfully'
    });

  } catch (error: any) {
    console.error('Profile POST API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
