import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-direct'

export async function GET(request: NextRequest) {
  try {
    // Get user ID from query params (simpler approach)
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID required' },
        { status: 400 }
      )
    }

    console.log('🏢 Checking business ownership for user:', userId)
    // Query to get user's businesses and business access requests status

    const businessQuery = `
          SELECT 
            b.id,
            b.name,
            b.category,
            b.premium_discount,
            b.logo_url,
            b.website,
            b.is_active,
            b.business_spotlight,
            b.user_id,
            b.created_at,
            -- Count interactions/visits
            COALESCE(bv.visit_count, 0) as interaction_count,
            -- Count referrals (businesses that reference this one)
            COALESCE(br.referral_count, 0) as referral_count
          FROM businesses b
          LEFT JOIN (
            SELECT business_id, COUNT(*) as visit_count
            FROM business_visits
            GROUP BY business_id
          ) bv ON b.id = bv.business_id
          LEFT JOIN (
            SELECT referring_business_id, COUNT(*) as referral_count
            FROM businesses
            WHERE referring_business_id IS NOT NULL
            GROUP BY referring_business_id
          ) br ON b.id = br.referring_business_id
          WHERE b.user_id = $1 AND b.is_active = true
          ORDER BY b.created_at DESC
        `

    const { data: businesses, error: businessError } = await executeQuery(
      businessQuery,
      [userId]
    )

    if (businessError) {
      console.error('❌ Error fetching businesses:', businessError)
      return NextResponse.json(
        { error: 'Failed to fetch business data' },
        { status: 500 }
      )
    }

        // Query to check if user has pending business access requests
        const accessRequestQuery = `
          SELECT 
            bar.id,
            bar.business_id,
            bar.status,
            bar.requested_at,
            b.name as business_name
          FROM business_access_requests bar
          JOIN businesses b ON bar.business_id = b.id
          WHERE bar.user_id = $1 AND bar.status = 'pending'
          ORDER BY bar.requested_at DESC
        `

    const { data: accessRequests, error: accessError } = await executeQuery(
      accessRequestQuery,
      [userId]
    )

    if (accessError) {
      console.warn('⚠️ Error fetching access requests:', accessError)
    }

        // Query to get businesses that need user_id assignment (for admin purposes)
        const unassignedBusinessesQuery = `
          SELECT 
            id,
            name,
            category,
            contact_email,
            created_at
          FROM businesses 
          WHERE user_id IS NULL 
          ORDER BY created_at DESC
          LIMIT 10
        `

        const { data: unassignedBusinesses, error: unassignedError } = await executeQuery(
          unassignedBusinessesQuery,
          []
        )

        if (unassignedError) {
          console.warn('⚠️ Error fetching unassigned businesses:', unassignedError)
        }

    const result = {
      success: true,
      data: {
        businesses: businesses || [],
        hasBusinesses: (businesses || []).length > 0,
        isBusinessOwner: (businesses || []).length > 0,
        accessRequests: accessRequests || [],
        hasPendingRequests: (accessRequests || []).length > 0,
        unassignedBusinesses: unassignedBusinesses || [], // For admin users
        stats: {
          totalBusinesses: (businesses || []).length,
          totalInteractions: (businesses || []).reduce((sum, b) => sum + (b.interaction_count || 0), 0),
          totalReferrals: (businesses || []).reduce((sum, b) => sum + (b.referral_count || 0), 0)
        }
      },
      timestamp: new Date().toISOString()
    }

    console.log('✅ Business ownership check completed:', {
      userId: userId,
      businessCount: result.data.businesses.length,
      hasBusinesses: result.data.hasBusinesses,
      pendingRequests: result.data.accessRequests.length
    })

    return NextResponse.json(result)

  } catch (error: any) {
    console.error('❌ Business ownership API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
