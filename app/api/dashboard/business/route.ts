import { NextRequest, NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Get user ID from query parameters (similar to working endpoints)
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Fetching business data for user:', userId);

    // Use direct database query (this approach works for other endpoints)
    console.log('🔍 Using direct database query for user:', userId);

    // Import direct database function
    const { executeQuery } = await import('@/lib/database-direct');

    const { data: businessRows, error: businessError } = await executeQuery(
      `SELECT * FROM businesses WHERE user_id = $1 AND is_active = true LIMIT 1`,
      [userId]
    );

    const business = businessRows && businessRows.length > 0 ? businessRows[0] : null;
    console.log('🔍 Direct database result:', { business: !!business, businessError });

    if (businessError) {
      console.error('Business query error for user', userId, ':', businessError);
      console.error('Error details:', {
        code: businessError.code,
        message: businessError.message,
        details: businessError.details,
        hint: businessError.hint
      });

      return NextResponse.json(
        {
          error: 'Failed to fetch business data',
          details: businessError.message,
          code: businessError.code
        },
        { status: 500 }
      );
    }

    // No business found
    if (!business) {
      return NextResponse.json({
        business: null,
        message: 'No business found for this user'
      });
    }

    // Get interaction count with error handling using direct database queries
    let interactionCount = 0;
    try {
      const { data: interactionRows, error: interactionError } = await executeQuery(
        `SELECT COUNT(*) as count FROM qr_interactions WHERE scanned_business_id = $1`,
        [business.id]
      );

      if (interactionError) {
        console.warn('Failed to fetch interaction count:', interactionError);
      } else {
        interactionCount = interactionRows?.[0]?.count || 0;
      }
    } catch (error) {
      console.warn('Exception while fetching interaction count:', error);
    }

    // Get referral count with error handling using direct database queries
    let referralCount = 0;
    try {
      const { data: referralRows, error: referralError } = await executeQuery(
        `SELECT COUNT(*) as count FROM profiles WHERE referring_business_id = $1`,
        [business.id]
      );

      if (referralError) {
        console.warn('Failed to fetch referral count:', referralError);
      } else {
        referralCount = referralRows?.[0]?.count || 0;
      }
    } catch (error) {
      console.warn('Exception while fetching referral count:', error);
    }

    // Add counts to business data
    const businessWithCounts = {
      ...business,
      interaction_count: interactionCount,
      referral_count: referralCount
    };

    console.log('Successfully fetched business data for user', userId, ':', business.name);

    return NextResponse.json({
      business: businessWithCounts,
      message: 'Business data retrieved successfully'
    });

  } catch (error: any) {
    console.error('Business dashboard API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// POST method for updating business data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { businessData, userId } = body;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    if (!businessData) {
      return NextResponse.json(
        { error: 'Missing required parameter: businessData' },
        { status: 400 }
      );
    }

    console.log('Updating business data for user:', userId);

    // Filter allowed fields for security
    const allowedFields = [
      'name', 'category', 'business_address', 'contact_phone',
      'contact_email', 'website', 'premium_discount',
      'loyalty_reward_frequency', 'business_spotlight'
    ];

    const updateData: any = {};
    for (const field of allowedFields) {
      if (businessData[field] !== undefined) {
        updateData[field] = businessData[field];
      }
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Use direct database query for update
    const { executeQuery } = await import('@/lib/database-direct');

    // Build dynamic update query
    const setClause = Object.keys(updateData).map((key, index) => `${key} = $${index + 2}`).join(', ');
    const values = [userId, ...Object.values(updateData)];

    const updateQuery = `
      UPDATE businesses
      SET ${setClause}, updated_at = NOW()
      WHERE user_id = $1 AND is_active = true
      RETURNING *
    `;

    const { data: updatedRows, error } = await executeQuery(updateQuery, values);

    if (error) {
      console.error('Business update error for user', userId, ':', error);
      return NextResponse.json(
        {
          error: 'Failed to update business',
          details: error
        },
        { status: 500 }
      );
    }

    if (!updatedRows || updatedRows.length === 0) {
      return NextResponse.json(
        { error: 'Business not found or update failed' },
        { status: 404 }
      );
    }

    const updatedBusiness = updatedRows[0];
    console.log('Successfully updated business for user', userId, ':', updatedBusiness.name);

    return NextResponse.json({
      business: updatedBusiness,
      message: 'Business updated successfully'
    });

  } catch (error: any) {
    console.error('Business update API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}
