import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing required parameter: userId' },
        { status: 400 }
      );
    }

    console.log('Fetching dashboard interactions for user:', userId);

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      console.error('Missing Supabase environment variables');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        db: {
          schema: 'public'
        }
      }
    );

    // Optimized query with joins to get all data in one call
    const { data: interactions, error: interactionsError } = await supabase
      .from('qr_interactions')
      .select(`
        id,
        scanner_user_id,
        scanned_user_id,
        scanned_business_id,
        interaction_type,
        created_at,
        scanner_profile:profiles!qr_interactions_scanner_user_id_fkey(
          first_name,
          last_name,
          user_email,
          is_card_holder
        ),
        scanned_profile:profiles!qr_interactions_scanned_user_id_fkey(
          first_name,
          last_name,
          user_email,
          is_card_holder
        ),
        business:businesses!qr_interactions_scanned_business_id_fkey(
          name,
          category,
          logo_url
        )
      `)
      .or(`scanner_user_id.eq.${userId},scanned_user_id.eq.${userId}`)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (interactionsError) {
      console.error('Interactions query error:', interactionsError);
      
      // Try fallback with direct database access
      try {
        const { getUserQRInteractionsDirect } = await import('@/lib/database-direct');
        const directInteractions = await getUserQRInteractionsDirect(userId);

        if (directInteractions) {
          console.log('Successfully fetched interactions via direct database access');
          return NextResponse.json({
            interactions: directInteractions.slice(0, limit),
            message: 'Interactions data retrieved successfully (fallback)'
          });
        }
      } catch (fallbackError) {
        console.error('Fallback database access also failed:', fallbackError);
      }

      return NextResponse.json(
        { 
          error: 'Failed to fetch interactions data', 
          details: interactionsError.message,
          code: interactionsError.code 
        },
        { status: 500 }
      );
    }

    // Transform the data to match expected format
    const formattedInteractions = (interactions || []).map((interaction: any) => ({
      id: interaction.id,
      scanner_user_id: interaction.scanner_user_id,
      scanned_user_id: interaction.scanned_user_id,
      scanned_business_id: interaction.scanned_business_id,
      interaction_type: interaction.interaction_type,
      created_at: interaction.created_at,
      scanner_profile: interaction.scanner_profile,
      scanned_profile: interaction.scanned_profile,
      business: interaction.business
    }));

    console.log(`Successfully fetched ${formattedInteractions.length} interactions for user:`, userId);

    return NextResponse.json({
      interactions: formattedInteractions,
      message: 'Interactions data retrieved successfully'
    });

  } catch (error: any) {
    console.error('Dashboard interactions API error:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}
