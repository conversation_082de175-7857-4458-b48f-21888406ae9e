import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, referringBusinessId } = body;

    if (!userId || !referringBusinessId) {
      return NextResponse.json(
        { error: 'User ID and referring business ID are required' },
        { status: 400 }
      );
    }

    console.log(`Updating referring business for user ${userId} to ${referringBusinessId}`);

    // Use Supabase Management API for direct database query
    const response = await fetch(`https://supabase.com/api/v1/projects/haqbtbpmyadkocakqnew/database/query`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: `
          UPDATE profiles
          SET referring_business_id = '${referringBusinessId}'
          WHERE id = '${userId}'
          RETURNING *;
        `
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Management API error:', response.status, errorText);
      return NextResponse.json(
        { error: `Database update failed: ${response.status}` },
        { status: 500 }
      );
    }

    const data = await response.json();
    console.log('Successfully updated referring business');

    return NextResponse.json({
      data: data[0] || null,
      message: 'Referring business updated successfully'
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
