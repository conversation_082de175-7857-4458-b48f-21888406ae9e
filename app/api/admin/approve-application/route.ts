import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface LogoProcessingResult {
  success: boolean;
  logoUrl?: string;
  fileSize?: number;
  mimeType?: string;
  width?: number;
  height?: number;
  error?: string;
}

/**
 * Enhanced admin approval endpoint that handles the complete workflow:
 * 1. Approve network application
 * 2. Create business record
 * 3. Process logo if available
 * 4. Send approval email
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { applicationId, reviewedBy, action } = body;

    if (!applicationId || !reviewedBy || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: applicationId, reviewedBy, action' },
        { status: 400 }
      );
    }

    if (action !== 'approved' && action !== 'rejected') {
      return NextResponse.json(
        { error: 'Action must be "approved" or "rejected"' },
        { status: 400 }
      );
    }

    console.log(`🔄 Processing ${action} for application: ${applicationId}`);

    // Get the network application
    const { data: application, error: fetchError } = await supabase
      .from('network_applications')
      .select('*')
      .eq('id', applicationId)
      .single();

    if (fetchError || !application) {
      return NextResponse.json(
        { error: 'Network application not found' },
        { status: 404 }
      );
    }

    // Update application status
    const { error: updateError } = await supabase
      .from('network_applications')
      .update({
        status: action,
        reviewed_by: reviewedBy,
        reviewed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', applicationId);

    if (updateError) {
      console.error('Failed to update application status:', updateError);
      return NextResponse.json(
        { error: 'Failed to update application status' },
        { status: 500 }
      );
    }

    // If rejected, just return success
    if (action === 'rejected') {
      console.log(`✅ Application rejected: ${applicationId}`);
      return NextResponse.json({
        success: true,
        message: 'Application rejected successfully'
      });
    }

    // For approved applications, create business and process logo
    console.log(`🏢 Creating business for approved application: ${application.business_name}`);

    // Check if business already exists for this user
    const { data: existingBusiness } = await supabase
      .from('businesses')
      .select('id, name')
      .eq('user_id', application.user_id)
      .eq('name', application.business_name)
      .single();

    let businessId;
    let businessCreated = false;

    if (existingBusiness) {
      businessId = existingBusiness.id;
      console.log(`📝 Found existing business: ${existingBusiness.name}`);
    } else {
      // Create new business record
      const businessData = {
        user_id: application.user_id,
        name: application.business_name,
        category: application.category || 'General',
        website: application.website,
        contact_name: application.contact_name,
        contact_email: application.contact_email,
        contact_phone: application.contact_phone,
        business_address: application.business_address,
        premium_discount: application.proposed_discount || '10%',
        loyalty_reward_frequency: application.loyalty_reward_frequency || 'monthly',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: newBusiness, error: createError } = await supabase
        .from('businesses')
        .insert(businessData)
        .select('id')
        .single();

      if (createError) {
        console.error('Failed to create business:', createError);
        return NextResponse.json(
          { error: 'Failed to create business record' },
          { status: 500 }
        );
      }

      businessId = newBusiness.id;
      businessCreated = true;
      console.log(`✅ Created new business: ${application.business_name} (${businessId})`);
    }

    // Process logo if available
    let logoProcessingResult: LogoProcessingResult = { success: false };
    
    if (application.logo_data) {
      console.log(`🖼️  Processing logo for business: ${application.business_name}`);
      logoProcessingResult = await processLogoFromApplication(application);
      
      if (logoProcessingResult.success) {
        // Update business with logo information
        const { error: logoUpdateError } = await supabase
          .from('businesses')
          .update({
            logo_url: logoProcessingResult.logoUrl,
            logo_optimized_url: logoProcessingResult.logoUrl,
            logo_file_size: logoProcessingResult.fileSize,
            logo_mime_type: logoProcessingResult.mimeType,
            logo_width: logoProcessingResult.width,
            logo_height: logoProcessingResult.height,
            updated_at: new Date().toISOString()
          })
          .eq('id', businessId);

        if (logoUpdateError) {
          console.error('Failed to update business with logo:', logoUpdateError);
          // Don't fail the entire process if logo update fails
        } else {
          console.log(`✅ Updated business with logo: ${logoProcessingResult.logoUrl}`);
        }
      } else {
        console.warn(`⚠️  Logo processing failed: ${logoProcessingResult.error}`);
      }
    }

    // Send approval email
    try {
      await sendApprovalEmail(application);
      console.log(`📧 Approval email sent to: ${application.contact_email}`);
    } catch (emailError) {
      console.error('Failed to send approval email:', emailError);
      // Don't fail the entire process if email fails
    }

    // Update profile to mark as business applicant
    try {
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          is_business_applicant: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', application.user_id);

      if (profileError) {
        console.warn('Failed to update profile:', profileError);
      }
    } catch (profileUpdateError) {
      console.warn('Exception updating profile:', profileUpdateError);
    }

    console.log(`🎉 Application approval completed successfully: ${application.business_name}`);

    return NextResponse.json({
      success: true,
      message: 'Application approved and business created successfully',
      data: {
        applicationId,
        businessId,
        businessCreated,
        logoProcessed: logoProcessingResult.success,
        logoUrl: logoProcessingResult.logoUrl
      }
    });

  } catch (error) {
    console.error('Error in application approval:', error);
    return NextResponse.json(
      { error: 'Internal server error during approval process' },
      { status: 500 }
    );
  }
}

/**
 * Process logo from network application data
 */
async function processLogoFromApplication(application: any): Promise<LogoProcessingResult> {
  try {
    if (!application.logo_data) {
      return { success: false, error: 'No logo data found' };
    }

    // Parse logo data JSON
    const logoData = JSON.parse(application.logo_data);
    const { filename, mimetype, size, data } = logoData;

    if (!data) {
      return { success: false, error: 'No logo file data found' };
    }

    console.log(`📁 Processing logo: ${filename} (${formatFileSize(size)})`);

    // Convert base64 to buffer
    const logoBuffer = Buffer.from(data, 'base64');

    // Try Docker processing first
    const dockerResult = await processWithDocker(logoBuffer, filename, mimetype);
    if (dockerResult.success) {
      return dockerResult;
    }

    // Fallback to direct upload
    console.log(`⚠️  Docker processing failed, using fallback upload`);
    return await fallbackUpload(logoBuffer, filename, mimetype);

  } catch (error) {
    return { success: false, error: (error as Error).message };
  }
}

/**
 * Process logo using Docker service
 */
async function processWithDocker(logoBuffer: Buffer, filename: string, mimetype: string): Promise<LogoProcessingResult> {
  try {
    const dockerServiceUrl = process.env.DOCKER_LOGO_SERVICE_URL || 'http://localhost:3001';
    
    // Check if Docker service is available
    const healthCheck = await fetch(`${dockerServiceUrl}/health`).catch(() => null);
    if (!healthCheck || !healthCheck.ok) {
      return { success: false, error: 'Docker service unavailable' };
    }

    // Create form data
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('logo', logoBuffer, {
      filename: filename,
      contentType: mimetype
    });

    // Send to Docker processor
    const response = await fetch(`${dockerServiceUrl}/api/process-logo`, {
      method: 'POST',
      body: formData,
      headers: formData.getHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `Docker processing failed: ${response.status} - ${errorText}` };
    }

    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        logoUrl: result.processedImages?.original?.path || result.logoUrl,
        fileSize: result.metadata?.size || logoBuffer.length,
        mimeType: result.metadata?.format || mimetype,
        width: result.metadata?.width,
        height: result.metadata?.height
      };
    } else {
      return { success: false, error: result.error || 'Docker processing failed' };
    }

  } catch (error) {
    return { success: false, error: (error as Error).message };
  }
}

/**
 * Fallback upload to Supabase storage
 */
async function fallbackUpload(logoBuffer: Buffer, filename: string, mimetype: string): Promise<LogoProcessingResult> {
  try {
    // Generate unique filename
    const timestamp = Date.now();
    const sanitizedName = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
    const storageFileName = `business-logos/${timestamp}_${sanitizedName}`;

    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from('business-assets')
      .upload(storageFileName, logoBuffer, {
        contentType: mimetype,
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      return { success: false, error: `Storage upload failed: ${error.message}` };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('business-assets')
      .getPublicUrl(storageFileName);

    return {
      success: true,
      logoUrl: urlData.publicUrl,
      fileSize: logoBuffer.length,
      mimeType: mimetype,
      width: null,
      height: null
    };

  } catch (error) {
    return { success: false, error: (error as Error).message };
  }
}

/**
 * Send approval email to business owner
 */
async function sendApprovalEmail(application: any) {
  const emailData = {
    from: 'Fuse Network <<EMAIL>>',
    to: application.contact_email,
    subject: 'Your FUSE Network Application Has Been Approved!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #3A56FF;">Congratulations! 🎉</h2>
        <p>Dear ${application.contact_name || "Business Owner"},</p>
        <p>Your business, <strong>${application.business_name}</strong>, has been approved to join the FUSE Network!</p>
        <p>Welcome aboard! You can now:</p>
        <ul>
          <li>Track your referral rewards in the Business Portal</li>
          <li>Access your business dashboard</li>
          <li>Start earning FUSE tokens from customer visits</li>
        </ul>
        <p>Visit your dashboard to get started: <a href="${process.env.NEXT_PUBLIC_SITE_URL}/dashboard/business">Business Dashboard</a></p>
        <p>Best regards,<br>The FUSE Team</p>
      </div>
    `
  };

  // Use Resend API for email sending
  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(emailData)
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Failed to send email: ${errorData.message}`);
  }
}

/**
 * Format file size in human readable format
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
