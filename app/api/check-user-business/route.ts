import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/api-security'
import { checkUserHasBusinessDirect } from '@/lib/database-direct'

export async function GET(request: NextRequest) {
  return withSecurity(
    request,
    async (req, { user }) => {
      try {
        if (!user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }

        console.log('🏢 Checking if authenticated user has business:', user.id)

        // Check if authenticated user has business using direct database access
        const hasBusiness = await checkUserHasBusinessDirect(user.id)

        console.log('✅ User business check completed:', hasBusiness)

        return NextResponse.json({
          success: true,
          data: hasBusiness,
          message: 'User business check completed successfully'
        })

      } catch (error: any) {
        console.error('❌ Check user business API error for user', user?.id, ':', error)

        return NextResponse.json(
          {
            error: 'Internal server error',
            details: error.message
          },
          { status: 500 }
        )
      }
    },
    {
      requireAuth: true, // Enforce authentication
      rateLimit: { windowMs: 60000, maxRequests: 60 }, // 60 requests per minute
      logRequest: true // Log all requests for security monitoring
    }
  );
}
