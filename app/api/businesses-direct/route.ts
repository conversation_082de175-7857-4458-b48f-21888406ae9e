import { NextResponse } from 'next/server'
import { getActiveBusinessesDirect } from '@/lib/database-direct'

export async function GET() {
  try {
    console.log('🔄 Fetching businesses via direct database connection...')

    // Use direct database connection
    const businesses = await getActiveBusinessesDirect()

    if (!businesses) {
      console.error('❌ Failed to fetch businesses from database')
      return NextResponse.json(
        { error: 'Failed to fetch businesses from database' },
        { status: 500 }
      )
    }

    console.log(`✅ Successfully fetched ${businesses.length} businesses via direct database`)

    return NextResponse.json({
      data: businesses,
      source: 'direct_database',
      timestamp: new Date().toISOString(),
      count: businesses.length
    })

  } catch (error) {
    console.error('❌ Error in businesses API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch businesses', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
