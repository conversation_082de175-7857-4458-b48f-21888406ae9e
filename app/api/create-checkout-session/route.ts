import { NextResponse } from "next/server"
import Stripe from "stripe"
import { createClient } from "@/lib/supabase/server"

// Initialize Stripe with proper error handling
const stripeSecretKey = process.env.STRIPE_SECRET_KEY
if (!stripeSecretKey) {
  throw new Error("STRIPE_SECRET_KEY environment variable is not set")
}

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: "2025-05-28.basil",
})

export async function POST(request: Request) {
  try {
    console.log('🛒 Creating checkout session...')
    const { cardType, price, userId, guestEmail, isGuest } = await request.json()

    // Validate the request
    if (!cardType || !price) {
      console.error('❌ Missing required fields:', { cardType, price })
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Allow guest purchases - generate guest ID if needed
    let finalUserId = userId;
    let customerEmail = guestEmail;

    if (isGuest || !userId) {
      console.log('🎫 Processing guest purchase')
      finalUserId = `guest-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

      if (!guestEmail) {
        console.error('❌ Guest email required for guest purchases')
        return NextResponse.json({
          error: "Email address is required for guest purchases."
        }, { status: 400 })
      }

      console.log('✅ Guest purchase setup:', { finalUserId, customerEmail })
    }

    // For authenticated users, verify the user exists (profile not required)
    if (!isGuest && userId) {
      const supabase = await createClient()
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError || !user || user.id !== userId) {
        console.error('❌ Authentication failed:', { authError, userId, actualUserId: user?.id })
        return NextResponse.json({
          error: "Invalid authentication. Please sign in to continue."
        }, { status: 401 })
      }

      console.log('✅ User authenticated:', user.id)
      customerEmail = user.email // Use authenticated user's email
    }

    // Create a checkout session
    const sessionConfig = {
      payment_method_types: ["card" as const],
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `${cardType} VIP Membership`,
              description: `${cardType} tier VIP membership for Fuse.vip - Access to exclusive discounts and rewards`,
            },
            unit_amount: Math.round(price * 100), // Convert to cents and ensure integer
          },
          quantity: 1,
        },
      ],
      mode: "payment" as const,
      success_url: `${request.headers.get("origin")}/checkout-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${request.headers.get("origin")}/industry`,
      client_reference_id: finalUserId, // This is the primary way to identify the user
      metadata: {
        cardType,
        userId: finalUserId, // Backup identification
        isGuest: isGuest ? "true" : "false",
        originalUserId: userId || "none", // Track original user ID if any
      },
      customer_email: customerEmail, // Use guest email or authenticated user's email
    }

    console.log('💳 Creating Stripe session for user:', finalUserId, 'cardType:', cardType, 'price:', price, 'isGuest:', isGuest)
    const session = await stripe.checkout.sessions.create(sessionConfig)

    console.log('✅ Checkout session created:', session.id)
    return NextResponse.json({ url: session.url })

  } catch (error) {
    console.error("❌ Error creating checkout session:", error)
    return NextResponse.json({
      error: "An error occurred while creating the checkout session",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
