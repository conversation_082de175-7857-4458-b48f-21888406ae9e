import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/api-security'
import { getUserBusinessesDirect } from '@/lib/database-direct'

export async function GET(request: NextRequest) {
  return withSecurity(
    request,
    async (req, { user }) => {
      try {
        if (!user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }

        console.log('🏢 Fetching businesses for authenticated user:', user.id)

        // Get authenticated user's businesses using direct database access
        const businesses = await getUserBusinessesDirect(user.id)

        console.log(`✅ Found ${businesses?.length || 0} businesses for user ${user.id}`)

        return NextResponse.json({
          success: true,
          data: businesses || [],
          message: 'User businesses retrieved successfully'
        })

      } catch (error: any) {
        console.error('❌ User businesses API error for user', user?.id, ':', error)

        return NextResponse.json(
          {
            error: 'Internal server error',
            details: error.message
          },
          { status: 500 }
        )
      }
    },
    {
      requireAuth: true, // Enforce authentication
      rateLimit: { windowMs: 60000, maxRequests: 60 }, // 60 requests per minute
      logRequest: true // Log all requests for security monitoring
    }
  );
}
