import { NextRequest, NextResponse } from 'next/server';
import { withSecurity } from '@/lib/api-security';

export async function GET(request: NextRequest) {
  return withSecurity(
    request,
    async (req, { user, supabase }) => {
      try {
        console.log('🔍 Test Auth - User from withSecurity:', {
          hasUser: !!user,
          userId: user?.id,
          userEmail: user?.email,
          userAud: user?.aud,
          userRole: user?.role
        });

        // Also test direct supabase auth call
        const { data: { user: directUser }, error: directError } = await supabase.auth.getUser();
        
        console.log('🔍 Test Auth - Direct supabase.auth.getUser():', {
          hasDirectUser: !!directUser,
          directUserId: directUser?.id,
          directUserEmail: directUser?.email,
          directError: directError?.message
        });

        // Test database query with the user ID
        if (user?.id) {
          const { executeQuery } = await import('@/lib/database-direct');
          const { data: businessRows, error: businessError } = await executeQuery(
            `SELECT id, name, user_id FROM businesses WHERE user_id = $1 AND is_active = true`,
            [user.id]
          );

          console.log('🔍 Test Auth - Database query result:', {
            businessRows: businessRows?.length || 0,
            businessError: businessError?.message,
            businesses: businessRows?.map(b => ({ id: b.id, name: b.name }))
          });

          return NextResponse.json({
            success: true,
            auth: {
              withSecurityUser: {
                id: user.id,
                email: user.email,
                aud: user.aud
              },
              directUser: directUser ? {
                id: directUser.id,
                email: directUser.email,
                aud: directUser.aud
              } : null,
              directError: directError?.message
            },
            database: {
              businessCount: businessRows?.length || 0,
              businesses: businessRows?.map(b => ({ id: b.id, name: b.name })) || [],
              error: businessError?.message
            }
          });
        }

        return NextResponse.json({
          success: false,
          error: 'No user ID available',
          auth: {
            withSecurityUser: user,
            directUser: directUser,
            directError: directError?.message
          }
        });

      } catch (error: any) {
        console.error('❌ Test Auth Error:', error);
        return NextResponse.json(
          {
            success: false,
            error: 'Internal server error',
            details: error.message
          },
          { status: 500 }
        );
      }
    },
    {
      requireAuth: true,
      logRequest: true
    }
  );
}
