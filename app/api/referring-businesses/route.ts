import { NextRequest, NextResponse } from 'next/server'
import { searchBusinessesDirect } from '@/lib/database-direct'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')

    console.log('🔄 Fetching referring businesses using direct database connection...')

    // Use direct database connection to get all active businesses
    const businesses = await searchBusinessesDirect(search || undefined, 100)

    if (!businesses) {
      console.error('❌ Failed to fetch referring businesses from database')
      return NextResponse.json(
        { error: 'Failed to fetch referring businesses from database' },
        { status: 500 }
      )
    }

    console.log(`✅ Successfully fetched ${businesses.length} referring businesses via direct database`)

    // Map to the format expected by BusinessCombobox
    const mappedBusinesses = businesses.map((business: any) => ({
      id: business.id,
      name: business.name,
      category: business.category,
      logo_url: business.logo_url,
      website: business.website
    }))

    return NextResponse.json({
      data: mappedBusinesses,
      source: 'direct_database',
      timestamp: new Date().toISOString(),
      count: mappedBusinesses.length
    })

  } catch (error: any) {
    console.error('❌ Referring businesses API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
