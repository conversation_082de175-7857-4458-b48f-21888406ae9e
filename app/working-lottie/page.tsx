"use client"

import { useEffect, useRef, useState } from "react"

export default function WorkingLottiePage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Working Lottie Solutions
          </h1>
          <p className="text-lg text-gray-600">
            Multiple approaches to ensure your Lottie animation works
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Method 1: Iframe (Most Reliable) */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-center">Method 1: Iframe</h2>
            <div className="flex justify-center mb-4">
              <iframe
                src="https://lottie.host/embed/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.html"
                width="300"
                height="300"
                className="border-0"
                title="Lottie Animation via Iframe"
              />
            </div>
            <div className="text-sm text-gray-600 space-y-1">
              <p>✅ Most reliable</p>
              <p>✅ No CSP issues</p>
              <p>✅ Works immediately</p>
            </div>
          </div>

          {/* Method 2: Web Component with Fallback */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-center">Method 2: Web Component</h2>
            <div className="flex justify-center mb-4">
              <WebComponentLottie />
            </div>
            <div className="text-sm text-gray-600 space-y-1">
              <p>⚡ Modern approach</p>
              <p>🔄 Auto-fallback to iframe</p>
              <p>🎛️ More control options</p>
            </div>
          </div>
        </div>

        {/* Implementation Code Examples */}
        <div className="mt-12 space-y-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold mb-4">Implementation Code</h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Method 1: Iframe (Recommended)</h3>
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
{`<iframe
  src="https://lottie.host/embed/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.html"
  width="300"
  height="300"
  className="border-0"
  title="Lottie Animation"
/>`}
                </pre>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Method 2: Web Component (Your Original Request)</h3>
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
{`<script src="https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js" type="module"></script>
<dotlottie-wc 
  src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie" 
  style="width: 300px;height: 300px" 
  speed="1" 
  autoplay 
  loop>
</dotlottie-wc>`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function WebComponentLottie() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [status, setStatus] = useState<'loading' | 'success' | 'fallback'>('loading')

  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    const loadAndCreate = async () => {
      if (!containerRef.current) return

      try {
        // Set a timeout for fallback
        timeoutId = setTimeout(() => {
          setStatus('fallback')
          createIframeFallback()
        }, 3000)

        // Load the script
        const script = document.createElement("script")
        script.src = "https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js"
        script.type = "module"

        script.onload = () => {
          clearTimeout(timeoutId)
          setTimeout(() => {
            createWebComponent()
          }, 200)
        }

        script.onerror = () => {
          clearTimeout(timeoutId)
          setStatus('fallback')
          createIframeFallback()
        }

        document.head.appendChild(script)
      } catch (error) {
        clearTimeout(timeoutId)
        setStatus('fallback')
        createIframeFallback()
      }
    }

    const createWebComponent = () => {
      if (!containerRef.current) return

      try {
        containerRef.current.innerHTML = `
          <dotlottie-wc 
            src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie" 
            style="width: 300px; height: 300px;" 
            speed="1" 
            autoplay 
            loop>
          </dotlottie-wc>
        `
        setStatus('success')
      } catch (error) {
        setStatus('fallback')
        createIframeFallback()
      }
    }

    const createIframeFallback = () => {
      if (!containerRef.current) return

      containerRef.current.innerHTML = `
        <iframe
          src="https://lottie.host/embed/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.html"
          width="300"
          height="300"
          style="border: 0;"
          title="Lottie Animation Fallback"
        ></iframe>
      `
    }

    loadAndCreate()

    return () => {
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [])

  return (
    <div className="relative">
      <div ref={containerRef} className="flex justify-center items-center">
        <div className="w-[300px] h-[300px] bg-gray-100 rounded-lg flex items-center justify-center">
          Loading...
        </div>
      </div>
      <div className="text-center mt-2 text-sm">
        Status: <span className={`font-semibold ${
          status === 'success' ? 'text-green-600' : 
          status === 'fallback' ? 'text-blue-600' : 
          'text-yellow-600'
        }`}>
          {status === 'success' ? 'Web Component' : 
           status === 'fallback' ? 'Iframe Fallback' : 
           'Loading...'}
        </span>
      </div>
    </div>
  )
}
