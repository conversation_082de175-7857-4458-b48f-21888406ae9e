import { RegisterForm } from "@/components/auth/register-form"
import { PageHeader } from "@/components/page-header"
import { CtaSection } from "@/components/cta-section"

export default function RegisterPage() {
  return (
    <div className="container max-w-screen-xl mx-auto py-12">
      <PageHeader
        title="Join Fuse.Vip as a Customer or Business Owner"
        subtitle="GET YOUR VIP CARD OR JOIN OUR NETWORK"
        description="🎯 Create your account • 💎 Get your VIP card for exclusive discounts • 🏢 Submit business app in Dashboard to join our partner network"
      />

      <div className="mt-8 mb-16">
        <RegisterForm />
        <div className="mt-8 max-w-2xl mx-auto">
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
              ✨ What happens after registration?
            </h3>
            <div className="space-y-4 text-sm">
              <div className="flex items-start space-x-3 p-3 bg-white/60 rounded-lg border border-blue-100">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-xs">💎</span>
                </div>
                <div>
                  <p className="font-semibold text-blue-900">For Customers</p>
                  <p className="text-blue-700 mt-1">Visit the <strong>Upgrade</strong> tab to purchase your VIP card and unlock exclusive discounts at partner businesses.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-white/60 rounded-lg border border-blue-100">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-xs">🏢</span>
                </div>
                <div>
                  <p className="font-semibold text-blue-900">For Business Owners</p>
                  <p className="text-blue-700 mt-1">After registration, visit <strong>Business Registration</strong> to submit your application and join our partner network.</p>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 bg-gray-50 rounded-lg px-4 py-2 inline-flex items-center space-x-2">
              <span>🚀</span>
              <span>You'll be redirected to your dashboard to choose your next step</span>
            </p>
          </div>
        </div>
      </div>

      <CtaSection />
    </div>
  )
}
