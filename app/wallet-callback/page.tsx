'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function WalletCallbackPage() {
  const router = useRouter();

  useEffect(() => {
    // This page handles the OAuth2 redirect from Xaman
    // The XummPkce library will automatically handle the callback
    // and trigger the appropriate events
    
    // Redirect back to the main page after a short delay
    const timer = setTimeout(() => {
      router.push('/');
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
            Processing Wallet Connection
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Please wait while we complete your wallet connection...
          </p>
          <div className="mt-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          </div>
          <p className="mt-4 text-xs text-gray-500 dark:text-gray-400">
            You will be redirected automatically
          </p>
        </div>
      </div>
    </div>
  );
}
