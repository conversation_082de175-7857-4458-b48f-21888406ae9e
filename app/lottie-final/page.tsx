"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>xa<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ottieExample } from "@/components/your-lottie-example"
import { LottieIframe } from "@/components/lottie-iframe"

export default function LottieFinalPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            ✅ Working Lottie Solutions
          </h1>
          <p className="text-lg text-gray-600">
            Based on the Medium article reference - all animations should work perfectly
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* Method 1: lottie-react (Recommended) */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-center text-green-600">
              ✅ Method 1: lottie-react (Recommended)
            </h2>
            <div className="flex justify-center mb-4">
              <YourLottieExample />
            </div>
            <div className="text-sm text-gray-600 space-y-1">
              <p>✅ Perfect Next.js compatibility</p>
              <p>✅ No CSP issues</p>
              <p>✅ Reliable and maintained</p>
              <p>✅ Full control over playback</p>
            </div>
          </div>

          {/* Method 2: Iframe (Backup) */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-center text-blue-600">
              🔄 Method 2: Iframe (Backup)
            </h2>
            <div className="flex justify-center mb-4">
              <LottieIframe
                src="https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie"
                width={300}
                height={300}
              />
            </div>
            <div className="text-sm text-gray-600 space-y-1">
              <p>✅ Always works</p>
              <p>✅ No dependencies</p>
              <p>✅ Instant loading</p>
              <p>⚠️ Less control</p>
            </div>
          </div>
        </div>

        {/* Additional Examples */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">Simple Implementation</h3>
            <div className="flex justify-center">
              <SimpleLottieExample />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 text-center">With Controls</h3>
            <div className="flex justify-center">
              <AdvancedLottieExample />
            </div>
          </div>
        </div>

        {/* Implementation Code */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold mb-6">📝 Implementation Code</h2>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-semibold mb-3 text-green-600">Method 1: lottie-react (Following Medium Article)</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">1. Install the package:</h4>
                  <pre className="bg-gray-100 p-3 rounded text-sm">
                    <code>npm install lottie-react</code>
                  </pre>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">2. Create your component:</h4>
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`"use client"

import { useLottie } from "lottie-react"
import { useEffect, useState } from "react"

export function YourLottieAnimation() {
  const [animationData, setAnimationData] = useState(null)

  useEffect(() => {
    fetch("https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie")
      .then(response => response.json())
      .then(data => setAnimationData(data))
  }, [])

  const { View } = useLottie({
    animationData: animationData,
    loop: true,
  })

  return (
    <div style={{ width: "300px", height: "300px" }}>
      {View}
    </div>
  )
}`}
                  </pre>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3 text-blue-600">Method 2: Iframe (Backup)</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`<iframe
  src="https://lottie.host/embed/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.html"
  width="300"
  height="300"
  className="border-0"
  title="Lottie Animation"
/>`}
              </pre>
            </div>
          </div>
        </div>

        {/* Summary */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-bold text-green-800 mb-4">✅ Summary</h2>
          <div className="text-green-700 space-y-2">
            <p><strong>Recommended:</strong> Use lottie-react library as shown in the Medium article</p>
            <p><strong>Backup:</strong> Use iframe method for guaranteed compatibility</p>
            <p><strong>Your Animation:</strong> https://lottie.host/66b677b5-df76-4dd5-85ba-f34e353869b4/LFZk9KY1Cy.lottie</p>
            <p><strong>Result:</strong> Both methods should work perfectly in your Next.js app</p>
          </div>
        </div>
      </div>
    </div>
  )
}
