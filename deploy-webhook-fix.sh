#!/bin/bash

echo "🚀 Deploying Stripe webhook handler fixes and auth-gated checkout..."

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Navigate to the app directory where the supabase config is
cd app

# Deploy the webhook handler function
echo "📤 Deploying stripe-webhook-handler function..."
supabase functions deploy stripe-webhook-handler --no-verify-jwt

echo "✅ Deployment complete!"
echo ""
echo "🔧 What was fixed:"
echo "  ✅ Fixed Stripe webhook 401 authentication error"
echo "  ✅ Added verify_jwt: false to disable auth requirement for webhooks"
echo "  ✅ Enhanced logging for debugging webhook requests"  
echo "  ✅ Added proper card tier handling (monthly/annual/lifetime)"
echo "  ✅ Fixed membership duration calculations"
echo "  ✅ Created auth-gated checkout flow"
echo "  ✅ Users now MUST create accounts before purchasing"
echo ""
echo "🎯 UX Improvements:"
echo "  ✅ Seamless signup-to-payment flow in single modal"
echo "  ✅ Progress indicator showing Account → Payment steps"
echo "  ✅ Quick signup form optimized for conversion"
echo "  ✅ Automatic transition from signup to payment"
echo "  ✅ No more 'Please sign in first' errors mid-purchase"
echo ""
echo "🛠️ Next steps:"
echo "  1. Test the new auth-gated purchase flow:"
echo "     - Click 'Get VIP Card' while logged out"
echo "     - Complete quick signup form"
echo "     - Verify seamless transition to payment"
echo "  2. Test purchase completion:"
echo "     - Complete $9.99 monthly subscription purchase"  
echo "     - Check webhook processes successfully in Supabase logs"
echo "     - Verify user profile gets is_card_holder: true"
echo "  3. Verify user flow sequence:"
echo "     - New users: Register → Upgrade → Purchase"
echo "     - Existing users: Direct to payment"