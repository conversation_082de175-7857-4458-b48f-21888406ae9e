// =============================================
// INDUSTRY PAGE E2E TESTS
// SOAR Architecture Review - Testing Phase
// Target: Page load ≤2s, API response ≤300ms
// =============================================

import { test, expect } from '@playwright/test'

test.describe('Industry Page Performance & Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to industry page
    await page.goto('/industry')
  })

  test('should load industry page within performance target', async ({ page }) => {
    const startTime = Date.now()
    
    // Wait for page to be fully loaded
    await page.waitForLoadState('networkidle')
    
    const loadTime = Date.now() - startTime
    
    // SOAR target: ≤2s page load
    expect(loadTime).toBeLessThan(2000)
    
    // Verify page title
    await expect(page).toHaveTitle(/Industry/i)
    
    // Verify main heading is visible
    await expect(page.getByRole('heading', { name: /fuse discounts/i })).toBeVisible()
  })

  test('should display businesses with optimized API', async ({ page }) => {
    // Wait for businesses to load
    await page.waitForSelector('[data-testid="business-card"]', { timeout: 5000 })
    
    // Verify businesses are displayed
    const businessCards = page.locator('[data-testid="business-card"]')
    await expect(businessCards).toHaveCount.greaterThan(0)
    
    // Verify business information is displayed
    const firstBusiness = businessCards.first()
    await expect(firstBusiness.locator('h3')).toBeVisible() // Business name
    await expect(firstBusiness.locator('[data-testid="business-website"]')).toBeVisible() // Website link
  })

  test('should handle pagination correctly', async ({ page }) => {
    // Wait for initial load
    await page.waitForSelector('[data-testid="business-card"]')
    
    // Check if pagination controls exist
    const nextButton = page.getByRole('button', { name: /next/i })
    
    if (await nextButton.isVisible()) {
      // Click next page
      await nextButton.click()
      
      // Wait for new businesses to load
      await page.waitForLoadState('networkidle')
      
      // Verify URL contains page parameter
      expect(page.url()).toContain('page=2')
      
      // Verify previous button is now enabled
      await expect(page.getByRole('button', { name: /previous/i })).toBeEnabled()
    }
  })

  test('should filter businesses by category', async ({ page }) => {
    // Wait for initial load
    await page.waitForSelector('[data-testid="business-card"]')
    
    // Find category filter
    const categorySelect = page.locator('select[data-testid="category-filter"]')
    
    if (await categorySelect.isVisible()) {
      // Select a specific category
      await categorySelect.selectOption('restaurant')
      
      // Wait for filtered results
      await page.waitForLoadState('networkidle')
      
      // Verify URL contains category parameter
      expect(page.url()).toContain('category=restaurant')
      
      // Verify businesses are filtered (if any exist)
      const businessCards = page.locator('[data-testid="business-card"]')
      const count = await businessCards.count()
      
      if (count > 0) {
        // All visible businesses should be restaurants
        for (let i = 0; i < Math.min(count, 5); i++) {
          const category = await businessCards.nth(i).locator('[data-testid="business-category"]').textContent()
          expect(category?.toLowerCase()).toContain('restaurant')
        }
      }
    }
  })

  test('should search businesses', async ({ page }) => {
    // Wait for initial load
    await page.waitForSelector('[data-testid="business-card"]')
    
    // Find search input
    const searchInput = page.locator('input[data-testid="search-input"]')
    
    if (await searchInput.isVisible()) {
      // Search for a business
      await searchInput.fill('pizza')
      await searchInput.press('Enter')
      
      // Wait for search results
      await page.waitForLoadState('networkidle')
      
      // Verify URL contains search parameter
      expect(page.url()).toContain('search=pizza')
      
      // Verify search results (if any)
      const businessCards = page.locator('[data-testid="business-card"]')
      const count = await businessCards.count()
      
      if (count > 0) {
        // At least one business should contain "pizza" in name or description
        const firstBusinessName = await businessCards.first().locator('h3').textContent()
        expect(firstBusinessName?.toLowerCase()).toContain('pizza')
      }
    }
  })

  test('should display spotlight businesses first', async ({ page }) => {
    // Wait for businesses to load
    await page.waitForSelector('[data-testid="business-card"]')
    
    // Check if spotlight filter exists
    const spotlightButton = page.getByRole('button', { name: /spotlight/i })
    
    if (await spotlightButton.isVisible()) {
      await spotlightButton.click()
      
      // Wait for filtered results
      await page.waitForLoadState('networkidle')
      
      // Verify spotlight businesses are displayed
      const businessCards = page.locator('[data-testid="business-card"]')
      const count = await businessCards.count()
      
      if (count > 0) {
        // Verify spotlight indicator is visible
        await expect(businessCards.first().locator('[data-testid="spotlight-badge"]')).toBeVisible()
      }
    }
  })

  test('should handle business website links', async ({ page }) => {
    // Wait for businesses to load
    await page.waitForSelector('[data-testid="business-card"]')
    
    const businessCards = page.locator('[data-testid="business-card"]')
    const count = await businessCards.count()
    
    if (count > 0) {
      const websiteLink = businessCards.first().locator('[data-testid="business-website"]')
      
      if (await websiteLink.isVisible()) {
        // Verify link has correct attributes
        await expect(websiteLink).toHaveAttribute('target', '_blank')
        await expect(websiteLink).toHaveAttribute('rel', 'noopener noreferrer')
        
        // Verify link has valid href
        const href = await websiteLink.getAttribute('href')
        expect(href).toMatch(/^https?:\/\//)
      }
    }
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Wait for page to load
    await page.waitForLoadState('networkidle')
    
    // Verify mobile layout
    await expect(page.getByRole('heading', { name: /fuse discounts/i })).toBeVisible()
    
    // Verify businesses are still displayed
    const businessCards = page.locator('[data-testid="business-card"]')
    await expect(businessCards.first()).toBeVisible()
    
    // Verify mobile navigation works
    const mobileMenu = page.locator('[data-testid="mobile-menu"]')
    if (await mobileMenu.isVisible()) {
      await mobileMenu.click()
      await expect(page.locator('[data-testid="mobile-nav-items"]')).toBeVisible()
    }
  })

  test('should handle API errors gracefully', async ({ page }) => {
    // Intercept API calls and simulate error
    await page.route('/api/businesses-optimized*', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      })
    })
    
    // Navigate to page
    await page.goto('/industry')
    
    // Wait for error state
    await page.waitForSelector('[data-testid="error-message"]', { timeout: 10000 })
    
    // Verify error message is displayed
    await expect(page.getByText(/failed to load/i)).toBeVisible()
    
    // Verify retry button is available
    await expect(page.getByRole('button', { name: /retry/i })).toBeVisible()
  })

  test('should meet Core Web Vitals targets', async ({ page }) => {
    // Navigate and wait for load
    await page.goto('/industry')
    await page.waitForLoadState('networkidle')
    
    // Measure Core Web Vitals
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const vitals: Record<string, number> = {}
          
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              vitals.fcp = entry.startTime
            }
            if (entry.entryType === 'largest-contentful-paint') {
              vitals.lcp = entry.startTime
            }
            if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
              vitals.cls = (vitals.cls || 0) + entry.value
            }
          })
          
          // Resolve after collecting metrics for 3 seconds
          setTimeout(() => resolve(vitals), 3000)
        })
        
        observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'layout-shift'] })
      })
    })
    
    // Verify Core Web Vitals meet targets
    if (vitals.fcp) {
      expect(vitals.fcp).toBeLessThan(2000) // FCP < 2s
    }
    if (vitals.lcp) {
      expect(vitals.lcp).toBeLessThan(2500) // LCP < 2.5s
    }
    if (vitals.cls) {
      expect(vitals.cls).toBeLessThan(0.1) // CLS < 0.1
    }
  })

  test('should load with optimized images', async ({ page }) => {
    // Wait for businesses to load
    await page.waitForSelector('[data-testid="business-card"]')
    
    // Check business logos
    const logos = page.locator('[data-testid="business-logo"]')
    const count = await logos.count()
    
    if (count > 0) {
      // Verify first logo loads successfully
      const firstLogo = logos.first()
      await expect(firstLogo).toBeVisible()
      
      // Verify image has proper attributes
      await expect(firstLogo).toHaveAttribute('loading', 'lazy')
      await expect(firstLogo).toHaveAttribute('alt')
      
      // Verify image loads without errors
      const naturalWidth = await firstLogo.evaluate((img: HTMLImageElement) => img.naturalWidth)
      expect(naturalWidth).toBeGreaterThan(0)
    }
  })
})
