// =============================================
// PLAYWRIGHT GLOBAL SETUP
// E2E Testing Environment Setup
// =============================================

import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test environment setup...')
  
  // Launch browser for setup
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Wait for the development server to be ready
    console.log('⏳ Waiting for development server...')
    
    let retries = 0
    const maxRetries = 30
    
    while (retries < maxRetries) {
      try {
        const response = await page.goto('http://localhost:3000/api/health')
        if (response?.ok()) {
          console.log('✅ Development server is ready')
          break
        }
      } catch (error) {
        retries++
        if (retries === maxRetries) {
          throw new Error('Development server failed to start within timeout')
        }
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
    
    // Verify database connectivity
    console.log('🔍 Checking database connectivity...')
    
    try {
      const healthResponse = await page.goto('http://localhost:3000/api/health')
      const healthData = await healthResponse?.json()
      
      if (healthData?.database?.status !== 'healthy') {
        console.warn('⚠️ Database health check failed, some tests may fail')
      } else {
        console.log('✅ Database connectivity verified')
      }
    } catch (error) {
      console.warn('⚠️ Could not verify database connectivity:', error)
    }
    
    // Pre-warm the application
    console.log('🔥 Pre-warming application...')
    
    const pages = [
      '/',
      '/industry',
      '/dashboard',
      '/api/businesses-optimized?page=1&limit=5'
    ]
    
    for (const pagePath of pages) {
      try {
        await page.goto(`http://localhost:3000${pagePath}`)
        await page.waitForLoadState('networkidle', { timeout: 10000 })
      } catch (error) {
        console.warn(`⚠️ Failed to pre-warm ${pagePath}:`, error)
      }
    }
    
    console.log('✅ Application pre-warming completed')
    
    // Set up test data if needed
    console.log('📊 Setting up test data...')
    
    // You can add test data setup here if needed
    // For example, creating test businesses, users, etc.
    
    console.log('✅ E2E test environment setup completed')
    
  } catch (error) {
    console.error('❌ E2E setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

export default globalSetup
