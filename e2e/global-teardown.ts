// =============================================
// PLAYWRIGHT GLOBAL TEARDOWN
// E2E Testing Environment Cleanup
// =============================================

import { FullConfig } from '@playwright/test'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test environment cleanup...')
  
  try {
    // Clean up test data if any was created
    console.log('📊 Cleaning up test data...')
    
    // You can add test data cleanup here if needed
    // For example, removing test businesses, users, etc.
    
    // Clear any temporary files
    console.log('🗂️ Clearing temporary files...')
    
    // Performance monitoring cleanup
    console.log('📈 Cleaning up performance monitoring data...')
    
    console.log('✅ E2E test environment cleanup completed')
    
  } catch (error) {
    console.error('❌ E2E teardown failed:', error)
    // Don't throw error to avoid failing the test run
  }
}

export default globalTeardown
