// Test script to verify businesses table connection
// Run this with: node test-business-connection.js

const { executeQuery } = require('./lib/database-direct');

async function testBusinessConnection() {
  console.log('🔍 Testing businesses table connection...');
  
  try {
    // Test 1: Simple query to check if we can connect to businesses table
    const testQuery = 'SELECT COUNT(*) as count FROM businesses';
    const { data: countData, error: countError } = await executeQuery(testQuery, []);
    
    if (countError) {
      console.error('❌ Failed to query businesses table:', countError);
      return false;
    }
    
    console.log('✅ Successfully connected to businesses table');
    console.log('📊 Total businesses:', countData?.[0]?.count || 0);
    
    // Test 2: Query active businesses
    const activeQuery = 'SELECT id, name, user_id, is_active FROM businesses WHERE is_active = true LIMIT 5';
    const { data: activeData, error: activeError } = await executeQuery(activeQuery, []);
    
    if (activeError) {
      console.error('❌ Failed to query active businesses:', activeError);
      return false;
    }
    
    console.log('✅ Successfully queried active businesses');
    console.log('📊 Active businesses sample:', activeData || []);
    
    // Test 3: Check table schema
    const schemaQuery = `
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'businesses' 
      ORDER BY ordinal_position
    `;
    const { data: schemaData, error: schemaError } = await executeQuery(schemaQuery, []);
    
    if (schemaError) {
      console.error('❌ Failed to query businesses schema:', schemaError);
      return false;
    }
    
    console.log('✅ Successfully queried businesses schema');
    console.log('📊 Schema columns:', schemaData?.map(col => col.column_name) || []);
    
    return true;
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

// Run the test
testBusinessConnection()
  .then(success => {
    if (success) {
      console.log('🎉 All tests passed!');
    } else {
      console.log('❌ Some tests failed');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });