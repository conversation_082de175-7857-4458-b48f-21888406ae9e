# =============================================
# OPTIMIZED LOGO PROCESSOR DOCKERFILE
# Multi-stage build for minimal production image
# =============================================

# ---- Build Stage ----
FROM node:18-alpine AS build

# Install build dependencies
RUN apk add --no-cache \
    imagemagick \
    imagemagick-dev \
    graphicsmagick \
    libwebp-tools \
    optipng \
    jpegoptim \
    gifsicle \
    autoconf \
    automake \
    libtool \
    nasm \
    build-base \
    python3 \
    make \
    g++ \
    curl \
    ca-certificates

WORKDIR /app

# Copy package files
COPY package*.json ./

# Set Sharp environment variables
ENV SHARP_IGNORE_GLOBAL_LIBVIPS=1
ENV SHARP_FORCE_GLOBAL_LIBVIPS=0

# Install dependencies
RUN npm ci --only=production --no-audit --no-fund --prefer-offline \
    && npm cache clean --force

# ---- Runtime Stage ----
FROM node:18-alpine AS runtime

# Install only runtime dependencies
RUN apk add --no-cache \
    imagemagick \
    graphicsmagick \
    libwebp-tools \
    optipng \
    jpegoptim \
    gifsicle \
    curl \
    ca-certificates \
    && apk upgrade \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup --system --gid 1001 logoprocessor \
    && adduser --system --uid 1001 logoprocessor

WORKDIR /app

# Copy dependencies and application from build stage
COPY --from=build --chown=logoprocessor:logoprocessor /app/node_modules ./node_modules
COPY --chown=logoprocessor:logoprocessor . .

# Create directories for processing with proper permissions
RUN mkdir -p /app/uploads /app/processed /app/temp \
    && chown -R logoprocessor:logoprocessor /app/uploads /app/processed /app/temp \
    && chmod -R 755 /app/uploads /app/processed /app/temp

# Switch to non-root user
USER logoprocessor

# Set production environment
ENV NODE_ENV=production
ENV PORT=3001

# Expose port
EXPOSE 3001

# Enhanced health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Start the service
CMD ["node", "server.js"]
