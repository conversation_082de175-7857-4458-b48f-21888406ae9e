{"name": "fuse-logo-processor", "version": "1.0.0", "description": "Docker service for processing and optimizing business logos", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "file-type": "^16.5.4", "form-data": "^4.0.3", "helmet": "^7.1.0", "mime-types": "^2.1.35", "multer": "^2.0.0", "node-fetch": "^3.3.2", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}