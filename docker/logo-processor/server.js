const express = require('express');
const multer = require('multer');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { v4: uuidv4 } = require('uuid');
const mime = require('mime-types');
const FileType = require('file-type');
const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedMimes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'image/svg+xml'
    ];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, WebP, and SVG files are allowed.'));
    }
  }
});

// Middleware
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Logo processing endpoint
app.post('/api/process-logo', upload.single('logo'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { buffer, originalname, mimetype } = req.file;
    const fileId = uuidv4();
    
    // Validate file type
    const detectedType = await FileType.fromBuffer(buffer);
    if (!detectedType || !['jpg', 'jpeg', 'png', 'webp', 'svg'].includes(detectedType.ext)) {
      return res.status(400).json({ error: 'Invalid file type detected' });
    }

    // Process the image
    const processedImages = await processLogo(buffer, fileId, mimetype, detectedType);
    
    res.json({
      success: true,
      fileId,
      originalName: originalname,
      processedImages,
      metadata: processedImages.original.metadata
    });

  } catch (error) {
    console.error('Logo processing error:', error);
    res.status(500).json({ 
      error: 'Failed to process logo',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get processed logo endpoint
app.get('/api/logo/:fileId/:size?', async (req, res) => {
  try {
    const { fileId, size = 'original' } = req.params;
    const filePath = path.join(__dirname, 'processed', fileId, `${size}.webp`);
    
    // Check if file exists
    try {
      await fs.access(filePath);
    } catch {
      return res.status(404).json({ error: 'Logo not found' });
    }

    // Set appropriate headers
    res.set({
      'Content-Type': 'image/webp',
      'Cache-Control': 'public, max-age=31536000', // 1 year
      'ETag': `"${fileId}-${size}"`
    });

    // Stream the file
    const fileBuffer = await fs.readFile(filePath);
    res.send(fileBuffer);

  } catch (error) {
    console.error('Logo retrieval error:', error);
    res.status(500).json({ error: 'Failed to retrieve logo' });
  }
});

// Logo processing function
async function processLogo(buffer, fileId, mimetype, detectedType) {
  const outputDir = path.join(__dirname, 'processed', fileId);
  await fs.mkdir(outputDir, { recursive: true });

  const results = {};

  // Define sizes to generate
  const sizes = {
    thumbnail: { width: 150, height: 150 },
    small: { width: 300, height: 300 },
    medium: { width: 600, height: 600 },
    large: { width: 1200, height: 1200 }
  };

  try {
    // Create temporary input file
    const tempInputPath = path.join('/tmp', `input_${fileId}.${detectedType.ext}`);
    await fs.writeFile(tempInputPath, buffer);

    // Get original image metadata using ImageMagick
    const { stdout: identifyOutput } = await execAsync(`identify -format "%wx%h %m %b" "${tempInputPath}"`);
    const [dimensions, format, size] = identifyOutput.trim().split(' ');
    const [width, height] = dimensions.split('x').map(Number);

    const metadata = {
      width,
      height,
      format: format.toLowerCase(),
      size: buffer.length
    };

    // Process original image - just copy for now, optimization can be added later
    let originalBuffer = buffer;

    // Save original as WebP using ImageMagick
    const originalPath = path.join(outputDir, 'original.webp');
    if (mimetype === 'image/svg+xml') {
      // Convert SVG to WebP with size limit
      await execAsync(`convert "${tempInputPath}" -resize 1200x1200> -quality 95 "${originalPath}"`);
    } else {
      // Convert to WebP
      await execAsync(`convert "${tempInputPath}" -quality 90 "${originalPath}"`);
    }

    results.original = {
      path: `/api/logo/${fileId}/original`,
      metadata: {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        size: buffer.length
      }
    };

    // Generate different sizes using ImageMagick
    for (const [sizeName, dimensions] of Object.entries(sizes)) {
      const outputPath = path.join(outputDir, `${sizeName}.webp`);

      // Use ImageMagick to resize and convert
      await execAsync(`convert "${tempInputPath}" -resize ${dimensions.width}x${dimensions.height}> -background transparent -quality 85 "${outputPath}"`);

      results[sizeName] = {
        path: `/api/logo/${fileId}/${sizeName}`,
        width: dimensions.width,
        height: dimensions.height
      };
    }

    // Clean up temporary file
    await fs.unlink(tempInputPath).catch(() => {});

    return results;

  } catch (error) {
    // Cleanup on error
    try {
      await fs.rmdir(outputDir, { recursive: true });
    } catch (cleanupError) {
      console.error('Cleanup error:', cleanupError);
    }
    throw error;
  }
}

// Cleanup old files (run every hour)
setInterval(async () => {
  try {
    const processedDir = path.join(__dirname, 'processed');
    const files = await fs.readdir(processedDir);
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const file of files) {
      const filePath = path.join(processedDir, file);
      const stats = await fs.stat(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        await fs.rmdir(filePath, { recursive: true });
        console.log(`Cleaned up old processed logo: ${file}`);
      }
    }
  } catch (error) {
    console.error('Cleanup error:', error);
  }
}, 60 * 60 * 1000); // Run every hour

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 10MB.' });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ error: 'Too many files. Only one file allowed.' });
    }
  }
  
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Logo processor service running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
