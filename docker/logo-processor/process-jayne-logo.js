const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function processJayneLogo() {
  try {
    console.log('🔄 Processing Jayne Bikinis logo...');
    
    const logoPath = path.join(__dirname, 'uploads', 'jayne-bikinis-logo.svg');
    
    // Check if logo file exists
    if (!fs.existsSync(logoPath)) {
      throw new Error(`Logo file not found: ${logoPath}`);
    }

    // Read the logo file
    const logoBuffer = fs.readFileSync(logoPath);
    
    // Create form data for upload
    const formData = new FormData();
    formData.append('logo', logoBuffer, {
      filename: 'jayne-bikinis-logo.svg',
      contentType: 'image/svg+xml'
    });
    formData.append('businessId', '6ae0e66b-3d9f-4383-bc99-4e6a34063ab7');
    formData.append('businessName', '<PERSON>');

    // Upload to logo processor service
    console.log('📤 Uploading to logo processor...');
    const response = await fetch('http://localhost:3001/api/process-logo', {
      method: 'POST',
      body: formData,
      headers: formData.getHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Logo processor failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Logo processed successfully:', result);

    // Now update the database
    console.log('🔄 Updating database...');
    
    // You would typically use your Supabase client here
    // For now, let's just log the information needed
    console.log('📝 Database update needed:');
    console.log('Business ID:', '6ae0e66b-3d9f-4383-bc99-4e6a34063ab7');
    console.log('New logo URL:', result.logoUrl || result.url);
    
    return result;
  } catch (error) {
    console.error('❌ Error processing logo:', error);
    throw error;
  }
}

// Run the function
processJayneLogo()
  .then(result => {
    console.log('🎉 Jayne Bikinis logo processing completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Logo processing failed:', error);
    process.exit(1);
  });
