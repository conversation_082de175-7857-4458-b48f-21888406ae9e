events {
    worker_connections 1024;
}

http {
    upstream logo_processor {
        server logo-processor:3001;
    }

    server {
        listen 80;
        server_name localhost;

        # Logo processing API
        location /api/ {
            proxy_pass http://logo_processor;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Increase timeouts for file uploads
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Increase max body size for file uploads
            client_max_body_size 10M;
        }

        # Serve processed logos directly
        location /logos/ {
            alias /var/www/logos/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
        }

        # Health check
        location /health {
            proxy_pass http://logo_processor/health;
        }
    }
}
