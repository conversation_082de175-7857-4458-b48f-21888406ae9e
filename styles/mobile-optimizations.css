/* Mobile Browser Optimizations for Fuse.vip */

/* Universal Mobile Optimizations */
@media (max-width: 768px) {
  /* Prevent zoom on input focus */
  input[type="text"],
  input[type="email"],
  input[type="number"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="url"],
  select,
  textarea {
    font-size: 16px !important;
    transform: translateZ(0);
  }

  /* Optimize touch targets */
  button,
  .btn,
  [role="button"],
  input[type="submit"],
  input[type="button"] {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
  }

  /* Smooth scrolling */
  * {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Prevent text size adjustment */
  html {
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Optimize font rendering */
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Mobile-specific toggle sections */
  .mobile-toggle-section {
    margin-bottom: 1rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .mobile-toggle-header {
    padding: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .mobile-toggle-header:active {
    transform: scale(0.98);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  }

  .mobile-toggle-content {
    padding: 16px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
  }

  /* Game containers */
  .mobile-game-container {
    touch-action: none;
    overscroll-behavior: none;
    -webkit-overflow-scrolling: auto;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Yield pool dashboard mobile optimizations */
  .yield-pool-mobile {
    padding: 12px;
    margin-bottom: 16px;
  }

  .yield-pool-card {
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Mobile navigation improvements */
  .mobile-nav-item {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background-color 0.2s ease;
  }

  .mobile-nav-item:active {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Safari-specific optimizations */
@supports (-webkit-appearance: none) {
  /* Fix 100vh issue on iOS Safari */
  .full-height-mobile {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
  }

  /* Prevent bounce scrolling */
  body {
    overscroll-behavior: none;
  }

  /* Fix input zoom on iOS */
  @media screen and (max-width: 768px) {
    input,
    select,
    textarea {
      font-size: 16px !important;
    }
  }

  /* Optimize animations for Safari */
  .safari-optimized {
    transform: translateZ(0);
    will-change: transform, opacity;
    backface-visibility: hidden;
  }
}

/* Chrome mobile optimizations */
@media screen and (max-width: 768px) {
  .chrome-optimized {
    transform: translateZ(0);
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Smooth scrolling for Chrome */
  .chrome-smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Hardware acceleration */
  .chrome-accelerated {
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
  }
}

/* Firefox mobile optimizations */
@-moz-document url-prefix() {
  @media screen and (max-width: 768px) {
    .firefox-touch-fix {
      touch-action: manipulation;
      -moz-user-select: none;
      -moz-touch-callout: none;
    }

    .firefox-smooth-animation {
      animation-fill-mode: both;
      animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Fix for Firefox mobile scrolling */
    .firefox-scroll-fix {
      overflow: auto;
      -moz-overflow-scrolling: touch;
    }
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-optimized {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  /* Sharper text on high DPI */
  .high-dpi-text {
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: auto;
  }
}

/* Dark mode mobile optimizations */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  .mobile-dark-optimized {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
    color: rgba(255, 255, 255, 0.9);
  }

  .mobile-dark-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

/* Performance optimizations */
@media (max-width: 768px) {
  /* Reduce motion for better performance */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* GPU acceleration for animations */
  .mobile-animated {
    transform: translateZ(0);
    will-change: transform, opacity;
    backface-visibility: hidden;
  }

  /* Optimize images for mobile */
  img {
    max-width: 100%;
    height: auto;
    image-rendering: -webkit-optimize-contrast;
  }

  /* Lazy loading optimization */
  .mobile-lazy-load {
    content-visibility: auto;
    contain-intrinsic-size: 200px;
  }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
  /* Focus indicators */
  button:focus,
  input:focus,
  select:focus,
  textarea:focus,
  [tabindex]:focus {
    outline: 2px solid #3A56FF;
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .mobile-high-contrast {
      border: 2px solid currentColor;
      background: transparent;
    }
  }

  /* Large text support */
  @media (prefers-reduced-motion: no-preference) {
    .mobile-scale-text {
      font-size: clamp(1rem, 4vw, 1.5rem);
    }
  }
}

/* Utility classes for mobile */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }

  .mobile-visible {
    display: block !important;
  }

  .mobile-flex {
    display: flex !important;
  }

  .mobile-grid {
    display: grid !important;
  }

  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-center {
    text-align: center !important;
  }

  .mobile-padding {
    padding: 16px !important;
  }

  .mobile-margin {
    margin: 16px !important;
  }

  .mobile-no-scroll {
    overflow: hidden !important;
  }

  .mobile-scroll {
    overflow: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
}
