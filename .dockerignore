# =============================================
# FUSE.VIP OPTIMIZED .dockerignore
# Reduces build context size for faster builds
# =============================================

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js build outputs
.next/
out/
build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
docs/
*.md

# Testing
coverage/
.nyc_output/
jest.config.js
*.test.js
*.test.ts
*.spec.js
*.spec.ts
__tests__/
test/
tests/

# Linting
.eslintrc*
.prettierrc*
.stylelintrc*

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Local development files
.local/
.cache/

# Supabase
supabase/.branches
supabase/.temp

# Business logos (these should be in volumes)
public/images/BusinessLogos/
docker/volumes/

# Performance monitoring data
performance-logs/
monitoring/

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar
*.tar.gz
*.rar

# Large media files that shouldn't be in containers
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm

# Database files
*.db
*.sqlite
*.sqlite3

# Certificate files
*.pem
*.key
*.crt
*.cert

# Terraform
*.tfstate
*.tfstate.backup
.terraform/

# Kubernetes
*.yaml
*.yml
k8s/

# Monitoring and observability
grafana/
prometheus/
loki/
