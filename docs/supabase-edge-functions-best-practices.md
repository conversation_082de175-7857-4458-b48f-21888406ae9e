# Supabase Edge Functions Best Practices

This document outlines the best practices for implementing Supabase Edge Functions based on the official Supabase documentation.

## Key Principles

### 1. User Authentication Context

**✅ DO**: Create a Supabase client with the user's auth context for each request:

```typescript
const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_ANON_KEY') ?? '',
  {
    global: {
      headers: { Authorization: req.headers.get('Authorization')! },
    },
  }
);
```

**❌ DON'T**: Use a global admin client for all operations, as this bypasses Row Level Security (RLS).

### 2. Row Level Security (RLS) Enforcement

When you create a client with the user's auth context, all database queries will automatically enforce RLS policies. This ensures users can only access data they're authorized to see.

### 3. Fetching User Data

```typescript
// Get the session or user object
const authHeader = req.headers.get('Authorization')!;
const token = authHeader.replace('Bearer ', '');
const { data } = await supabaseClient.auth.getUser(token);
const user = data.user;
```

### 4. When to Use Service Role Client

Use the service role client **only** for operations that require elevated permissions:

```typescript
const supabaseAdmin = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);
```

Examples:
- Admin-only operations
- System-level tasks
- Operations that need to bypass RLS temporarily

## Implementation Checklist

### ✅ Required for All Edge Functions

1. **Authorization Header Validation**
   ```typescript
   const authHeader = req.headers.get('Authorization');
   if (!authHeader) {
     return new Response(JSON.stringify({ error: 'Missing Authorization header' }), {
       status: 401,
       headers: { 'Content-Type': 'application/json' }
     });
   }
   ```

2. **User Context Client Creation**
   - Create client inside the `serve()` callback
   - Pass Authorization header to client configuration
   - Use this client for all user-scoped operations

3. **User Validation**
   ```typescript
   const { data } = await supabaseClient.auth.getUser(token);
   const user = data.user;
   if (!user) {
     return new Response(JSON.stringify({ error: 'Unauthorized' }), {
       status: 401,
       headers: { 'Content-Type': 'application/json' }
     });
   }
   ```

4. **CORS Headers**
   ```typescript
   const corsHeaders = {
     'Access-Control-Allow-Origin': '*',
     'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
     'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
   };
   ```

5. **Error Handling**
   - Consistent error response format
   - Proper HTTP status codes
   - Include CORS headers in error responses

## Updated Functions

### Functions Updated to Follow Best Practices:

1. **`redeem-vip-code/index.ts`**
   - ✅ Creates user-context client for RLS enforcement
   - ✅ Uses admin client only for RPC calls that require service role
   - ✅ Validates Authorization header properly

2. **`generate-vip-codes/index.ts`**
   - ✅ Creates user-context client for admin role verification
   - ✅ Uses admin client only for VIP code insertion
   - ✅ Improved error handling

### Functions That Need Updates:

1. **`stripe-webhook-handler/index.ts`** - Uses admin client only (appropriate for webhooks)
2. **`create-checkout-session/index.ts`** - Uses admin client only (appropriate for webhooks)
3. **`rapid-processor/index.ts`** - No authentication (may need review)

## Environment Variables Required

Make sure these environment variables are set in your Supabase project:

```bash
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key  # Only for admin operations
```

## Testing Your Edge Functions

1. **Test with valid JWT token**
2. **Test without Authorization header**
3. **Test with invalid/expired token**
4. **Verify RLS policies are enforced**
5. **Test CORS preflight requests**

## Template

Use the template at `app/supabase/functions/_template/index.ts` as a starting point for new edge functions.

## Security Benefits

Following these practices ensures:

- ✅ Row Level Security is enforced
- ✅ Users can only access their authorized data
- ✅ Consistent authentication across all functions
- ✅ Proper error handling and CORS support
- ✅ TypeScript support for better development experience
