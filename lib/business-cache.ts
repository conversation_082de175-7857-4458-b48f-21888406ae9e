/**
 * Business Cache Service
 * Implements server-side caching to reduce database connections for business data
 */

import { getActiveBusinessesDirect, searchBusinessesDirect } from '@/lib/database-direct';

interface BusinessCacheEntry {
  id: string;
  name: string;
  logo_url: string | null;
  website: string | null;
  category: string;
  premium_discount: string | null;
  is_active: boolean;
  business_spotlight: boolean;
  contact_name: string | null;
  contact_email: string | null;
  contact_phone: string | null;
  business_address: string | null;
  created_at: string;
  cachedAt: Date;
  expiresAt: Date;
}

interface BusinessCacheStats {
  totalEntries: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  lastRefresh: Date | null;
}

class BusinessCache {
  private cache = new Map<string, BusinessCacheEntry>();
  private allBusinessesCache: BusinessCacheEntry[] | null = null;
  private searchCache = new Map<string, BusinessCacheEntry[]>();
  private hitCount = 0;
  private missCount = 0;
  private lastRefresh: Date | null = null;
  private readonly cacheDuration = 10 * 60 * 1000; // 10 minutes
  private readonly searchCacheDuration = 5 * 60 * 1000; // 5 minutes
  private readonly maxCacheSize = 2000; // Maximum entries
  private readonly maxSearchCacheSize = 100; // Maximum search entries
  private cleanupInterval?: NodeJS.Timeout;
  private allBusinessesCacheExpiry: Date | null = null;

  constructor() {
    this.startCleanupInterval();
  }

  private startCleanupInterval() {
    // Clean up expired entries every 3 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 3 * 60 * 1000);
  }

  private cleanup() {
    const now = new Date();
    const expiredKeys: string[] = [];

    // Clean up individual business cache
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt < now) {
        expiredKeys.push(key);
      }
    }
    expiredKeys.forEach(key => this.cache.delete(key));

    // Clean up all businesses cache
    if (this.allBusinessesCacheExpiry && this.allBusinessesCacheExpiry < now) {
      this.allBusinessesCache = null;
      this.allBusinessesCacheExpiry = null;
    }

    // Clean up search cache
    const expiredSearchKeys: string[] = [];
    for (const [key, results] of this.searchCache.entries()) {
      if (results.length > 0 && results[0].expiresAt < now) {
        expiredSearchKeys.push(key);
      }
    }
    expiredSearchKeys.forEach(key => this.searchCache.delete(key));

    // Limit cache sizes
    if (this.cache.size > this.maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].cachedAt.getTime() - b[1].cachedAt.getTime());
      const toRemove = entries.slice(0, this.cache.size - this.maxCacheSize);
      toRemove.forEach(([key]) => this.cache.delete(key));
    }

    if (this.searchCache.size > this.maxSearchCacheSize) {
      const searchEntries = Array.from(this.searchCache.entries());
      const toRemove = searchEntries.slice(0, this.searchCache.size - this.maxSearchCacheSize);
      toRemove.forEach(([key]) => this.searchCache.delete(key));
    }

    if (expiredKeys.length > 0 || expiredSearchKeys.length > 0) {
      console.log(`🧹 Business Cache cleanup: removed ${expiredKeys.length} business entries, ${expiredSearchKeys.length} search entries`);
    }
  }

  private generateCacheKey(businessId: string): string {
    return `business_${businessId}`;
  }

  private generateSearchCacheKey(searchTerm: string, limit: number): string {
    return `search_${searchTerm || 'all'}_${limit}`;
  }

  private createCacheEntry(businessData: any): BusinessCacheEntry {
    const now = new Date();
    return {
      ...businessData,
      cachedAt: now,
      expiresAt: new Date(now.getTime() + this.cacheDuration)
    };
  }

  async getBusiness(businessId: string): Promise<BusinessCacheEntry | null> {
    const cacheKey = this.generateCacheKey(businessId);
    
    // Check cache first
    const cachedEntry = this.cache.get(cacheKey);
    if (cachedEntry && cachedEntry.expiresAt > new Date()) {
      this.hitCount++;
      console.log(`🎯 Business Cache hit for ID ${businessId}`);
      return cachedEntry;
    }

    // Cache miss - fetch from database
    this.missCount++;
    console.log(`📊 Business Cache miss for ID ${businessId}, fetching from database`);

    try {
      // Get from all businesses cache first, or fetch individual business
      const allBusinesses = await this.getAllBusinesses();
      const business = allBusinesses.find(b => b.id === businessId);
      
      if (business) {
        const entry = this.createCacheEntry(business);
        this.cache.set(cacheKey, entry);
        return entry;
      }

      return null;
    } catch (error) {
      console.error('Error fetching business from database:', error);
      return null;
    }
  }

  async getAllBusinesses(): Promise<BusinessCacheEntry[]> {
    const now = new Date();
    
    // Check if all businesses cache is valid
    if (this.allBusinessesCache && this.allBusinessesCacheExpiry && this.allBusinessesCacheExpiry > now) {
      this.hitCount++;
      console.log(`🎯 All Businesses Cache hit`);
      return this.allBusinessesCache;
    }

    // Cache miss - fetch from database
    this.missCount++;
    console.log(`📊 All Businesses Cache miss, fetching from database`);

    try {
      const businesses = await getActiveBusinessesDirect();
      
      if (businesses) {
        // Cache all businesses
        this.allBusinessesCache = businesses.map(business => this.createCacheEntry(business));
        this.allBusinessesCacheExpiry = new Date(now.getTime() + this.cacheDuration);
        this.lastRefresh = now;

        // Also cache individual businesses
        this.allBusinessesCache.forEach(business => {
          const cacheKey = this.generateCacheKey(business.id);
          this.cache.set(cacheKey, business);
        });

        console.log(`✅ Cached ${this.allBusinessesCache.length} businesses`);
        return this.allBusinessesCache;
      }

      return [];
    } catch (error) {
      console.error('Error fetching all businesses from database:', error);
      return [];
    }
  }

  async searchBusinesses(searchTerm?: string, limit: number = 100): Promise<BusinessCacheEntry[]> {
    const searchKey = this.generateSearchCacheKey(searchTerm || '', limit);
    
    // Check search cache first
    const cachedResults = this.searchCache.get(searchKey);
    if (cachedResults && cachedResults.length > 0 && cachedResults[0].expiresAt > new Date()) {
      this.hitCount++;
      console.log(`🎯 Business Search Cache hit for "${searchTerm}"`);
      return cachedResults;
    }

    // Cache miss - perform search
    this.missCount++;
    console.log(`📊 Business Search Cache miss for "${searchTerm}", searching database`);

    try {
      const businesses = await searchBusinessesDirect(searchTerm, limit);
      
      if (businesses) {
        const now = new Date();
        const cachedResults = businesses.map(business => ({
          ...business,
          cachedAt: now,
          expiresAt: new Date(now.getTime() + this.searchCacheDuration)
        }));

        // Cache search results
        this.searchCache.set(searchKey, cachedResults);

        // Also cache individual businesses
        cachedResults.forEach(business => {
          const cacheKey = this.generateCacheKey(business.id);
          this.cache.set(cacheKey, business);
        });

        console.log(`✅ Cached search results: ${cachedResults.length} businesses for "${searchTerm}"`);
        return cachedResults;
      }

      return [];
    } catch (error) {
      console.error('Error searching businesses in database:', error);
      return [];
    }
  }

  async getBusinessesBySpotlight(spotlightOnly: boolean = true): Promise<BusinessCacheEntry[]> {
    const allBusinesses = await this.getAllBusinesses();
    return allBusinesses.filter(business => 
      spotlightOnly ? business.business_spotlight : true
    );
  }

  async getBusinessesByCategory(category: string): Promise<BusinessCacheEntry[]> {
    const allBusinesses = await this.getAllBusinesses();
    return allBusinesses.filter(business => 
      business.category.toLowerCase().includes(category.toLowerCase())
    );
  }

  async preloadBusinesses(businessIds: string[]): Promise<void> {
    try {
      console.log(`🔄 Preloading businesses for ${businessIds.length} IDs`);
      
      // Filter out businesses already in cache
      const uncachedBusinessIds = businessIds.filter(businessId => {
        const cacheKey = this.generateCacheKey(businessId);
        const cachedEntry = this.cache.get(cacheKey);
        return !cachedEntry || cachedEntry.expiresAt <= new Date();
      });

      if (uncachedBusinessIds.length === 0) {
        console.log('✅ All businesses already cached');
        return;
      }

      // Get all businesses and filter for requested IDs
      const allBusinesses = await this.getAllBusinesses();
      const requestedBusinesses = allBusinesses.filter(business => 
        uncachedBusinessIds.includes(business.id)
      );

      console.log(`✅ Preloaded ${requestedBusinesses.length} businesses into cache`);
    } catch (error) {
      console.error('Error preloading businesses:', error);
    }
  }

  invalidateBusiness(businessId: string): void {
    const cacheKey = this.generateCacheKey(businessId);
    const deleted = this.cache.delete(cacheKey);
    
    // Also invalidate all businesses cache
    this.allBusinessesCache = null;
    this.allBusinessesCacheExpiry = null;
    
    // Clear search cache
    this.searchCache.clear();
    
    if (deleted) {
      console.log(`🗑️ Invalidated business cache for ID ${businessId}`);
    }
  }

  invalidateAll(): void {
    this.cache.clear();
    this.allBusinessesCache = null;
    this.allBusinessesCacheExpiry = null;
    this.searchCache.clear();
    console.log('🧹 All business cache invalidated');
  }

  async refresh(): Promise<void> {
    console.log('🔄 Refreshing all business cache...');
    this.invalidateAll();
    await this.getAllBusinesses();
    console.log('✅ Business cache refreshed');
  }

  clear(): void {
    this.cache.clear();
    this.allBusinessesCache = null;
    this.allBusinessesCacheExpiry = null;
    this.searchCache.clear();
    this.hitCount = 0;
    this.missCount = 0;
    this.lastRefresh = null;
    console.log('🧹 Business Cache cleared');
  }

  getStats(): BusinessCacheStats {
    const totalRequests = this.hitCount + this.missCount;
    return {
      totalEntries: this.cache.size,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0,
      lastRefresh: this.lastRefresh
    };
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Create singleton instance
const businessCache = new BusinessCache();

export default businessCache;
export { businessCache };
export type { BusinessCacheEntry, BusinessCacheStats };