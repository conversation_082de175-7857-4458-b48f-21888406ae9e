// Monitoring and logging utilities for security and performance tracking

interface LogEvent {
  timestamp: Date
  level: 'info' | 'warn' | 'error' | 'security'
  category: string
  message: string
  userId?: string
  ip?: string
  userAgent?: string
  metadata?: Record<string, any>
}

interface SecurityEvent extends LogEvent {
  level: 'security'
  eventType: 'auth_failure' | 'suspicious_activity' | 'rate_limit_exceeded' | 'unauthorized_access' | 'data_breach_attempt'
  severity: 'low' | 'medium' | 'high' | 'critical'
}

class Logger {
  private static instance: Logger
  private logs: LogEvent[] = []
  private maxLogs = 10000 // Keep last 10k logs in memory

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  private formatLog(event: LogEvent): string {
    return JSON.stringify({
      ...event,
      timestamp: event.timestamp.toISOString()
    })
  }

  private addLog(event: LogEvent) {
    this.logs.push(event)
    
    // Keep only the most recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs)
    }

    // Console output for development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${event.level.toUpperCase()}] ${event.category}: ${event.message}`)
    }

    // In production, send to external logging service
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalLogger(event)
    }
  }

  private async sendToExternalLogger(event: LogEvent) {
    // TODO: Implement integration with external logging service
    // Examples: Datadog, LogRocket, Sentry, etc.
    try {
      // Example: Send to webhook endpoint
      if (process.env.LOGGING_WEBHOOK_URL) {
        await fetch(process.env.LOGGING_WEBHOOK_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: this.formatLog(event)
        })
      }
    } catch (error) {
      console.error('Failed to send log to external service:', error)
    }
  }

  info(category: string, message: string, metadata?: Record<string, any>) {
    this.addLog({
      timestamp: new Date(),
      level: 'info',
      category,
      message,
      metadata
    })
  }

  warn(category: string, message: string, metadata?: Record<string, any>) {
    this.addLog({
      timestamp: new Date(),
      level: 'warn',
      category,
      message,
      metadata
    })
  }

  error(category: string, message: string, metadata?: Record<string, any>) {
    this.addLog({
      timestamp: new Date(),
      level: 'error',
      category,
      message,
      metadata
    })
  }

  security(event: Omit<SecurityEvent, 'timestamp' | 'level'>) {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
      level: 'security'
    }
    
    this.addLog(securityEvent)
    
    // Send immediate alerts for critical security events
    if (event.severity === 'critical') {
      this.sendSecurityAlert(securityEvent)
    }
  }

  private async sendSecurityAlert(event: SecurityEvent) {
    // TODO: Implement immediate alerting for critical security events
    // Examples: Email, Slack, SMS, etc.
    console.error('CRITICAL SECURITY EVENT:', event)
    
    try {
      if (process.env.SECURITY_ALERT_WEBHOOK) {
        await fetch(process.env.SECURITY_ALERT_WEBHOOK, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: `🚨 CRITICAL SECURITY EVENT: ${event.eventType}\n${event.message}`,
            event
          })
        })
      }
    } catch (error) {
      console.error('Failed to send security alert:', error)
    }
  }

  getLogs(filters?: {
    level?: LogEvent['level']
    category?: string
    since?: Date
    limit?: number
  }): LogEvent[] {
    let filteredLogs = [...this.logs]

    if (filters?.level) {
      filteredLogs = filteredLogs.filter(log => log.level === filters.level)
    }

    if (filters?.category) {
      filteredLogs = filteredLogs.filter(log => log.category === filters.category)
    }

    if (filters?.since) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= filters.since!)
    }

    if (filters?.limit) {
      filteredLogs = filteredLogs.slice(-filters.limit)
    }

    return filteredLogs
  }
}

// Singleton logger instance
export const logger = Logger.getInstance()

// Security monitoring functions
export const securityMonitor = {
  logAuthFailure: (ip: string, userAgent: string, email?: string) => {
    logger.security({
      category: 'authentication',
      eventType: 'auth_failure',
      severity: 'medium',
      message: `Authentication failure from IP: ${ip}`,
      ip,
      userAgent,
      metadata: { email }
    })
  },

  logSuspiciousActivity: (userId: string, activity: string, ip: string, severity: SecurityEvent['severity'] = 'medium') => {
    logger.security({
      category: 'suspicious_activity',
      eventType: 'suspicious_activity',
      severity,
      message: `Suspicious activity detected: ${activity}`,
      userId,
      ip,
      metadata: { activity }
    })
  },

  logRateLimitExceeded: (ip: string, endpoint: string) => {
    logger.security({
      category: 'rate_limiting',
      eventType: 'rate_limit_exceeded',
      severity: 'low',
      message: `Rate limit exceeded for endpoint: ${endpoint}`,
      ip,
      metadata: { endpoint }
    })
  },

  logUnauthorizedAccess: (userId: string | null, resource: string, ip: string) => {
    logger.security({
      category: 'authorization',
      eventType: 'unauthorized_access',
      severity: 'high',
      message: `Unauthorized access attempt to: ${resource}`,
      userId: userId || undefined,
      ip,
      metadata: { resource }
    })
  },

  logDataBreachAttempt: (userId: string | null, attemptedAction: string, ip: string) => {
    logger.security({
      category: 'data_security',
      eventType: 'data_breach_attempt',
      severity: 'critical',
      message: `Potential data breach attempt: ${attemptedAction}`,
      userId: userId || undefined,
      ip,
      metadata: { attemptedAction }
    })
  }
}

// Performance monitoring - enhanced with new performance monitor
import { performanceMonitor as perfMon } from './performance-monitor'

export const performanceMonitor = {
  logSlowQuery: (query: string, duration: number, userId?: string) => {
    logger.warn('performance', `Slow database query detected (${duration}ms)`, {
      query: query.substring(0, 200), // Truncate long queries
      duration,
      userId
    })

    // Also track in the new performance monitor
    const table = query.match(/FROM\s+(\w+)/i)?.[1] || 'unknown'
    const operation = query.trim().split(' ')[0].toUpperCase() as any
    perfMon.trackDatabaseQuery(query, table, operation, duration)
  },

  logApiResponse: (endpoint: string, method: string, duration: number, status: number, userId?: string) => {
    const level = status >= 500 ? 'error' : status >= 400 ? 'warn' : 'info'
    logger[level]('api_performance', `${method} ${endpoint} - ${status} (${duration}ms)`, {
      endpoint,
      method,
      duration,
      status,
      userId
    })

    // Also track in the new performance monitor
    perfMon.trackAPICall(endpoint, method, duration, status, userId)
  }
}

// Business metrics monitoring
export const businessMonitor = {
  logUserRegistration: (userId: string, method: string) => {
    logger.info('business_metrics', 'New user registration', {
      userId,
      registrationMethod: method
    })
  },

  logBusinessRegistration: (businessId: string, userId: string) => {
    logger.info('business_metrics', 'New business registration', {
      businessId,
      userId
    })
  },

  logPurchase: (purchaseId: string, userId: string, amount: number, method: string) => {
    logger.info('business_metrics', 'Purchase completed', {
      purchaseId,
      userId,
      amount,
      paymentMethod: method
    })
  },

  logQRScan: (scannerId: string, scannedId: string, type: string) => {
    logger.info('business_metrics', 'QR code scanned', {
      scannerId,
      scannedId,
      scanType: type
    })
  }
}

// Enhanced Chat Bot Monitoring
export interface ChatMetrics {
  totalConversations: number;
  complexQueriesHandled: number;
  escalationRequests: number;
  tokenUsage: { total: number; average: number; peak: number };
  intents: Record<string, number>;
}

export class ChatMonitoring {
  private static instance: ChatMonitoring;
  private metrics: ChatMetrics = {
    totalConversations: 0,
    complexQueriesHandled: 0,
    escalationRequests: 0,
    tokenUsage: { total: 0, average: 0, peak: 0 },
    intents: {}
  };

  static getInstance(): ChatMonitoring {
    if (!ChatMonitoring.instance) {
      ChatMonitoring.instance = new ChatMonitoring();
    }
    return ChatMonitoring.instance;
  }

  trackConversation(sessionId: string, intent: string, tokenUsage: number, isComplex: boolean = false): void {
    this.metrics.totalConversations++;
    this.metrics.intents[intent] = (this.metrics.intents[intent] || 0) + 1;
    this.metrics.tokenUsage.total += tokenUsage;
    this.metrics.tokenUsage.average = this.metrics.tokenUsage.total / this.metrics.totalConversations;
    this.metrics.tokenUsage.peak = Math.max(this.metrics.tokenUsage.peak, tokenUsage);
    
    if (isComplex) {
      this.metrics.complexQueriesHandled++;
    }
  }

  trackEscalation(sessionId: string, userId: string | undefined, message: string, intent: string): void {
    this.metrics.escalationRequests++;
    
    logger.info('chat_escalation', 'Support escalation requested', {
      sessionId,
      userId,
      intent,
      message: message.substring(0, 200),
      timestamp: new Date().toISOString()
    });
  }

  getMetrics(): ChatMetrics {
    return { ...this.metrics };
  }
}

export const chatMonitor = ChatMonitoring.getInstance();
