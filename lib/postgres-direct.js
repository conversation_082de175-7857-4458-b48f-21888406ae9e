import postgres from 'postgres'
import { config } from 'dotenv'

// Load environment variables
config({ path: '.env.local' })

// Direct PostgreSQL connection using postgres library
// Connection parameters from Supabase project settings
const connectionString = process.env.DATABASE_URL

if (!connectionString) {
  throw new Error('DATABASE_URL environment variable is required')
}

const sql = postgres(connectionString, {
  host: 'db.haqbtbpmyadkocakqnew.supabase.co',
  port: 5432,
  database: 'postgres',
  username: 'postgres',
  password: process.env.SUPABASE_DB_PASSWORD,
  ssl: 'require', // Supabase requires SSL
  max: 10, // Maximum number of connections
  idle_timeout: 20, // Close connections after 20 seconds of inactivity
  connect_timeout: 10, // Connection timeout in seconds
  transform: {
    undefined: null // Transform undefined values to null
  }
})

export default sql
