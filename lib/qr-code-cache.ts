/**
 * QR Code Cache Service
 * Implements caching to reduce database connections for QR code data
 */

import { getActiveUserQRCodesDirect } from '@/lib/database-direct';

interface QRCodeCacheEntry {
  userId: string;
  qrData: string;
  qrCodeUrl: string;
  isActive: boolean;
  createdAt: Date;
  expiresAt: Date;
}

interface QRCodeCacheStats {
  totalEntries: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
}

class QRCodeCache {
  private cache = new Map<string, QRCodeCacheEntry>();
  private hitCount = 0;
  private missCount = 0;
  private readonly cacheDuration = 60 * 60 * 1000; // 1 hour
  private readonly maxCacheSize = 1000; // Maximum entries
  private cleanupInterval?: NodeJS.Timeout;

  constructor() {
    // Start cleanup interval
    this.startCleanupInterval();
  }

  private startCleanupInterval() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup() {
    const now = new Date();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt < now) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));

    // If cache is still too large, remove oldest entries
    if (this.cache.size > this.maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].createdAt.getTime() - b[1].createdAt.getTime());
      
      const toRemove = entries.slice(0, this.cache.size - this.maxCacheSize);
      toRemove.forEach(([key]) => this.cache.delete(key));
    }

    if (expiredKeys.length > 0) {
      console.log(`🧹 QR Cache cleanup: removed ${expiredKeys.length} expired entries`);
    }
  }

  private generateCacheKey(userId: string): string {
    return `qr_code_${userId}`;
  }

  async getQRCode(userId: string): Promise<QRCodeCacheEntry | null> {
    const cacheKey = this.generateCacheKey(userId);
    
    // Check cache first
    const cachedEntry = this.cache.get(cacheKey);
    if (cachedEntry && cachedEntry.expiresAt > new Date()) {
      this.hitCount++;
      console.log(`🎯 QR Cache hit for user ${userId}`);
      return cachedEntry;
    }

    // Cache miss - fetch from database
    this.missCount++;
    console.log(`📊 QR Cache miss for user ${userId}, fetching from database`);

    try {
      const qrCodes = await getActiveUserQRCodesDirect([userId]);
      
      if (qrCodes.length === 0) {
        return null;
      }

      const qrCode = qrCodes[0];
      const now = new Date();
      const entry: QRCodeCacheEntry = {
        userId: qrCode.user_id,
        qrData: qrCode.qr_data,
        qrCodeUrl: qrCode.qr_code_url,
        isActive: qrCode.is_active,
        createdAt: now,
        expiresAt: new Date(now.getTime() + this.cacheDuration)
      };

      // Store in cache
      this.cache.set(cacheKey, entry);
      
      return entry;
    } catch (error) {
      console.error('Error fetching QR code from database:', error);
      return null;
    }
  }

  async preloadQRCodes(userIds: string[]): Promise<void> {
    try {
      console.log(`🔄 Preloading QR codes for ${userIds.length} users`);
      
      // Filter out users already in cache
      const uncachedUserIds = userIds.filter(userId => {
        const cacheKey = this.generateCacheKey(userId);
        const cachedEntry = this.cache.get(cacheKey);
        return !cachedEntry || cachedEntry.expiresAt <= new Date();
      });

      if (uncachedUserIds.length === 0) {
        console.log('✅ All QR codes already cached');
        return;
      }

      // Fetch uncached QR codes
      const qrCodes = await getActiveUserQRCodesDirect(uncachedUserIds);
      const now = new Date();

      // Cache the results
      qrCodes.forEach(qrCode => {
        const cacheKey = this.generateCacheKey(qrCode.user_id);
        const entry: QRCodeCacheEntry = {
          userId: qrCode.user_id,
          qrData: qrCode.qr_data,
          qrCodeUrl: qrCode.qr_code_url,
          isActive: qrCode.is_active,
          createdAt: now,
          expiresAt: new Date(now.getTime() + this.cacheDuration)
        };

        this.cache.set(cacheKey, entry);
      });

      console.log(`✅ Preloaded ${qrCodes.length} QR codes into cache`);
    } catch (error) {
      console.error('Error preloading QR codes:', error);
    }
  }

  invalidateUser(userId: string): void {
    const cacheKey = this.generateCacheKey(userId);
    const deleted = this.cache.delete(cacheKey);
    
    if (deleted) {
      console.log(`🗑️ Invalidated QR cache for user ${userId}`);
    }
  }

  clear(): void {
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
    console.log('🧹 QR Cache cleared');
  }

  getStats(): QRCodeCacheStats {
    const totalRequests = this.hitCount + this.missCount;
    return {
      totalEntries: this.cache.size,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0
    };
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Create singleton instance
const qrCodeCache = new QRCodeCache();

export default qrCodeCache;
export { qrCodeCache };
export type { QRCodeCacheEntry, QRCodeCacheStats };