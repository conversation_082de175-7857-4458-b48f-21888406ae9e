'use client';

import { useAuth } from '@/contexts/auth-context';

// Enhanced payload service for Xaman integration with user token support
export interface XamanPayloadOptions {
  userToken?: string;
  returnUrl?: string;
  memo?: string;
  forceNetwork?: 'MAINNET' | 'TESTNET';
}

export interface XamanPayloadResponse {
  uuid: string;
  next?: {
    always?: string;
    no_push_msg_received?: string;
  };
  refs?: {
    qr_png?: string;
    qr_matrix?: string;
    qr_uri_quality_opts?: string[];
    websocket_status?: string;
  };
  pushed?: boolean;
}

export class XamanPayloadService {
  private static instance: XamanPayloadService;
  
  public static getInstance(): XamanPayloadService {
    if (!XamanPayloadService.instance) {
      XamanPayloadService.instance = new XamanPayloadService();
    }
    return XamanPayloadService.instance;
  }

  // Get user token from database for current user
  async getUserToken(userId: string): Promise<string | null> {
    try {
      const response = await fetch(`/api/wallet-connection?userId=${userId}`);
      if (!response.ok) return null;
      
      const data = await response.json();
      return data.data?.hasValidUserToken ? data.data.userToken : null;
    } catch (error) {
      console.error('Failed to get user token:', error);
      return null;
    }
  }

  // Create enhanced SignIn payload with user token support
  async createSignInPayload(userId?: string, options: XamanPayloadOptions = {}): Promise<any> {
    const basePayload = {
      TransactionType: 'SignIn',
      SignIn: true
    };

    // If user has a valid token, include it for push notifications
    if (userId && !options.userToken) {
      options.userToken = await this.getUserToken(userId);
    }

    // Enhanced payload with user token
    const enhancedPayload: any = {
      txjson: basePayload,
      options: {
        force_network: options.forceNetwork || 'MAINNET',
        return_url: options.returnUrl ? {
          app: options.returnUrl,
          web: options.returnUrl
        } : undefined
      },
      custom_meta: {
        identifier: `signin_${Date.now()}`,
        instruction: 'Sign in to Fuse.vip with your Xaman wallet',
        blob: {
          appName: '$Fuse Rewards',
          appIcon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png'
        }
      }
    };

    // Add user token if available for push notifications
    if (options.userToken) {
      enhancedPayload.user_token = options.userToken;
      console.log('🔔 Including user token for push notification delivery');
    }

    return enhancedPayload;
  }

  // Create enhanced Payment payload with user token support
  async createPaymentPayload(
    destination: string, 
    amount: string, 
    currency: string = 'XRP',
    userId?: string,
    options: XamanPayloadOptions = {}
  ): Promise<any> {
    // Get user token if not provided
    if (userId && !options.userToken) {
      options.userToken = await this.getUserToken(userId);
    }

    const paymentPayload: any = {
      txjson: {
        TransactionType: 'Payment',
        Destination: destination,
        Amount: currency === 'XRP' ? amount : {
          currency: currency,
          issuer: 'rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU', // Treasury wallet
          value: amount
        }
      },
      options: {
        force_network: options.forceNetwork || 'MAINNET',
        return_url: options.returnUrl ? {
          app: options.returnUrl,
          web: options.returnUrl
        } : undefined
      },
      custom_meta: {
        identifier: `payment_${Date.now()}`,
        instruction: `Send ${amount} ${currency} to ${destination}`,
        blob: {
          appName: '$Fuse Rewards',
          appIcon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png'
        }
      }
    };

    // Add memo if provided
    if (options.memo) {
      paymentPayload.txjson.Memos = [{
        Memo: {
          MemoType: Buffer.from('memo').toString('hex').toUpperCase(),
          MemoData: Buffer.from(options.memo).toString('hex').toUpperCase()
        }
      }];
    }

    // Add user token if available for push notifications
    if (options.userToken) {
      paymentPayload.user_token = options.userToken;
      console.log('🔔 Including user token for push notification delivery');
    }

    return paymentPayload;
  }

  // Create enhanced TrustSet payload with user token support
  async createTrustlinePayload(
    currency: string = 'FUSE',
    issuer: string = 'rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU',
    limit: string = '1000000000',
    userId?: string,
    options: XamanPayloadOptions = {}
  ): Promise<any> {
    // Get user token if not provided
    if (userId && !options.userToken) {
      options.userToken = await this.getUserToken(userId);
    }

    const trustlinePayload: any = {
      txjson: {
        TransactionType: 'TrustSet',
        LimitAmount: {
          currency: currency,
          issuer: issuer,
          value: limit
        }
      },
      options: {
        force_network: options.forceNetwork || 'MAINNET',
        return_url: options.returnUrl ? {
          app: options.returnUrl,
          web: options.returnUrl
        } : undefined
      },
      custom_meta: {
        identifier: `trustline_${Date.now()}`,
        instruction: `Create trustline for ${currency} token`,
        blob: {
          appName: '$Fuse Rewards',
          appIcon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png'
        }
      }
    };

    // Add user token if available for push notifications
    if (options.userToken) {
      trustlinePayload.user_token = options.userToken;
      console.log('🔔 Including user token for push notification delivery');
    }

    return trustlinePayload;
  }

  // Submit payload to Xaman SDK and handle user token updates
  async submitPayload(
    sdk: any, 
    payload: any, 
    userId?: string
  ): Promise<XamanPayloadResponse> {
    try {
      console.log('📤 Submitting payload to Xaman:', {
        hasUserToken: !!payload.user_token,
        transactionType: payload.txjson?.TransactionType
      });

      const response = await sdk.payload.create(payload);
      
      console.log('📥 Xaman payload response:', {
        uuid: response.uuid,
        pushed: response.pushed,
        hasDeeplink: !!response.next?.always
      });

      // If payload was pushed via user token, log success
      if (response.pushed && payload.user_token) {
        console.log('🔔 Payload successfully pushed to user via user token');
      }

      return response;
    } catch (error) {
      console.error('❌ Failed to submit payload to Xaman:', error);
      throw error;
    }
  }

  // Handle payload resolution and user token updates
  async handlePayloadResolution(
    payloadUuid: string,
    userId?: string,
    userToken?: string
  ): Promise<void> {
    try {
      // If we got a user token from the resolution, store it
      if (userId && userToken) {
        await this.updateUserToken(userId, userToken);
      }

      // Extend token expiration for successful sign
      if (userId) {
        await this.extendUserTokenExpiration(userId);
      }
    } catch (error) {
      console.error('Failed to handle payload resolution:', error);
    }
  }

  // Update user token in database
  private async updateUserToken(userId: string, userToken: string): Promise<void> {
    try {
      const response = await fetch('/api/wallet-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          userToken,
          action: 'update_token'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update user token');
      }

      console.log('✅ User token updated successfully');
    } catch (error) {
      console.error('Failed to update user token:', error);
      throw error;
    }
  }

  // Extend user token expiration after successful sign
  private async extendUserTokenExpiration(userId: string): Promise<void> {
    try {
      // This could be a separate API endpoint or handled in the existing one
      console.log('🔄 Extending user token expiration for user:', userId);
      // Implementation would call the database function to extend expiration
    } catch (error) {
      console.error('Failed to extend user token expiration:', error);
    }
  }
}

// Hook for using the payload service
export function useXamanPayloadService() {
  const { user } = useAuth();
  const payloadService = XamanPayloadService.getInstance();

  const createSignInPayload = async (options?: XamanPayloadOptions) => {
    return payloadService.createSignInPayload(user?.id, options);
  };

  const createPaymentPayload = async (
    destination: string,
    amount: string,
    currency?: string,
    options?: XamanPayloadOptions
  ) => {
    return payloadService.createPaymentPayload(destination, amount, currency, user?.id, options);
  };

  const createTrustlinePayload = async (
    currency?: string,
    issuer?: string,
    limit?: string,
    options?: XamanPayloadOptions
  ) => {
    return payloadService.createTrustlinePayload(currency, issuer, limit, user?.id, options);
  };

  const submitPayload = async (sdk: any, payload: any) => {
    return payloadService.submitPayload(sdk, payload, user?.id);
  };

  const handlePayloadResolution = async (payloadUuid: string, userToken?: string) => {
    return payloadService.handlePayloadResolution(payloadUuid, user?.id, userToken);
  };

  return {
    createSignInPayload,
    createPaymentPayload,
    createTrustlinePayload,
    submitPayload,
    handlePayloadResolution
  };
}
