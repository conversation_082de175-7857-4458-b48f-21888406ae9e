/**
 * QR Code generation utilities for user-specific QR codes
 */

import QRCode from 'qrcode';

export interface UserQRData {
  userId: string;
  type: 'user' | 'business';
  timestamp: number;
}

export interface QRCodeOptions {
  size?: number;
  margin?: number;
  color?: {
    dark?: string;
    light?: string;
  };
}

/**
 * Generate QR code data string for a user
 */
export function generateUserQRData(userId: string): string {
  const qrData: UserQRData = {
    userId,
    type: 'user',
    timestamp: Date.now()
  };
  
  return JSON.stringify(qrData);
}

/**
 * Generate QR code data string for a business
 */
export function generateBusinessQRData(businessId: string): string {
  const qrData: UserQRData = {
    userId: businessId,
    type: 'business',
    timestamp: Date.now()
  };
  
  return JSON.stringify(qrData);
}

/**
 * Parse QR code data string
 */
export function parseQRData(qrString: string): UserQRData | null {
  try {
    // Try to parse as JSON first (new format)
    const parsed = JSON.parse(qrString);
    if (parsed.userId && parsed.type) {
      return parsed;
    }
  } catch {
    // If JSON parsing fails, treat as legacy business ID format
    if (qrString.length >= 36) { // UUID length check
      return {
        userId: qrString.trim(),
        type: 'business',
        timestamp: 0
      };
    }
  }
  
  return null;
}

/**
 * Generate QR code as data URL
 */
export async function generateQRCodeDataURL(
  data: string, 
  options: QRCodeOptions = {}
): Promise<string> {
  const defaultOptions = {
    width: options.size || 256,
    margin: options.margin || 2,
    color: {
      dark: options.color?.dark || '#000000',
      light: options.color?.light || '#FFFFFF'
    }
  };

  try {
    return await QRCode.toDataURL(data, defaultOptions);
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
}

/**
 * Generate QR code as SVG string
 */
export async function generateQRCodeSVG(
  data: string, 
  options: QRCodeOptions = {}
): Promise<string> {
  const defaultOptions = {
    width: options.size || 256,
    margin: options.margin || 2,
    color: {
      dark: options.color?.dark || '#000000',
      light: options.color?.light || '#FFFFFF'
    }
  };

  try {
    return await QRCode.toString(data, { 
      type: 'svg',
      ...defaultOptions 
    });
  } catch (error) {
    console.error('Error generating QR code SVG:', error);
    throw new Error('Failed to generate QR code SVG');
  }
}

/**
 * Validate if a string is a valid QR code data
 */
export function isValidQRData(qrString: string): boolean {
  const parsed = parseQRData(qrString);
  return parsed !== null && parsed.userId.length >= 36;
}

/**
 * Generate a unique QR code identifier for a user
 * This creates a more complex identifier that includes user ID and additional data
 */
export function generateUniqueUserQRId(userId: string, userEmail?: string): string {
  const baseData = {
    id: userId,
    type: 'user',
    platform: 'fuse.vip',
    timestamp: Date.now()
  };
  
  if (userEmail) {
    // Add email hash for additional uniqueness (first 8 chars)
    const emailHash = btoa(userEmail).substring(0, 8);
    return JSON.stringify({ ...baseData, hash: emailHash });
  }
  
  return JSON.stringify(baseData);
}
