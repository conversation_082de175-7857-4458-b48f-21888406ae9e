import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { createClient } from '@/lib/supabase/server'

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

interface RateLimitOptions {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Max requests per window
}

interface SecurityOptions {
  requireAuth?: boolean
  requireRole?: string
  rateLimit?: RateLimitOptions
  validateInput?: (body: any) => { isValid: boolean; errors?: string[] }
  logRequest?: boolean
}

/**
 * Rate limiting middleware
 */
export function rateLimit(ip: string, options: RateLimitOptions): boolean {
  const now = Date.now()
  const key = `rate_limit:${ip}`
  const limit = rateLimitStore.get(key)

  if (!limit || now > limit.resetTime) {
    // Reset or create new limit
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + options.windowMs
    })
    return true
  }

  if (limit.count >= options.maxRequests) {
    return false // Rate limit exceeded
  }

  // Increment count
  limit.count++
  rateLimitStore.set(key, limit)
  return true
}

/**
 * Input validation helpers
 */
export const validators = {
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },
  
  uuid: (uuid: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(uuid)
  },
  
  sanitizeString: (str: string): string => {
    return str.replace(/[<>\"'&]/g, (match) => {
      const escapeMap: { [key: string]: string } = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '&': '&amp;'
      }
      return escapeMap[match]
    })
  },
  
  isValidBusinessName: (name: string): boolean => {
    return name.length >= 2 && name.length <= 100 && /^[a-zA-Z0-9\s\-&.,']+$/.test(name)
  },
  
  isValidPhoneNumber: (phone: string): boolean => {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,15}$/
    return phoneRegex.test(phone)
  }
}

/**
 * Security middleware for API routes
 */
export async function withSecurity(
  request: NextRequest,
  handler: (req: NextRequest, context: { user?: any; supabase: any; body?: any }) => Promise<NextResponse>,
  options: SecurityOptions = {}
): Promise<NextResponse> {
  const startTime = Date.now()
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  const userAgent = request.headers.get('user-agent') || 'unknown'
  const method = request.method
  const url = request.url

  try {
    // Rate limiting
    if (options.rateLimit) {
      if (!rateLimit(ip, options.rateLimit)) {
        if (options.logRequest) {
          console.warn(`Rate limit exceeded for IP: ${ip}, URL: ${url}`)
        }
        return NextResponse.json(
          { error: 'Too many requests. Please try again later.' },
          { status: 429 }
        )
      }
    }

    // Authentication check
    let user = null
    let supabase = null

    // Always create a Supabase client for the handler to use
    // Use the same createClient function that properly handles cookies
    if (!supabase) {
      try {
        supabase = await createClient()
      } catch (error) {
        console.error('Failed to create Supabase client:', error)
        return NextResponse.json(
          { error: 'Internal server error - client creation failed' },
          { status: 500 }
        )
      }
    }

    if (options.requireAuth || options.requireRole) {
      const { data: { user: authUser }, error } = await supabase.auth.getUser()

      // Enhanced debugging for authentication issues
      if (options.logRequest) {
        const cookieHeader = request.headers.get('cookie');
        console.log(`🔍 Auth check for ${url}:`, {
          hasAuthUser: !!authUser,
          authUserId: authUser?.id,
          authError: error?.message,
          hasCookies: !!cookieHeader,
          cookieCount: cookieHeader ? cookieHeader.split(';').length : 0,
          cookiePreview: cookieHeader?.substring(0, 100) + '...' // Log first 100 chars of cookies
        })
      }

      if (error || !authUser) {
        if (options.logRequest) {
          console.warn(`❌ Unauthorized access attempt from IP: ${ip}, URL: ${url}`, {
            error: error?.message,
            hasAuthUser: !!authUser,
            errorCode: error?.status,
            errorName: error?.name
          })
        }
        return NextResponse.json(
          {
            error: 'Authentication required',
            details: error?.message,
            code: error?.status || 401
          },
          { status: 401 }
        )
      }

      user = authUser
      
      // Role check
      if (options.requireRole) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single()
        
        if (!profile || profile.role !== options.requireRole) {
          if (options.logRequest) {
            console.warn(`Insufficient permissions for user: ${user.id}, required role: ${options.requireRole}`)
          }
          return NextResponse.json(
            { error: 'Insufficient permissions' },
            { status: 403 }
          )
        }
      }
    }

    // Input validation
    let parsedBody = null
    if (options.validateInput && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      try {
        parsedBody = await request.json()
        const validation = options.validateInput(parsedBody)

        if (!validation.isValid) {
          if (options.logRequest) {
            console.warn(`Invalid input from IP: ${ip}, errors: ${validation.errors?.join(', ')}`)
          }
          return NextResponse.json(
            { error: 'Invalid input', details: validation.errors },
            { status: 400 }
          )
        }
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid JSON' },
          { status: 400 }
        )
      }
    }

    // Execute the handler
    const response = await handler(request, { user, supabase, body: parsedBody })

    // Request logging
    if (options.logRequest) {
      const duration = Date.now() - startTime
      console.log(`API Request: ${method} ${url} - IP: ${ip} - User: ${user?.id || 'anonymous'} - Duration: ${duration}ms - Status: ${response.status}`)
    }

    return response

  } catch (error) {
    console.error('API Security Error:', error)
    
    if (options.logRequest) {
      console.error(`API Error: ${method} ${url} - IP: ${ip} - Error: ${error}`)
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Webhook signature verification
 */
export function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const crypto = require('crypto')
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex')
    
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    )
  } catch (error) {
    console.error('Webhook signature verification error:', error)
    return false
  }
}

/**
 * Common validation schemas
 */
export const validationSchemas = {
  businessRegistration: (data: any) => {
    const errors: string[] = []
    
    if (!data.name || !validators.isValidBusinessName(data.name)) {
      errors.push('Invalid business name')
    }
    
    if (data.email && !validators.email(data.email)) {
      errors.push('Invalid email address')
    }
    
    if (data.phone && !validators.isValidPhoneNumber(data.phone)) {
      errors.push('Invalid phone number')
    }
    
    if (!data.category || data.category.length < 2) {
      errors.push('Business category is required')
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  },
  
  userProfile: (data: any) => {
    const errors: string[] = []
    
    if (data.email && !validators.email(data.email)) {
      errors.push('Invalid email address')
    }
    
    if (data.phone && !validators.isValidPhoneNumber(data.phone)) {
      errors.push('Invalid phone number')
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  }
}
