/**
 * Wallet Service for managing multiple XRP/FUSE wallet addresses per user
 */

import { getSupabaseClient } from '@/lib/supabase';
import { profileCache } from '@/lib/profile-cache';

export interface UserWallet {
  id: string;
  user_id: string;
  wallet_address: string;
  wallet_type: 'xrp' | 'fuse';
  is_primary: boolean;
  nickname?: string;
  connected_at: string;
  last_used: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface WalletConnectionResult {
  success: boolean;
  error?: string;
  wallet?: UserWallet;
}

export interface WalletDisconnectionResult {
  success: boolean;
  error?: string;
}

export class WalletService {
  private supabase;

  constructor() {
    this.supabase = getSupabaseClient();
  }

  /**
   * Get all wallets for a user
   * Note: Currently using profiles.xrp_wallet_address as the only wallet
   */
  async getUserWallets(userId: string, walletType?: 'xrp' | 'fuse'): Promise<UserWallet[]> {
    if (!this.supabase) throw new Error('Supabase client not available');

    // For now, only support XRP wallets from profiles table
    if (walletType && walletType !== 'xrp') {
      return [];
    }

    const { data, error } = await this.supabase
      .from('profiles')
      .select('id, xrp_wallet_address, wallet_connected_at, wallet_last_used')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user wallets:', error);
      return [];
    }

    if (!data?.xrp_wallet_address) {
      return [];
    }

    // Convert profiles data to UserWallet format
    const wallet: UserWallet = {
      id: `${userId}-xrp-primary`,
      user_id: userId,
      wallet_address: data.xrp_wallet_address as string,
      wallet_type: 'xrp' as const,
      is_primary: true,
      nickname: 'Primary XRP Wallet',
      connected_at: (data.wallet_connected_at as string) || new Date().toISOString(),
      last_used: (data.wallet_last_used as string) || new Date().toISOString(),
      is_active: true,
      created_at: (data.wallet_connected_at as string) || new Date().toISOString(),
      updated_at: (data.wallet_last_used as string) || new Date().toISOString()
    };

    return [wallet];
  }

  /**
   * Connect a wallet address to a user's profile
   * This is the centralized method for all wallet connections
   * Uses service role API to bypass RLS policies
   */
  async connectWallet(userId: string, walletAddress: string, walletType: 'xrp' | 'fuse' = 'xrp'): Promise<WalletConnectionResult> {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    if (!walletAddress) {
      return { success: false, error: 'Wallet address is required' };
    }

    console.log('💾 [WalletService] Connecting wallet via API (bypasses RLS):', {
      userId,
      walletAddress: `${walletAddress.substring(0, 6)}...${walletAddress.substring(walletAddress.length - 4)}`,
      walletType
    });

    try {
      // Use the API endpoint that bypasses RLS with service role permissions
      const response = await fetch('/api/wallet-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          walletAddress,
          walletType,
          action: 'connect'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ [WalletService] API request failed:', errorData);
        return { success: false, error: errorData.error || `HTTP ${response.status}: Failed to connect wallet` };
      }

      const result = await response.json();
      
      if (!result.success) {
        console.error('❌ [WalletService] API returned error:', result.error);
        return { success: false, error: result.error || 'API request failed' };
      }

      // Update cache
      profileCache.updateWalletInCache(userId, walletAddress);

      const now = new Date().toISOString();
      const wallet: UserWallet = {
        id: `${userId}-xrp-primary`,
        user_id: userId,
        wallet_address: walletAddress,
        wallet_type: walletType as 'xrp' | 'fuse',
        is_primary: true,
        nickname: 'Primary XRP Wallet',
        connected_at: now,
        last_used: now,
        is_active: true,
        created_at: now,
        updated_at: now
      };

      console.log('✅ [WalletService] Wallet connected successfully via API');
      return { success: true, wallet };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ [WalletService] Exception connecting wallet via API:', error);
      return { success: false, error: `Network error: ${errorMessage}` };
    }
  }

  /**
   * Disconnect a wallet from a user's profile
   * Uses service role API to bypass RLS policies
   */
  async disconnectWallet(userId: string, walletType: 'xrp' | 'fuse' = 'xrp'): Promise<WalletDisconnectionResult> {
    if (!userId) {
      return { success: false, error: 'User ID is required' };
    }

    console.log('🔌 [WalletService] Disconnecting wallet via API (bypasses RLS):', { userId, walletType });

    try {
      // Use the API endpoint that bypasses RLS with service role permissions
      const response = await fetch('/api/wallet-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          walletType,
          action: 'disconnect'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ [WalletService] API request failed:', errorData);
        return { success: false, error: errorData.error || `HTTP ${response.status}: Failed to disconnect wallet` };
      }

      const result = await response.json();
      
      if (!result.success) {
        console.error('❌ [WalletService] API returned error:', result.error);
        return { success: false, error: result.error || 'API request failed' };
      }

      // Update cache
      profileCache.updateWalletInCache(userId, null);

      console.log('✅ [WalletService] Wallet disconnected successfully via API');
      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ [WalletService] Exception disconnecting wallet via API:', error);
      return { success: false, error: `Network error: ${errorMessage}` };
    }
  }

  /**
   * Get primary wallet for a user
   * Note: Currently using profiles.xrp_wallet_address as the primary wallet
   */
  async getPrimaryWallet(userId: string, walletType: 'xrp' | 'fuse' = 'xrp'): Promise<UserWallet | null> {
    if (!this.supabase) throw new Error('Supabase client not available');

    // For now, only support XRP wallets from profiles table
    if (walletType !== 'xrp') {
      return null;
    }

    const { data, error } = await this.supabase
      .from('profiles')
      .select('id, xrp_wallet_address, wallet_connected_at, wallet_last_used')
      .eq('id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      console.error('Error fetching primary wallet:', error);
      // Don't throw error, just return null to handle gracefully
      return null;
    }

    if (!data?.xrp_wallet_address) {
      return null;
    }

    // Convert profiles data to UserWallet format
    return {
      id: `${userId}-xrp-primary`,
      user_id: userId,
      wallet_address: data.xrp_wallet_address as string,
      wallet_type: 'xrp' as const,
      is_primary: true,
      nickname: 'Primary XRP Wallet',
      connected_at: (data.wallet_connected_at as string) || new Date().toISOString(),
      last_used: (data.wallet_last_used as string) || new Date().toISOString(),
      is_active: true,
      created_at: (data.wallet_connected_at as string) || new Date().toISOString(),
      updated_at: (data.wallet_last_used as string) || new Date().toISOString()
    } as UserWallet;
  }

  /**
   * Add a new wallet for a user
   * Note: Currently only supports XRP wallets in profiles table
   */
  async addWallet(
    userId: string,
    walletAddress: string,
    walletType: 'xrp' | 'fuse' = 'xrp',
    nickname?: string
  ): Promise<UserWallet> {
    if (!this.supabase) throw new Error('Supabase client not available');

    // For now, only support XRP wallets
    if (walletType !== 'xrp') {
      throw new Error('Only XRP wallets are currently supported');
    }

    // Check if user already has a wallet
    const { data: existing } = await this.supabase
      .from('profiles')
      .select('xrp_wallet_address')
      .eq('id', userId)
      .single();

    if (existing?.xrp_wallet_address) {
      throw new Error('User already has a wallet address');
    }

    // Update the profiles table with the new wallet address
    const { error } = await this.supabase
      .from('profiles')
      .update({
        xrp_wallet_address: walletAddress,
        wallet_connected_at: new Date().toISOString(),
        wallet_last_used: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error adding wallet:', error);
      throw error;
    }

    // Convert to UserWallet format
    return {
      id: `${userId}-xrp-primary`,
      user_id: userId,
      wallet_address: walletAddress,
      wallet_type: 'xrp' as const,
      is_primary: true,
      nickname: nickname || 'Primary XRP Wallet',
      connected_at: new Date().toISOString(),
      last_used: new Date().toISOString(),
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    } as UserWallet;
  }

  /**
   * Set a wallet as primary
   * Note: Currently only supports XRP wallets in profiles table
   */
  async setPrimaryWallet(userId: string, walletAddress: string, walletType: 'xrp' | 'fuse' = 'xrp'): Promise<boolean> {
    if (!this.supabase) throw new Error('Supabase client not available');

    // For now, only support XRP wallets
    if (walletType !== 'xrp') {
      throw new Error('Only XRP wallets are currently supported');
    }

    // Update the profiles table with the wallet address
    const { error } = await this.supabase
      .from('profiles')
      .update({
        xrp_wallet_address: walletAddress,
        wallet_last_used: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error setting primary wallet:', error);
      throw error;
    }

    return true;
  }

  /**
   * Update wallet nickname
   * Note: Currently not supported as profiles table doesn't have nickname field
   */
  async updateWalletNickname(userId: string, walletAddress: string, nickname: string): Promise<boolean> {
    if (!this.supabase) throw new Error('Supabase client not available');

    // For now, this is a no-op since profiles table doesn't have nickname field
    console.log(`Wallet nickname update requested for user ${userId}, wallet ${walletAddress}, nickname: ${nickname}`);
    console.log('Note: Nickname updates are not currently supported with profiles table');

    return true;
  }

  /**
   * Remove a wallet (soft delete)
   * Note: Currently removes XRP wallet from profiles table
   */
  async removeWallet(userId: string, walletAddress: string): Promise<boolean> {
    if (!this.supabase) throw new Error('Supabase client not available');

    // Verify the wallet belongs to the user before removing
    const { data: profile } = await this.supabase
      .from('profiles')
      .select('xrp_wallet_address')
      .eq('id', userId)
      .single();

    if (!profile || profile.xrp_wallet_address !== walletAddress) {
      throw new Error('Wallet not found or does not belong to user');
    }

    const { error } = await this.supabase
      .from('profiles')
      .update({
        xrp_wallet_address: null,
        wallet_last_used: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error removing wallet:', error);
      throw error;
    }

    return true;
  }

  /**
   * Update wallet last used timestamp
   * Note: Currently updates profiles.wallet_last_used
   */
  async updateWalletLastUsed(userId: string, walletAddress: string): Promise<boolean> {
    if (!this.supabase) throw new Error('Supabase client not available');

    // Verify the wallet belongs to the user before updating
    const { data: profile } = await this.supabase
      .from('profiles')
      .select('xrp_wallet_address')
      .eq('id', userId)
      .single();

    if (!profile || profile.xrp_wallet_address !== walletAddress) {
      console.error('Wallet not found or does not belong to user');
      return false;
    }

    const { error } = await this.supabase
      .from('profiles')
      .update({
        wallet_last_used: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating wallet last used:', error);
      return false;
    }

    return true;
  }

  /**
   * Get wallet by address
   * Note: Currently searches profiles.xrp_wallet_address
   */
  async getWalletByAddress(walletAddress: string): Promise<UserWallet | null> {
    if (!this.supabase) throw new Error('Supabase client not available');

    const { data, error } = await this.supabase
      .from('profiles')
      .select('id, xrp_wallet_address, wallet_connected_at, wallet_last_used')
      .eq('xrp_wallet_address', walletAddress)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching wallet by address:', error);
      throw error;
    }

    if (!data) {
      return null;
    }

    // Convert to UserWallet format
    return {
      id: `${data.id}-xrp-primary`,
      user_id: data.id as string,
      wallet_address: walletAddress,
      wallet_type: 'xrp' as const,
      is_primary: true,
      nickname: 'Primary XRP Wallet',
      connected_at: (data.wallet_connected_at as string) || new Date().toISOString(),
      last_used: (data.wallet_last_used as string) || new Date().toISOString(),
      is_active: true,
      created_at: (data.wallet_connected_at as string) || new Date().toISOString(),
      updated_at: (data.wallet_last_used as string) || new Date().toISOString()
    } as UserWallet;
  }

  /**
   * Check if user has any wallets
   * Note: Currently checks profiles.xrp_wallet_address
   */
  async hasWallets(userId: string, walletType?: 'xrp' | 'fuse'): Promise<boolean> {
    if (!this.supabase) throw new Error('Supabase client not available');

    // For now, only support XRP wallets
    if (walletType && walletType !== 'xrp') {
      return false;
    }

    const { data, error } = await this.supabase
      .from('profiles')
      .select('xrp_wallet_address')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking if user has wallets:', error);
      return false;
    }

    return !!(data?.xrp_wallet_address);
  }
}

// Export singleton instance
export const walletService = new WalletService();
