import { Client, convertHexToString } from 'xrpl'

const client = new Client('wss://xrplcluster.com')

export async function hasUSDCTrustline(address: string): Promise<boolean> {
  await client.connect()
  const response = await client.request({
    command: 'account_lines',
    account: address,
  })
  await client.disconnect()

  return response.result.lines.some(
    (line) =>
      line.currency === convertHexToString('5553444300000000000000000000000000000000') &&
      line.account === 'rHuGNhqTG32mfmAvWA8hUyWRLV3tCSwKQt'
  )
}