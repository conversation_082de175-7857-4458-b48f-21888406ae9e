/**
 * XRPL Signature Verification Utilities
 * 
 * Provides functions for verifying XRPL transaction signatures using the verify-xrpl-signature package.
 * This enhances security by allowing independent verification of signed transactions.
 */

import { verifySignature } from 'verify-xrpl-signature'

export interface SignatureVerificationResult {
  isValid: boolean
  signer?: string
  error?: string
}

/**
 * Verify a signed XRPL transaction blob
 * @param signedTxBlob - Hex-encoded signed transaction blob
 * @param expectedSigner - Optional expected signer address for validation
 * @returns Verification result with validity status and details
 */
export async function verifyXrplSignature(
  signedTxBlob: string,
  expectedSigner?: string
): Promise<SignatureVerificationResult> {
  try {
    // Basic validation of input
    if (!signedTxBlob || typeof signedTxBlob !== 'string') {
      return {
        isValid: false,
        error: 'Invalid transaction blob: must be a non-empty string'
      }
    }

    // Ensure hex format (remove 0x prefix if present)
    const cleanBlob = signedTxBlob.replace(/^0x/, '').toUpperCase()
    
    // Verify the signature using verify-xrpl-signature package
    const result = verifySignature(cleanBlob)
    
    if (result && result.signedBy) {
      // If expectedSigner is provided, validate it matches
      if (expectedSigner && result.signedBy !== expectedSigner) {
        return {
          isValid: false,
          signer: result.signedBy,
          error: `Signature valid but signed by ${result.signedBy}, expected ${expectedSigner}`
        }
      }

      return {
        isValid: true,
        signer: result.signedBy
      }
    }

    return {
      isValid: false,
      error: 'Signature verification failed - invalid signature'
    }
  } catch (error) {
    console.error('Error verifying XRPL signature:', error)
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown verification error'
    }
  }
}

/**
 * Verify multiple signatures for multi-signed transactions
 * @param signedTxBlob - Hex-encoded signed transaction blob
 * @param expectedSigners - Array of expected signer addresses
 * @returns Array of verification results for each signer
 */
export async function verifyMultiSignature(
  signedTxBlob: string,
  expectedSigners: string[]
): Promise<SignatureVerificationResult[]> {
  try {
    const results: SignatureVerificationResult[] = []
    
    for (const signer of expectedSigners) {
      const result = await verifyXrplSignature(signedTxBlob, signer)
      results.push(result)
    }
    
    return results
  } catch (error) {
    console.error('Error verifying multi-signatures:', error)
    return expectedSigners.map(() => ({
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown verification error'
    }))
  }
}

/**
 * Utility function to check if a transaction blob is properly formatted
 * @param txBlob - Transaction blob to validate
 * @returns True if the blob appears to be valid hex format
 */
export function isValidTransactionBlob(txBlob: string): boolean {
  if (!txBlob || typeof txBlob !== 'string') {
    return false
  }
  
  // Remove 0x prefix if present
  const cleanBlob = txBlob.replace(/^0x/, '')
  
  // Check if it's valid hex and reasonable length
  const hexRegex = /^[0-9A-Fa-f]+$/
  return hexRegex.test(cleanBlob) && cleanBlob.length >= 20 && cleanBlob.length <= 2000
}

/**
 * Enhanced verification function that includes additional validation
 * @param signedTxBlob - Hex-encoded signed transaction blob
 * @param options - Verification options
 * @returns Comprehensive verification result
 */
export async function verifyXrplSignatureEnhanced(
  signedTxBlob: string,
  options: {
    expectedSigner?: string
    requireValidFormat?: boolean
    network?: 'mainnet' | 'testnet' | 'devnet'
  } = {}
): Promise<SignatureVerificationResult> {
  const { expectedSigner, requireValidFormat = true } = options
  
  // Format validation if required
  if (requireValidFormat && !isValidTransactionBlob(signedTxBlob)) {
    return {
      isValid: false,
      error: 'Invalid transaction blob format'
    }
  }
  
  // Perform signature verification
  return await verifyXrplSignature(signedTxBlob, expectedSigner)
}