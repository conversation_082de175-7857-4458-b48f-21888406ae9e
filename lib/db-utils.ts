// Re-export the main Supabase client to avoid conflicts
export { getSupabaseClient, resetSupabaseClient } from '@/lib/supabase';

// Use this function in client components instead of direct pg connections
export async function executeQuery(query: string, params?: any[]) {
  const supabase = getSupabaseClient();
  if (!supabase) {
    throw new Error("Supabase client not initialized");
  }
  
  // Use Supabase's RPC function to execute raw SQL
  // Note: You need to create a PostgreSQL function that can execute the query
  const { data, error } = await supabase.rpc('execute_query', {
    query_text: query,
    query_params: params
  });
  
  if (error) throw error;
  return data;
}

// For user session data, use Supabase auth instead of direct DB queries
export async function getUserData(userId: string) {
  const supabase = getSupabaseClient();
  if (!supabase) {
    throw new Error("Supabase client not initialized");
  }
  
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
    
  if (error) throw error;
  return data;
}
