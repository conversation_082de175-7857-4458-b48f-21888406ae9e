"use client"

// Browser-only imports
let Core: any = null
let Web3Wallet: any = null
let getSdkError: any = null

// Dynamic imports for browser-only code
if (typeof window !== 'undefined') {
  import('@walletconnect/core').then(module => {
    Core = module.Core
  })
  import('@walletconnect/web3wallet').then(module => {
    Web3Wallet = module.Web3Wallet
  })
  import('@walletconnect/utils').then(module => {
    getSdkError = module.getSdkError
  })
}

// WalletConnect configuration
const PROJECT_ID = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'demo-project-id'

// Metadata for the dApp
const metadata = {
  name: 'Fuse.vip',
  description: 'VIP Card Payment - Loyalty Reimagined. Commerce Reconnected!',
  url: typeof window !== 'undefined' ? window.location.origin : 'https://fuse.vip',
  icons: ['https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png']
}

// Supported XRPL chains
const XRPL_CHAIN_ID = 'xrpl:0' // XRPL Mainnet

export interface WalletConnectSession {
  topic: string
  account: string
  chainId: string
}

export class WalletConnectService {
  private web3wallet: any = null
  private core: any = null
  private currentSession: WalletConnectSession | null = null
  private isInitialized = false

  // Initialize WalletConnect
  async initialize(): Promise<void> {
    if (this.isInitialized || typeof window === 'undefined') return

    try {
      // Wait for dynamic imports
      if (!Core || !Web3Wallet) {
        const [coreModule, web3WalletModule, utilsModule] = await Promise.all([
          import('@walletconnect/core'),
          import('@walletconnect/web3wallet'),
          import('@walletconnect/utils')
        ])
        
        Core = coreModule.Core
        Web3Wallet = web3WalletModule.Web3Wallet
        getSdkError = utilsModule.getSdkError
      }

      // Initialize Core
      this.core = new Core({
        projectId: PROJECT_ID,
      })

      // Initialize Web3Wallet
      this.web3wallet = await Web3Wallet.init({
        core: this.core,
        metadata: metadata,
      })

      this.setupEventListeners()
      this.isInitialized = true
      console.log('WalletConnect initialized successfully')
    } catch (error) {
      console.error('Failed to initialize WalletConnect:', error)
      throw error
    }
  }

  // Setup event listeners
  private setupEventListeners(): void {
    if (!this.web3wallet) return

    // Session proposal event
    this.web3wallet.on('session_proposal', async (event) => {
      console.log('Session proposal received:', event)
      // Auto-approve sessions that support XRPL
      const { requiredNamespaces, relays } = event.params
      
      try {
        const session = await this.web3wallet!.approveSession({
          id: event.id,
          namespaces: {
            xrpl: {
              accounts: [`${XRPL_CHAIN_ID}:${this.currentSession?.account || 'rUnknown'}`],
              methods: ['xrpl_signTransaction', 'xrpl_signMessage'],
              events: ['accountsChanged', 'chainChanged']
            }
          }
        })
        console.log('Session approved:', session)
      } catch (error) {
        console.error('Failed to approve session:', error)
        await this.web3wallet!.rejectSession({
          id: event.id,
          reason: getSdkError('USER_REJECTED')
        })
      }
    })

    // Session request event
    this.web3wallet.on('session_request', async (event) => {
      console.log('Session request received:', event)
      const { topic, params, id } = event
      const { request } = params

      try {
        let result
        
        switch (request.method) {
          case 'xrpl_signTransaction':
            result = await this.handleSignTransaction(request.params)
            break
          case 'xrpl_signMessage':
            result = await this.handleSignMessage(request.params)
            break
          default:
            throw new Error(`Unsupported method: ${request.method}`)
        }

        await this.web3wallet!.respondSessionRequest({
          topic,
          response: {
            id,
            result,
            jsonrpc: '2.0'
          }
        })
      } catch (error) {
        console.error('Failed to handle session request:', error)
        await this.web3wallet!.respondSessionRequest({
          topic,
          response: {
            id,
            error: {
              code: -32000,
              message: error instanceof Error ? error.message : 'Unknown error'
            },
            jsonrpc: '2.0'
          }
        })
      }
    })

    // Session delete event
    this.web3wallet.on('session_delete', (event) => {
      console.log('Session deleted:', event)
      this.currentSession = null
    })
  }

  // Connect to Joey Wallet via WalletConnect
  async connectJoeyWallet(): Promise<WalletConnectSession> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    if (!this.web3wallet) {
      throw new Error('WalletConnect not initialized')
    }

    try {
      // Create connection URI
      const { uri, approval } = await this.web3wallet.connect({
        requiredNamespaces: {
          xrpl: {
            methods: ['xrpl_signTransaction', 'xrpl_signMessage'],
            chains: [XRPL_CHAIN_ID],
            events: ['accountsChanged', 'chainChanged']
          }
        }
      })

      if (!uri) {
        throw new Error('Failed to generate connection URI')
      }

      // Open Joey Wallet with deep link
      this.openJoeyWallet(uri)

      // Wait for session approval
      const session = await approval()
      
      // Extract account information
      const xrplNamespace = session.namespaces.xrpl
      if (!xrplNamespace || !xrplNamespace.accounts.length) {
        throw new Error('No XRPL accounts found in session')
      }

      const accountString = xrplNamespace.accounts[0]
      const account = accountString.split(':')[2] // Extract address from "xrpl:0:rXXXXXX" format

      this.currentSession = {
        topic: session.topic,
        account: account,
        chainId: XRPL_CHAIN_ID
      }

      console.log('Connected to Joey Wallet:', this.currentSession)
      return this.currentSession

    } catch (error) {
      console.error('Failed to connect to Joey Wallet:', error)
      throw error
    }
  }

  // Open Joey Wallet app with WalletConnect URI
  private openJoeyWallet(uri: string): void {
    if (typeof window === 'undefined') return

    // Joey Wallet deep link format
    const joeyDeepLink = `joey://wc?uri=${encodeURIComponent(uri)}`
    
    // Try to open Joey Wallet app
    const openApp = () => {
      try {
        window.location.href = joeyDeepLink
      } catch (error) {
        console.log('Failed to open Joey Wallet app, falling back to universal link')
        // Fallback to universal link or app store
        window.open('https://joeywallet.xyz/', '_blank')
      }
    }

    // For mobile devices, try immediate redirect
    if (/Mobi|Android/i.test(navigator.userAgent)) {
      openApp()
    } else {
      // For desktop, show QR code and provide manual link
      console.log('WalletConnect URI for Joey Wallet:', uri)
      console.log('Joey Wallet deep link:', joeyDeepLink)
      
      // Try to open app, but don't block if it fails
      setTimeout(openApp, 100)
    }
  }

  // Handle transaction signing
  private async handleSignTransaction(params: any): Promise<any> {
    console.log('Handling transaction signing:', params)
    // This would integrate with Joey Wallet's signing flow
    // For now, return a placeholder response
    return {
      signature: 'placeholder_signature',
      txHash: 'placeholder_tx_hash'
    }
  }

  // Handle message signing
  private async handleSignMessage(params: any): Promise<any> {
    console.log('Handling message signing:', params)
    // This would integrate with Joey Wallet's message signing
    return {
      signature: 'placeholder_message_signature'
    }
  }

  // Disconnect from Joey Wallet
  async disconnect(): Promise<void> {
    if (!this.web3wallet || !this.currentSession) {
      return
    }

    try {
      await this.web3wallet.disconnectSession({
        topic: this.currentSession.topic,
        reason: getSdkError('USER_DISCONNECTED')
      })
      
      this.currentSession = null
      console.log('Disconnected from Joey Wallet')
    } catch (error) {
      console.error('Failed to disconnect from Joey Wallet:', error)
      throw error
    }
  }

  // Get current session
  getCurrentSession(): WalletConnectSession | null {
    return this.currentSession
  }

  // Check if connected
  isConnected(): boolean {
    return this.currentSession !== null
  }

  // Get connected account
  getAccount(): string | null {
    return this.currentSession?.account || null
  }
}

// Export singleton instance
export const walletConnectService = new WalletConnectService()

// Utility functions
export const formatJoeyWalletAddress = (address: string): string => {
  if (!address || address.length < 8) return address
  return `${address.substring(0, 4)}...${address.substring(address.length - 4)}`
}

export const isValidXRPLAddress = (address: string): boolean => {
  return address.startsWith('r') && address.length >= 25 && address.length <= 34
}