// Enhanced fetch utility with error handling, retries, and type safety

export interface FetchOptions extends RequestInit {
  timeout?: number
  retries?: number
  retryDelay?: number
  baseURL?: string
}

export interface ApiResponse<T = any> {
  data?: T
  error?: string
  success: boolean
  status: number
}

class FetchError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: Response
  ) {
    super(message)
    this.name = 'FetchError'
  }
}

/**
 * Enhanced fetch function with automatic retries, timeout, and error handling
 */
export async function fetchWithRetry<T = any>(
  url: string,
  options: FetchOptions = {}
): Promise<ApiResponse<T>> {
  const {
    timeout = 10000,
    retries = 3,
    retryDelay = 1000,
    baseURL = '',
    ...fetchOptions
  } = options

  const fullUrl = baseURL ? `${baseURL}${url}` : url
  let lastError: Error

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      const response = await fetch(fullUrl, {
        ...fetchOptions,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...fetchOptions.headers,
        },
      })

      clearTimeout(timeoutId)

      // Handle HTTP errors
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new FetchError(
          `HTTP ${response.status}: ${errorText}`,
          response.status,
          response
        )
      }

      // Parse JSON response
      const data = await response.json().catch(() => null)

      return {
        data,
        success: true,
        status: response.status,
      }
    } catch (error) {
      lastError = error as Error

      // Don't retry on certain errors
      if (
        error instanceof FetchError &&
        (error.status === 401 || error.status === 403 || error.status === 404)
      ) {
        break
      }

      // Don't retry on the last attempt
      if (attempt === retries) {
        break
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)))
    }
  }

  // Return error response
  return {
    success: false,
    status: lastError instanceof FetchError ? lastError.status : 0,
    error: lastError.message,
  }
}

/**
 * GET request helper
 */
export async function get<T = any>(
  url: string,
  options: Omit<FetchOptions, 'method' | 'body'> = {}
): Promise<ApiResponse<T>> {
  return fetchWithRetry<T>(url, { ...options, method: 'GET' })
}

/**
 * POST request helper
 */
export async function post<T = any>(
  url: string,
  data?: any,
  options: Omit<FetchOptions, 'method'> = {}
): Promise<ApiResponse<T>> {
  return fetchWithRetry<T>(url, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * PUT request helper
 */
export async function put<T = any>(
  url: string,
  data?: any,
  options: Omit<FetchOptions, 'method'> = {}
): Promise<ApiResponse<T>> {
  return fetchWithRetry<T>(url, {
    ...options,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * PATCH request helper
 */
export async function patch<T = any>(
  url: string,
  data?: any,
  options: Omit<FetchOptions, 'method'> = {}
): Promise<ApiResponse<T>> {
  return fetchWithRetry<T>(url, {
    ...options,
    method: 'PATCH',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * DELETE request helper
 */
export async function del<T = any>(
  url: string,
  options: Omit<FetchOptions, 'method' | 'body'> = {}
): Promise<ApiResponse<T>> {
  return fetchWithRetry<T>(url, { ...options, method: 'DELETE' })
}

/**
 * API client class for organized API calls
 */
export class ApiClient {
  private baseURL: string
  private defaultOptions: FetchOptions

  constructor(baseURL: string = '', defaultOptions: FetchOptions = {}) {
    this.baseURL = baseURL
    this.defaultOptions = defaultOptions
  }

  async get<T = any>(url: string, options: FetchOptions = {}): Promise<ApiResponse<T>> {
    return get<T>(url, { ...this.defaultOptions, ...options, baseURL: this.baseURL })
  }

  async post<T = any>(url: string, data?: any, options: FetchOptions = {}): Promise<ApiResponse<T>> {
    return post<T>(url, data, { ...this.defaultOptions, ...options, baseURL: this.baseURL })
  }

  async put<T = any>(url: string, data?: any, options: FetchOptions = {}): Promise<ApiResponse<T>> {
    return put<T>(url, data, { ...this.defaultOptions, ...options, baseURL: this.baseURL })
  }

  async patch<T = any>(url: string, data?: any, options: FetchOptions = {}): Promise<ApiResponse<T>> {
    return patch<T>(url, data, { ...this.defaultOptions, ...options, baseURL: this.baseURL })
  }

  async delete<T = any>(url: string, options: FetchOptions = {}): Promise<ApiResponse<T>> {
    return del<T>(url, { ...this.defaultOptions, ...options, baseURL: this.baseURL })
  }
}

// Default API client instance
export const apiClient = new ApiClient()

// Auth-specific API client
export const authApiClient = new ApiClient('/api/auth')

// Convenience functions for common API patterns
export const api = {
  get,
  post,
  put,
  patch,
  delete: del,
  client: apiClient,
  auth: authApiClient,
}

export default api
