// Xaman SDK integration utilities
import { XamanConnect } from "@xaman/connect-sdk"
import { XAMAN_CONFIG, validateXamanConfig } from "./xaman-config"

// Initialize the Xaman SDK with the API key
let xamanSDK: XamanConnect | null = null

export function getXamanSDK(): XamanConnect {
  if (!xamanSDK) {
    // Validate configuration
    const validation = validateXamanConfig()
    if (!validation.valid) {
      throw new Error(`Xaman configuration invalid: ${validation.errors.join(', ')}`)
    }

    xamanSDK = new XamanConnect({
      apiKey: XAMAN_CONFIG.API_KEY,
      redirectUrl: XAMAN_CONFIG.getRedirectUrl(XAMAN_CONFIG.REDIRECT_URLS.DASHBOARD),
    })
  }

  return xamanSDK
}

// Types for Xaman connection response
export interface XamanConnectionResponse {
  uuid: string
  qrUrl: string
  deeplink: {
    universal: string
    ios: string
    android: string
  }
}

// Types for Xaman user info
export interface XamanUserInfo {
  sub: string
  account: string
  network: string
  iat: number
  exp: number
}

// Function to check if the device is mobile
export function isMobileDevice(): boolean {
  if (typeof window === "undefined") return false

  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}
