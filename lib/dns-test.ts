/**
 * DNS Resolution Testing Utilities
 */

import { promisify } from 'util';

/**
 * Test DNS resolution for Supabase hostname
 */
export async function testDNSResolution(): Promise<{
  success: boolean;
  hostname: string;
  addresses: string[];
  error?: string;
}> {
  const hostname = 'db.haqbtbpmyadkocakqnew.supabase.co';
  
  try {
    // Dynamic import for server-side only
    if (typeof window !== 'undefined') {
      return {
        success: false,
        hostname,
        addresses: [],
        error: 'DNS testing is server-side only'
      };
    }

    const dns = await import('dns');
    const lookup = promisify(dns.lookup);
    
    console.log(`🔍 Testing DNS resolution for: ${hostname}`);
    
    // Test both IPv4 and IPv6
    const results = await Promise.allSettled([
      lookup(hostname, { family: 4 }), // IPv4
      lookup(hostname, { family: 6 }), // IPv6
      lookup(hostname, { family: 0 })  // Both
    ]);
    
    const addresses: string[] = [];
    let hasSuccess = false;
    
    results.forEach((result, index) => {
      const family = index === 0 ? 'IPv4' : index === 1 ? 'IPv6' : 'Any';
      
      if (result.status === 'fulfilled') {
        hasSuccess = true;
        const addr = Array.isArray(result.value) ? result.value[0].address : result.value.address;
        addresses.push(`${addr} (${family})`);
        console.log(`✅ DNS ${family} resolution successful: ${addr}`);
      } else {
        console.log(`❌ DNS ${family} resolution failed:`, result.reason.message);
      }
    });
    
    return {
      success: hasSuccess,
      hostname,
      addresses,
      error: hasSuccess ? undefined : 'All DNS resolution attempts failed'
    };
    
  } catch (error: any) {
    console.error('❌ DNS test error:', error.message);
    return {
      success: false,
      hostname,
      addresses: [],
      error: error.message
    };
  }
}

/**
 * Test network connectivity to Supabase
 */
export async function testNetworkConnectivity(): Promise<{
  success: boolean;
  latency?: number;
  error?: string;
}> {
  try {
    if (typeof window !== 'undefined') {
      return {
        success: false,
        error: 'Network testing is server-side only'
      };
    }

    const net = await import('net');
    const startTime = Date.now();
    
    return new Promise((resolve) => {
      const socket = new net.Socket();
      
      const timeout = setTimeout(() => {
        socket.destroy();
        resolve({
          success: false,
          error: 'Connection timeout after 10 seconds'
        });
      }, 10000);
      
      socket.connect(5432, 'db.haqbtbpmyadkocakqnew.supabase.co', () => {
        clearTimeout(timeout);
        const latency = Date.now() - startTime;
        socket.destroy();
        
        console.log(`✅ Network connectivity test successful (${latency}ms)`);
        resolve({
          success: true,
          latency
        });
      });
      
      socket.on('error', (error: any) => {
        clearTimeout(timeout);
        console.error('❌ Network connectivity test failed:', error.message);
        resolve({
          success: false,
          error: error.message
        });
      });
    });
    
  } catch (error: any) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Comprehensive connection diagnostics
 */
export async function runConnectionDiagnostics(): Promise<{
  dns: any;
  network: any;
  recommendations: string[];
}> {
  console.log('🔧 Running connection diagnostics...');
  
  const dns = await testDNSResolution();
  const network = await testNetworkConnectivity();
  
  const recommendations: string[] = [];
  
  if (!dns.success) {
    recommendations.push('DNS resolution failed - check network connectivity and DNS settings');
    recommendations.push('Consider adding NODE_OPTIONS=--dns-result-order=ipv4first to environment');
  }
  
  if (!network.success) {
    recommendations.push('Network connectivity failed - check firewall and network settings');
    recommendations.push('Verify port 5432 is accessible');
  }
  
  if (dns.success && network.success) {
    recommendations.push('DNS and network connectivity are working - issue may be with authentication or database configuration');
  }
  
  if (!dns.success && !network.success) {
    recommendations.push('Both DNS and network failed - this appears to be a network infrastructure issue');
    recommendations.push('Consider using Supabase REST API as fallback');
  }
  
  console.log('🔧 Diagnostics complete');
  
  return {
    dns,
    network,
    recommendations
  };
}
