/**
 * Schema Management Utility for PGRST002 Error Resolution
 * Handles PostgREST schema cache issues and provides automated fixes
 */

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Admin client for schema operations
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

export interface SchemaReloadResult {
  success: boolean
  message: string
  timestamp: string
  details?: any
}

export interface SchemaHealthCheck {
  isHealthy: boolean
  exposedSchemas: string[]
  cacheStatus: string
  lastReload?: string
  errors: string[]
}

/**
 * Force PostgREST to reload its schema cache
 */
export async function reloadPostgRESTSchema(): Promise<SchemaReloadResult> {
  try {
    console.log('🔄 Triggering PostgREST schema reload...')
    
    // Execute NOTIFY command to trigger schema reload
    const { data, error } = await supabaseAdmin.rpc('notify_schema_reload')
    
    if (error) {
      // Fallback: Direct SQL execution
      const { error: sqlError } = await supabaseAdmin
        .from('pg_notify')
        .insert({ channel: 'pgrst', payload: 'reload schema' })
      
      if (sqlError) {
        // Final fallback: Use raw SQL
        const { error: rawError } = await supabaseAdmin
          .rpc('exec_sql', { 
            sql: "NOTIFY pgrst, 'reload schema'" 
          })
        
        if (rawError) {
          throw new Error(`Failed to reload schema: ${rawError.message}`)
        }
      }
    }

    const result: SchemaReloadResult = {
      success: true,
      message: 'PostgREST schema cache reloaded successfully',
      timestamp: new Date().toISOString(),
      details: { method: 'notify_command' }
    }

    console.log('✅ Schema reload completed:', result)
    return result

  } catch (error) {
    const result: SchemaReloadResult = {
      success: false,
      message: `Schema reload failed: ${error instanceof Error ? error.message : String(error)}`,
      timestamp: new Date().toISOString(),
      details: { error }
    }

    console.error('❌ Schema reload failed:', result)
    return result
  }
}

/**
 * Check PostgREST schema health and configuration
 */
export async function checkSchemaHealth(): Promise<SchemaHealthCheck> {
  try {
    // Check current schema configuration
    const { data: schemaData, error: schemaError } = await supabaseAdmin
      .rpc('get_postgrest_config')
    
    if (schemaError) {
      console.warn('Could not fetch PostgREST config:', schemaError.message)
    }

    // Test basic connectivity
    const { data: testData, error: testError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .limit(1)

    const errors: string[] = []
    if (testError) {
      errors.push(`Connection test failed: ${testError.message}`)
    }

    // Check for common PGRST002 indicators
    if (testError?.message?.includes('schema cache')) {
      errors.push('PGRST002: Schema cache error detected')
    }

    if (testError?.message?.includes('503')) {
      errors.push('HTTP 503: Service temporarily unavailable')
    }

    return {
      isHealthy: errors.length === 0,
      exposedSchemas: schemaData?.exposed_schemas || ['unknown'],
      cacheStatus: errors.length === 0 ? 'healthy' : 'error',
      errors
    }

  } catch (error) {
    return {
      isHealthy: false,
      exposedSchemas: [],
      cacheStatus: 'error',
      errors: [`Health check failed: ${error instanceof Error ? error.message : String(error)}`]
    }
  }
}

/**
 * Automated schema reload with retry logic
 */
export async function autoReloadSchema(maxRetries: number = 3): Promise<SchemaReloadResult> {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 Schema reload attempt ${attempt}/${maxRetries}`)
      
      const result = await reloadPostgRESTSchema()
      
      if (result.success) {
        // Wait a moment for the reload to take effect
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // Verify the reload worked
        const healthCheck = await checkSchemaHealth()
        
        if (healthCheck.isHealthy) {
          return {
            ...result,
            message: `Schema reloaded successfully on attempt ${attempt}`,
            details: { ...result.details, attempt, healthCheck }
          }
        } else {
          console.warn(`Schema reload attempt ${attempt} completed but health check failed`)
          if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 3000 * attempt))
            continue
          }
        }
      }
      
      return result
      
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))
      console.error(`Schema reload attempt ${attempt} failed:`, lastError.message)
      
      if (attempt < maxRetries) {
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, 2000 * Math.pow(2, attempt - 1)))
      }
    }
  }

  return {
    success: false,
    message: `Schema reload failed after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`,
    timestamp: new Date().toISOString(),
    details: { maxRetries, lastError: lastError?.message }
  }
}

/**
 * Monitor schema health and auto-reload if needed
 */
export async function monitorAndMaintainSchema(): Promise<void> {
  try {
    const healthCheck = await checkSchemaHealth()
    
    if (!healthCheck.isHealthy) {
      console.warn('🚨 Schema health check failed, attempting auto-reload...')
      console.warn('Errors detected:', healthCheck.errors)
      
      const reloadResult = await autoReloadSchema()
      
      if (reloadResult.success) {
        console.log('✅ Auto-reload successful')
      } else {
        console.error('❌ Auto-reload failed:', reloadResult.message)
        
        // Log to monitoring system or send alert
        // You could integrate with your notification system here
      }
    } else {
      console.log('✅ Schema health check passed')
    }
    
  } catch (error) {
    console.error('Schema monitoring error:', error)
  }
}

/**
 * Emergency schema reset - use only when all else fails
 */
export async function emergencySchemaReset(): Promise<SchemaReloadResult> {
  try {
    console.log('🚨 Performing emergency schema reset...')
    
    // Multiple reload attempts with different methods
    const methods = [
      () => supabaseAdmin.rpc('exec_sql', { sql: "NOTIFY pgrst, 'reload schema'" }),
      () => supabaseAdmin.rpc('exec_sql', { sql: "NOTIFY pgrst, 'reload config'" }),
      () => supabaseAdmin.rpc('exec_sql', { sql: "SELECT pg_notify('pgrst', 'reload schema')" })
    ]
    
    for (const method of methods) {
      try {
        await method()
        await new Promise(resolve => setTimeout(resolve, 3000))
        
        const healthCheck = await checkSchemaHealth()
        if (healthCheck.isHealthy) {
          return {
            success: true,
            message: 'Emergency schema reset successful',
            timestamp: new Date().toISOString(),
            details: { method: 'emergency_reset' }
          }
        }
      } catch (error) {
        console.warn('Emergency method failed:', error)
        continue
      }
    }
    
    throw new Error('All emergency reset methods failed')
    
  } catch (error) {
    return {
      success: false,
      message: `Emergency schema reset failed: ${error instanceof Error ? error.message : String(error)}`,
      timestamp: new Date().toISOString(),
      details: { error }
    }
  }
}
