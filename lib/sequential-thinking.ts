/**
 * Sequential Thinking Integration for Complex Problem Solving
 * Provides step-by-step reasoning capabilities for the chat bot
 */

import { PerplexityClient } from './perplexity';

export interface ThinkingStep {
  step: number;
  thought: string;
  reasoning: string;
  nextAction?: string;
  conclusion?: string;
}

export interface ThinkingProcess {
  problem: string;
  steps: ThinkingStep[];
  finalConclusion: string;
  actionItems: string[];
}

export class SequentialThinking {
  private perplexity: PerplexityClient;

  constructor(apiKey: string) {
    this.perplexity = new PerplexityClient(apiKey);
  }

  async processComplexQuery(
    query: string,
    context: string,
    conversationHistory: Array<{role: string, content: string}> = []
  ): Promise<ThinkingProcess> {
    
    const thinkingPrompt = `You are an expert problem solver with advanced reasoning capabilities. Break down this complex query into logical steps.

CONTEXT: ${context}

CONVERSATION HISTORY:
${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n')}

COMPLEX QUERY: ${query}

Please think through this step by step:

1. First, identify the core problem or question
2. Break down the problem into logical components
3. Consider relevant context and previous conversation
4. Work through each component systematically
5. Provide actionable steps or recommendations
6. Conclude with a clear, helpful response

Use this format:
STEP 1: [Identify the core problem]
REASONING: [Your thought process]

STEP 2: [Break down components]
REASONING: [Your analysis]

STEP 3: [Consider context and history]
REASONING: [How this relates to previous conversation]

STEP 4: [Work through systematically]
REASONING: [Your systematic approach]

STEP 5: [Provide actionable steps]
REASONING: [What actions to recommend]

FINAL RESPONSE: [Clear, helpful conclusion with specific next steps]`;

    const response = await this.perplexity.chat([
      {
        role: 'system',
        content: thinkingPrompt
      },
      {
        role: 'user',
        content: query
      }
    ]);

    return this.parseThinkingResponse(response.choices[0].message.content, query);
  }

  private parseThinkingResponse(response: string, originalQuery: string): ThinkingProcess {
    const steps: ThinkingStep[] = [];
    const lines = response.split('\n');
    
    let currentStep: Partial<ThinkingStep> | null = null;
    let finalConclusion = '';
    let actionItems: string[] = [];

    for (const line of lines) {
      if (line.startsWith('STEP ')) {
        // Save previous step if exists
        if (currentStep && currentStep.step && currentStep.thought) {
          steps.push(currentStep as ThinkingStep);
        }
        
        // Start new step
        const stepMatch = line.match(/STEP (\d+): (.+)/);
        if (stepMatch) {
          currentStep = {
            step: parseInt(stepMatch[1]),
            thought: stepMatch[2],
            reasoning: ''
          };
        }
      } else if (line.startsWith('REASONING: ') && currentStep) {
        currentStep.reasoning = line.substring(11);
      } else if (line.startsWith('FINAL RESPONSE: ')) {
        finalConclusion = line.substring(16);
      } else if (line.startsWith('ACTION: ')) {
        actionItems.push(line.substring(8));
      }
    }

    // Add last step if exists
    if (currentStep && currentStep.step && currentStep.thought) {
      steps.push(currentStep as ThinkingStep);
    }

    // Extract action items from final conclusion if not explicitly listed
    if (actionItems.length === 0 && finalConclusion) {
      const actionMatches = finalConclusion.match(/(?:next step|action|should|need to|recommend).*?(?:\.|$)/gi);
      if (actionMatches) {
        actionItems = actionMatches.slice(0, 3); // Limit to 3 main actions
      }
    }

    return {
      problem: originalQuery,
      steps,
      finalConclusion: finalConclusion || response,
      actionItems
    };
  }

  async isComplexQuery(query: string): Promise<boolean> {
    const complexityIndicators = [
      'how do I', 'step by step', 'help me', 'I need to', 'I want to',
      'problem', 'issue', 'troubleshoot', 'setup', 'configure',
      'multiple', 'several', 'both', 'also', 'and then',
      'but', 'however', 'although', 'complex', 'complicated'
    ];

    const queryLower = query.toLowerCase();
    const indicatorCount = complexityIndicators.filter(indicator => 
      queryLower.includes(indicator)
    ).length;

    // Consider complex if:
    // - Contains multiple complexity indicators
    // - Is longer than 20 words
    // - Contains question words with detailed context
    return indicatorCount >= 2 || 
           query.split(' ').length > 20 || 
           (queryLower.includes('?') && query.split(' ').length > 10);
  }
}

export default SequentialThinking;