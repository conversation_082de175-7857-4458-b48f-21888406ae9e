"use client"

import { Client, Wallet, xrpToDrops, dropsToXrp, Payment, Transaction } from 'xrpl'
import { pricingService } from './pricing-service'

// XRPL Configuration
export const XRPL_CONFIG = {
  // Mainnet: wss://xrplcluster.com
  // Testnet: wss://s.altnet.rippletest.net:51233
  SERVER_URL: process.env.NODE_ENV === 'production' 
    ? 'wss://xrplcluster.com' 
    : 'wss://s.altnet.rippletest.net:51233',
  DESTINATION_WALLET: 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx',
  FUSE_TOKEN: {
    currency: 'FUSE',
    issuer: 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo' // FUSE token issuer
  }
}

// VIP Card Pricing - USD base prices, XRP/FUSE calculated dynamically
export const VIP_CARD_PRICES = {
  'Monthly VIP Card': { usd: 9.99 },
  'Premium Card': { usd: 100 },
  'Gold Card': { usd: 250 },
  'Platinum Card': { usd: 500 },
  'Diamond Card': { usd: 1000 },
  'Obsidian Card': { usd: 1500 }
}

export interface PaymentRequest {
  cardType: string
  paymentMethod: 'XRP' | 'FUSE'
  amount: string
  userWallet?: string
}

export interface PaymentResponse {
  success: boolean
  transactionHash?: string
  error?: string
  paymentUrl?: string
}

export class XRPLPaymentService {
  private client: Client
  private connected: boolean = false

  constructor() {
    this.client = new Client(XRPL_CONFIG.SERVER_URL)
  }

  async connect(): Promise<void> {
    if (!this.connected) {
      await this.client.connect()
      this.connected = true
    }
  }

  async disconnect(): Promise<void> {
    if (this.connected) {
      await this.client.disconnect()
      this.connected = false
    }
  }

  // Get current prices using the pricing service
  async getCurrentPrices() {
    return await pricingService.getCurrentPrices()
  }

  // Calculate XRP amount needed for a given USD value
  async calculateXRPAmount(usdAmount: string): Promise<string> {
    return await pricingService.calculateXRPAmount(usdAmount)
  }

  // Calculate FUSE amount needed for a given USD value
  async calculateFUSEAmount(usdAmount: string): Promise<string> {
    return await pricingService.calculateFUSEAmount(usdAmount)
  }

  // Get VIP card prices in all currencies
  async getVIPCardPrices() {
    return await pricingService.getVIPCardPrices()
  }

  // Create a payment transaction for XRP
  createXRPPayment(senderAddress: string, amount: string): Payment {
    return {
      TransactionType: "Payment",
      Account: senderAddress,
      Destination: XRPL_CONFIG.DESTINATION_WALLET,
      Amount: xrpToDrops(amount),
      Memos: [{
        Memo: {
          MemoType: Buffer.from('fuse-vip-payment', 'utf8').toString('hex').toUpperCase(),
          MemoData: Buffer.from(JSON.stringify({
            service: 'fuse-vip',
            timestamp: Date.now()
          }), 'utf8').toString('hex').toUpperCase()
        }
      }]
    }
  }

  // Create a payment transaction for FUSE token
  createFUSEPayment(senderAddress: string, amount: string): Payment {
    return {
      TransactionType: "Payment",
      Account: senderAddress,
      Destination: XRPL_CONFIG.DESTINATION_WALLET,
      Amount: {
        currency: XRPL_CONFIG.FUSE_TOKEN.currency,
        issuer: XRPL_CONFIG.FUSE_TOKEN.issuer,
        value: amount
      },
      Memos: [{
        Memo: {
          MemoType: Buffer.from('fuse-vip-payment', 'utf8').toString('hex').toUpperCase(),
          MemoData: Buffer.from(JSON.stringify({
            service: 'fuse-vip',
            timestamp: Date.now(),
            currency: 'FUSE'
          }), 'utf8').toString('hex').toUpperCase()
        }
      }]
    }
  }

  // Verify a transaction hash
  async verifyTransaction(txHash: string): Promise<{
    verified: boolean
    amount?: string
    currency?: string
    sender?: string
    destination?: string
  }> {
    try {
      await this.connect()
      const response = await this.client.request({
        command: 'tx',
        transaction: txHash
      })

      if (response.result && response.result.TransactionType === 'Payment') {
        const tx = response.result
        return {
          verified: tx.meta?.TransactionResult === 'tesSUCCESS',
          amount: typeof tx.Amount === 'string' ? dropsToXrp(tx.Amount) : tx.Amount?.value,
          currency: typeof tx.Amount === 'string' ? 'XRP' : tx.Amount?.currency,
          sender: tx.Account,
          destination: tx.Destination
        }
      }

      return { verified: false }
    } catch (error) {
      console.error('Transaction verification failed:', error)
      return { verified: false }
    }
  }

  // Monitor transaction status
  async monitorTransaction(txHash: string, timeoutMs: number = 30000): Promise<boolean> {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeoutMs) {
      const result = await this.verifyTransaction(txHash)
      if (result.verified) {
        return true
      }
      
      // Wait 2 seconds before checking again
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
    
    return false
  }

  // Get account balance for both XRP and FUSE
  async getAccountBalance(address: string): Promise<{
    xrp: string
    fuse: string
  }> {
    try {
      await this.connect()
      
      const response = await this.client.request({
        command: 'account_lines',
        account: address
      })

      const accountInfo = await this.client.request({
        command: 'account_info',
        account: address
      })

      const xrpBalance = dropsToXrp(accountInfo.result?.account_data?.Balance || '0')
      
      // Find FUSE token balance
      const fuseBalance = response.result?.lines?.find(
        (line: any) => line.currency === XRPL_CONFIG.FUSE_TOKEN.currency && 
        line.account === XRPL_CONFIG.FUSE_TOKEN.issuer
      )?.balance || '0'

      return {
        xrp: xrpBalance,
        fuse: fuseBalance
      }
    } catch (error) {
      console.error('Failed to get account balance:', error)
      return { xrp: '0', fuse: '0' }
    }
  }
}

// Singleton instance
export const xrplService = new XRPLPaymentService()

// Utility functions
export const formatXRPAmount = (amount: string): string => {
  return `${parseFloat(amount).toFixed(6)} XRP`
}

export const formatFUSEAmount = (amount: string): string => {
  return `${parseFloat(amount).toFixed(2)} FUSE`
}

export const isValidXRPLAddress = (address: string): boolean => {
  return /^r[0-9A-Fa-f]{24,34}$/.test(address)
}