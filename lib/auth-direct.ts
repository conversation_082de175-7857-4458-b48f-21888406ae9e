/**
 * Direct authentication client to bypass Supabase auth service 503 errors
 */

interface AuthResponse {
  access_token?: string
  token_type?: string
  expires_in?: number
  user?: any
  error?: string
}

interface DirectAuthClient {
  signIn: (email: string, password: string) => Promise<AuthResponse>
  signUp: (email: string, password: string, userData?: any) => Promise<AuthResponse>
  updatePassword: (newPassword: string, accessToken: string) => Promise<AuthResponse>
  getUser: (email: string) => Promise<AuthResponse>
}

class DirectAuth implements DirectAuthClient {
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/auth-direct'
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      console.log('🔄 Direct auth sign in attempt for:', email)
      
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'sign_in',
          email,
          password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        console.error('❌ Direct auth sign in failed:', data.error)
        return { error: data.error || 'Sign in failed' }
      }

      console.log('✅ Direct auth sign in successful')
      
      // Store token in localStorage for client-side access
      if (typeof window !== 'undefined' && data.access_token) {
        localStorage.setItem('supabase.auth.token', JSON.stringify({
          access_token: data.access_token,
          token_type: data.token_type,
          expires_in: data.expires_in,
          expires_at: Date.now() + (data.expires_in * 1000),
          user: data.user
        }))
      }

      return data
    } catch (error) {
      console.error('❌ Direct auth sign in error:', error)
      return { error: 'Network error during sign in' }
    }
  }

  async signUp(email: string, password: string, userData: any = {}): Promise<AuthResponse> {
    try {
      console.log('🔄 Direct auth sign up attempt for:', email)
      
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'sign_up',
          email,
          password,
          userData,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        console.error('❌ Direct auth sign up failed:', data.error)
        return { error: data.error || 'Sign up failed' }
      }

      console.log('✅ Direct auth sign up successful')
      
      // Store token in localStorage for client-side access
      if (typeof window !== 'undefined' && data.access_token) {
        localStorage.setItem('supabase.auth.token', JSON.stringify({
          access_token: data.access_token,
          token_type: data.token_type,
          expires_in: data.expires_in,
          expires_at: Date.now() + (data.expires_in * 1000),
          user: data.user
        }))
      }

      return data
    } catch (error) {
      console.error('❌ Direct auth sign up error:', error)
      return { error: 'Network error during sign up' }
    }
  }

  async updatePassword(newPassword: string, accessToken: string): Promise<AuthResponse> {
    try {
      console.log('🔄 Direct auth password update attempt')
      
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          action: 'update_password',
          newPassword,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        console.error('❌ Direct auth password update failed:', data.error)
        return { error: data.error || 'Password update failed' }
      }

      console.log('✅ Direct auth password update successful')
      return data
    } catch (error) {
      console.error('❌ Direct auth password update error:', error)
      return { error: 'Network error during password update' }
    }
  }

  async getUser(email: string): Promise<AuthResponse> {
    try {
      console.log('🔄 Direct auth get user attempt for:', email)
      
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get_user',
          email,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        console.error('❌ Direct auth get user failed:', data.error)
        return { error: data.error || 'Get user failed' }
      }

      console.log('✅ Direct auth get user successful')
      return data
    } catch (error) {
      console.error('❌ Direct auth get user error:', error)
      return { error: 'Network error during get user' }
    }
  }

  // Helper to get stored token from localStorage
  getStoredToken(): any {
    if (typeof window === 'undefined') return null
    
    try {
      const stored = localStorage.getItem('supabase.auth.token')
      if (!stored) return null
      
      const parsed = JSON.parse(stored)
      
      // Check if token is expired
      if (parsed.expires_at && Date.now() > parsed.expires_at) {
        localStorage.removeItem('supabase.auth.token')
        return null
      }
      
      return parsed
    } catch (error) {
      console.error('Error parsing stored token:', error)
      localStorage.removeItem('supabase.auth.token')
      return null
    }
  }

  // Helper to clear stored token
  clearStoredToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('supabase.auth.token')
    }
  }
}

// Export singleton instance
export const directAuth = new DirectAuth()

// Helper function to check if we should use direct auth (when Supabase auth is down)
export function shouldUseDirectAuth(): boolean {
  // You can add logic here to detect when Supabase auth is down
  // For now, we'll use an environment variable or localStorage flag
  if (typeof window !== 'undefined') {
    return localStorage.getItem('use_direct_auth') === 'true'
  }
  return process.env.USE_DIRECT_AUTH === 'true'
}

// Helper to enable/disable direct auth mode
export function setDirectAuthMode(enabled: boolean): void {
  if (typeof window !== 'undefined') {
    if (enabled) {
      localStorage.setItem('use_direct_auth', 'true')
    } else {
      localStorage.removeItem('use_direct_auth')
    }
  }
}
