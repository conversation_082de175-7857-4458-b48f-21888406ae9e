// Network ban log analysis for legitimate traffic validation

export interface NetworkBanLogEntry {
  event_message: string
  id: string
  metadata: Array<{
    request: Array<{
      cf: Array<{
        asOrganization: string
        asn: number
        botManagement: Array<{
          corporateProxy: boolean
          detectionIds: any[]
          ja3Hash: string
          jsDetection: Array<{ passed: boolean }>
          score: number
          staticResource: boolean
          verifiedBot: boolean
        }>
        city: string
        country: string
        clientTrustScore: number
      }>
      headers: Array<{
        user_agent: string
        cf_connecting_ip: string
        host: string
        method: string
        content_type: string
      }>
      sb: Array<{
        jwt: Array<{
          authorization: Array<{
            payload: Array<{
              role: string
              issuer: string
              signature_prefix: string
            }>
          }>
        }>
      }>
    }>
  }>
}

/**
 * Analyze network ban log entry to determine if traffic is legitimate
 */
export function analyzeNetworkBanEntry(logEntry: NetworkBanLogEntry): {
  isLegitimate: boolean
  riskLevel: 'low' | 'medium' | 'high'
  reasons: string[]
  recommendations: string[]
} {
  const reasons: string[] = []
  const recommendations: string[] = []
  let isLegitimate = true
  let riskLevel: 'low' | 'medium' | 'high' = 'low'

  const request = logEntry.metadata[0]?.request[0]
  if (!request) {
    return {
      isLegitimate: false,
      riskLevel: 'high',
      reasons: ['Malformed request metadata'],
      recommendations: ['Block this traffic pattern']
    }
  }

  const cf = request.cf[0]
  const headers = request.headers[0]
  const sb = request.sb[0]

  // Check Cloudflare bot management score
  if (cf.botManagement) {
    const botData = cf.botManagement[0]
    if (botData.score < 50) {
      reasons.push(`Low bot management score: ${botData.score}`)
      riskLevel = 'high'
      isLegitimate = false
    } else if (botData.score < 80) {
      reasons.push(`Medium bot management score: ${botData.score}`)
      riskLevel = 'medium'
    }
    
    if (!botData.jsDetection[0].passed) {
      reasons.push('JavaScript detection failed')
      riskLevel = 'medium'
    }
  }

  // Check user agent - this looks like Supabase internal traffic
  if (headers.user_agent === 'supabase-api/3be831b') {
    reasons.push('Supabase internal API traffic detected')
    // This is likely legitimate internal traffic
    isLegitimate = true
    riskLevel = 'low'
  }

  // Check JWT authentication
  if (sb.jwt) {
    const auth = sb.jwt[0].authorization[0]
    if (auth.payload[0].role === 'supabase_admin') {
      reasons.push('Valid Supabase admin role detected')
      isLegitimate = true
    }
    
    if (auth.payload[0].issuer === 'supabase') {
      reasons.push('Valid Supabase JWT issuer')
      isLegitimate = true
    }
  }

  // Check IP and location
  if (cf.asOrganization === 'Amazon Technologies Inc.' && cf.asn === 16509) {
    reasons.push('Traffic from AWS infrastructure (legitimate for Supabase)')
    isLegitimate = true
  }

  // Check endpoint accessed
  if (headers.host === 'haqbtbpmyadkocakqnew.supabase.co' && 
      logEntry.event_message.includes('/admin/v1/network-bans/retrieve')) {
    reasons.push('Accessing network ban retrieval endpoint')
    // This could be legitimate admin traffic checking for bans
    isLegitimate = true
  }

  // Generate recommendations
  if (isLegitimate) {
    recommendations.push('Traffic appears legitimate - consider allowlisting this pattern')
    if (riskLevel === 'medium') {
      recommendations.push('Monitor for unusual patterns but allow')
    }
  } else {
    recommendations.push('Consider blocking this traffic pattern')
    recommendations.push('Investigate source IP and user agent patterns')
  }

  return {
    isLegitimate,
    riskLevel,
    reasons,
    recommendations
  }
}

/**
 * Specific analysis for the provided log entry
 */
export function analyzeProvidedLogEntry(): {
  analysis: ReturnType<typeof analyzeNetworkBanEntry>
  summary: string
} {
  // The log entry you provided
  const logEntry: NetworkBanLogEntry = {
    event_message: "POST | 200 | ************ | 956b81523954f98f | https://haqbtbpmyadkocakqnew.supabase.co/admin/v1/network-bans/retrieve | supabase-api/3be831b",
    id: "dca7e02d-acd8-493c-ac73-eee876a8e2c8",
    metadata: [{
      request: [{
        cf: [{
          asOrganization: "Amazon Technologies Inc.",
          asn: 16509,
          botManagement: [{
            corporateProxy: false,
            detectionIds: [],
            ja3Hash: "1a28e69016765d92e3b381168d68922c",
            jsDetection: [{ passed: false }],
            score: 83,
            staticResource: false,
            verifiedBot: false
          }],
          city: "San Jose",
          country: "US",
          clientTrustScore: 83
        }],
        headers: [{
          user_agent: "supabase-api/3be831b",
          cf_connecting_ip: "************",
          host: "haqbtbpmyadkocakqnew.supabase.co",
          method: "POST",
          content_type: "application/json"
        }],
        sb: [{
          jwt: [{
            authorization: [{
              payload: [{
                role: "supabase_admin",
                issuer: "supabase",
                signature_prefix: "C869LA"
              }]
            }]
          }]
        }]
      }]
    }]
  }

  const analysis = analyzeNetworkBanEntry(logEntry)
  
  const summary = `
ANALYSIS SUMMARY:
- This is LEGITIMATE Supabase internal traffic
- User-Agent: supabase-api/3be831b (Supabase internal API)
- JWT Role: supabase_admin (valid admin access)
- Source: AWS infrastructure (************, Amazon Technologies Inc.)
- Bot Score: 83/100 (good score, though JS detection failed)
- Endpoint: /admin/v1/network-bans/retrieve (checking for network bans)

VERDICT: This is NOT rogue employee traffic - it's Supabase's internal infrastructure
checking your network ban settings. This traffic should be ALLOWED.
  `.trim()

  return { analysis, summary }
}