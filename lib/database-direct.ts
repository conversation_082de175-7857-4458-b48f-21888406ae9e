/**
 * Direct PostgreSQL Database Client
 * Bypasses PostgREST completely for reliable database access
 * Enhanced with optimized pool configurations for PGRST002 error mitigation
 */

import { createPoolConfig, PoolHealthMonitor, POOL_CONFIGS } from './database-pool-config'

// Dynamic imports for server-side only
let Pool: any = null;

// Create a connection pool for direct database access
let pool: any = null;
let isPoolInitializing = false;

async function initPgTypes() {
  if (typeof window !== 'undefined') {
    throw new Error('Direct database connection is server-side only')
  }

  if (!Pool) {
    const pg = await import('pg')
    Pool = pg.Pool
  }

  return { Pool }
}

async function createPool(): Promise<any> {
  if (!pool && !isPoolInitializing) {
    isPoolInitializing = true;

    await initPgTypes();

    try {
      // Build connection string with proper password substitution
      const dbPassword = process.env.SUPABASE_DB_PASSWORD || process.env.POSTGRES_PASSWORD;
      
      // During build time, environment variables might not be available
      // This is normal for static generation and shouldn't cause the build to fail
      if (!dbPassword && !process.env.DATABASE_URL) {
        console.warn('⚠️ Database credentials not found - direct database access disabled');
        console.warn('⚠️ This is normal during build time, but should be available at runtime');
        isPoolInitializing = false;
        return null;
      }

      // Try multiple connection methods with optimized pool configurations
      const connectionConfigs = [
        // Primary: High-performance configuration for PGRST002 handling
        createPoolConfig(dbPassword, 'HIGH_PERFORMANCE', {
          application_name: 'fuse-vip-primary'
        }),
        // Fallback: Standard configuration with connection string from environment
        {
          connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URL,
          ssl: { rejectUnauthorized: false }, // Always use SSL for Supabase
          ...POOL_CONFIGS.STANDARD
        }
      ];

      let lastError: any = null;

      for (const config of connectionConfigs) {
        try {
          console.log('🔗 Attempting database connection with config:', {
            host: config.host || 'connection string',
            port: config.port || 'default'
          });

          pool = new Pool(config);

          // Test the connection
          const testClient = await pool.connect();
          await testClient.query('SELECT 1');
          testClient.release();

          console.log('✅ Database connection successful');
          break;
        } catch (error: any) {
          console.error('❌ Database connection failed:', error.message);
          lastError = error;

          if (pool) {
            try {
              await pool.end();
            } catch (endError) {
              console.error('Error ending failed pool:', endError);
            }
            pool = null;
          }
        }
      }

      if (!pool) {
        console.error('❌ All database connection attempts failed. Last error:', lastError?.message);
        isPoolInitializing = false;
        return null;
      }

      // Configure pool with better error handling and connection lifecycle management
      pool.on('error', (err: any) => {
        console.error('🔥 Database pool error:', err.message);
        if (err.message?.includes('ENOTFOUND') || err.message?.includes('ECONNREFUSED')) {
          console.error('🌐 DNS/Connection issue detected. Consider using IP address or checking network.');
        }
        if (err.message?.includes('too many connections')) {
          console.error('🚨 Connection limit exceeded. Scaling down operations.');
        }
      });

      pool.on('connect', (client: any) => {
        console.log('🔌 New client connected to database pool');
        // Set connection-level timeout and keepalive to prevent hanging connections
        client.query('SET statement_timeout = 120000'); // 2 minutes
        client.query('SET idle_in_transaction_session_timeout = 300000'); // 5 minutes
        
        // Enable TCP keepalive to prevent client disconnections
        if (client.connection && client.connection.stream) {
          client.connection.stream.setKeepAlive(true, 60000); // Keep alive every 60 seconds
        }
      });

      pool.on('remove', () => {
        console.log('🔌 Client removed from database pool');
      });

      // Add enhanced connection health monitoring with pool metrics
      setInterval(async () => {
        try {
          const client = await pool.connect();
          await client.query('SELECT 1');
          client.release();

          // Record pool health metrics
          PoolHealthMonitor.recordPoolStats('main_pool', {
            totalConnections: pool.totalCount || 0,
            activeConnections: pool.totalCount - pool.idleCount || 0,
            idleConnections: pool.idleCount || 0,
            waitingClients: pool.waitingCount || 0
          });

        } catch (err) {
          console.warn('⚠️ Pool health check failed:', err);

          // Log pool health status on failure
          const health = PoolHealthMonitor.getPoolHealth('main_pool');
          if (health.status !== 'healthy') {
            console.warn('🏥 Pool health status:', health.status);
            console.warn('📋 Recommendations:', health.recommendations);
          }
        }
      }, 60000); // Check every minute

      console.log('🏊 Database pool created successfully');
    } catch (error) {
      console.error('❌ Failed to create database pool:', error);
      throw error;
    } finally {
      isPoolInitializing = false;
    }
  }

  // Return null if pool creation failed (e.g., during build time)
  if (!pool) {
    console.warn('⚠️ Database pool not available - this is normal during build time');
    return null;
  }

  return pool;
}

/**
 * Execute a SQL query with parameters
 */
export async function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<{ data: T[] | null; error: string | null }> {
  // Ensure this only runs on server-side
  if (typeof window !== 'undefined') {
    console.warn('⚠️ Direct database queries can only be executed on the server');
    return {
      data: null,
      error: 'Direct database queries are server-side only'
    };
  }

  // During build time, skip database queries for static generation
  if (!process.env.SUPABASE_DB_PASSWORD) {
    console.warn('⚠️ Database queries skipped - SUPABASE_DB_PASSWORD not available');
    return {
      data: [],
      error: null
    };
  }

  // Check if we're in a build context (Vercel build, npm run build, etc.)
  if (process.env.VERCEL_ENV === 'preview' || process.env.CI === 'true' || process.env.NODE_ENV === 'production') {
    // During build, return empty data instead of failing
    if (!process.env.SUPABASE_DB_PASSWORD) {
      console.warn('⚠️ Build-time database access skipped - returning empty data');
      return {
        data: [],
        error: null
      };
    }
  }

  let client: any = null;
  const startTime = Date.now();

  try {
    const dbPool = await createPool();

    if (!dbPool) {
      console.warn('⚠️ Direct database connection not available - returning empty data for build compatibility');
      return {
        data: [],
        error: null
      };
    }

    // Get client from pool with extended timeout to prevent client timeouts
    const connectionPromise = dbPool.connect();
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Connection timeout after 2 minutes')), 120000); // Increased to 2 minutes
    });

    client = await Promise.race([connectionPromise, timeoutPromise]);

    console.log('🔗 Executing direct SQL query:', query.substring(0, 100) + '...');

    // Execute query with extended timeout to prevent client timeouts
    const queryPromise = client.query(query, params);
    const queryTimeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Query timeout after 2 minutes')), 120000); // Increased to 2 minutes
    });

    const result = await Promise.race([queryPromise, queryTimeoutPromise]);

    const duration = Date.now() - startTime;
    const rowCount = result.rowCount || result.rows?.length || 0;

    console.log(`✅ Direct SQL query successful, rows: ${rowCount}, duration: ${duration}ms`);

    // Log performance metrics for monitoring
    try {
      // Extract table name from query for better monitoring
      const tableMatch = query.match(/FROM\s+(\w+)/i);
      const tableName = tableMatch ? tableMatch[1] : 'unknown';

      // Log to performance monitoring system
      if (typeof window === 'undefined') {
        // Only import on server side
        const { performanceMonitor } = await import('./performance-monitor');
        performanceMonitor.trackDatabaseQuery(
          query.substring(0, 100), // Truncate long queries
          tableName,
          query.trim().toUpperCase().startsWith('SELECT') ? 'SELECT' :
          query.trim().toUpperCase().startsWith('INSERT') ? 'INSERT' :
          query.trim().toUpperCase().startsWith('UPDATE') ? 'UPDATE' :
          query.trim().toUpperCase().startsWith('DELETE') ? 'DELETE' : 'SELECT',
          duration,
          rowCount
        );
      }

      // Log to database performance table if query was slow
      if (duration > 1000) { // Log queries slower than 1 second
        const logQuery = `SELECT log_query_performance($1, $2, $3, $4)`;
        const logParams = [
          query.substring(0, 100),
          duration,
          rowCount,
          JSON.stringify({ params: params.length })
        ];

        // Don't await this to avoid blocking the main query
        client.query(logQuery, logParams).catch((err: any) => {
          console.warn('Failed to log query performance:', err);
        });
      }
    } catch (monitoringError) {
      console.warn('Performance monitoring error:', monitoringError);
    }

    return {
      data: result.rows as T[],
      error: null
    };
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`❌ Direct SQL query failed after ${duration}ms:`, error.message);

    // Handle specific error types
    if (error.message.includes('Connection timeout') || error.message.includes('Query timeout')) {
      return {
        data: null,
        error: 'Database operation timed out. Please try again.'
      };
    }

    if (error.code === 'ECONNREFUSED') {
      return {
        data: null,
        error: 'Database connection refused. Please check database availability.'
      };
    }

    return {
      data: null,
      error: error.message || 'Database query failed'
    };
  } finally {
    if (client) {
      try {
        client.release();
      } catch (releaseError) {
        console.error('❌ Error releasing database client:', releaseError);
      }
    }
  }
}

/**
 * Fallback to Supabase REST API when direct connection fails
 */
async function getProfileViaSupabaseAPI(userId: string): Promise<any | null> {
  try {
    console.log('🔄 Falling back to Supabase REST API for profile');

    // Check if we're in a server environment
    if (typeof window !== 'undefined') {
      console.warn('⚠️ Cannot use server-side Supabase client in browser environment');
      return null;
    }

    const { createServerSupabaseClient } = await import('@/lib/supabase/server-only');
    const supabase = await createServerSupabaseClient();

    const { data, error } = await supabase
      .from('profiles')
      .select('id, first_name, last_name, user_email, is_card_holder')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Supabase API fallback error:', error);
      return null;
    }

    console.log('✅ Profile retrieved via Supabase API fallback');
    return data;
  } catch (error) {
    console.error('Supabase API fallback failed:', error);
    return null;
  }
}

/**
 * Get minimal user profile by ID using direct SQL - only essential fields for initial login
 */
export async function getUserProfileMinimal(userId: string): Promise<any | null> {
  if (!userId) {
    console.error('getUserProfileMinimal: No userId provided');
    return null;
  }

  console.log(`👤 Fetching MINIMAL profile directly from database for user: ${userId}`);

  const query = `
    SELECT
      id,
      first_name,
      last_name,
      user_email,
      is_card_holder
    FROM profiles
    WHERE id = $1
  `;

  const { data, error } = await executeQuery(query, [userId]);

  if (error) {
    console.error('getUserProfileMinimal direct query error:', error);

    // Fallback to Supabase REST API if direct connection fails
    console.log('🔄 Attempting Supabase API fallback...');
    return await getProfileViaSupabaseAPI(userId);
  }

  if (data && data.length > 0) {
    console.log(`✅ Minimal profile found directly for user: ${userId}`);
    return data[0];
  }

  console.log(`ℹ️ No minimal profile found directly, trying API fallback...`);
  return await getProfileViaSupabaseAPI(userId);
}

/**
 * Get user profile by ID using direct SQL - full profile data
 */
export async function getUserProfileDirect(userId: string): Promise<any | null> {
  if (!userId) {
    console.error('getUserProfileDirect: No userId provided');
    return null;
  }

  console.log(`👤 Fetching FULL profile directly from database for user: ${userId}`);

  const query = `
    SELECT
      id,
      first_name,
      last_name,
      user_email,
      is_card_holder,
      is_business_applicant,
      card_tier,
      phone,
      xrp_wallet_address,
      membership_start_date,
      membership_end_date,
      referring_business_id,
      created_at
    FROM profiles
    WHERE id = $1
  `;

  const { data, error } = await executeQuery(query, [userId]);

  if (error) {
    console.error('getUserProfileDirect error:', error);
    return null;
  }

  if (data && data.length > 0) {
    console.log(`✅ Full profile found directly for user: ${userId}`);
    return data[0];
  }

  console.log(`ℹ️ No full profile found directly for user: ${userId}`);
  return null;
}

/**
 * Check if user has any businesses - minimal query for auth context
 */
export async function checkUserHasBusinessDirect(userId: string): Promise<boolean> {
  if (!userId) {
    console.error('checkUserHasBusinessDirect: No userId provided');
    return false;
  }

  console.log(`🏢 Checking if user has business for user: ${userId}`);

  const query = `
    SELECT 1
    FROM businesses
    WHERE user_id = $1 AND is_active = true
    LIMIT 1
  `;

  const { data, error } = await executeQuery(query, [userId]);

  if (error) {
    console.error('checkUserHasBusinessDirect error:', error);
    return false;
  }

  const hasBusiness = !!(data && data.length > 0);
  console.log(`✅ User ${userId} has business: ${hasBusiness}`);
  return hasBusiness;
}

/**
 * Get user businesses by user ID using direct SQL
 */
export async function getUserBusinessesDirect(userId: string): Promise<any[] | null> {
  if (!userId) {
    console.error('getUserBusinessesDirect: No userId provided');
    return null;
  }

  console.log(`🏢 Fetching businesses directly from database for user: ${userId}`);

  const query = `
    SELECT
      id,
      name,
      logo_url,
      website,
      category,
      premium_discount,
      is_active,
      business_spotlight,
      contact_info,
      business_address,
      created_at
    FROM businesses
    WHERE user_id = $1 AND is_active = true
    ORDER BY created_at DESC
  `;

  const { data, error } = await executeQuery(query, [userId]);

  if (error) {
    console.error('getUserBusinessesDirect error:', error);
    return null;
  }

  console.log(`✅ Found ${data?.length || 0} businesses directly for user: ${userId}`);
  return data || [];
}

/**
 * Get all active businesses using direct SQL
 */
export async function getActiveBusinessesDirect(): Promise<any[] | null> {
  console.log('🏢 Fetching all active businesses directly from database');

  const query = `
    SELECT
      id,
      name,
      logo_url,
      website,
      category,
      premium_discount,
      is_active,
      business_spotlight,
      contact_name,
      contact_email,
      contact_phone,
      business_address,
      created_at
    FROM businesses
    WHERE is_active = true
    ORDER BY created_at DESC
  `;

  const { data, error } = await executeQuery(query, []);

  if (error) {
    console.error('getActiveBusinessesDirect error:', error);
    return null;
  }

  console.log(`✅ Found ${data?.length || 0} active businesses directly`);
  return data || [];
}

/**
 * Create or update user profile using direct SQL
 */
export async function upsertUserProfileDirect(userId: string, profileData: any): Promise<any | null> {
  if (!userId) {
    console.error('upsertUserProfileDirect: No userId provided');
    return null;
  }

  console.log(`💾 Upserting profile directly for user: ${userId}`);

  const query = `
    INSERT INTO profiles (
      id, first_name, last_name, phone, user_email, 
      is_card_holder, is_business_applicant, xrp_wallet_address
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    ON CONFLICT (id) DO UPDATE SET
      first_name = EXCLUDED.first_name,
      last_name = EXCLUDED.last_name,
      phone = EXCLUDED.phone,
      user_email = EXCLUDED.user_email,
      is_card_holder = EXCLUDED.is_card_holder,
      is_business_applicant = EXCLUDED.is_business_applicant,
      xrp_wallet_address = EXCLUDED.xrp_wallet_address
    RETURNING *
  `;

  const params = [
    userId,
    profileData.first_name || null,
    profileData.last_name || null,
    profileData.phone || null,
    profileData.user_email || null,
    profileData.is_card_holder || false,
    profileData.is_business_applicant || false,
    profileData.xrp_wallet_address || null
  ];

  const { data, error } = await executeQuery(query, params);

  if (error) {
    console.error('upsertUserProfileDirect error:', error);
    return null;
  }

  if (data && data.length > 0) {
    console.log(`✅ Profile upserted directly for user: ${userId}`);
    return data[0];
  }

  return null;
}

/**
 * Get QR interactions for a user using direct SQL
 */
export async function getUserQRInteractionsDirect(userId: string): Promise<any[] | null> {
  if (!userId) return null;

  const query = `
    SELECT
      qi.id,
      qi.scanned_business_id,
      qi.created_at,
      qi.interaction_metadata,
      b.name as business_name,
      b.category as business_category,
      b.logo_url as business_logo_url
    FROM qr_interactions qi
    LEFT JOIN businesses b ON qi.scanned_business_id = b.id
    WHERE qi.scanner_user_id = $1
      AND qi.interaction_type = 'business_scan'
    ORDER BY qi.created_at DESC
  `;

  const { data, error } = await executeQuery(query, [userId]);

  if (error) {
    console.error('getUserQRInteractionsDirect error:', error);
    return null;
  }

  return data || [];
}

/**
 * Create profile using direct SQL - ensures ID matches auth.users ID
 */
export async function createProfileDirect(profileData: any): Promise<any | null> {
  console.log(`📝 Creating profile directly for user: ${profileData.id}`);

  // Ensure the profile ID matches the auth user ID exactly
  if (!profileData.id) {
    console.error('❌ Profile creation failed: No user ID provided');
    return null;
  }

  const query = `
    INSERT INTO profiles (
      id, first_name, last_name, phone, user_email,
      is_card_holder, is_business_applicant, xrp_wallet_address,
      card_tier, created_at, referring_business_id
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    ON CONFLICT (id) DO UPDATE SET
      first_name = EXCLUDED.first_name,
      last_name = EXCLUDED.last_name,
      phone = EXCLUDED.phone,
      user_email = EXCLUDED.user_email,
      is_business_applicant = EXCLUDED.is_business_applicant,
      xrp_wallet_address = EXCLUDED.xrp_wallet_address,
      referring_business_id = EXCLUDED.referring_business_id
    RETURNING *
  `;

  const params = [
    profileData.id, // This MUST match the auth.users.id
    profileData.first_name || null,
    profileData.last_name || null,
    profileData.phone || null,
    profileData.user_email || null,
    profileData.is_card_holder || false,
    profileData.is_business_applicant || false,
    profileData.xrp_wallet_address || null,
    profileData.card_tier || 'Premium',
    profileData.created_at || new Date().toISOString(),
    profileData.referring_business_id || null
  ];

  const { data, error } = await executeQuery(query, params);

  if (error) {
    console.error('❌ createProfileDirect error:', error);
    return null;
  }

  console.log('✅ Profile created/updated successfully:', data?.[0]?.id);
  return data && data.length > 0 ? data[0] : null;
}

/**
 * Ensure user has a profile - create if missing
 */
export async function ensureUserProfileDirect(userId: string, userData: any = {}): Promise<any | null> {
  console.log(`🔍 Ensuring profile exists for user: ${userId}`);

  // First try to get existing profile
  const existingProfile = await getUserProfileDirect(userId);

  if (existingProfile) {
    console.log('✅ Profile already exists for user:', userId);
    return existingProfile;
  }

  // Create new profile if it doesn't exist
  console.log('📝 Creating new profile for user:', userId);
  return await createProfileDirect({
    id: userId,
    first_name: userData.first_name || null,
    last_name: userData.last_name || null,
    phone: userData.phone || null,
    user_email: userData.email || null,
    is_card_holder: false,
    is_business_applicant: userData.is_business_applicant || false,
    xrp_wallet_address: null,
    card_tier: 'Premium',
    created_at: new Date().toISOString(),
    referring_business_id: userData.referring_business_id || null
  });
}

/**
 * Update user profile using direct SQL
 */
export async function updateUserProfileDirect(userId: string, updateData: any): Promise<any | null> {
  console.log(`📝 Updating profile directly for user: ${userId}`);

  if (!userId) {
    console.error('❌ Profile update failed: No user ID provided');
    return null;
  }

  // Build dynamic update query based on provided fields
  const updateFields = [];
  const params = [userId];
  let paramIndex = 2;

  for (const [key, value] of Object.entries(updateData)) {
    if (value !== undefined && value !== null) {
      updateFields.push(`${key} = $${paramIndex}`);
      params.push(value as any);
      paramIndex++;
    }
  }

  if (updateFields.length === 0) {
    console.log('⚠️ No fields to update');
    return null;
  }

  const query = `
    UPDATE profiles
    SET ${updateFields.join(', ')}, updated_at = NOW()
    WHERE id = $1
    RETURNING *
  `;

  const { data, error } = await executeQuery(query, params);

  if (error) {
    console.error('❌ updateUserProfileDirect error:', error);
    return null;
  }

  console.log('✅ Profile updated successfully:', data?.[0]?.id);
  return data && data.length > 0 ? data[0] : null;
}

/**
 * Get user's business using direct SQL
 */
export async function getUserBusinessDirect(userId: string): Promise<any | null> {
  console.log(`🏢 Getting business directly for user: ${userId}`);

  if (!userId) {
    console.error('❌ Business fetch failed: No user ID provided');
    return null;
  }

  const query = `
    SELECT *
    FROM businesses
    WHERE user_id = $1 AND is_active = true
    LIMIT 1
  `;

  const { data, error } = await executeQuery(query, [userId]);

  if (error) {
    console.error('❌ getUserBusinessDirect error:', error);
    return null;
  }

  if (!data || data.length === 0) {
    console.log('📭 No business found for user:', userId);
    return null;
  }

  console.log('✅ Business found:', data[0].name);
  return data[0];
}

/**
 * Create business application using direct SQL
 */
export async function createBusinessApplicationDirect(applicationData: any): Promise<any | null> {
  console.log(`📝 Creating business application for user: ${applicationData.user_id}`);

  if (!applicationData.user_id) {
    console.error('❌ Business application creation failed: No user ID provided');
    return null;
  }

  const query = `
    INSERT INTO network_applications (
      user_id, business_name, website, category,
      contact_name, contact_email, contact_phone, proposed_discount,
      business_address, logo_url, loyalty_reward_frequency,
      status, created_at, updated_at, logo_data
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
    RETURNING *
  `;

  const params = [
    applicationData.user_id,
    applicationData.business_name || applicationData.businessName,
    applicationData.website || applicationData.business_website || null,
    applicationData.category || applicationData.business_category,
    applicationData.contact_name || applicationData.contactName || null,
    applicationData.contact_email || applicationData.contactEmail || null,
    applicationData.contact_phone || applicationData.contactPhone || null,
    applicationData.proposed_discount || applicationData.proposedDiscount,
    applicationData.business_address || applicationData.businessAddress || null,
    applicationData.logo_url || applicationData.logoUrl || null,
    applicationData.loyalty_reward_frequency || applicationData.loyaltyRewardFrequency || 'monthly',
    'pending',
    new Date().toISOString(),
    new Date().toISOString(),
    applicationData.logo_data || null
  ];

  const { data, error } = await executeQuery(query, params);

  if (error) {
    console.error('❌ createBusinessApplicationDirect error:', error);
    return null;
  }

  console.log('✅ Business application created successfully:', data?.[0]?.id);
  return data && data.length > 0 ? data[0] : null;
}

/**
 * Search businesses using direct SQL
 */
export async function searchBusinessesDirect(searchTerm?: string, limit: number = 100): Promise<any[] | null> {
  let query = `
    SELECT id, name, category, logo_url, website, premium_discount
    FROM businesses
    WHERE is_active = true
  `;

  const params: any[] = [];

  if (searchTerm) {
    query += ` AND name ILIKE $1`;
    params.push(`%${searchTerm}%`);
  }

  query += ` ORDER BY name LIMIT $${params.length + 1}`;
  params.push(limit);

  const { data, error } = await executeQuery(query, params);

  if (error) {
    console.error('searchBusinessesDirect error:', error);
    return null;
  }

  return data || [];
}

/**
 * Get active QR codes for users using direct SQL
 */
export async function getActiveUserQRCodesDirect(userIds: string[]): Promise<any[]> {
  if (!userIds || userIds.length === 0) {
    return [];
  }

  console.log(`🔍 Fetching QR codes directly for ${userIds.length} users`);

  const query = `
    SELECT
      user_id,
      qr_data,
      qr_code_url,
      is_active,
      created_at,
      updated_at
    FROM user_qr_codes
    WHERE user_id = ANY($1) AND is_active = true
  `;

  const { data, error } = await executeQuery(query, [userIds]);

  if (error) {
    console.error('getActiveUserQRCodesDirect error:', error);
    return [];
  }

  console.log(`✅ Found ${data?.length || 0} active QR codes`);
  return data || [];
}

/**
 * Get QR code for single user using direct SQL
 */
export async function getUserQRCodeDirect(userId: string): Promise<any | null> {
  if (!userId) {
    return null;
  }

  console.log(`🔍 Fetching QR code directly for user: ${userId}`);

  const query = `
    SELECT
      user_id,
      qr_data,
      qr_code_url,
      is_active,
      created_at,
      updated_at
    FROM user_qr_codes
    WHERE user_id = $1 AND is_active = true
    LIMIT 1
  `;

  const { data, error } = await executeQuery(query, [userId]);

  if (error) {
    console.error('getUserQRCodeDirect error:', error);
    return null;
  }

  if (data && data.length > 0) {
    console.log(`✅ QR code found for user: ${userId}`);
    return data[0];
  }

  console.log(`ℹ️ No QR code found for user: ${userId}`);
  return null;
}

/**
 * Upsert QR code for user using direct SQL
 */
export async function upsertUserQRCodeDirect(userId: string, qrData: string, qrCodeUrl: string): Promise<any | null> {
  if (!userId || !qrData || !qrCodeUrl) {
    console.error('upsertUserQRCodeDirect: Missing required parameters');
    return null;
  }

  console.log(`💾 Upserting QR code directly for user: ${userId}`);

  const query = `
    INSERT INTO user_qr_codes (
      user_id, qr_data, qr_code_url, is_active, created_at, updated_at
    ) VALUES ($1, $2, $3, true, NOW(), NOW())
    ON CONFLICT (user_id) DO UPDATE SET
      qr_data = EXCLUDED.qr_data,
      qr_code_url = EXCLUDED.qr_code_url,
      is_active = EXCLUDED.is_active,
      updated_at = NOW()
    RETURNING *
  `;

  const params = [userId, qrData, qrCodeUrl];

  const { data, error } = await executeQuery(query, params);

  if (error) {
    console.error('upsertUserQRCodeDirect error:', error);
    return null;
  }

  if (data && data.length > 0) {
    console.log(`✅ QR code upserted for user: ${userId}`);
    return data[0];
  }

  return null;
}

/**
 * Close the database pool (for cleanup)
 */
export async function closeDatabasePool(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('🔒 Database pool closed');
  }
}
