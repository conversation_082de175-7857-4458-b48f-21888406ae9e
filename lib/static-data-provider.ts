/**
 * Static Data Provider
 * 
 * Serves pre-generated static businesses data instead of making runtime API calls.
 * This eliminates database connection pool issues and API rate limiting.
 */

import { BusinessData } from '@/types/business';

// Cache for static data to avoid repeated file reads
const staticDataCache = new Map<string, any>();

/**
 * Load static data from pre-generated JSON files
 */
async function loadStaticData<T>(filename: string): Promise<T | null> {
  // Check cache first
  if (staticDataCache.has(filename)) {
    return staticDataCache.get(filename);
  }

  try {
    // In production, files are served from /static-data/
    // In development, we might need to generate them first
    const response = await fetch(`/static-data/${filename}`, {
      cache: 'force-cache', // Use browser cache for static files
    });

    if (!response.ok) {
      console.warn(`Static data file not found: ${filename}`);
      return null;
    }

    const data = await response.json();
    
    // Cache the data
    staticDataCache.set(filename, data);
    
    return data;
  } catch (error) {
    console.error(`Error loading static data ${filename}:`, error);
    return null;
  }
}

/**
 * Get businesses data with different detail levels
 */
export async function getStaticBusinesses(level: 'minimal' | 'standard' | 'full' = 'standard'): Promise<BusinessData[]> {
  const filename = `businesses-${level}.json`;
  const data = await loadStaticData<BusinessData[]>(filename);
  return data || [];
}

/**
 * Get only active businesses (most common use case)
 */
export async function getActiveBusinesses(): Promise<BusinessData[]> {
  const data = await loadStaticData<BusinessData[]>('businesses-active.json');
  return data || [];
}

/**
 * Get spotlight businesses
 */
export async function getSpotlightBusinesses(): Promise<BusinessData[]> {
  const data = await loadStaticData<BusinessData[]>('businesses-spotlight.json');
  return data || [];
}

/**
 * Get all business categories
 */
export async function getBusinessCategories(): Promise<string[]> {
  const data = await loadStaticData<string[]>('categories.json');
  return data || [];
}

/**
 * Get static data metadata (generation info, stats)
 */
export async function getStaticDataMetadata(): Promise<{
  total: number;
  active: number;
  spotlight: number;
  categories: number;
  generated_at: string;
  version: string;
} | null> {
  return await loadStaticData('metadata.json');
}

/**
 * Search businesses by name or category (client-side filtering)
 */
export async function searchBusinesses(query: string, level: 'minimal' | 'standard' | 'full' = 'standard'): Promise<BusinessData[]> {
  const businesses = await getStaticBusinesses(level);
  
  if (!query.trim()) {
    return businesses;
  }

  const searchTerm = query.toLowerCase();
  
  return businesses.filter(business => 
    business.name.toLowerCase().includes(searchTerm) ||
    business.category?.toLowerCase().includes(searchTerm) ||
    business.premium_discount?.toLowerCase().includes(searchTerm)
  );
}

/**
 * Filter businesses by category
 */
export async function getBusinessesByCategory(category: string, level: 'minimal' | 'standard' | 'full' = 'standard'): Promise<BusinessData[]> {
  const businesses = await getStaticBusinesses(level);
  
  return businesses.filter(business => 
    business.category?.toLowerCase() === category.toLowerCase()
  );
}

/**
 * Get a single business by ID
 */
export async function getBusinessById(id: string, level: 'minimal' | 'standard' | 'full' = 'full'): Promise<BusinessData | null> {
  const businesses = await getStaticBusinesses(level);
  
  return businesses.find(business => business.id === id) || null;
}

/**
 * Check if static data is available and fresh
 */
export async function isStaticDataAvailable(): Promise<boolean> {
  try {
    const metadata = await getStaticDataMetadata();
    
    if (!metadata) {
      return false;
    }

    // Check if data is not too old (24 hours)
    const generatedAt = new Date(metadata.generated_at);
    const now = new Date();
    const hoursDiff = (now.getTime() - generatedAt.getTime()) / (1000 * 60 * 60);
    
    return hoursDiff < 24;
  } catch {
    return false;
  }
}

/**
 * Clear static data cache (useful for development)
 */
export function clearStaticDataCache(): void {
  staticDataCache.clear();
}

/**
 * Preload all static data (useful for critical pages)
 */
export async function preloadStaticData(): Promise<void> {
  const files = [
    'businesses-minimal.json',
    'businesses-standard.json',
    'businesses-active.json',
    'categories.json',
    'metadata.json'
  ];

  await Promise.all(
    files.map(filename => loadStaticData(filename))
  );
}

/**
 * Hook-like interface for React components
 */
export class StaticBusinessesProvider {
  private static instance: StaticBusinessesProvider;
  private businesses: BusinessData[] = [];
  private categories: string[] = [];
  private metadata: any = null;
  private loaded = false;

  static getInstance(): StaticBusinessesProvider {
    if (!StaticBusinessesProvider.instance) {
      StaticBusinessesProvider.instance = new StaticBusinessesProvider();
    }
    return StaticBusinessesProvider.instance;
  }

  async initialize(): Promise<void> {
    if (this.loaded) return;

    try {
      const [businesses, categories, metadata] = await Promise.all([
        getActiveBusinesses(),
        getBusinessCategories(),
        getStaticDataMetadata()
      ]);

      this.businesses = businesses;
      this.categories = categories;
      this.metadata = metadata;
      this.loaded = true;

      console.log(`✅ Static data loaded: ${businesses.length} businesses, ${categories.length} categories`);
    } catch (error) {
      console.error('❌ Failed to initialize static data provider:', error);
      throw error;
    }
  }

  getBusinesses(): BusinessData[] {
    return this.businesses;
  }

  getCategories(): string[] {
    return this.categories;
  }

  getMetadata(): any {
    return this.metadata;
  }

  isLoaded(): boolean {
    return this.loaded;
  }
}
