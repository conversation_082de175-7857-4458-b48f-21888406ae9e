/**
 * Server-only Supabase utilities
 * This file should only be imported in server-side code (API routes, server components, etc.)
 */

import { createServerClient } from '@supabase/ssr'
import { createClient as createSupabaseClient } from '@supabase/supabase-js'

/**
 * Create Supabase server client with cookies support
 * Only use this in server-side code (API routes, server components)
 */
export async function createServerSupabaseClient() {
  // Dynamic import of next/headers to avoid client-side issues
  let cookieStore = null;

  if (typeof window === 'undefined') {
    try {
      const { cookies } = await import("next/headers");
      cookieStore = await cookies();
    } catch (error) {
      console.warn('Failed to import next/headers:', error);
    }
  }

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore?.getAll() || []
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              if (cookieStore) {
                cookieStore.set(name, value, options || {})
              }
            })
          } catch (error) {
            console.warn('Cookie setting failed:', error)
          }
        },
      },
    },
  )
}

/**
 * Create Supabase admin client with service role key
 * Safe to use anywhere as it doesn't depend on cookies
 */
export function createAdminSupabaseClient() {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error('Supabase environment variables are missing')
  }

  return createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}