// Performance monitoring and optimization utilities

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private static instance: PerformanceMonitor;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Start timing an operation
  startTimer(name: string, metadata?: Record<string, any>): void {
    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      metadata,
    });
  }

  // End timing an operation
  endTimer(name: string): number | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance timer '${name}' not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    // Log slow operations
    if (duration > 1000) { // More than 1 second
      console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`, metric.metadata);
    }

    return duration;
  }

  // Get performance metrics
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values()).filter(m => m.duration !== undefined);
  }

  // Clear metrics
  clearMetrics(): void {
    this.metrics.clear();
  }

  // Get average duration for an operation
  getAverageDuration(operationName: string): number | null {
    const operations = this.getMetrics().filter(m => m.name.includes(operationName));
    if (operations.length === 0) return null;

    const totalDuration = operations.reduce((sum, op) => sum + (op.duration || 0), 0);
    return totalDuration / operations.length;
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Database operation timing decorator
export function timed(operationName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const timerName = `${operationName}_${Date.now()}`;
      performanceMonitor.startTimer(timerName, {
        operation: operationName,
        args: args.length,
      });

      try {
        const result = await method.apply(this, args);
        performanceMonitor.endTimer(timerName);
        return result;
      } catch (error) {
        performanceMonitor.endTimer(timerName);
        throw error;
      }
    };

    return descriptor;
  };
}

// Database query optimization utilities
export class QueryOptimizer {
  // Optimize SELECT queries by adding appropriate indexes suggestions
  static analyzeQuery(table: string, filters: Record<string, any>, orderBy?: string): string[] {
    const suggestions: string[] = [];

    // Suggest indexes for frequently filtered columns
    const filterColumns = Object.keys(filters);
    if (filterColumns.length > 0) {
      suggestions.push(`Consider adding index on: ${table}(${filterColumns.join(', ')})`);
    }

    // Suggest index for ORDER BY columns
    if (orderBy) {
      suggestions.push(`Consider adding index on: ${table}(${orderBy})`);
    }

    // Suggest composite indexes for multiple filters
    if (filterColumns.length > 1) {
      suggestions.push(`Consider composite index on: ${table}(${filterColumns.join(', ')})`);
    }

    return suggestions;
  }

  // Suggest query optimizations
  static optimizeFilters(filters: Record<string, any>): Record<string, any> {
    const optimized: Record<string, any> = {};

    Object.entries(filters).forEach(([key, value]) => {
      // Skip null/undefined values to reduce query complexity
      if (value !== null && value !== undefined && value !== '') {
        optimized[key] = value;
      }
    });

    return optimized;
  }

  // Pagination optimization
  static optimizePagination(limit: number, offset: number): { limit: number; offset: number } {
    // Limit maximum page size to prevent performance issues
    const maxLimit = 100;
    const optimizedLimit = Math.min(limit, maxLimit);

    // Warn about large offsets (consider cursor-based pagination instead)
    if (offset > 1000) {
      console.warn('Large offset detected. Consider using cursor-based pagination for better performance.');
    }

    return {
      limit: optimizedLimit,
      offset,
    };
  }
}

// Connection health monitoring
export class ConnectionHealthMonitor {
  private static instance: ConnectionHealthMonitor;
  private healthChecks: Map<string, { timestamp: number; status: 'healthy' | 'unhealthy' }> = new Map();

  static getInstance(): ConnectionHealthMonitor {
    if (!ConnectionHealthMonitor.instance) {
      ConnectionHealthMonitor.instance = new ConnectionHealthMonitor();
    }
    return ConnectionHealthMonitor.instance;
  }

  // Record a health check
  recordHealthCheck(connectionId: string, isHealthy: boolean): void {
    this.healthChecks.set(connectionId, {
      timestamp: Date.now(),
      status: isHealthy ? 'healthy' : 'unhealthy',
    });

    // Clean up old health checks (older than 5 minutes)
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    for (const [id, check] of this.healthChecks.entries()) {
      if (check.timestamp < fiveMinutesAgo) {
        this.healthChecks.delete(id);
      }
    }
  }

  // Get overall health status
  getHealthStatus(): { healthy: number; unhealthy: number; total: number } {
    const checks = Array.from(this.healthChecks.values());
    const healthy = checks.filter(c => c.status === 'healthy').length;
    const unhealthy = checks.filter(c => c.status === 'unhealthy').length;

    return {
      healthy,
      unhealthy,
      total: checks.length,
    };
  }

  // Check if system is healthy
  isSystemHealthy(): boolean {
    const status = this.getHealthStatus();
    if (status.total === 0) return true; // No checks recorded yet

    const healthyPercentage = status.healthy / status.total;
    return healthyPercentage >= 0.8; // 80% healthy threshold
  }
}

// Export health monitor instance
export const connectionHealthMonitor = ConnectionHealthMonitor.getInstance();

// Memory usage monitoring
export function getMemoryUsage(): { used: number; total: number; percentage: number } | null {
  if (typeof window !== 'undefined' && 'memory' in performance) {
    const memory = (performance as any).memory;
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
    };
  }
  return null;
}

// Performance reporting
export function generatePerformanceReport(): {
  metrics: PerformanceMetric[];
  health: ReturnType<ConnectionHealthMonitor['getHealthStatus']>;
  memory: ReturnType<typeof getMemoryUsage>;
  timestamp: string;
} {
  return {
    metrics: performanceMonitor.getMetrics(),
    health: connectionHealthMonitor.getHealthStatus(),
    memory: getMemoryUsage(),
    timestamp: new Date().toISOString(),
  };
}

// Cleanup function
export function cleanupPerformanceMonitoring(): void {
  performanceMonitor.clearMetrics();
}
