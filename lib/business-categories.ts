/**
 * Standardized business categories across the application
 * This ensures consistency between forms, displays, and data processing
 */

export interface BusinessCategory {
  value: string;
  label: string;
  icon: string;
  color: string;
}

export const BUSINESS_CATEGORIES: BusinessCategory[] = [
  {
    value: "restaurant",
    label: "Restaurant", 
    icon: "🍽️",
    color: "bg-orange-500"
  },
  {
    value: "retail",
    label: "Retail",
    icon: "🛍️", 
    color: "bg-blue-500"
  },
  {
    value: "health_wellness",
    label: "Health & Wellness",
    icon: "💊",
    color: "bg-green-500"
  },
  {
    value: "professional_services", 
    label: "Professional Services",
    icon: "💼",
    color: "bg-purple-500"
  },
  {
    value: "entertainment",
    label: "Entertainment",
    icon: "🎭", 
    color: "bg-pink-500"
  },
  {
    value: "automotive",
    label: "Automotive",
    icon: "🚗",
    color: "bg-red-500"
  },
  {
    value: "beauty_personal_care",
    label: "Beauty & Personal Care", 
    icon: "💄",
    color: "bg-yellow-500"
  },
  {
    value: "home_garden",
    label: "Home & Garden",
    icon: "🏡",
    color: "bg-green-600"
  },
  {
    value: "technology",
    label: "Technology",
    icon: "💻",
    color: "bg-blue-600"
  },
  {
    value: "education",
    label: "Education", 
    icon: "📚",
    color: "bg-indigo-500"
  },
  {
    value: "fitness_sports",
    label: "Fitness & Sports",
    icon: "🏋️",
    color: "bg-emerald-500"
  },
  {
    value: "hospitality",
    label: "Hospitality",
    icon: "🏨", 
    color: "bg-cyan-500"
  },
  {
    value: "other",
    label: "Other",
    icon: "📦",
    color: "bg-gray-500"
  }
];

/**
 * Get category info by value or label (case-insensitive)
 */
export function getCategoryInfo(categoryIdentifier: string): BusinessCategory | null {
  if (!categoryIdentifier) return null;
  
  const identifier = categoryIdentifier.toLowerCase().trim();
  
  // Try exact value match first
  let category = BUSINESS_CATEGORIES.find(cat => cat.value === identifier);
  
  // Try exact label match
  if (!category) {
    category = BUSINESS_CATEGORIES.find(cat => cat.label.toLowerCase() === identifier);
  }
  
  // Try partial label match for legacy data
  if (!category) {
    category = BUSINESS_CATEGORIES.find(cat => 
      cat.label.toLowerCase().includes(identifier) || 
      identifier.includes(cat.label.toLowerCase())
    );
  }
  
  return category || {
    value: "other",
    label: "Other", 
    icon: "📦",
    color: "bg-gray-500"
  };
}

/**
 * Get category options for form dropdowns
 */
export function getCategoryOptions(): { value: string; label: string }[] {
  return BUSINESS_CATEGORIES.map(cat => ({
    value: cat.value,
    label: cat.label
  }));
}

/**
 * Legacy category mapping for backward compatibility
 */
export const LEGACY_CATEGORY_MAPPING: Record<string, string> = {
  "Food & Beverage": "restaurant",
  "Healthcare": "health_wellness", 
  "Finance": "professional_services",
  "Manufacturing": "other",
  "Real Estate": "professional_services",
  "Financial Services": "professional_services",
  "Travel & Tourism": "hospitality",
  "Arts & Crafts": "other",
  "Beauty & Spa": "beauty_personal_care"
};

/**
 * Normalize category value for database storage
 */
export function normalizeCategoryValue(input: string): string {
  if (!input) return "other";
  
  const normalized = input.toLowerCase().trim();
  
  // Check legacy mapping first
  const legacyMatch = Object.entries(LEGACY_CATEGORY_MAPPING).find(
    ([legacy]) => legacy.toLowerCase() === input.toLowerCase()
  );
  
  if (legacyMatch) {
    return legacyMatch[1];
  }
  
  // Find matching category
  const category = getCategoryInfo(normalized);
  return category?.value || "other";
}