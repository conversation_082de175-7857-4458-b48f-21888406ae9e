/**
 * Error handling utilities for Supabase 503 schema cache errors
 */

export interface RetryOptions {
  maxRetries?: number
  baseDelay?: number
  maxDelay?: number
  backoffMultiplier?: number
}

const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2
}

/**
 * Check if an error is a 503 Service Unavailable error
 */
export function is503Error(error: any): boolean {
  if (!error) return false
  
  const errorString = error.toString().toLowerCase()
  const errorMessage = error.message?.toLowerCase() || ''
  
  return (
    errorString.includes('503') ||
    errorString.includes('service unavailable') ||
    errorMessage.includes('503') ||
    errorMessage.includes('service unavailable') ||
    errorMessage.includes('schema cache') ||
    error.status === 503 ||
    error.code === 503
  )
}

/**
 * Check if an error is a network/connection error
 */
export function isNetworkError(error: any): boolean {
  if (!error) return false
  
  const errorString = error.toString().toLowerCase()
  const errorMessage = error.message?.toLowerCase() || ''
  
  return (
    errorString.includes('network') ||
    errorString.includes('connection') ||
    errorString.includes('timeout') ||
    errorMessage.includes('network') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('timeout') ||
    error.code === 'ECONNREFUSED' ||
    error.code === 'ENOTFOUND' ||
    error.code === 'ETIMEDOUT'
  )
}

/**
 * Retry a function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const opts = { ...DEFAULT_RETRY_OPTIONS, ...options }
  let lastError: any
  
  for (let attempt = 0; attempt <= opts.maxRetries!; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      // Don't retry on the last attempt
      if (attempt === opts.maxRetries) {
        break
      }
      
      // Only retry on 503 or network errors
      if (!is503Error(error) && !isNetworkError(error)) {
        throw error
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(
        opts.baseDelay! * Math.pow(opts.backoffMultiplier!, attempt),
        opts.maxDelay!
      )
      
      console.log(`Retry attempt ${attempt + 1}/${opts.maxRetries! + 1} after ${delay}ms delay`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}

/**
 * Wrapper for database operations with 503 error handling
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  fallbackValue: T,
  operationName: string = 'database operation'
): Promise<T> {
  try {
    return await retryWithBackoff(operation, {
      maxRetries: 2, // Fewer retries for faster UX
      baseDelay: 500,
      maxDelay: 2000
    })
  } catch (error) {
    console.error(`${operationName} failed after retries:`, error)
    
    if (is503Error(error)) {
      console.warn(`Using fallback value for ${operationName} due to 503 error`)
      return fallbackValue
    }
    
    // Re-throw non-503 errors
    throw error
  }
}

/**
 * Get cached data with fallback
 */
export function getCachedDataWithFallback<T>(
  cacheKey: string,
  maxAge: number = 300000, // 5 minutes default
  fallbackValue: T | null = null
): T | null {
  try {
    const cached = localStorage.getItem(cacheKey)
    const timestamp = localStorage.getItem(`${cacheKey}_timestamp`)
    
    if (cached && timestamp) {
      const age = Date.now() - parseInt(timestamp)
      if (age < maxAge) {
        return JSON.parse(cached)
      }
    }
  } catch (error) {
    console.error('Error reading from cache:', error)
  }
  
  return fallbackValue
}

/**
 * Set cached data with timestamp
 */
export function setCachedData<T>(cacheKey: string, data: T): void {
  try {
    localStorage.setItem(cacheKey, JSON.stringify(data))
    localStorage.setItem(`${cacheKey}_timestamp`, Date.now().toString())
  } catch (error) {
    console.error('Error writing to cache:', error)
  }
}

/**
 * Clear cached data
 */
export function clearCachedData(cacheKey: string): void {
  try {
    localStorage.removeItem(cacheKey)
    localStorage.removeItem(`${cacheKey}_timestamp`)
  } catch (error) {
    console.error('Error clearing cache:', error)
  }
}

/**
 * Handle auth errors specifically
 */
export function handleAuthError(error: any, context: string = 'authentication'): {
  shouldRetry: boolean
  shouldRedirect: boolean
  redirectPath?: string
  message: string
} {
  if (is503Error(error)) {
    return {
      shouldRetry: true,
      shouldRedirect: false,
      message: 'Service temporarily unavailable. Retrying...'
    }
  }
  
  if (isNetworkError(error)) {
    return {
      shouldRetry: true,
      shouldRedirect: false,
      message: 'Network error. Please check your connection.'
    }
  }
  
  if (error?.message?.includes('JWT') || error?.message?.includes('session')) {
    return {
      shouldRetry: false,
      shouldRedirect: true,
      redirectPath: '/login',
      message: 'Session expired. Please log in again.'
    }
  }
  
  if (error?.message?.includes('permission') || error?.message?.includes('unauthorized')) {
    return {
      shouldRetry: false,
      shouldRedirect: true,
      redirectPath: '/login',
      message: 'Access denied. Please log in again.'
    }
  }
  
  return {
    shouldRetry: false,
    shouldRedirect: false,
    message: error?.message || 'An unexpected error occurred'
  }
}
