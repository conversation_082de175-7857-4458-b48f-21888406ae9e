/**
 * Businesses API Utility
 * 
 * Centralized utility for accessing businesses data across the shopping paradise app.
 * Provides consistent, fast access to businesses with proper error handling and fallbacks.
 */

export type BusinessFields = 'minimal' | 'standard' | 'full'

export interface BusinessFilters {
  category?: string
  spotlight?: boolean
  search?: string
  userId?: string
  limit?: number
  offset?: number
}

export interface BusinessMinimal {
  id: string
  name: string
  logo_url: string
  logo_optimized_url?: string
  website: string
  premium_discount: string
  is_active: boolean
  contact_info?: string
  created_at: string
}

export interface BusinessStandard extends BusinessMinimal {
  logo_alt_text?: string
  logo_width?: number
  logo_height?: number
  category: string
  business_spotlight: boolean
  business_address?: string
  contact_name?: string
  contact_email?: string
  contact_phone?: string
  display_order?: number
  updated_at?: string
}

export interface BusinessFull extends BusinessStandard {
  logo_file_size?: number
  logo_mime_type?: string
  contact_info?: string
  user_id: string
  latitude?: number
  longitude?: number
  business_referral?: string
  referring_business_id?: string
  loyalty_reward_frequency?: number
}

export interface BusinessApiResponse<T = any> {
  success: boolean
  data: T[]
  source: 'cache' | 'database'
  fields: BusinessFields
  pagination: {
    limit: number
    offset: number
    count: number
    hasMore?: boolean
  }
  filters?: BusinessFilters
  performance: {
    queryTime: number
    cached: boolean
    indexesUsed?: string[]
  }
  timestamp: string
  error?: string
}

/**
 * Fetch businesses using the optimized fast API
 */
export async function fetchBusinesses<T = BusinessStandard>(
  fields: BusinessFields = 'standard',
  filters: BusinessFilters = {}
): Promise<BusinessApiResponse<T>> {
  const { category, spotlight, search, userId, limit = 100, offset = 0 } = filters
  
  // Build query parameters
  const params = new URLSearchParams({
    fields,
    limit: limit.toString(),
    offset: offset.toString()
  })
  
  if (category) params.append('category', category)
  if (spotlight) params.append('spotlight', 'true')
  if (search) params.append('search', search)
  if (userId) params.append('user_id', userId)
  
  try {
    console.log(`🏢 Fetching businesses: ${fields} fields with filters:`, filters)
    
    const response = await fetch(`/api/businesses-fast?${params}`)
    
    if (!response.ok) {
      throw new Error(`API responded with status ${response.status}`)
    }
    
    const result: BusinessApiResponse<T> = await response.json()
    
    if (!result.success) {
      throw new Error(result.error || 'API returned unsuccessful response')
    }
    
    console.log(`✅ Fetched ${result.data.length} businesses in ${result.performance.queryTime}ms`)
    
    return result
  } catch (error) {
    console.error('❌ Error fetching businesses:', error)
    
    // Return error response with empty data
    return {
      success: false,
      data: [],
      source: 'database',
      fields,
      pagination: { limit, offset, count: 0 },
      filters,
      performance: { queryTime: 0, cached: false },
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Fetch businesses with automatic fallback to older APIs
 */
export async function fetchBusinessesWithFallback<T = BusinessStandard>(
  fields: BusinessFields = 'standard',
  filters: BusinessFilters = {}
): Promise<BusinessApiResponse<T>> {
  // Try the fast API first
  const result = await fetchBusinesses<T>(fields, filters)
  
  if (result.success && result.data.length > 0) {
    return result
  }
  
  console.log('🔄 Fast API failed or returned no data, trying fallback APIs...')
  
  // Fallback to older APIs
  const fallbackEndpoints = [
    '/api/businesses-optimized',
    '/api/businesses-direct',
    '/api/businesses-public',
    '/api/active-businesses'
  ]
  
  for (const endpoint of fallbackEndpoints) {
    try {
      console.log(`🔄 Trying fallback: ${endpoint}`)
      
      const response = await fetch(endpoint)
      if (response.ok) {
        const fallbackResult = await response.json()
        const businessData = fallbackResult.data || fallbackResult.businesses || []
        
        if (businessData.length > 0) {
          console.log(`✅ Fallback ${endpoint} returned ${businessData.length} businesses`)
          
          return {
            success: true,
            data: businessData,
            source: 'database',
            fields,
            pagination: {
              limit: filters.limit || 100,
              offset: filters.offset || 0,
              count: businessData.length
            },
            filters,
            performance: { queryTime: 0, cached: false },
            timestamp: new Date().toISOString()
          }
        }
      }
    } catch (fallbackError) {
      console.log(`⚠️ Fallback ${endpoint} failed:`, fallbackError)
      continue
    }
  }
  
  // All fallbacks failed
  console.error('❌ All business API endpoints failed')
  return result // Return the original error response
}

/**
 * Fetch businesses for carousel/cycling components (minimal data)
 */
export async function fetchBusinessesForCarousel(): Promise<BusinessMinimal[]> {
  const result = await fetchBusinessesWithFallback<BusinessMinimal>('minimal', { limit: 50 })
  
  if (result.success) {
    // Filter businesses with premium discounts for carousel display
    return result.data.filter(business => 
      business.premium_discount && business.premium_discount.trim() !== ''
    )
  }
  
  return []
}

/**
 * Fetch businesses for industry/listing pages (standard data)
 */
export async function fetchBusinessesForListing(filters: BusinessFilters = {}): Promise<BusinessStandard[]> {
  const result = await fetchBusinessesWithFallback<BusinessStandard>('standard', {
    limit: 100,
    ...filters
  })
  
  return result.success ? result.data : []
}

/**
 * Fetch businesses for admin/management (full data)
 */
export async function fetchBusinessesForAdmin(userId?: string): Promise<BusinessFull[]> {
  const result = await fetchBusinessesWithFallback<BusinessFull>('full', {
    userId,
    limit: 500 // Higher limit for admin views
  })
  
  return result.success ? result.data : []
}

/**
 * Clear business cache (useful after updates)
 */
export async function clearBusinessCache(): Promise<boolean> {
  try {
    const response = await fetch('/api/businesses-fast', { method: 'DELETE' })
    const result = await response.json()
    
    console.log('🧹 Business cache cleared:', result.success)
    return result.success
  } catch (error) {
    console.error('❌ Failed to clear business cache:', error)
    return false
  }
}

/**
 * Get business cache statistics
 */
export async function getBusinessCacheStats(): Promise<any> {
  try {
    const response = await fetch('/api/businesses-fast', { method: 'HEAD' })
    
    return {
      cacheSize: parseInt(response.headers.get('X-Cache-Size') || '0'),
      cacheDuration: parseInt(response.headers.get('X-Cache-Duration') || '0')
    }
  } catch (error) {
    console.error('❌ Failed to get cache stats:', error)
    return { cacheSize: 0, cacheDuration: 0 }
  }
}
