import { Client } from 'xrpl';
import { XrplTxData } from 'xrpl-txdata';
import { createClient } from '@supabase/supabase-js';

export interface XamanTransactionVerification {
  txid: string;
  isValid: boolean;
  networkType: string;
  account: string;
  deliveredAmount?: string;
  errors: string[];
  metadata?: any;
}

export class XamanTransactionVerifier {
  private xrplClient: Client;
  private supabase: ReturnType<typeof createClient>;

  constructor() {
    // Initialize XRPL client for mainnet
    this.xrplClient = new Client('wss://xrplcluster.com');
    
    // Initialize Supabase client
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Verify transaction following Xaman implementation checklist
   */
  async verifyTransaction(
    txid: string,
    userId: string,
    payloadId: string,
    expectedAmount?: string
  ): Promise<XamanTransactionVerification> {
    const verification: XamanTransactionVerification = {
      txid,
      isValid: false,
      networkType: 'unknown',
      account: '',
      errors: []
    };

    try {
      // Step 1: Connect to XRPL
      await this.xrplClient.connect();

      // Step 2: Fetch transaction by hash
      const txData = await this.xrplClient.request({
        command: 'tx',
        transaction: txid,
        binary: false
      });

      if (!txData.result) {
        verification.errors.push('Transaction not found on ledger');
        return verification;
      }

      // Step 3: Use xrpl-txdata to parse transaction details
      const parsedTx = new XrplTxData(txData.result);
      
      // Step 4: Verify transaction was successful
      if (txData.result.meta?.TransactionResult !== 'tesSUCCESS') {
        verification.errors.push(`Transaction failed: ${txData.result.meta?.TransactionResult}`);
        return verification;
      }

      // Step 5: Check network type (should be MAINNET for real payments)
      verification.networkType = this.determineNetworkType(txData.result);
      
      if (verification.networkType !== 'MAINNET') {
        verification.errors.push(`Transaction not on mainnet: ${verification.networkType}`);
      }

      // Step 6: Extract account information
      verification.account = txData.result.Account;

      // Step 7: For payments, examine delivered amount
      if (txData.result.TransactionType === 'Payment') {
        const deliveredAmount = txData.result.meta?.delivered_amount;
        
        if (deliveredAmount) {
          verification.deliveredAmount = this.parseAmount(deliveredAmount);
          
          // Step 8: Validate expected amount if provided
          if (expectedAmount && verification.deliveredAmount !== expectedAmount) {
            verification.errors.push(
              `Amount mismatch: expected ${expectedAmount}, delivered ${verification.deliveredAmount}`
            );
          }
        } else {
          verification.errors.push('No delivered amount found in payment transaction');
        }
      }

      // Step 9: Store transaction metadata for future reference
      verification.metadata = {
        transactionType: txData.result.TransactionType,
        fee: txData.result.Fee,
        sequence: txData.result.Sequence,
        ledgerIndex: txData.result.ledger_index,
        date: txData.result.date,
        parsedData: parsedTx.get()
      };

      // Step 10: Update user profile if this is a sign-in transaction
      if (txData.result.TransactionType === 'SignIn' || 
          (txData.result.TransactionType === 'Payment' && txData.result.Amount === '0')) {
        await this.updateUserWalletAddress(userId, verification.account);
      }

      // Step 11: Log transaction for audit trail
      await this.logTransaction(userId, payloadId, verification);

      // Mark as valid if no errors
      verification.isValid = verification.errors.length === 0;

      return verification;

    } catch (error) {
      console.error('Transaction verification error:', error);
      verification.errors.push(`Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return verification;
    } finally {
      // Always disconnect from XRPL
      if (this.xrplClient.isConnected()) {
        await this.xrplClient.disconnect();
      }
    }
  }

  /**
   * Determine network type based on transaction data
   */
  private determineNetworkType(txData: any): string {
    // Check ledger index ranges to determine network
    const ledgerIndex = txData.ledger_index;
    
    if (ledgerIndex && ledgerIndex > 32570) {
      return 'MAINNET';
    } else if (ledgerIndex && ledgerIndex > 0) {
      return 'TESTNET';
    }
    
    return 'UNKNOWN';
  }

  /**
   * Parse amount from XRPL format to human readable
   */
  private parseAmount(amount: any): string {
    if (typeof amount === 'string') {
      // XRP amount in drops
      return (parseInt(amount) / 1000000).toString();
    } else if (typeof amount === 'object' && amount.value) {
      // Token amount
      return `${amount.value} ${amount.currency}`;
    }
    
    return 'Unknown amount format';
  }

  /**
   * Update user profile with verified wallet address
   */
  private async updateUserWalletAddress(userId: string, walletAddress: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('profiles')
        .update({
          xrp_wallet_address: walletAddress,
          wallet_connected_at: new Date().toISOString(),
          wallet_last_used: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('Error updating wallet address:', error);
      } else {
        console.log('✅ Updated wallet address for user:', userId);
      }
    } catch (error) {
      console.error('Error updating user wallet address:', error);
    }
  }

  /**
   * Log transaction for audit trail
   */
  private async logTransaction(
    userId: string, 
    payloadId: string, 
    verification: XamanTransactionVerification
  ): Promise<void> {
    try {
      // This could be expanded to use a dedicated transactions table
      console.log('Transaction verification completed:', {
        userId,
        payloadId,
        txid: verification.txid,
        isValid: verification.isValid,
        networkType: verification.networkType,
        account: verification.account,
        deliveredAmount: verification.deliveredAmount,
        errors: verification.errors,
        timestamp: new Date().toISOString()
      });

      // Optionally store in database
      // await this.supabase.from('transaction_verifications').insert({
      //   user_id: userId,
      //   payload_id: payloadId,
      //   transaction_id: verification.txid,
      //   is_valid: verification.isValid,
      //   network_type: verification.networkType,
      //   account: verification.account,
      //   delivered_amount: verification.deliveredAmount,
      //   errors: verification.errors,
      //   metadata: verification.metadata,
      //   created_at: new Date().toISOString()
      // });

    } catch (error) {
      console.error('Error logging transaction:', error);
    }
  }
}

/**
 * Verify Xaman transaction with comprehensive checks
 */
export async function verifyXamanTransaction(
  txid: string,
  userId: string,
  payloadId: string,
  expectedAmount?: string
): Promise<XamanTransactionVerification> {
  const verifier = new XamanTransactionVerifier();
  return await verifier.verifyTransaction(txid, userId, payloadId, expectedAmount);
}