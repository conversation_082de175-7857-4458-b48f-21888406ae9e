/**
 * Conversation Management System
 * Handles conversation state, context, and user sessions
 */

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface ConversationSession {
  id: string;
  userId?: string;
  sessionId: string;
  messages: ConversationMessage[];
  context: Record<string, any>;
  lastActivity: Date;
  isActive: boolean;
}

export class ConversationManager {
  private static instance: ConversationManager;
  private sessions: Map<string, ConversationSession> = new Map();

  static getInstance(): ConversationManager {
    if (!ConversationManager.instance) {
      ConversationManager.instance = new ConversationManager();
    }
    return ConversationManager.instance;
  }

  async getOrCreateSession(sessionId: string, userId?: string): Promise<ConversationSession> {
    // Try to get from memory first
    let session = this.sessions.get(sessionId);
    
    if (!session) {
      // Try to load from database
      const { data, error } = await supabase
        .from('conversation_sessions')
        .select('*')
        .eq('session_id', sessionId)
        .single();

      if (data && !error) {
        session = {
          id: data.id,
          userId: data.user_id,
          sessionId: data.session_id,
          messages: data.messages || [],
          context: data.context || {},
          lastActivity: new Date(data.last_activity),
          isActive: data.is_active
        };
      } else {
        // Create new session
        session = {
          id: crypto.randomUUID(),
          userId,
          sessionId,
          messages: [],
          context: {},
          lastActivity: new Date(),
          isActive: true
        };

        // Save to database
        await supabase
          .from('conversation_sessions')
          .insert({
            id: session.id,
            user_id: userId,
            session_id: sessionId,
            messages: session.messages,
            context: session.context,
            last_activity: session.lastActivity.toISOString(),
            is_active: session.isActive
          });
      }

      this.sessions.set(sessionId, session);
    }

    return session;
  }

  async addMessage(sessionId: string, message: ConversationMessage): Promise<void> {
    const session = await this.getOrCreateSession(sessionId);
    
    session.messages.push(message);
    session.lastActivity = new Date();

    // Keep only last 20 messages to avoid token limits
    if (session.messages.length > 20) {
      session.messages = session.messages.slice(-20);
    }

    // Update in database
    await supabase
      .from('conversation_sessions')
      .update({
        messages: session.messages,
        last_activity: session.lastActivity.toISOString()
      })
      .eq('session_id', sessionId);
  }

  async getConversationHistory(sessionId: string, limit: number = 10): Promise<ConversationMessage[]> {
    const session = await this.getOrCreateSession(sessionId);
    return session.messages.slice(-limit);
  }

  async updateContext(sessionId: string, context: Record<string, any>): Promise<void> {
    const session = await this.getOrCreateSession(sessionId);
    session.context = { ...session.context, ...context };
    session.lastActivity = new Date();

    // Update in database
    await supabase
      .from('conversation_sessions')
      .update({
        context: session.context,
        last_activity: session.lastActivity.toISOString()
      })
      .eq('session_id', sessionId);
  }

  async getContext(sessionId: string): Promise<Record<string, any>> {
    const session = await this.getOrCreateSession(sessionId);
    return session.context;
  }

  async analyzeIntent(message: string): Promise<{
    intent: string;
    confidence: number;
    entities: Record<string, any>;
    requiresEscalation: boolean;
  }> {
    // Simple intent analysis - can be enhanced with ML models
    const intents = {
      'account_help': ['account', 'login', 'password', 'profile', 'settings'],
      'token_info': ['token', 'fuse', '$fuse', 'price', 'wallet', 'xaman'],
      'loyalty_program': ['loyalty', 'points', 'rewards', 'redeem', 'tier'],
      'technical_support': ['error', 'bug', 'problem', 'issue', 'not working'],
      'business_inquiry': ['business', 'integration', 'enterprise', 'partnership'],
      'general_info': ['what', 'how', 'when', 'where', 'why']
    };

    const messageLower = message.toLowerCase();
    let bestIntent = 'general_info';
    let bestScore = 0;

    for (const [intent, keywords] of Object.entries(intents)) {
      const score = keywords.reduce((acc, keyword) => {
        return acc + (messageLower.includes(keyword) ? 1 : 0);
      }, 0);

      if (score > bestScore) {
        bestScore = score;
        bestIntent = intent;
      }
    }

    // Escalation triggers
    const escalationTriggers = ['urgent', 'emergency', 'manager', 'supervisor', 'complaint', 'refund'];
    const requiresEscalation = escalationTriggers.some(trigger => messageLower.includes(trigger));

    return {
      intent: bestIntent,
      confidence: bestScore / 10,
      entities: {},
      requiresEscalation
    };
  }

  async cleanupOldSessions(): Promise<void> {
    const cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago

    // Clean up from database
    await supabase
      .from('conversation_sessions')
      .delete()
      .lt('last_activity', cutoffDate.toISOString());

    // Clean up from memory
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.lastActivity < cutoffDate) {
        this.sessions.delete(sessionId);
      }
    }
  }
}

export default ConversationManager;