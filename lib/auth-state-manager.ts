import { User, Session } from '@supabase/supabase-js';
import { UserProfile } from '@/types/user';

export interface StoredAuthState {
  sessionId: string;
  userId: string;
  userEmail: string;
  targetRoute: string;
  timestamp: number;
  expiresAt: number;
  walletContext?: {
    isWalletReturn: boolean;
    walletAddress?: string;
    transactionId?: string;
  };
}

export interface AuthRestoration {
  success: boolean;
  state: StoredAuthState | null;
  error?: string;
}

class AuthStateManager {
  private readonly STORAGE_PREFIX = 'fuse_auth_state_';
  private readonly EXPIRY_MINUTES = 30; // 30 minutes max for security
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    // Periodically cleanup expired states
    if (typeof window !== 'undefined') {
      setInterval(() => this.cleanupExpiredStates(), this.CLEANUP_INTERVAL);
    }
  }

  /**
   * Store authentication state before wallet redirect
   */
  storeAuthState(params: {
    user: User;
    targetRoute: string;
    walletContext?: {
      transactionId?: string;
      walletAddress?: string;
    };
  }): string {
    const sessionId = this.generateSessionId();
    const timestamp = Date.now();
    const expiresAt = timestamp + (this.EXPIRY_MINUTES * 60 * 1000);

    const authState: StoredAuthState = {
      sessionId,
      userId: params.user.id,
      userEmail: params.user.email || '',
      targetRoute: params.targetRoute,
      timestamp,
      expiresAt,
      walletContext: {
        isWalletReturn: true,
        ...params.walletContext,
      },
    };

    try {
      const storageKey = this.getStorageKey(sessionId);
      const encrypted = this.encryptState(authState);
      localStorage.setItem(storageKey, encrypted);

      console.log('🔐 Auth state stored for wallet redirect:', {
        sessionId,
        targetRoute: params.targetRoute,
        expiresAt: new Date(expiresAt).toISOString(),
      });

      return sessionId;
    } catch (error) {
      console.error('❌ Failed to store auth state:', error);
      throw new Error('Failed to store authentication state');
    }
  }

  /**
   * Restore authentication state from session ID
   */
  restoreAuthState(sessionId: string): AuthRestoration {
    if (!sessionId) {
      return { success: false, state: null, error: 'No session ID provided' };
    }

    try {
      const storageKey = this.getStorageKey(sessionId);
      const encrypted = localStorage.getItem(storageKey);

      if (!encrypted) {
        return { success: false, state: null, error: 'No stored state found' };
      }

      const state = this.decryptState(encrypted);

      // Validate expiration
      if (Date.now() > state.expiresAt) {
        localStorage.removeItem(storageKey);
        return { success: false, state: null, error: 'Stored state has expired' };
      }

      // Validate session ID matches
      if (state.sessionId !== sessionId) {
        return { success: false, state: null, error: 'Session ID mismatch' };
      }

      console.log('✅ Auth state restored successfully:', {
        sessionId,
        userId: state.userId,
        targetRoute: state.targetRoute,
        walletReturn: state.walletContext?.isWalletReturn,
      });

      return { success: true, state };
    } catch (error) {
      console.error('❌ Failed to restore auth state:', error);
      return { success: false, state: null, error: 'Failed to decrypt stored state' };
    }
  }

  /**
   * Check if current URL indicates a wallet return
   */
  isWalletReturn(): { isReturn: boolean; sessionId?: string; source?: string } {
    if (typeof window === 'undefined') {
      return { isReturn: false };
    }

    const url = new URL(window.location.href);
    const searchParams = url.searchParams;

    // Check for wallet return indicators
    const sessionId = searchParams.get('wallet_session') || 
                     searchParams.get('state') || 
                     searchParams.get('session_id');

    const hasWalletParams = searchParams.has('wallet_return') ||
                           searchParams.has('xaman_return') ||
                           url.pathname.includes('/wallet-connected') ||
                           url.pathname.includes('/xaman-success');

    const hasOAuthParams = searchParams.has('code') && searchParams.has('state');

    if (sessionId && (hasWalletParams || hasOAuthParams)) {
      return {
        isReturn: true,
        sessionId,
        source: hasOAuthParams ? 'oauth' : 'wallet',
      };
    }

    return { isReturn: false };
  }

  /**
   * Clean up authentication state after successful restoration
   */
  cleanupAuthState(sessionId: string): void {
    if (!sessionId) return;

    try {
      const storageKey = this.getStorageKey(sessionId);
      localStorage.removeItem(storageKey);
      console.log('🧹 Cleaned up auth state:', sessionId);
    } catch (error) {
      console.warn('⚠️ Failed to cleanup auth state:', error);
    }
  }

  /**
   * Create wallet return URL with session ID
   */
  createWalletReturnUrl(baseUrl: string, sessionId: string): string {
    const url = new URL(baseUrl);
    url.searchParams.set('wallet_return', 'true');
    url.searchParams.set('wallet_session', sessionId);
    return url.toString();
  }

  /**
   * Clean up expired authentication states
   */
  private cleanupExpiredStates(): void {
    if (typeof window === 'undefined') return;

    try {
      const now = Date.now();
      const keysToRemove: string[] = [];

      // Check all localStorage keys for expired auth states
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(this.STORAGE_PREFIX)) {
          try {
            const encrypted = localStorage.getItem(key);
            if (encrypted) {
              const state = this.decryptState(encrypted);
              if (now > state.expiresAt) {
                keysToRemove.push(key);
              }
            }
          } catch {
            // If we can't decrypt, assume it's corrupted and remove
            keysToRemove.push(key);
          }
        }
      }

      keysToRemove.forEach(key => localStorage.removeItem(key));

      if (keysToRemove.length > 0) {
        console.log('🧹 Cleaned up expired auth states:', keysToRemove.length);
      }
    } catch (error) {
      console.warn('⚠️ Error during auth state cleanup:', error);
    }
  }

  private generateSessionId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private getStorageKey(sessionId: string): string {
    return `${this.STORAGE_PREFIX}${sessionId}`;
  }

  private encryptState(state: StoredAuthState): string {
    // Simple base64 encoding for now - in production, consider stronger encryption
    return btoa(JSON.stringify(state));
  }

  private decryptState(encrypted: string): StoredAuthState {
    return JSON.parse(atob(encrypted));
  }
}

// Export singleton instance
export const authStateManager = new AuthStateManager();
export default authStateManager;