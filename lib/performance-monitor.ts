/**
 * Enhanced Performance Monitoring System
 * Tracks API response times, database queries, and frontend performance
 */

interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface APIMetric extends PerformanceMetric {
  endpoint: string;
  method: string;
  status: number;
  userId?: string;
  cached?: boolean;
}

interface DatabaseMetric extends PerformanceMetric {
  query: string;
  table: string;
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE';
  rowCount?: number;
}

interface FrontendMetric extends PerformanceMetric {
  component: string;
  renderTime: number;
  props?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 1000; // Keep last 1000 metrics
  private slowThresholds = {
    api: 1000, // 1 second
    database: 500, // 500ms
    frontend: 100, // 100ms
  };

  // API Performance Tracking
  trackAPICall(
    endpoint: string,
    method: string,
    duration: number,
    status: number,
    userId?: string,
    cached?: boolean
  ): void {
    const metric: APIMetric = {
      name: 'api_call',
      endpoint,
      method,
      duration,
      status,
      timestamp: Date.now(),
      userId,
      cached,
    };

    this.addMetric(metric);

    // Log slow API calls
    if (duration > this.slowThresholds.api) {
      console.warn(`🐌 Slow API call detected: ${method} ${endpoint} took ${duration}ms`);
    }

    // Log errors
    if (status >= 400) {
      console.error(`❌ API error: ${method} ${endpoint} returned ${status}`);
    }
  }

  // Database Performance Tracking
  trackDatabaseQuery(
    query: string,
    table: string,
    operation: DatabaseMetric['operation'],
    duration: number,
    rowCount?: number
  ): void {
    const metric: DatabaseMetric = {
      name: 'database_query',
      query: query.substring(0, 200), // Truncate long queries
      table,
      operation,
      duration,
      timestamp: Date.now(),
      rowCount,
    };

    this.addMetric(metric);

    // Log slow queries
    if (duration > this.slowThresholds.database) {
      console.warn(`🐌 Slow database query detected: ${operation} on ${table} took ${duration}ms`);
    }
  }

  // Frontend Performance Tracking
  trackComponentRender(
    component: string,
    renderTime: number,
    props?: Record<string, any>
  ): void {
    const metric: FrontendMetric = {
      name: 'component_render',
      component,
      renderTime,
      duration: renderTime,
      timestamp: Date.now(),
      props,
    };

    this.addMetric(metric);

    // Log slow renders
    if (renderTime > this.slowThresholds.frontend) {
      console.warn(`🐌 Slow component render: ${component} took ${renderTime}ms`);
    }
  }

  // Page Load Performance
  trackPageLoad(page: string, loadTime: number, metrics?: Record<string, number>): void {
    const metric: PerformanceMetric = {
      name: 'page_load',
      duration: loadTime,
      timestamp: Date.now(),
      metadata: {
        page,
        ...metrics,
      },
    };

    this.addMetric(metric);

    console.log(`📊 Page load: ${page} took ${loadTime}ms`);
  }

  // Cache Performance
  trackCacheHit(key: string, hit: boolean): void {
    const metric: PerformanceMetric = {
      name: 'cache_access',
      duration: 0,
      timestamp: Date.now(),
      metadata: {
        key,
        hit,
      },
    };

    this.addMetric(metric);
  }

  private addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);

    // Keep only the last N metrics to prevent memory leaks
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  // Analytics and Reporting
  getMetrics(type?: string, limit: number = 100): PerformanceMetric[] {
    let filtered = this.metrics;

    if (type) {
      filtered = this.metrics.filter(m => m.name === type);
    }

    return filtered.slice(-limit);
  }

  getSlowOperations(threshold?: number): PerformanceMetric[] {
    const defaultThreshold = 1000; // 1 second
    return this.metrics.filter(m => m.duration > (threshold || defaultThreshold));
  }

  getAverageResponseTime(endpoint?: string): number {
    const apiMetrics = this.metrics.filter(m => 
      m.name === 'api_call' && 
      (!endpoint || (m as APIMetric).endpoint === endpoint)
    ) as APIMetric[];

    if (apiMetrics.length === 0) return 0;

    const total = apiMetrics.reduce((sum, m) => sum + m.duration, 0);
    return total / apiMetrics.length;
  }

  getCacheHitRate(): number {
    const cacheMetrics = this.metrics.filter(m => m.name === 'cache_access');
    if (cacheMetrics.length === 0) return 0;

    const hits = cacheMetrics.filter(m => m.metadata?.hit).length;
    return (hits / cacheMetrics.length) * 100;
  }

  // Performance Summary
  getSummary(): {
    totalMetrics: number;
    slowOperations: number;
    averageApiResponseTime: number;
    cacheHitRate: number;
    recentErrors: number;
  } {
    const now = Date.now();
    const lastHour = now - (60 * 60 * 1000);

    const recentMetrics = this.metrics.filter(m => m.timestamp > lastHour);
    const slowOps = this.getSlowOperations();
    const recentErrors = recentMetrics.filter(m => 
      m.name === 'api_call' && (m as APIMetric).status >= 400
    ).length;

    return {
      totalMetrics: this.metrics.length,
      slowOperations: slowOps.length,
      averageApiResponseTime: this.getAverageResponseTime(),
      cacheHitRate: this.getCacheHitRate(),
      recentErrors,
    };
  }

  // Clear old metrics
  clearOldMetrics(olderThanHours: number = 24): void {
    const cutoff = Date.now() - (olderThanHours * 60 * 60 * 1000);
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for component performance tracking
export function usePerformanceTracking(componentName: string) {
  const startTime = performance.now();

  React.useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    performanceMonitor.trackComponentRender(componentName, renderTime);
  });
}

// API wrapper with performance tracking
export async function trackAPICall<T>(
  endpoint: string,
  method: string,
  apiCall: () => Promise<Response>,
  userId?: string
): Promise<T> {
  const startTime = performance.now();
  
  try {
    const response = await apiCall();
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    performanceMonitor.trackAPICall(
      endpoint,
      method,
      duration,
      response.status,
      userId,
      response.headers.get('x-cache') === 'HIT'
    );
    
    if (!response.ok) {
      throw new Error(`API call failed: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    performanceMonitor.trackAPICall(
      endpoint,
      method,
      duration,
      500,
      userId
    );
    
    throw error;
  }
}

// Database query wrapper with performance tracking
export async function trackDatabaseQuery<T>(
  query: string,
  table: string,
  operation: DatabaseMetric['operation'],
  queryFn: () => Promise<T>
): Promise<T> {
  const startTime = performance.now();
  
  try {
    const result = await queryFn();
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    performanceMonitor.trackDatabaseQuery(query, table, operation, duration);
    
    return result;
  } catch (error) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    performanceMonitor.trackDatabaseQuery(query, table, operation, duration);
    
    throw error;
  }
}

// Page performance tracking
export function trackPagePerformance(pageName: string) {
  if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const loadTime = navigation.loadEventEnd - navigation.fetchStart;
      
      performanceMonitor.trackPageLoad(pageName, loadTime, {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
      });
    });
  }
}

// Cleanup old metrics periodically
if (typeof window === 'undefined') {
  setInterval(() => {
    performanceMonitor.clearOldMetrics(24); // Keep last 24 hours
  }, 60 * 60 * 1000); // Run every hour
}
