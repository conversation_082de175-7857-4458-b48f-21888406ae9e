/**
 * Simplified Upload Service
 * Uses only the main Supabase client to avoid connection conflicts
 */

import { getSupabaseClient } from './supabase'

interface UploadOptions {
  contentType?: string
  cacheControl?: string
  upsert?: boolean
}

interface UploadResult {
  url?: string
  error?: string
  path?: string
}

/**
 * Simple file upload using only the main Supabase client
 */
export async function uploadFile(
  bucket: string,
  path: string,
  file: File,
  options: UploadOptions = {}
): Promise<UploadResult> {
  try {
    if (!supabase) {
      return { error: 'Supabase client not available' }
    }

    // Validate file
    if (!file) {
      return { error: 'No file provided' }
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      return { error: 'File size must be less than 5MB' }
    }

    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml']
    if (!allowedTypes.includes(file.type)) {
      return { error: 'File must be an image (JPEG, PNG, WebP, or SVG)' }
    }

    // Upload file
    const supabase = getSupabaseClient();
    if (!supabase) {
      return { error: 'Supabase client not initialized' };
    }

    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        contentType: options.contentType || file.type,
        cacheControl: options.cacheControl || '3600',
        upsert: options.upsert !== false
      })

    if (error) {
      console.error('Upload error:', error)
      return { error: error.message }
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(data.path)

    return {
      url: urlData.publicUrl,
      path: data.path
    }

  } catch (error) {
    console.error('Upload failed:', error)
    return { 
      error: error instanceof Error ? error.message : 'Upload failed' 
    }
  }
}

/**
 * Upload business logo with proper naming
 */
export async function uploadBusinessLogo(
  businessId: string,
  file: File
): Promise<UploadResult> {
  const timestamp = Date.now()
  const fileExt = file.name.split('.').pop()
  const fileName = `logo-${businessId}-${timestamp}.${fileExt}`
  const filePath = `business-logos/${fileName}`

  return uploadFile('business-assets', filePath, file)
}

/**
 * Delete file from storage
 */
export async function deleteFile(bucket: string, path: string): Promise<{ error?: string }> {
  try {
    if (!supabase) {
      return { error: 'Supabase client not available' }
    }

    const { error } = await supabase.storage
      .from(bucket)
      .remove([path])

    if (error) {
      console.error('Delete error:', error)
      return { error: error.message }
    }

    return {}
  } catch (error) {
    console.error('Delete failed:', error)
    return { 
      error: error instanceof Error ? error.message : 'Delete failed' 
    }
  }
}
