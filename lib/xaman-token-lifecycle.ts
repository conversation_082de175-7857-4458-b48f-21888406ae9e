'use client';

// Xaman User Token Lifecycle Management
// Handles token expiration, refresh, and cleanup operations

export interface UserTokenStatus {
  hasToken: boolean;
  isValid: boolean;
  expiresAt: string | null;
  daysUntilExpiration: number | null;
  needsRefresh: boolean;
}

export class XamanTokenLifecycleManager {
  private static instance: XamanTokenLifecycleManager;
  
  public static getInstance(): XamanTokenLifecycleManager {
    if (!XamanTokenLifecycleManager.instance) {
      XamanTokenLifecycleManager.instance = new XamanTokenLifecycleManager();
    }
    return XamanTokenLifecycleManager.instance;
  }

  // Check user token status for a given user
  async checkUserTokenStatus(userId: string): Promise<UserTokenStatus> {
    try {
      const response = await fetch(`/api/wallet-connection?userId=${userId}`);
      if (!response.ok) {
        return this.getDefaultTokenStatus();
      }

      const data = await response.json();
      const profile = data.data;

      if (!profile.hasUserToken || !profile.userTokenExpiresAt) {
        return this.getDefaultTokenStatus();
      }

      const expiresAt = new Date(profile.userTokenExpiresAt);
      const now = new Date();
      const isValid = expiresAt > now;
      const daysUntilExpiration = Math.ceil((expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      // Consider token needs refresh if it expires in less than 7 days
      const needsRefresh = daysUntilExpiration <= 7 && daysUntilExpiration > 0;

      return {
        hasToken: true,
        isValid,
        expiresAt: profile.userTokenExpiresAt,
        daysUntilExpiration: isValid ? daysUntilExpiration : null,
        needsRefresh
      };
    } catch (error) {
      console.error('Failed to check user token status:', error);
      return this.getDefaultTokenStatus();
    }
  }

  private getDefaultTokenStatus(): UserTokenStatus {
    return {
      hasToken: false,
      isValid: false,
      expiresAt: null,
      daysUntilExpiration: null,
      needsRefresh: false
    };
  }

  // Extend user token expiration after successful payload signature
  async extendUserTokenExpiration(userId: string): Promise<boolean> {
    try {
      // This would typically be called after a successful payload signature
      // For now, we'll use the existing API to update the token expiration
      const response = await fetch('/api/wallet-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          action: 'extend_expiration'
        })
      });

      if (!response.ok) {
        console.error('Failed to extend user token expiration');
        return false;
      }

      console.log('✅ User token expiration extended successfully');
      return true;
    } catch (error) {
      console.error('Failed to extend user token expiration:', error);
      return false;
    }
  }

  // Clear expired user token
  async clearExpiredToken(userId: string): Promise<boolean> {
    try {
      const response = await fetch('/api/wallet-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          action: 'clear_expired_token'
        })
      });

      if (!response.ok) {
        console.error('Failed to clear expired token');
        return false;
      }

      console.log('✅ Expired user token cleared successfully');
      return true;
    } catch (error) {
      console.error('Failed to clear expired token:', error);
      return false;
    }
  }

  // Get token expiration warning message
  getExpirationWarningMessage(status: UserTokenStatus): string | null {
    if (!status.isValid || !status.daysUntilExpiration) {
      return null;
    }

    if (status.daysUntilExpiration <= 1) {
      return 'Your Xaman push notifications will expire today. Sign a transaction to extend them.';
    } else if (status.daysUntilExpiration <= 3) {
      return `Your Xaman push notifications will expire in ${status.daysUntilExpiration} days. Sign a transaction to extend them.`;
    } else if (status.daysUntilExpiration <= 7) {
      return `Your Xaman push notifications will expire in ${status.daysUntilExpiration} days.`;
    }

    return null;
  }

  // Check if user should be prompted to refresh token
  shouldPromptTokenRefresh(status: UserTokenStatus): boolean {
    return status.needsRefresh && status.isValid;
  }

  // Monitor token status and provide notifications
  async monitorTokenStatus(userId: string, onStatusChange?: (status: UserTokenStatus) => void): Promise<void> {
    const checkStatus = async () => {
      const status = await this.checkUserTokenStatus(userId);
      
      if (onStatusChange) {
        onStatusChange(status);
      }

      // Log warnings for tokens that need attention
      const warning = this.getExpirationWarningMessage(status);
      if (warning) {
        console.warn('🔔 Xaman Token Warning:', warning);
      }

      // Clear expired tokens automatically
      if (status.hasToken && !status.isValid) {
        console.log('🧹 Clearing expired user token');
        await this.clearExpiredToken(userId);
      }
    };

    // Initial check
    await checkStatus();

    // Set up periodic monitoring (every hour)
    const intervalId = setInterval(checkStatus, 60 * 60 * 1000);

    // Return cleanup function
    return () => clearInterval(intervalId);
  }
}

// Hook for using token lifecycle management
export function useXamanTokenLifecycle(userId?: string) {
  const manager = XamanTokenLifecycleManager.getInstance();

  const checkTokenStatus = async () => {
    if (!userId) return manager.getDefaultTokenStatus();
    return manager.checkUserTokenStatus(userId);
  };

  const extendTokenExpiration = async () => {
    if (!userId) return false;
    return manager.extendUserTokenExpiration(userId);
  };

  const clearExpiredToken = async () => {
    if (!userId) return false;
    return manager.clearExpiredToken(userId);
  };

  const getExpirationWarning = (status: UserTokenStatus) => {
    return manager.getExpirationWarningMessage(status);
  };

  const shouldPromptRefresh = (status: UserTokenStatus) => {
    return manager.shouldPromptTokenRefresh(status);
  };

  const startMonitoring = (onStatusChange?: (status: UserTokenStatus) => void) => {
    if (!userId) return () => {};
    return manager.monitorTokenStatus(userId, onStatusChange);
  };

  return {
    checkTokenStatus,
    extendTokenExpiration,
    clearExpiredToken,
    getExpirationWarning,
    shouldPromptRefresh,
    startMonitoring
  };
}

// Utility function to create a cleanup job for expired tokens (server-side)
export async function createTokenCleanupJob() {
  try {
    const response = await fetch('/api/wallet-connection/cleanup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      console.error('Failed to run token cleanup job');
      return false;
    }

    const result = await response.json();
    console.log('✅ Token cleanup job completed:', result);
    return true;
  } catch (error) {
    console.error('Failed to run token cleanup job:', error);
    return false;
  }
}

// React component for displaying token status warnings
export function XamanTokenStatusWarning({ 
  userId, 
  className = '' 
}: { 
  userId?: string; 
  className?: string; 
}) {
  const { checkTokenStatus, getExpirationWarning } = useXamanTokenLifecycle(userId);
  const [status, setStatus] = React.useState<UserTokenStatus | null>(null);
  const [warning, setWarning] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (!userId) return;

    const updateStatus = async () => {
      const tokenStatus = await checkTokenStatus();
      setStatus(tokenStatus);
      setWarning(getExpirationWarning(tokenStatus));
    };

    updateStatus();
    
    // Update every hour
    const interval = setInterval(updateStatus, 60 * 60 * 1000);
    return () => clearInterval(interval);
  }, [userId, checkTokenStatus, getExpirationWarning]);

  if (!warning) return null;

  return (
    <div className={`bg-yellow-50 border border-yellow-200 rounded-md p-3 ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm text-yellow-800">{warning}</p>
        </div>
      </div>
    </div>
  );
}

// Add React import for the component
import React from 'react';
