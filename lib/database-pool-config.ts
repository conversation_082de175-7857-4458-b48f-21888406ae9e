/**
 * Database Pool Configuration for PGRST002 Error Mitigation
 * Optimized pool sizes and connection settings to handle PostgREST schema cache issues
 */

export interface DatabasePoolConfig {
  max: number
  min: number
  connectionTimeoutMillis: number
  idleTimeoutMillis: number
  acquireTimeoutMillis: number
  createTimeoutMillis: number
  destroyTimeoutMillis: number
  reapIntervalMillis: number
  createRetryIntervalMillis: number
  statement_timeout?: number
  query_timeout?: number
  application_name?: string
}

/**
 * Pool size configurations for different use cases
 */
export const POOL_CONFIGS = {
  // Optimized for 44 monthly active users - main application with extended timeouts to prevent client disconnects
  HIGH_PERFORMANCE: {
    max: 5,               // Reduced for build stability
    min: 1,               // Conservative minimum for small user base
    connectionTimeoutMillis: 120000, // 2 minutes to prevent client timeouts
    idleTimeoutMillis: 300000,       // 5 minutes to keep connections alive longer
    acquireTimeoutMillis: 180000,    // 3 minutes for build operations
    createTimeoutMillis: 90000,      // 90 seconds for build operations
    destroyTimeoutMillis: 10000,     // 10 seconds
    reapIntervalMillis: 2000,        // 2 seconds
    createRetryIntervalMillis: 500,  // 500ms
    statement_timeout: 120000,       // 2 minutes for build queries
    query_timeout: 90000,            // 90 seconds for build queries
    application_name: 'fuse-vip-small-scale'
  } as DatabasePoolConfig,

  // Standard configuration for regular operations with extended timeouts
  STANDARD: {
    max: 5,               // Appropriate for low-traffic operations
    min: 1,               // Single connection minimum
    connectionTimeoutMillis: 60000,  // 60 seconds to prevent client timeouts
    idleTimeoutMillis: 180000,       // 3 minutes to keep connections alive
    acquireTimeoutMillis: 90000,     // 90 seconds
    createTimeoutMillis: 45000,      // 45 seconds
    destroyTimeoutMillis: 5000,      // 5 seconds
    reapIntervalMillis: 2000,        // 2 seconds
    createRetryIntervalMillis: 300,  // 300ms
    statement_timeout: 60000,        // 60 seconds
    query_timeout: 45000,            // 45 seconds
    application_name: 'fuse-vip-standard'
  } as DatabasePoolConfig,

  // Emergency configuration for schema reload operations
  EMERGENCY: {
    max: 12,              // Higher for emergency but still reasonable
    min: 3,               // Moderate minimum for immediate availability
    connectionTimeoutMillis: 20000,  // 20 seconds
    idleTimeoutMillis: 60000,        // 60 seconds
    acquireTimeoutMillis: 90000,     // 90 seconds
    createTimeoutMillis: 30000,      // 30 seconds
    destroyTimeoutMillis: 3000,      // 3 seconds
    reapIntervalMillis: 500,         // 500ms
    createRetryIntervalMillis: 100,  // 100ms
    statement_timeout: 45000,        // 45 seconds
    query_timeout: 40000,            // 40 seconds
    application_name: 'fuse-vip-emergency'
  } as DatabasePoolConfig,

  // Lightweight configuration for background tasks
  BACKGROUND: {
    max: 3,               // Minimal for background work
    min: 1,               // Single connection minimum
    connectionTimeoutMillis: 10000,  // 10 seconds
    idleTimeoutMillis: 25000,        // 25 seconds
    acquireTimeoutMillis: 30000,     // 30 seconds
    createTimeoutMillis: 15000,      // 15 seconds
    destroyTimeoutMillis: 5000,      // 5 seconds
    reapIntervalMillis: 3000,        // 3 seconds
    createRetryIntervalMillis: 500,  // 500ms
    statement_timeout: 20000,        // 20 seconds
    query_timeout: 15000,            // 15 seconds
    application_name: 'fuse-vip-background'
  } as DatabasePoolConfig
} as const

/**
 * Get database configuration based on environment and use case
 */
export function getDatabaseConfig(
  useCase: keyof typeof POOL_CONFIGS = 'STANDARD',
  customOverrides?: Partial<DatabasePoolConfig>
): DatabasePoolConfig {
  const baseConfig = POOL_CONFIGS[useCase]
  
  // Apply environment-specific adjustments
  const envAdjustments: Partial<DatabasePoolConfig> = {}
  
  if (process.env.NODE_ENV === 'production') {
    // Production: modest increase for 44 users
    envAdjustments.max = Math.min((baseConfig.max || 5) * 1.2, 15) // Increase by 20%, cap at 15
    envAdjustments.min = Math.max((baseConfig.min || 1) * 1.5, 2)  // Increase minimum slightly
  } else if (process.env.NODE_ENV === 'development') {
    // Development: keep minimal for resource conservation
    envAdjustments.max = Math.max((baseConfig.max || 5) * 0.8, 3)  // Reduce by 20%, at least 3
    envAdjustments.min = Math.max((baseConfig.min || 1), 1)        // Keep at least 1
  }

  // Merge configurations
  return {
    ...baseConfig,
    ...envAdjustments,
    ...customOverrides
  }
}

/**
 * Create PostgreSQL pool configuration object
 */
export function createPoolConfig(
  dbPassword: string,
  useCase: keyof typeof POOL_CONFIGS = 'STANDARD',
  customOverrides?: Partial<DatabasePoolConfig>
): any {
  const config = getDatabaseConfig(useCase, customOverrides)

  // Use environment variables for database connection
  const host = process.env.POSTGRES_HOST || 'db.haqbtbpmyadkocakqnew.supabase.co'
  const port = parseInt(process.env.POSTGRES_PORT || '5432')
  const database = process.env.POSTGRES_DATABASE || 'postgres'
  const user = process.env.POSTGRES_USER || 'postgres'

  // Always use SSL for Supabase - never connect to localhost
  return {
    host,
    port,
    database,
    user,
    password: dbPassword,
    ssl: { rejectUnauthorized: false }, // Always use SSL for Supabase
    ...config
  }
}

/**
 * Monitor pool health and suggest optimizations
 */
export class PoolHealthMonitor {
  private static metrics = new Map<string, {
    totalConnections: number
    activeConnections: number
    idleConnections: number
    waitingClients: number
    lastCheck: number
  }>()

  static recordPoolStats(poolName: string, stats: {
    totalConnections: number
    activeConnections: number
    idleConnections: number
    waitingClients: number
  }): void {
    this.metrics.set(poolName, {
      ...stats,
      lastCheck: Date.now()
    })
  }

  static getPoolHealth(poolName: string): {
    status: 'healthy' | 'warning' | 'critical'
    recommendations: string[]
    metrics?: any
  } {
    const stats = this.metrics.get(poolName)
    
    if (!stats) {
      return {
        status: 'warning',
        recommendations: ['No metrics available for this pool']
      }
    }

    const recommendations: string[] = []
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'

    // Check for high utilization
    const utilizationRate = stats.activeConnections / stats.totalConnections
    if (utilizationRate > 0.9) {
      status = 'critical'
      recommendations.push('Pool utilization > 90% - consider increasing max pool size')
    } else if (utilizationRate > 0.7) {
      status = 'warning'
      recommendations.push('Pool utilization > 70% - monitor for potential bottlenecks')
    }

    // Check for waiting clients
    if (stats.waitingClients > 0) {
      status = 'critical'
      recommendations.push(`${stats.waitingClients} clients waiting - increase pool size or optimize queries`)
    }

    // Check for idle connections
    const idleRate = stats.idleConnections / stats.totalConnections
    if (idleRate > 0.8 && stats.totalConnections > 10) {
      recommendations.push('High idle connection rate - consider reducing max pool size')
    }

    // Check for stale metrics
    const age = Date.now() - stats.lastCheck
    if (age > 300000) { // 5 minutes
      status = 'warning'
      recommendations.push('Metrics are stale - pool monitoring may not be working')
    }

    return {
      status,
      recommendations,
      metrics: stats
    }
  }

  static getAllPoolHealth(): Record<string, ReturnType<typeof PoolHealthMonitor.getPoolHealth>> {
    const result: Record<string, ReturnType<typeof PoolHealthMonitor.getPoolHealth>> = {}
    
    for (const [poolName] of this.metrics) {
      result[poolName] = this.getPoolHealth(poolName)
    }
    
    return result
  }
}

/**
 * Auto-scaling pool configuration based on load
 */
export class AdaptivePoolManager {
  private static loadHistory = new Map<string, number[]>()
  
  static recordLoad(poolName: string, currentLoad: number): void {
    if (!this.loadHistory.has(poolName)) {
      this.loadHistory.set(poolName, [])
    }
    
    const history = this.loadHistory.get(poolName)!
    history.push(currentLoad)
    
    // Keep only last 100 measurements
    if (history.length > 100) {
      history.shift()
    }
  }
  
  static getOptimalPoolSize(poolName: string, currentConfig: DatabasePoolConfig): {
    recommendedMax: number
    recommendedMin: number
    confidence: number
    reasoning: string[]
  } {
    const history = this.loadHistory.get(poolName) || []
    
    if (history.length < 10) {
      return {
        recommendedMax: currentConfig.max,
        recommendedMin: currentConfig.min,
        confidence: 0.1,
        reasoning: ['Insufficient data for recommendations']
      }
    }
    
    const avgLoad = history.reduce((a, b) => a + b, 0) / history.length
    const maxLoad = Math.max(...history)
    const minLoad = Math.min(...history)
    
    const reasoning: string[] = []
    let recommendedMax = currentConfig.max
    let recommendedMin = currentConfig.min
    
    // Adjust based on average load
    if (avgLoad > 0.8) {
      recommendedMax = Math.ceil(currentConfig.max * 1.3)
      reasoning.push(`High average load (${(avgLoad * 100).toFixed(1)}%) - increasing max pool size`)
    } else if (avgLoad < 0.3) {
      recommendedMax = Math.max(Math.ceil(currentConfig.max * 0.8), 5)
      reasoning.push(`Low average load (${(avgLoad * 100).toFixed(1)}%) - reducing max pool size`)
    }
    
    // Adjust based on peak load
    if (maxLoad > 0.95) {
      recommendedMax = Math.max(recommendedMax, Math.ceil(currentConfig.max * 1.5))
      reasoning.push(`Peak load reached ${(maxLoad * 100).toFixed(1)}% - ensuring headroom`)
    }
    
    // Adjust minimum based on minimum load
    if (minLoad > 0.2) {
      recommendedMin = Math.ceil(currentConfig.min * 1.2)
      reasoning.push(`Minimum load is ${(minLoad * 100).toFixed(1)}% - increasing min pool size`)
    }
    
    const confidence = Math.min(history.length / 100, 1)
    
    return {
      recommendedMax,
      recommendedMin,
      confidence,
      reasoning
    }
  }
}

// Export default configurations for easy access
export const DEFAULT_CONFIGS = POOL_CONFIGS
export default POOL_CONFIGS
