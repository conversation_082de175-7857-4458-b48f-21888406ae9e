"use client"

// Mobile browser detection and optimization utilities
export interface BrowserInfo {
  isMobile: boolean
  isIOS: boolean
  isAndroid: boolean
  isSafari: boolean
  isChrome: boolean
  isFirefox: boolean
  version: string
  supportsTouch: boolean
  supportsVibration: boolean
  supportsWebGL: boolean
  devicePixelRatio: number
}

export function getBrowserInfo(): BrowserInfo {
  if (typeof window === 'undefined') {
    return {
      isMobile: false,
      isIOS: false,
      isAndroid: false,
      isSafari: false,
      isChrome: false,
      isFirefox: false,
      version: '',
      supportsTouch: false,
      supportsVibration: false,
      supportsWebGL: false,
      devicePixelRatio: 1
    }
  }

  const userAgent = navigator.userAgent.toLowerCase()
  const vendor = navigator.vendor?.toLowerCase() || ''
  
  // Mobile detection
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent) ||
                   window.innerWidth < 768

  // OS detection
  const isIOS = /iphone|ipad|ipod/i.test(userAgent)
  const isAndroid = /android/i.test(userAgent)

  // Browser detection
  const isSafari = /safari/i.test(userAgent) && /apple/i.test(vendor) && !/chrome|chromium|crios|fxios/i.test(userAgent)
  const isChrome = /chrome|chromium|crios/i.test(userAgent) && /google/i.test(vendor)
  const isFirefox = /firefox|fxios/i.test(userAgent)

  // Version extraction
  let version = ''
  if (isSafari) {
    const match = userAgent.match(/version\/([0-9.]+)/)
    version = match ? match[1] : ''
  } else if (isChrome) {
    const match = userAgent.match(/chrome\/([0-9.]+)/)
    version = match ? match[1] : ''
  } else if (isFirefox) {
    const match = userAgent.match(/firefox\/([0-9.]+)/)
    version = match ? match[1] : ''
  }

  // Feature detection
  const supportsTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
  const supportsVibration = 'vibrate' in navigator
  const supportsWebGL = (() => {
    try {
      const canvas = document.createElement('canvas')
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    } catch {
      return false
    }
  })()

  return {
    isMobile,
    isIOS,
    isAndroid,
    isSafari,
    isChrome,
    isFirefox,
    version,
    supportsTouch,
    supportsVibration,
    supportsWebGL,
    devicePixelRatio: window.devicePixelRatio || 1
  }
}

// Safari-specific optimizations
export function applySafariOptimizations() {
  if (typeof window === 'undefined') return

  const browserInfo = getBrowserInfo()
  if (!browserInfo.isSafari) return

  // Prevent zoom on input focus
  const viewportMeta = document.querySelector('meta[name="viewport"]')
  if (viewportMeta) {
    viewportMeta.setAttribute('content', 
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
    )
  }

  // Fix 100vh issue on iOS Safari
  const setVH = () => {
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)
  }
  setVH()
  window.addEventListener('resize', setVH)
  window.addEventListener('orientationchange', setVH)

  // Prevent bounce scrolling
  document.body.style.overscrollBehavior = 'none'
  
  return () => {
    window.removeEventListener('resize', setVH)
    window.removeEventListener('orientationchange', setVH)
  }
}

// Chrome mobile optimizations
export function applyChromeOptimizations() {
  if (typeof window === 'undefined') return

  const browserInfo = getBrowserInfo()
  if (!browserInfo.isChrome) return

  // Enable hardware acceleration for smooth animations
  const style = document.createElement('style')
  style.textContent = `
    .chrome-optimized {
      transform: translateZ(0);
      will-change: transform, opacity;
      backface-visibility: hidden;
      perspective: 1000px;
    }
    
    .chrome-smooth-scroll {
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
    }
  `
  document.head.appendChild(style)

  // Optimize touch events
  document.addEventListener('touchstart', () => {}, { passive: true })
  document.addEventListener('touchmove', () => {}, { passive: true })
}

// Firefox mobile optimizations
export function applyFirefoxOptimizations() {
  if (typeof window === 'undefined') return

  const browserInfo = getBrowserInfo()
  if (!browserInfo.isFirefox) return

  // Firefox-specific touch event normalization
  const style = document.createElement('style')
  style.textContent = `
    .firefox-touch-fix {
      touch-action: manipulation;
      -moz-user-select: none;
      -moz-touch-callout: none;
    }
    
    .firefox-smooth-animation {
      animation-fill-mode: both;
      animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
  `
  document.head.appendChild(style)
}

// Universal mobile optimizations
export function applyUniversalMobileOptimizations() {
  if (typeof window === 'undefined') return

  const browserInfo = getBrowserInfo()
  if (!browserInfo.isMobile) return

  // Add mobile-optimized CSS
  const style = document.createElement('style')
  style.textContent = `
    .mobile-optimized {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
      
      /* Prevent text size adjustment */
      -webkit-text-size-adjust: 100%;
      -moz-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
      text-size-adjust: 100%;
      
      /* Optimize touch interactions */
      touch-action: manipulation;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -webkit-tap-highlight-color: transparent;
      user-select: none;
    }
    
    .mobile-game-container {
      /* Prevent zoom and scroll during games */
      touch-action: none;
      overscroll-behavior: none;
      -webkit-overflow-scrolling: auto;
    }
    
    .mobile-button {
      /* Larger touch targets for mobile */
      min-height: 44px;
      min-width: 44px;
      padding: 12px 16px;
    }
    
    .mobile-input {
      /* Prevent zoom on input focus */
      font-size: 16px;
    }
  `
  document.head.appendChild(style)

  // Add mobile classes to body
  document.body.classList.add('mobile-optimized')
  
  // Prevent double-tap zoom
  let lastTouchEnd = 0
  document.addEventListener('touchend', (event) => {
    const now = new Date().getTime()
    if (now - lastTouchEnd <= 300) {
      event.preventDefault()
    }
    lastTouchEnd = now
  }, false)
}

// Initialize all mobile optimizations
export function initializeMobileOptimizations() {
  if (typeof window === 'undefined') return

  const cleanupFunctions: (() => void)[] = []

  // Apply universal optimizations
  applyUniversalMobileOptimizations()

  // Apply browser-specific optimizations
  const safariCleanup = applySafariOptimizations()
  if (safariCleanup) cleanupFunctions.push(safariCleanup)

  applyChromeOptimizations()
  applyFirefoxOptimizations()

  // Return cleanup function
  return () => {
    cleanupFunctions.forEach(cleanup => cleanup())
  }
}

// Hook for React components
export function useMobileBrowserOptimizations() {
  if (typeof window === 'undefined') return

  // Note: This should be used with React.useEffect in the component
  // React.useEffect(() => {
  //   const cleanup = initializeMobileOptimizations()
  //   return cleanup
  // }, [])
}

// Performance monitoring for mobile
export function measureMobilePerformance() {
  if (typeof window === 'undefined' || !window.performance) return

  const browserInfo = getBrowserInfo()
  if (!browserInfo.isMobile) return

  const perfData = {
    loadTime: window.performance.timing.loadEventEnd - window.performance.timing.navigationStart,
    domReady: window.performance.timing.domContentLoadedEventEnd - window.performance.timing.navigationStart,
    firstPaint: 0,
    deviceInfo: browserInfo
  }

  // Get First Paint if available
  if (window.performance.getEntriesByType) {
    const paintEntries = window.performance.getEntriesByType('paint')
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint')
    if (firstPaint) {
      perfData.firstPaint = firstPaint.startTime
    }
  }

  console.log('Mobile Performance Metrics:', perfData)
  return perfData
}
