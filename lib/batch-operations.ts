import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Use service role for batch operations
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { persistSession: false }
})

export interface BatchDeleteResult {
  id: number
  method: string
  url: string
  timeout_milliseconds: number
  headers: string[]
  body: string
}

/**
 * Batch delete HTTP requests using simplified RPC function
 * Replaces complex CTE DELETE with PostgREST-compatible RPC call
 */
export async function batchDeleteHttpRequests(
  batchLimit: number = 100,
  headerSeparator: string = ': '
): Promise<BatchDeleteResult[]> {
  const { data, error } = await supabaseAdmin.rpc('batch_delete_http_requests', {
    batch_limit: batchLimit,
    header_separator: headerSeparator
  })

  if (error) {
    console.error('Batch delete HTTP requests failed:', error)
    throw new Error(`Failed to batch delete HTTP requests: ${error.message}`)
  }

  return data || []
}

/**
 * Batch delete old HTTP responses using simplified RPC function
 */
export async function batchDeleteOldResponses(
  olderThan: string = '1 day',
  batchLimit: number = 1000
): Promise<number> {
  const { data, error } = await supabaseAdmin.rpc('batch_delete_old_responses', {
    older_than: olderThan,
    batch_limit: batchLimit
  })

  if (error) {
    console.error('Batch delete old responses failed:', error)
    throw new Error(`Failed to batch delete old responses: ${error.message}`)
  }

  return data || 0
}

/**
 * Cleanup old QR interactions from your actual schema
 */
export async function cleanupOldQrInteractions(
  olderThan: string = '30 days',
  batchLimit: number = 500
): Promise<number> {
  const { data, error } = await supabaseAdmin.rpc('cleanup_old_qr_interactions', {
    older_than: olderThan,
    batch_limit: batchLimit
  })

  if (error) {
    console.error('Cleanup QR interactions failed:', error)
    throw new Error(`Failed to cleanup QR interactions: ${error.message}`)
  }

  return data || 0
}

/**
 * Cleanup old business visits from your actual schema
 */
export async function cleanupOldBusinessVisits(
  olderThan: string = '90 days',
  batchLimit: number = 500
): Promise<number> {
  const { data, error } = await supabaseAdmin.rpc('cleanup_old_business_visits', {
    older_than: olderThan,
    batch_limit: batchLimit
  })

  if (error) {
    console.error('Cleanup business visits failed:', error)
    throw new Error(`Failed to cleanup business visits: ${error.message}`)
  }

  return data || 0
}

/**
 * Run all cleanup operations in sequence
 * Throttled to prevent connection saturation
 */
export async function runMaintenanceCleanup(): Promise<{
  qrInteractions: number
  businessVisits: number
  httpResponses?: number
}> {
  const results = {
    qrInteractions: 0,
    businessVisits: 0,
    httpResponses: 0
  }

  try {
    // Cleanup QR interactions (smaller batches to avoid locks)
    results.qrInteractions = await cleanupOldQrInteractions('30 days', 200)
    
    // Wait between operations to reduce lock contention
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Cleanup business visits
    results.businessVisits = await cleanupOldBusinessVisits('90 days', 200)
    
    // Wait before potential HTTP cleanup
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Only run HTTP cleanup if those tables exist
    try {
      results.httpResponses = await batchDeleteOldResponses('1 day', 500)
    } catch (err) {
      console.log('HTTP response cleanup skipped (table may not exist)')
    }

    console.log('Maintenance cleanup completed:', results)
    return results
    
  } catch (error) {
    console.error('Maintenance cleanup failed:', error)
    throw error
  }
}