import { createClient, SupabaseClient } from '@supabase/supabase-js'

// Optimized storage service with single connection and better error handling
class SupabaseStorageService {
  private client: SupabaseClient | null = null
  private retryAttempts = 3
  private retryDelay = 1000 // 1 second
  private connectionTimeout = 10000 // 10 seconds

  constructor() {
    this.initializeClient()
  }

  private initializeClient() {
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
      // Only use anon key for client-side operations
      // Service role operations should be handled server-side only
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

      this.client = createClient(supabaseUrl, supabaseKey, {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
        },
        global: {
          headers: {
            'X-Client-Info': 'fuse-storage-service',
          }
        },
        db: {
          schema: 'storage'
        }
      })
    } catch (error) {
      console.error('Failed to initialize storage client:', error)
      this.client = null
    }
  }

  private async getClient(): Promise<SupabaseClient> {
    if (!this.client) {
      this.initializeClient()
    }
    
    if (!this.client) {
      throw new Error('Storage client not available - initialization failed')
    }
    
    return this.client
  }

  private async retry<T>(
    operation: () => Promise<T>,
    attempt = 1
  ): Promise<T> {
    try {
      return await operation()
    } catch (error) {
      if (attempt >= this.retryAttempts) {
        throw error
      }

      // Exponential backoff with jitter
      const delay = this.retryDelay * Math.pow(2, attempt - 1) + Math.random() * 1000
      await new Promise(resolve => setTimeout(resolve, delay))
      
      console.warn(`Storage operation failed, retrying (attempt ${attempt + 1}/${this.retryAttempts}):`, error)
      return this.retry(operation, attempt + 1)
    }
  }

  async uploadFile(
    bucket: string,
    path: string,
    file: File,
    options?: {
      cacheControl?: string
      contentType?: string
      upsert?: boolean
    }
  ): Promise<{ data: any; error: any }> {
    const client = await this.getClient()
    
    return await this.retry(async () => {
      const { data, error } = await client.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: options?.cacheControl || '3600',
          contentType: options?.contentType || file.type,
          upsert: options?.upsert || true
        })

      if (error) {
        // Handle specific error types
        if (error.message?.includes('413') || error.message?.includes('too large')) {
          throw new Error('File is too large. Please upload a smaller file.')
        }
        if (error.message?.includes('429') || error.message?.includes('rate limit')) {
          throw new Error('Too many requests. Please wait a moment and try again.')
        }
        if (error.message?.includes('503') || error.message?.includes('service unavailable')) {
          throw new Error('Storage service is temporarily unavailable. Please try again.')
        }
        if (error.message?.includes('connections') || error.message?.includes('PGRST002')) {
          throw new Error('Database connection limit reached. Please try again in a moment.')
        }
        throw error
      }

      return { data, error }
    })
  }

  async deleteFile(bucket: string, path: string): Promise<{ data: any; error: any }> {
    const client = await this.getClient()
    
    return await this.retry(async () => {
      return await client.storage.from(bucket).remove([path])
    })
  }

  async getPublicUrl(bucket: string, path: string): Promise<string> {
    const client = await this.getClient()
    const { data } = client.storage.from(bucket).getPublicUrl(path)
    return data.publicUrl
  }

  async createSignedUrl(
    bucket: string, 
    path: string, 
    expiresIn: number = 3600
  ): Promise<{ data: any; error: any }> {
    const client = await this.getClient()
    
    return await this.retry(async () => {
      return await client.storage.from(bucket).createSignedUrl(path, expiresIn)
    })
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      const client = await this.getClient()
      const { data, error } = await client.storage.listBuckets()
      return !error
    } catch {
      return false
    }
  }

  // Get connection stats
  getStats() {
    return {
      clientInitialized: this.client !== null,
      serviceAvailable: true
    }
  }
}

// Create singleton instance
const storageService = new SupabaseStorageService()

export default storageService

// Helper functions for common operations
export async function uploadBusinessLogo(
  businessId: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<{ url: string; error?: string }> {
  try {
    // Validate file
    if (!file) {
      throw new Error('No file provided')
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      throw new Error('File size must be less than 5MB')
    }

    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml']
    if (!allowedTypes.includes(file.type)) {
      throw new Error('File must be an image (JPEG, PNG, WebP, or SVG)')
    }

    // Generate unique filename
    const timestamp = Date.now()
    const fileExt = file.name.split('.').pop()
    const fileName = `logo-${businessId}-${timestamp}.${fileExt}`
    const filePath = `business-logos/${fileName}`

    onProgress?.(10)

    // Upload file using storage service
    const { data, error } = await storageService.uploadFile(
      'business-assets',
      filePath,
      file,
      {
        contentType: file.type,
        upsert: true
      }
    )

    if (error) {
      throw new Error(error.message || 'Upload failed')
    }

    onProgress?.(90)

    // Get public URL
    const publicUrl = await storageService.getPublicUrl('business-assets', filePath)
    
    onProgress?.(100)

    return { url: publicUrl }
  } catch (error) {
    console.error('Logo upload error:', error)
    return { 
      url: '', 
      error: error instanceof Error ? error.message : 'Upload failed' 
    }
  }
}

export async function deleteBusinessLogo(logoUrl: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Extract path from URL
    const urlParts = logoUrl.split('/business-assets/')
    if (urlParts.length !== 2) {
      throw new Error('Invalid logo URL format')
    }

    const filePath = urlParts[1]
    const { error } = await storageService.deleteFile('business-assets', filePath)

    if (error) {
      throw new Error(error.message || 'Delete failed')
    }

    return { success: true }
  } catch (error) {
    console.error('Logo delete error:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Delete failed' 
    }
  }
}

// Export storage service instance for direct access
export { storageService }