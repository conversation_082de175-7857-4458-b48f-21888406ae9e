"use client"

import { XummSdk } from 'xumm-sdk'
import { XAMAN_CONFIG } from './xaman-config'

// Xumm SDK configuration (using centralized config)
const XUMM_CONFIG = {
  API_KEY: XAMAN_CONFIG.API_KEY,
  API_SECRET: XAMAN_CONFIG.API_SECRET,
}

// Initialize Xumm SDK
let xummSdk: XummSdk | null = null

if (typeof window !== 'undefined') {
  // Client-side only initialization - only initialize if we have both key and secret
  if (XUMM_CONFIG.API_KEY && XUMM_CONFIG.API_SECRET) {
    try {
      xummSdk = new XummSdk(XUMM_CONFIG.API_KEY, XUMM_CONFIG.API_SECRET)
    } catch (error) {
      console.warn('Failed to initialize XummSdk:', error)
      xummSdk = null
    }
  } else {
    console.warn('XummSdk not initialized: Missing API key or secret. Using browser-based Xumm integration instead.')
  }
}

export interface XummPayloadRequest {
  TransactionType: "Payment" | "SignIn"
  Account?: string
  Destination?: string
  Amount?: string | object
  Memos?: Array<{
    Memo: {
      MemoType: string
      MemoData: string
    }
  }>
}

export interface XummSubmitResponse {
  uuid: string
  next: {
    always: string
    no_push_msg_received?: string
  }
  refs: {
    qr_png: string
    qr_matrix: string
    qr_uri_quality_opts: string[]
    websocket_status: string
  }
  pushed: boolean
}

export interface XummGetResponse {
  meta: {
    exists: boolean
    uuid: string
    multisign: boolean
    submit: boolean
    destination: string
    resolved_destination: string
    signed: boolean
    cancelled: boolean
    expired: boolean
    pushed: boolean
    app_opened: boolean
    return_url_app: string | null
    return_url_web: string | null
  }
  application: {
    name: string
    description: string
    disabled: number
    uuidv4: string
    icon_url: string
    issued_user_token: string | null
  }
  payload: {
    tx_type: string
    tx_destination: string
    tx_destination_tag: number | null
    request_json: any
    created_at: string
    expires_at: string
  }
  response?: {
    hex: string
    txid: string
    resolved_at: string
    dispatched_result: string
    multisign_account: string
    account: string
  }
  custom_meta?: {
    identifier: string | null
    blob: any
    instruction: string | null
  }
}

export class XummPaymentService {
  private sdk: XummSdk | null = null

  constructor() {
    if (typeof window !== 'undefined' && xummSdk) {
      this.sdk = xummSdk
    }
  }

  // Create a payment request and get magic link
  async createPaymentRequest(paymentData: any): Promise<XummSubmitResponse> {
    if (!this.sdk) {
      throw new Error('Xumm SDK not initialized. Please ensure XUMM_API_SECRET is configured, or use the browser-based Xumm integration instead.')
    }

    try {
      const payload = await this.sdk.payload.create(paymentData)
      return payload as XummSubmitResponse
    } catch (error) {
      console.error('Failed to create Xumm payment request:', error)
      throw error
    }
  }

  // Get payment status
  async getPaymentStatus(uuid: string): Promise<XummGetResponse> {
    if (!this.sdk) {
      throw new Error('Xumm SDK not initialized')
    }

    try {
      const result = await this.sdk.payload.get(uuid)
      return result as XummGetResponse
    } catch (error) {
      console.error('Failed to get Xumm payment status:', error)
      throw error
    }
  }

  // Monitor payment until completion or timeout
  async monitorPayment(uuid: string, timeoutMs: number = 300000): Promise<XummGetResponse> {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeoutMs) {
      const status = await this.getPaymentStatus(uuid)
      
      if (status.meta.signed || status.meta.cancelled || status.meta.expired) {
        return status
      }
      
      // Wait 2 seconds before checking again
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
    
    throw new Error('Payment monitoring timed out')
  }

  // Create XRP payment transaction
  createXRPPayment(senderAddress: string, amount: string, destinationAddress: string): XummPayloadRequest {
    return {
      TransactionType: "Payment",
      Account: senderAddress,
      Destination: destinationAddress,
      Amount: amount,
      Memos: [{
        Memo: {
          MemoType: this.stringToHex('fuse-vip-payment'),
          MemoData: this.stringToHex(JSON.stringify({
            service: 'fuse-vip',
            timestamp: Date.now()
          }))
        }
      }]
    }
  }

  // Create FUSE token payment transaction
  createFUSEPayment(senderAddress: string, amount: string, destinationAddress: string, tokenIssuer: string): XummPayloadRequest {
    return {
      TransactionType: "Payment",
      Account: senderAddress,
      Destination: destinationAddress,
      Amount: {
        currency: 'FUSE',
        issuer: tokenIssuer,
        value: amount
      },
      Memos: [{
        Memo: {
          MemoType: this.stringToHex('fuse-vip-payment'),
          MemoData: this.stringToHex(JSON.stringify({
            service: 'fuse-vip',
            timestamp: Date.now(),
            currency: 'FUSE'
          }))
        }
      }]
    }
  }

  // Open Xaman app with magic link
  openXamanApp(magicLink: string): void {
    if (typeof window !== 'undefined') {
      // Try to open in Xaman app first
      const xamanAppLink = magicLink.replace('https://xumm.app/', 'xumm://')
      
      // Create a hidden iframe to trigger the app
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      iframe.src = xamanAppLink
      document.body.appendChild(iframe)
      
      // Fallback to web link after a short delay
      setTimeout(() => {
        document.body.removeChild(iframe)
        window.open(magicLink, '_blank', 'noopener,noreferrer')
      }, 1000)
    }
  }

  // Check if Xumm SDK is available
  isAvailable(): boolean {
    return this.sdk !== null
  }

  // Convert string to hex (browser-compatible alternative to Buffer)
  private stringToHex(str: string): string {
    let hex = ''
    for (let i = 0; i < str.length; i++) {
      const charCode = str.charCodeAt(i)
      hex += charCode.toString(16).padStart(2, '0')
    }
    return hex.toUpperCase()
  }
}

// Export singleton instance
export const xummService = new XummPaymentService()

// Utility functions
export const isValidXummResponse = (response: XummGetResponse): boolean => {
  return response.meta.exists && !response.meta.cancelled && !response.meta.expired
}

export const isPaymentSigned = (response: XummGetResponse): boolean => {
  return response.meta.signed && !!response.response?.txid
}

export const getTransactionId = (response: XummGetResponse): string | null => {
  return response.response?.txid || null
}