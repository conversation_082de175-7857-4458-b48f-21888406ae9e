// Centralized Xaman/XUMM configuration
// Using NEW Xaman app with API key ending in 2a5a
export const XAMAN_CONFIG = {
  // API Configuration - NEW APP ONLY
  API_KEY: process.env.NEXT_PUBLIC_XAMAN_API_KEY || '',
  API_SECRET: process.env.XUMM_API_SECRET || '',
  
  // Redirect URLs - these must be registered in your Xaman developer console
  REDIRECT_URLS: {
    // Primary callback for wallet connection (OAuth2 flow)
    WALLET_CONNECTED: '/wallet-connected',
    // Legacy callbacks for backward compatibility
    DASHBOARD: '/dashboard',
    XUMM_CALLBACK: '/xumm-callback',
    // Payment completion callback
    PAYMENT_SUCCESS: '/payment-success',
    // General success callback
    SUCCESS: '/xaman-success'
  },
  
  // Full URLs for different environments
  getRedirectUrl: (path: string): string => {
    if (typeof window === 'undefined') {
      // Server-side: use environment variable or fallback
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
      return `${baseUrl}${path}`;
    }
    
    // Client-side: use current origin
    return `${window.location.origin}${path}`;
  },
  
  // Get all possible redirect URLs for registration
  getAllRedirectUrls: (): string[] => {
    const baseUrls = [
      'https://fuse.vip',
      'https://www.fuse.vip', 
      'http://localhost:3000',
      'http://localhost:3001'
    ];
    
    const paths = Object.values(XAMAN_CONFIG.REDIRECT_URLS);
    
    const allUrls: string[] = [];
    baseUrls.forEach(baseUrl => {
      paths.forEach(path => {
        allUrls.push(`${baseUrl}${path}`);
      });
    });
    
    return allUrls;
  },
  
  // App metadata for Xaman
  APP_META: {
    name: '$Fuse Rewards',
    description: 'VIP Card and FUSE Token Platform',
    icon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png',
  }
};

// Validation helper
export const validateXamanConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!XAMAN_CONFIG.API_KEY) {
    errors.push('NEXT_PUBLIC_XAMAN_API_KEY is not configured');
  }
  
  if (!XAMAN_CONFIG.API_SECRET) {
    errors.push('XUMM_API_SECRET is not configured');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};
