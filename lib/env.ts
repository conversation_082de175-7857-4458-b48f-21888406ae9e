// This file provides a unified way to access environment variables
// regardless of whether we're in a Vite or Next.js environment

// Validate required environment variables
function validateEnvVar(name: string, value: string | undefined): string {
  if (!value) {
    throw new Error(`Missing required environment variable: ${name}`)
  }
  return value
}

// Client-safe environment variables
export const clientEnv = {
  // Supabase
  SUPABASE_URL: validateEnvVar(
    'SUPABASE_URL',
    process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.VITE_SUPABASE_URL
  ),
  SUPABASE_ANON_KEY: validateEnvVar(
    'SUPABASE_ANON_KEY',
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY
  ),

  // Stripe
  STRIPE_PUBLISHABLE_KEY: validateEnvVar(
    'STRIPE_PUBLISHABLE_KEY',
    process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || process.env.VITE_STRIPE_PUBLISHABLE_KEY
  ),

  // Xaman - client-side
  XAMAN_API_KEY: process.env.NEXT_PUBLIC_XAMAN_API_KEY || "",
}

// Server-only environment variables (never expose to client)
export const serverEnv = {
  SUPABASE_SERVICE_ROLE_KEY: validateEnvVar(
    'SUPABASE_SERVICE_ROLE_KEY',
    process.env.SUPABASE_SERVICE_ROLE_KEY
  ),
  SUPABASE_JWT_SECRET: validateEnvVar(
    'SUPABASE_JWT_SECRET',
    process.env.SUPABASE_JWT_SECRET
  ),
  SUPABASE_DB_PASSWORD: validateEnvVar(
    'SUPABASE_DB_PASSWORD',
    process.env.SUPABASE_DB_PASSWORD
  ),
  DATABASE_URL: process.env.DATABASE_URL,


  // Xaman - server-only
  XAMAN_API_KEY: process.env.XAMAN_API_KEY || "",
}

// For backward compatibility - ONLY USE IN SERVER COMPONENTS
export const env = {
  ...clientEnv,
  // These are intentionally not included to prevent client exposure
  // XAMAN_API_KEY: serverEnv.XAMAN_API_KEY,
}

// Helper function to check if required environment variables are set
export function checkRequiredEnvVars(requiredVars: string[], envObject = clientEnv) {
  const missing = requiredVars.filter((key) => !envObject[key as keyof typeof envObject])

  if (missing.length > 0) {
    console.warn(`Missing required environment variables: ${missing.join(", ")}`)
    return false
  }

  return true
}

// Helper to validate a URL string
export function isValidUrl(urlString: string): boolean {
  try {
    new URL(urlString)
    return true
  } catch (err) {
    return false
  }
}
