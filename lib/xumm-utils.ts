import { XummSdk } from 'xumm-sdk';

export interface XummTransaction {
  txjson: {
    TransactionType: string;
    Destination?: string;
    Amount?: string;
    Memos?: Array<{
      Memo: {
        MemoType: string;
        MemoData: string;
      };
    }>;
    [key: string]: any;
  };
  options?: {
    return_url?: {
      app?: string;
      web?: string;
    };
    force_network?: 'MAINNET' | 'TESTNET';
    submit?: boolean;
  };
  custom_meta?: {
    identifier?: string;
    instruction?: string;
    blob?: {
      appName?: string;
      appIcon?: string;
    };
  };
}

export interface XummPayloadResponse {
  uuid: string;
  next: {
    always: string;
    no_push_msg_received?: string;
  };
  refs: {
    qr_png: string;
    qr_matrix: string;
    qr_uri_quality_opts: string[];
    websocket_status: string;
  };
  pushed: boolean;
}

export interface XummGetResponse {
  meta: {
    signed: boolean;
    uuid: string;
    multisigned: boolean;
    submit: boolean;
    destination: string;
    resolved_destination: string;
    txid: string;
    user_token: {
      user_token: string;
      token_issued: number;
      token_expiry: number;
    };
    return_url: {
      app: string;
      web: string;
    };
  };
  response: {
    txid: string;
    account: string;
    hex: string;
    multisigned: boolean;
    dispatched_to: string;
    dispatched_result: string;
    dispatched_nodetype: string;
    network_id: number;
  };
  custom_meta: {
    identifier: string;
    instruction: string;
    blob: {
      appName: string;
      appIcon: string;
    };
  };
}

export interface XummPaymentRequest {
  destination: string;
  amount: string;
  memo?: string;
  returnUrl?: string;
  instruction?: string;
}

export class XummService {
  private static instance: XummService;
  private xumm: XummSdk | null = null;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): XummService {
    if (!XummService.instance) {
      XummService.instance = new XummService();
    }
    return XummService.instance;
  }

  public async initialize(apiKey: string, apiSecret?: string): Promise<void> {
    if (this.isInitialized) return;

    if (!apiKey) {
      throw new Error('XUMM API key is required');
    }

    if (!apiSecret) {
      console.warn('XUMM API secret not provided. Server-side SDK features will be limited. Consider using browser-based Xumm integration instead.');
      throw new Error('XUMM API secret is required for server-side SDK initialization');
    }

    try {
      this.xumm = new XummSdk(apiKey, apiSecret);
      await this.xumm.ping();
      this.isInitialized = true;
      console.log('XUMM SDK initialized successfully');
    } catch (error) {
      console.error('Failed to initialize XUMM SDK:', error);
      throw error;
    }
  }

  public isReady(): boolean {
    return this.isInitialized && this.xumm !== null;
  }

  public async createPayment(request: XummPaymentRequest): Promise<XummPayloadResponse> {
    if (!this.isReady()) {
      throw new Error('XUMM SDK not initialized');
    }

    const payload: XummTransaction = {
      txjson: {
        TransactionType: 'Payment',
        Destination: request.destination,
        Amount: request.amount,
      },
      options: {
        return_url: {
          app: request.returnUrl || `${window.location.origin}/payment-success`,
          web: request.returnUrl || `${window.location.origin}/payment-success`,
        },
        force_network: process.env.NODE_ENV === 'production' ? 'MAINNET' : 'TESTNET',
        submit: true,
      },
      custom_meta: {
        identifier: `fuse_payment_${Date.now()}`,
        instruction: request.instruction || 'Complete your Fuse.vip payment',
        blob: {
          appName: 'Fuse.vip',
          appIcon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png',
        },
      },
    };

    if (request.memo) {
      payload.txjson.Memos = [
        {
          Memo: {
            MemoType: Buffer.from('memo').toString('hex').toUpperCase(),
            MemoData: Buffer.from(request.memo).toString('hex').toUpperCase(),
          },
        },
      ];
    }

    try {
      const response = await this.xumm!.payload.create(payload);
      return response;
    } catch (error) {
      console.error('Failed to create payment payload:', error);
      throw error;
    }
  }

  public async getPaymentStatus(uuid: string): Promise<XummGetResponse> {
    if (!this.isReady()) {
      throw new Error('XUMM SDK not initialized');
    }

    try {
      const response = await this.xumm!.payload.get(uuid);
      return response;
    } catch (error) {
      console.error('Failed to get payment status:', error);
      throw error;
    }
  }

  public async createTrustSet(currency: string, issuer: string, limit?: string): Promise<XummPayloadResponse> {
    if (!this.isReady()) {
      throw new Error('XUMM SDK not initialized');
    }

    const payload: XummTransaction = {
      txjson: {
        TransactionType: 'TrustSet',
        LimitAmount: {
          currency: currency,
          issuer: issuer,
          value: limit || '1000000000',
        },
      },
      options: {
        return_url: {
          app: `${window.location.origin}/trustset-success`,
          web: `${window.location.origin}/trustset-success`,
        },
        force_network: process.env.NODE_ENV === 'production' ? 'MAINNET' : 'TESTNET',
        submit: true,
      },
      custom_meta: {
        identifier: `fuse_trustset_${Date.now()}`,
        instruction: `Set trust line for ${currency} token`,
        blob: {
          appName: 'Fuse.vip',
          appIcon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png',
        },
      },
    };

    try {
      const response = await this.xumm!.payload.create(payload);
      return response;
    } catch (error) {
      console.error('Failed to create trustset payload:', error);
      throw error;
    }
  }

  public async waitForPayment(uuid: string, timeout: number = 300000): Promise<XummGetResponse> {
    if (!this.isReady()) {
      throw new Error('XUMM SDK not initialized');
    }

    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const checkInterval = setInterval(async () => {
        try {
          const response = await this.getPaymentStatus(uuid);
          
          if (response.meta.signed === true) {
            clearInterval(checkInterval);
            resolve(response);
          } else if (response.meta.signed === false) {
            clearInterval(checkInterval);
            reject(new Error('Payment was rejected or cancelled'));
          } else if (Date.now() - startTime > timeout) {
            clearInterval(checkInterval);
            reject(new Error('Payment timeout'));
          }
        } catch (error) {
          clearInterval(checkInterval);
          reject(error);
        }
      }, 2000);
    });
  }

  public generateQRCode(uuid: string): string {
    return `https://xumm.app/sign/${uuid}`;
  }

  public generateDeepLink(uuid: string): string {
    return `xumm://xumm.app/sign/${uuid}`;
  }
}

export const xummService = XummService.getInstance();

export const formatXRPAmount = (amount: string): string => {
  const numAmount = parseFloat(amount);
  if (isNaN(numAmount)) return '0';
  
  if (numAmount >= 1000000) {
    return (numAmount / 1000000).toFixed(2) + 'M';
  } else if (numAmount >= 1000) {
    return (numAmount / 1000).toFixed(2) + 'K';
  }
  return numAmount.toFixed(6);
};

export const convertXRPToDrops = (xrp: string): string => {
  const numXRP = parseFloat(xrp);
  if (isNaN(numXRP)) return '0';
  return (numXRP * 1000000).toString();
};

export const convertDropsToXRP = (drops: string): string => {
  const numDrops = parseFloat(drops);
  if (isNaN(numDrops)) return '0';
  return (numDrops / 1000000).toString();
};