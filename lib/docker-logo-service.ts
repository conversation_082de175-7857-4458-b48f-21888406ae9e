/**
 * Docker logo processing service integration
 * Handles communication with the Docker-based logo processor
 */

export interface DockerLogoProcessResult {
  success: boolean;
  fileId: string;
  originalName: string;
  processedImages: {
    original: {
      path: string;
      metadata: {
        width: number;
        height: number;
        format: string;
        size: number;
      };
    };
    thumbnail: { path: string; dimensions: { width: number; height: number } };
    small: { path: string; dimensions: { width: number; height: number } };
    medium: { path: string; dimensions: { width: number; height: number } };
    large: { path: string; dimensions: { width: number; height: number } };
  };
  metadata: {
    width: number;
    height: number;
    format: string;
    size: number;
  };
}

export interface DockerLogoServiceConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
}

const DEFAULT_CONFIG: DockerLogoServiceConfig = {
  baseUrl: process.env.DOCKER_LOGO_SERVICE_URL || 'http://localhost:3001',
  timeout: 30000, // 30 seconds
  retries: 3
};

class DockerLogoService {
  private config: DockerLogoServiceConfig;

  constructor(config: Partial<DockerLogoServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Process a logo file using the Docker service
   */
  async processLogo(file: File): Promise<DockerLogoProcessResult> {
    const formData = new FormData();
    formData.append('logo', file);

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        const response = await fetch(`${this.config.baseUrl}/api/process-logo`, {
          method: 'POST',
          body: formData,
          signal: controller.signal,
          headers: {
            // Don't set Content-Type, let the browser set it with boundary
          }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Logo processing failed');
        }

        return result;

      } catch (error) {
        lastError = error as Error;
        console.warn(`Logo processing attempt ${attempt} failed:`, error);

        if (attempt < this.config.retries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw new Error(`Logo processing failed after ${this.config.retries} attempts: ${lastError?.message}`);
  }

  /**
   * Get a processed logo URL
   */
  getLogoUrl(fileId: string, size: 'original' | 'thumbnail' | 'small' | 'medium' | 'large' = 'original'): string {
    return `${this.config.baseUrl}/api/logo/${fileId}/${size}`;
  }

  /**
   * Check if the Docker service is healthy
   */
  async checkHealth(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout for health check

      const response = await fetch(`${this.config.baseUrl}/health`, {
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      
      if (!response.ok) {
        return false;
      }

      const health = await response.json();
      return health.status === 'healthy';

    } catch (error) {
      console.warn('Docker logo service health check failed:', error);
      return false;
    }
  }

  /**
   * Validate file before processing
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'image/svg+xml'
    ];

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (10MB)`
      };
    }

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
      };
    }

    return { isValid: true };
  }

  /**
   * Process logo with fallback to direct upload if Docker service is unavailable
   */
  async processLogoWithFallback(
    file: File,
    fallbackUploader: (file: File) => Promise<string>
  ): Promise<{ 
    dockerProcessed: boolean; 
    result?: DockerLogoProcessResult; 
    fallbackUrl?: string;
    metadata: any;
  }> {
    // Validate file first
    const validation = this.validateFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Check if Docker service is available
    const isHealthy = await this.checkHealth();
    
    if (isHealthy) {
      try {
        const result = await this.processLogo(file);
        return {
          dockerProcessed: true,
          result,
          metadata: result.metadata
        };
      } catch (error) {
        console.warn('Docker processing failed, falling back to direct upload:', error);
      }
    }

    // Fallback to direct upload
    const fallbackUrl = await fallbackUploader(file);
    
    // Generate basic metadata
    const metadata = await this.getBasicMetadata(file);
    
    return {
      dockerProcessed: false,
      fallbackUrl,
      metadata
    };
  }

  /**
   * Get basic metadata for a file (fallback when Docker service is unavailable)
   */
  private async getBasicMetadata(file: File): Promise<any> {
    return new Promise((resolve) => {
      if (file.type === 'image/svg+xml') {
        resolve({
          width: 0, // SVG is scalable
          height: 0,
          format: 'svg',
          size: file.size,
          mimeType: file.type
        });
        return;
      }

      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({
          width: img.width,
          height: img.height,
          format: file.type.split('/')[1],
          size: file.size,
          mimeType: file.type
        });
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        resolve({
          width: 0,
          height: 0,
          format: file.type.split('/')[1] || 'unknown',
          size: file.size,
          mimeType: file.type
        });
      };

      img.src = url;
    });
  }
}

// Export singleton instance
export const dockerLogoService = new DockerLogoService();

// Export class for custom configurations
export { DockerLogoService };
