/**
 * Image optimization utilities for business logos and other images
 * Provides validation, processing, and optimization functions
 */

export interface ImageMetadata {
  width: number;
  height: number;
  fileSize: number;
  mimeType: string;
  aspectRatio: number;
}

export interface OptimizedImageResult {
  originalUrl: string;
  optimizedUrl?: string;
  metadata: ImageMetadata;
  altText?: string;
}

export interface ImageValidationOptions {
  maxFileSize?: number; // in bytes
  maxWidth?: number;
  maxHeight?: number;
  allowedMimeTypes?: string[];
  minWidth?: number;
  minHeight?: number;
}

// Default validation options for business logos
export const DEFAULT_LOGO_VALIDATION: ImageValidationOptions = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  maxWidth: 2000,
  maxHeight: 2000,
  minWidth: 100,
  minHeight: 100,
  allowedMimeTypes: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp',
    'image/svg+xml'
  ]
};

/**
 * Validate image file before upload
 */
export function validateImageFile(file: File, options: ImageValidationOptions = DEFAULT_LOGO_VALIDATION): Promise<{ isValid: boolean; error?: string; metadata?: ImageMetadata }> {
  return new Promise((resolve) => {
    // Check file size
    if (options.maxFileSize && file.size > options.maxFileSize) {
      resolve({
        isValid: false,
        error: `File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(options.maxFileSize)})`
      });
      return;
    }

    // Check MIME type
    if (options.allowedMimeTypes && !options.allowedMimeTypes.includes(file.type)) {
      resolve({
        isValid: false,
        error: `File type ${file.type} is not allowed. Allowed types: ${options.allowedMimeTypes.join(', ')}`
      });
      return;
    }

    // For SVG files, we can't easily get dimensions, so just validate basic properties
    if (file.type === 'image/svg+xml') {
      resolve({
        isValid: true,
        metadata: {
          width: 0, // SVG is scalable
          height: 0,
          fileSize: file.size,
          mimeType: file.type,
          aspectRatio: 1
        }
      });
      return;
    }

    // Create image element to get dimensions
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);
      
      const metadata: ImageMetadata = {
        width: img.width,
        height: img.height,
        fileSize: file.size,
        mimeType: file.type,
        aspectRatio: img.width / img.height
      };

      // Check dimensions
      if (options.maxWidth && img.width > options.maxWidth) {
        resolve({
          isValid: false,
          error: `Image width (${img.width}px) exceeds maximum allowed width (${options.maxWidth}px)`
        });
        return;
      }

      if (options.maxHeight && img.height > options.maxHeight) {
        resolve({
          isValid: false,
          error: `Image height (${img.height}px) exceeds maximum allowed height (${options.maxHeight}px)`
        });
        return;
      }

      if (options.minWidth && img.width < options.minWidth) {
        resolve({
          isValid: false,
          error: `Image width (${img.width}px) is below minimum required width (${options.minWidth}px)`
        });
        return;
      }

      if (options.minHeight && img.height < options.minHeight) {
        resolve({
          isValid: false,
          error: `Image height (${img.height}px) is below minimum required height (${options.minHeight}px)`
        });
        return;
      }

      resolve({
        isValid: true,
        metadata
      });
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      resolve({
        isValid: false,
        error: 'Invalid image file or corrupted data'
      });
    };

    img.src = url;
  });
}

/**
 * Generate optimized image URLs for different sizes
 */
export function generateOptimizedImageUrls(originalUrl: string, metadata: ImageMetadata) {
  // For Supabase storage URLs, we can add transformation parameters
  if (originalUrl.includes('supabase.co/storage')) {
    const baseUrl = originalUrl.split('?')[0]; // Remove existing query params
    
    return {
      thumbnail: `${baseUrl}?width=150&height=150&resize=cover&quality=80`,
      small: `${baseUrl}?width=300&height=300&resize=contain&quality=85`,
      medium: `${baseUrl}?width=600&height=600&resize=contain&quality=90`,
      large: `${baseUrl}?width=1200&height=1200&resize=contain&quality=95`,
      original: originalUrl
    };
  }

  // For other URLs, return the original
  return {
    thumbnail: originalUrl,
    small: originalUrl,
    medium: originalUrl,
    large: originalUrl,
    original: originalUrl
  };
}

/**
 * Get the best image URL for a given display size
 */
export function getBestImageUrl(
  originalUrl: string, 
  metadata: ImageMetadata, 
  displayWidth: number, 
  displayHeight: number = displayWidth
): string {
  const optimizedUrls = generateOptimizedImageUrls(originalUrl, metadata);
  
  // Choose the best size based on display dimensions
  const maxDimension = Math.max(displayWidth, displayHeight);
  
  if (maxDimension <= 150) return optimizedUrls.thumbnail;
  if (maxDimension <= 300) return optimizedUrls.small;
  if (maxDimension <= 600) return optimizedUrls.medium;
  if (maxDimension <= 1200) return optimizedUrls.large;
  
  return optimizedUrls.original;
}

/**
 * Generate alt text for business logos
 */
export function generateLogoAltText(businessName: string): string {
  return `${businessName} logo`;
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Check if an image URL is accessible
 */
export function checkImageAccessibility(url: string): Promise<boolean> {
  return new Promise((resolve) => {
    const img = new Image();
    
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    
    // Set a timeout to avoid hanging
    setTimeout(() => resolve(false), 5000);
    
    img.src = url;
  });
}

/**
 * Preload critical images
 */
export function preloadImage(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = url;
  });
}

/**
 * Create responsive image srcSet for Next.js Image component
 */
export function createResponsiveSrcSet(originalUrl: string, metadata: ImageMetadata): string {
  const optimizedUrls = generateOptimizedImageUrls(originalUrl, metadata);
  
  return [
    `${optimizedUrls.small} 300w`,
    `${optimizedUrls.medium} 600w`,
    `${optimizedUrls.large} 1200w`,
    `${optimizedUrls.original} ${metadata.width}w`
  ].join(', ');
}

/**
 * Get optimal image sizes attribute for responsive images
 */
export function getOptimalSizes(maxWidth?: number): string {
  if (maxWidth) {
    return `(max-width: 768px) 100vw, (max-width: 1200px) 50vw, ${maxWidth}px`;
  }
  
  return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw';
}
