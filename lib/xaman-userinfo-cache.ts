'use client'

/**
 * XAMAN OAuth2 Userinfo Cache Service
 * 
 * Handles caching of user account information from the OAuth2 userinfo endpoint
 * to improve trustline setup flow and provide persistent wallet connection state.
 * 
 * Flow:
 * 1. User sets up trustline
 * 2. Cache their account info using https://oauth2.xumm.app/userinfo
 * 3. Store in localStorage and optionally sync to user profile
 * 4. Use cached info for future wallet interactions
 */

export interface XamanUserInfo {
  sub: string // User ID from XAMAN
  account: string // XRP wallet address
  name?: string // Display name if available
  network?: string // Network type (mainnet/testnet)
  iat?: number // Issued at timestamp
  exp?: number // Expiration timestamp
}

export interface CachedUserInfo extends XamanUserInfo {
  cachedAt: string
  userId?: string // Our app's user ID if linked
}

export class XamanUserInfoCache {
  private static instance: XamanUserInfoCache
  private readonly CACHE_KEY_PREFIX = 'fuse_xaman_userinfo_'
  private readonly CACHE_EXPIRY_HOURS = 24 // Cache expires after 24 hours

  public static getInstance(): XamanUserInfoCache {
    if (!XamanUserInfoCache.instance) {
      XamanUserInfoCache.instance = new XamanUserInfoCache()
    }
    return XamanUserInfoCache.instance
  }

  /**
   * Fetch user info from OAuth2 userinfo endpoint and cache it
   */
  async fetchAndCacheUserInfo(userId?: string): Promise<XamanUserInfo | null> {
    try {
      console.log('🔄 [XamanUserInfoCache] Fetching user info from OAuth2 userinfo endpoint...')
      
      const response = await fetch('https://oauth2.xumm.app/userinfo', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        credentials: 'include', // Include cookies for OAuth2 session
      })

      if (!response.ok) {
        console.warn('⚠️ [XamanUserInfoCache] Could not fetch userinfo:', response.status)
        return null
      }

      const userInfo: XamanUserInfo = await response.json()
      console.log('✅ [XamanUserInfoCache] User info retrieved:', {
        sub: userInfo.sub,
        account: userInfo.account,
        network: userInfo.network
      })

      // Cache the user info locally
      await this.cacheUserInfo(userInfo, userId)

      // Also sync to server-side profile if user ID is available
      if (userId) {
        try {
          await this.syncToServerProfile(userId, userInfo)
        } catch (syncError) {
          console.warn('⚠️ [XamanUserInfoCache] Could not sync to server profile:', syncError)
          // Don't fail the whole operation if server sync fails
        }
      }

      return userInfo
    } catch (error) {
      console.error('❌ [XamanUserInfoCache] Error fetching user info:', error)
      return null
    }
  }

  /**
   * Cache user info in localStorage
   */
  async cacheUserInfo(userInfo: XamanUserInfo, userId?: string): Promise<void> {
    try {
      const cachedInfo: CachedUserInfo = {
        ...userInfo,
        cachedAt: new Date().toISOString(),
        userId
      }

      // Store by wallet address (primary key)
      const walletKey = this.CACHE_KEY_PREFIX + userInfo.account
      localStorage.setItem(walletKey, JSON.stringify(cachedInfo))

      // Also store by user ID if provided
      if (userId) {
        const userKey = this.CACHE_KEY_PREFIX + 'user_' + userId
        localStorage.setItem(userKey, JSON.stringify(cachedInfo))
      }

      console.log('💾 [XamanUserInfoCache] User info cached successfully')
    } catch (error) {
      console.error('❌ [XamanUserInfoCache] Error caching user info:', error)
    }
  }

  /**
   * Get cached user info by wallet address
   */
  getCachedUserInfoByWallet(walletAddress: string): CachedUserInfo | null {
    try {
      const cacheKey = this.CACHE_KEY_PREFIX + walletAddress
      const cached = localStorage.getItem(cacheKey)
      
      if (!cached) {
        return null
      }

      const cachedInfo: CachedUserInfo = JSON.parse(cached)
      
      // Check if cache is expired
      if (this.isCacheExpired(cachedInfo.cachedAt)) {
        console.log('⏰ [XamanUserInfoCache] Cache expired for wallet:', walletAddress)
        localStorage.removeItem(cacheKey)
        return null
      }

      return cachedInfo
    } catch (error) {
      console.error('❌ [XamanUserInfoCache] Error reading cached user info:', error)
      return null
    }
  }

  /**
   * Get cached user info by user ID
   */
  getCachedUserInfoByUserId(userId: string): CachedUserInfo | null {
    try {
      const cacheKey = this.CACHE_KEY_PREFIX + 'user_' + userId
      const cached = localStorage.getItem(cacheKey)
      
      if (!cached) {
        return null
      }

      const cachedInfo: CachedUserInfo = JSON.parse(cached)
      
      // Check if cache is expired
      if (this.isCacheExpired(cachedInfo.cachedAt)) {
        console.log('⏰ [XamanUserInfoCache] Cache expired for user:', userId)
        localStorage.removeItem(cacheKey)
        return null
      }

      return cachedInfo
    } catch (error) {
      console.error('❌ [XamanUserInfoCache] Error reading cached user info:', error)
      return null
    }
  }

  /**
   * Sync user info to server-side profile
   */
  private async syncToServerProfile(userId: string, userInfo: XamanUserInfo): Promise<void> {
    try {
      const response = await fetch('/api/xaman/userinfo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userInfo,
          action: 'cache'
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to sync to server profile')
      }

      console.log('✅ [XamanUserInfoCache] User info synced to server profile')
    } catch (error) {
      console.error('❌ [XamanUserInfoCache] Error syncing to server profile:', error)
      throw error
    }
  }

  /**
   * Update user profile with cached wallet info
   */
  async syncCachedInfoToProfile(userId: string, walletAddress: string): Promise<boolean> {
    try {
      const cachedInfo = this.getCachedUserInfoByWallet(walletAddress)
      
      if (!cachedInfo) {
        console.warn('⚠️ [XamanUserInfoCache] No cached info found for wallet:', walletAddress)
        return false
      }

      // Update user profile via API
      const response = await fetch('/api/wallet-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          walletAddress: cachedInfo.account,
          walletType: 'xrp',
          action: 'connect'
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to sync to profile')
      }

      console.log('✅ [XamanUserInfoCache] Cached info synced to user profile')
      
      // Update cache with user ID link
      cachedInfo.userId = userId
      await this.cacheUserInfo(cachedInfo, userId)
      
      return true
    } catch (error) {
      console.error('❌ [XamanUserInfoCache] Error syncing cached info to profile:', error)
      return false
    }
  }

  /**
   * Clear cached info for a wallet address
   */
  clearCachedInfo(walletAddress: string): void {
    try {
      const walletKey = this.CACHE_KEY_PREFIX + walletAddress
      localStorage.removeItem(walletKey)
      console.log('🧹 [XamanUserInfoCache] Cleared cache for wallet:', walletAddress)
    } catch (error) {
      console.error('❌ [XamanUserInfoCache] Error clearing cache:', error)
    }
  }

  /**
   * Clear all cached user info
   */
  clearAllCachedInfo(): void {
    try {
      const keys = Object.keys(localStorage).filter(key => 
        key.startsWith(this.CACHE_KEY_PREFIX)
      )
      
      keys.forEach(key => localStorage.removeItem(key))
      console.log('🧹 [XamanUserInfoCache] Cleared all cached user info')
    } catch (error) {
      console.error('❌ [XamanUserInfoCache] Error clearing all cache:', error)
    }
  }

  /**
   * Check if cache is expired
   */
  private isCacheExpired(cachedAt: string): boolean {
    const cacheTime = new Date(cachedAt).getTime()
    const now = Date.now()
    const expiryTime = this.CACHE_EXPIRY_HOURS * 60 * 60 * 1000 // Convert to milliseconds
    
    return (now - cacheTime) > expiryTime
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { totalEntries: number; expiredEntries: number } {
    try {
      const keys = Object.keys(localStorage).filter(key => 
        key.startsWith(this.CACHE_KEY_PREFIX)
      )
      
      let expiredEntries = 0
      
      keys.forEach(key => {
        try {
          const cached = localStorage.getItem(key)
          if (cached) {
            const cachedInfo: CachedUserInfo = JSON.parse(cached)
            if (this.isCacheExpired(cachedInfo.cachedAt)) {
              expiredEntries++
            }
          }
        } catch (error) {
          // Invalid cache entry, count as expired
          expiredEntries++
        }
      })
      
      return {
        totalEntries: keys.length,
        expiredEntries
      }
    } catch (error) {
      console.error('❌ [XamanUserInfoCache] Error getting cache stats:', error)
      return { totalEntries: 0, expiredEntries: 0 }
    }
  }
}

// Export singleton instance
export const xamanUserInfoCache = XamanUserInfoCache.getInstance()

/**
 * React hook for using XAMAN userinfo cache
 */
export function useXamanUserInfoCache(userId?: string) {
  const cache = XamanUserInfoCache.getInstance()

  const fetchAndCache = async () => {
    return cache.fetchAndCacheUserInfo(userId)
  }

  const getCachedByWallet = (walletAddress: string) => {
    return cache.getCachedUserInfoByWallet(walletAddress)
  }

  const getCachedByUserId = (userIdParam?: string) => {
    const targetUserId = userIdParam || userId
    return targetUserId ? cache.getCachedUserInfoByUserId(targetUserId) : null
  }

  const syncToProfile = async (walletAddress: string) => {
    if (!userId) return false
    return cache.syncCachedInfoToProfile(userId, walletAddress)
  }

  const clearCache = (walletAddress: string) => {
    cache.clearCachedInfo(walletAddress)
  }

  const clearAllCache = () => {
    cache.clearAllCachedInfo()
  }

  const getCacheStats = () => {
    return cache.getCacheStats()
  }

  return {
    fetchAndCache,
    getCachedByWallet,
    getCachedByUserId,
    syncToProfile,
    clearCache,
    clearAllCache,
    getCacheStats
  }
}
