import { createClient, Supabase<PERSON>lient } from "@supabase/supabase-js"

// Database connection pool configuration
interface ConnectionPoolConfig {
  maxConnections: number
  idleTimeout: number
  connectionTimeout: number
  retryAttempts: number
  retryDelay: number
}

const DEFAULT_POOL_CONFIG: ConnectionPoolConfig = {
  maxConnections: 10,
  idleTimeout: 30000, // 30 seconds
  connectionTimeout: 10000, // 10 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
}

// Connection pool management
class DatabaseConnectionPool {
  private connections: Map<string, SupabaseClient> = new Map()
  private connectionCount = 0
  private config: ConnectionPoolConfig

  constructor(config: Partial<ConnectionPoolConfig> = {}) {
    this.config = { ...DEFAULT_POOL_CONFIG, ...config }
  }

  getConnection(key: string = 'default'): SupabaseClient {
    // Check if we have an existing connection
    if (this.connections.has(key)) {
      return this.connections.get(key)!
    }

    // Check connection limit
    if (this.connectionCount >= this.config.maxConnections) {
      // Return existing connection if at limit
      const firstConnection = this.connections.values().next().value
      if (firstConnection) return firstConnection
    }

    // Create new connection
    const connection = this.createConnection()
    this.connections.set(key, connection)
    this.connectionCount++

    // Set up idle timeout
    setTimeout(() => {
      this.closeConnection(key)
    }, this.config.idleTimeout)

    return connection
  }

  private createConnection(): SupabaseClient {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

    return createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
      global: {
        headers: {
          'X-Client-Info': 'fuse-vip-web',
          'User-Agent': 'Fuse.vip/1.0',
          'Prefer': 'return=minimal',
          'Accept': 'application/json',
          'Connection': 'keep-alive',
        }
      },
      realtime: {
        params: {
          eventsPerSecond: 10
        }
      }
    })
  }

  closeConnection(key: string): void {
    if (this.connections.has(key)) {
      this.connections.delete(key)
      this.connectionCount--
    }
  }

  closeAllConnections(): void {
    this.connections.clear()
    this.connectionCount = 0
  }

  getConnectionCount(): number {
    return this.connectionCount
  }
}

// Global connection pool instance
const connectionPool = new DatabaseConnectionPool()

// Database operation wrapper with error handling and retries
export class DatabaseManager {
  private static instance: DatabaseManager
  private client: SupabaseClient

  private constructor() {
    this.client = connectionPool.getConnection()
  }

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance
  }

  // Generic database operation with retry logic
  async executeWithRetry<T>(
    operation: (client: SupabaseClient) => Promise<T>,
    retries: number = DEFAULT_POOL_CONFIG.retryAttempts
  ): Promise<T> {
    let lastError: Error | null = null

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await operation(this.client)
      } catch (error) {
        lastError = error as Error
        
        // Log the error
        console.error(`Database operation failed (attempt ${attempt + 1}/${retries + 1}):`, error)

        // Don't retry on the last attempt
        if (attempt === retries) break

        // Wait before retrying
        await this.delay(DEFAULT_POOL_CONFIG.retryDelay * (attempt + 1))

        // Get a fresh connection for retry
        this.client = connectionPool.getConnection(`retry-${attempt}`)
      }
    }

    throw lastError || new Error('Database operation failed after retries')
  }

  // Helper method for delays
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Optimized query methods
  async select<T>(
    table: string,
    columns: string = '*',
    filters?: Record<string, any>,
    options?: {
      orderBy?: { column: string; ascending?: boolean }
      limit?: number
      offset?: number
    }
  ): Promise<T[]> {
    return this.executeWithRetry(async (client) => {
      let query = client.from(table).select(columns)

      // Apply filters
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.eq(key, value)
          }
        })
      }

      // Apply ordering
      if (options?.orderBy) {
        query = query.order(options.orderBy.column, { 
          ascending: options.orderBy.ascending ?? true 
        })
      }

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit)
      }
      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1)
      }

      const { data, error } = await query

      if (error) {
        throw new Error(`Database select error: ${error.message}`)
      }

      return data as T[]
    })
  }

  async insert<T>(table: string, data: Partial<T>): Promise<T> {
    return this.executeWithRetry(async (client) => {
      const { data: result, error } = await client
        .from(table)
        .insert(data)
        .select()
        .single()

      if (error) {
        throw new Error(`Database insert error: ${error.message}`)
      }

      return result as T
    })
  }

  async update<T>(
    table: string, 
    data: Partial<T>, 
    filters: Record<string, any>
  ): Promise<T> {
    return this.executeWithRetry(async (client) => {
      let query = client.from(table).update(data)

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value)
      })

      const { data: result, error } = await query.select().single()

      if (error) {
        throw new Error(`Database update error: ${error.message}`)
      }

      return result as T
    })
  }

  async delete(table: string, filters: Record<string, any>): Promise<void> {
    return this.executeWithRetry(async (client) => {
      let query = client.from(table).delete()

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value)
      })

      const { error } = await query

      if (error) {
        throw new Error(`Database delete error: ${error.message}`)
      }
    })
  }

  // Get the raw client for complex operations
  getClient(): SupabaseClient {
    return this.client
  }

  // Connection management
  closeConnection(): void {
    connectionPool.closeAllConnections()
  }

  getConnectionStats(): { count: number; maxConnections: number } {
    return {
      count: connectionPool.getConnectionCount(),
      maxConnections: DEFAULT_POOL_CONFIG.maxConnections
    }
  }
}

// Export singleton instance
export const db = DatabaseManager.getInstance()

// Export connection pool for advanced usage
export { connectionPool }

// Cleanup function for graceful shutdown
export function cleanupDatabase(): void {
  connectionPool.closeAllConnections()
}
