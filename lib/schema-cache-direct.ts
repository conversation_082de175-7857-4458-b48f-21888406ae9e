/**
 * Direct SQL Schema Cache
 * Loads entire database schemas into memory for ultra-fast access
 * Uses direct PostgreSQL connections to bypass any PostgREST or Supabase overhead
 */

import { executeQuery } from '@/lib/database-direct';

interface BusinessRecord {
  id: string;
  name: string;
  logo_url: string | null;
  website: string | null;
  category: string;
  premium_discount: string | null;
  is_active: boolean;
  business_spotlight: boolean;
  contact_name: string | null;
  contact_email: string | null;
  contact_phone: string | null;
  business_address: string | null;
  user_id: string;
  created_at: string;
  updated_at: string | null;
}

interface ProfileRecord {
  id: string;
  first_name: string;
  last_name: string;
  user_email: string;
  phone: string | null;
  is_card_holder: boolean;
  is_business_applicant: boolean;
  card_tier: string;
  xrp_wallet_address: string | null;
  membership_start_date: string | null;
  membership_end_date: string | null;
  referring_business_id: string | null;

  created_at: string;
}

interface CacheStats {
  businessesCount: number;
  profilesCount: number;
  lastRefresh: Date | null;
  refreshDuration: number | null;
  hitCount: number;
  missCount: number;
  hitRate: number;
}

class DirectSchemaCache {
  // In-memory data stores
  private businesses: Map<string, BusinessRecord> = new Map();
  private profiles: Map<string, ProfileRecord> = new Map();
  
  // Index structures for fast lookups
  private businessesByCategory: Map<string, BusinessRecord[]> = new Map();
  private businessesByUserId: Map<string, BusinessRecord[]> = new Map();
  private spotlightBusinesses: BusinessRecord[] = [];
  private activeBusinesses: BusinessRecord[] = [];
  
  // Performance tracking
  private hitCount = 0;
  private missCount = 0;
  private lastRefresh: Date | null = null;
  private refreshDuration: number | null = null;
  private isInitialized = false;
  private isRefreshing = false;

  constructor() {
    console.log('🔧 Direct Schema Cache initialized');
  }

  /**
   * Load all businesses from database using direct SQL
   */
  private async loadBusinesses(): Promise<void> {
    console.log('🏢 Loading all businesses via direct SQL...');
    
    const query = `
      SELECT 
        id, name, logo_url, website, category, premium_discount,
        is_active, business_spotlight, contact_name, contact_email,
        contact_phone, business_address, user_id, created_at, updated_at
      FROM businesses
      ORDER BY created_at DESC
    `;

    const { data, error } = await executeQuery<BusinessRecord>(query, []);
    
    if (error) {
      console.error('❌ Failed to load businesses:', error);
      throw new Error(`Failed to load businesses: ${error}`);
    }

    if (!data) {
      console.warn('⚠️ No business data returned');
      return;
    }

    // Clear existing data
    this.businesses.clear();
    this.businessesByCategory.clear();
    this.businessesByUserId.clear();
    this.spotlightBusinesses = [];
    this.activeBusinesses = [];

    // Load into memory with indexing
    for (const business of data) {
      // Main store
      this.businesses.set(business.id, business);

      // Category index
      const category = business.category.toLowerCase();
      if (!this.businessesByCategory.has(category)) {
        this.businessesByCategory.set(category, []);
      }
      this.businessesByCategory.get(category)!.push(business);

      // User ID index
      if (!this.businessesByUserId.has(business.user_id)) {
        this.businessesByUserId.set(business.user_id, []);
      }
      this.businessesByUserId.get(business.user_id)!.push(business);

      // Special indexes
      if (business.is_active) {
        this.activeBusinesses.push(business);
        
        if (business.business_spotlight) {
          this.spotlightBusinesses.push(business);
        }
      }
    }

    console.log(`✅ Loaded ${data.length} businesses into schema cache`);
    console.log(`📊 Active: ${this.activeBusinesses.length}, Spotlight: ${this.spotlightBusinesses.length}`);
    console.log(`🗂️ Categories: ${this.businessesByCategory.size}`);
  }

  /**
   * Load all profiles from database using direct SQL
   */
  private async loadProfiles(): Promise<void> {
    console.log('👤 Loading all profiles via direct SQL...');
    
    const query = `
      SELECT 
        id, first_name, last_name, user_email, phone,
        is_card_holder, is_business_applicant, card_tier,
        xrp_wallet_address, membership_start_date, membership_end_date,
        referring_business_id,
        created_at
      FROM profiles
      ORDER BY created_at DESC
    `;

    const { data, error } = await executeQuery<ProfileRecord>(query, []);
    
    if (error) {
      console.error('❌ Failed to load profiles:', error);
      throw new Error(`Failed to load profiles: ${error}`);
    }

    if (!data) {
      console.warn('⚠️ No profile data returned');
      return;
    }

    // Clear existing data
    this.profiles.clear();

    // Load into memory
    for (const profile of data) {
      this.profiles.set(profile.id, profile);
    }

    console.log(`✅ Loaded ${data.length} profiles into schema cache`);
  }

  /**
   * Initialize the cache - load all data from database
   */
  async initialize(): Promise<void> {
    if (this.isRefreshing) {
      console.log('⏳ Cache refresh already in progress...');
      return;
    }

    this.isRefreshing = true;
    const startTime = Date.now();

    try {
      console.log('🚀 Initializing Direct Schema Cache...');

      // Load all data in parallel
      await Promise.all([
        this.loadBusinesses(),
        this.loadProfiles()
      ]);

      this.refreshDuration = Date.now() - startTime;
      this.lastRefresh = new Date();
      this.isInitialized = true;

      console.log(`🎉 Schema cache initialized in ${this.refreshDuration}ms`);
      console.log(`📊 Cache contains: ${this.businesses.size} businesses, ${this.profiles.size} profiles`);

    } catch (error) {
      console.error('❌ Failed to initialize schema cache:', error);
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Refresh the cache - reload all data
   */
  async refresh(): Promise<void> {
    console.log('🔄 Refreshing schema cache...');
    await this.initialize();
  }

  /**
   * Ensure cache is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized && !this.isRefreshing) {
      await this.initialize();
    }
  }

  // ============ BUSINESS METHODS ============

  /**
   * Get all active businesses
   */
  async getAllBusinesses(): Promise<BusinessRecord[]> {
    await this.ensureInitialized();
    this.hitCount++;
    
    console.log(`🎯 Schema cache hit - returning ${this.activeBusinesses.length} active businesses`);
    return [...this.activeBusinesses]; // Return copy to prevent mutations
  }

  /**
   * Get business by ID
   */
  async getBusiness(businessId: string): Promise<BusinessRecord | null> {
    await this.ensureInitialized();
    
    const business = this.businesses.get(businessId);
    
    if (business) {
      this.hitCount++;
      console.log(`🎯 Schema cache hit - business ${businessId}`);
      return { ...business }; // Return copy
    } else {
      this.missCount++;
      console.log(`❌ Schema cache miss - business ${businessId} not found`);
      return null;
    }
  }

  /**
   * Get businesses by category
   */
  async getBusinessesByCategory(category: string): Promise<BusinessRecord[]> {
    await this.ensureInitialized();
    
    const categoryKey = category.toLowerCase();
    const businesses = this.businessesByCategory.get(categoryKey) || [];
    
    // Filter for active businesses only
    const activeBusinesses = businesses.filter(b => b.is_active);
    
    this.hitCount++;
    console.log(`🎯 Schema cache hit - ${activeBusinesses.length} businesses in category "${category}"`);
    return activeBusinesses.map(b => ({ ...b })); // Return copies
  }

  /**
   * Get spotlight businesses
   */
  async getSpotlightBusinesses(): Promise<BusinessRecord[]> {
    await this.ensureInitialized();
    this.hitCount++;
    
    console.log(`🎯 Schema cache hit - returning ${this.spotlightBusinesses.length} spotlight businesses`);
    return this.spotlightBusinesses.map(b => ({ ...b })); // Return copies
  }

  /**
   * Search businesses by name or category
   */
  async searchBusinesses(searchTerm: string): Promise<BusinessRecord[]> {
    await this.ensureInitialized();
    
    const term = searchTerm.toLowerCase();
    const results = this.activeBusinesses.filter(business => 
      business.name.toLowerCase().includes(term) ||
      business.category.toLowerCase().includes(term) ||
      (business.premium_discount && business.premium_discount.toLowerCase().includes(term))
    );

    this.hitCount++;
    console.log(`🎯 Schema cache hit - search "${searchTerm}" returned ${results.length} businesses`);
    return results.map(b => ({ ...b })); // Return copies
  }

  /**
   * Get businesses by user ID
   */
  async getBusinessesByUserId(userId: string): Promise<BusinessRecord[]> {
    await this.ensureInitialized();
    
    const businesses = this.businessesByUserId.get(userId) || [];
    const activeBusinesses = businesses.filter(b => b.is_active);
    
    this.hitCount++;
    console.log(`🎯 Schema cache hit - user ${userId} has ${activeBusinesses.length} active businesses`);
    return activeBusinesses.map(b => ({ ...b })); // Return copies
  }

  /**
   * Check if user is business owner
   */
  async isBusinessOwner(userId: string): Promise<boolean> {
    await this.ensureInitialized();
    
    const businesses = this.businessesByUserId.get(userId) || [];
    const hasActiveBusiness = businesses.some(b => b.is_active);
    
    this.hitCount++;
    console.log(`🎯 Schema cache hit - user ${userId} is business owner: ${hasActiveBusiness}`);
    return hasActiveBusiness;
  }

  // ============ PROFILE METHODS ============

  /**
   * Get profile by ID
   */
  async getProfile(userId: string): Promise<ProfileRecord | null> {
    await this.ensureInitialized();
    
    const profile = this.profiles.get(userId);
    
    if (profile) {
      this.hitCount++;
      console.log(`🎯 Schema cache hit - profile ${userId}`);
      return { ...profile }; // Return copy
    } else {
      this.missCount++;
      console.log(`❌ Schema cache miss - profile ${userId} not found`);
      return null;
    }
  }

  /**
   * Get all profiles (with pagination for large datasets)
   */
  async getAllProfiles(offset: number = 0, limit: number = 100): Promise<{ profiles: ProfileRecord[], total: number }> {
    await this.ensureInitialized();
    
    const allProfiles = Array.from(this.profiles.values());
    const profiles = allProfiles.slice(offset, offset + limit);
    
    this.hitCount++;
    console.log(`🎯 Schema cache hit - returning ${profiles.length} profiles (offset: ${offset}, limit: ${limit})`);
    
    return {
      profiles: profiles.map(p => ({ ...p })), // Return copies
      total: allProfiles.length
    };
  }

  /**
   * Get profiles by card holder status
   */
  async getCardHolders(): Promise<ProfileRecord[]> {
    await this.ensureInitialized();
    
    const cardHolders = Array.from(this.profiles.values()).filter(p => p.is_card_holder);
    
    this.hitCount++;
    console.log(`🎯 Schema cache hit - returning ${cardHolders.length} card holders`);
    return cardHolders.map(p => ({ ...p })); // Return copies
  }

  // ============ UTILITY METHODS ============

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalRequests = this.hitCount + this.missCount;
    
    return {
      businessesCount: this.businesses.size,
      profilesCount: this.profiles.size,
      lastRefresh: this.lastRefresh,
      refreshDuration: this.refreshDuration,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0
    };
  }

  /**
   * Check if cache is initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Clear all cache data
   */
  clear(): void {
    this.businesses.clear();
    this.profiles.clear();
    this.businessesByCategory.clear();
    this.businessesByUserId.clear();
    this.spotlightBusinesses = [];
    this.activeBusinesses = [];
    
    this.hitCount = 0;
    this.missCount = 0;
    this.lastRefresh = null;
    this.refreshDuration = null;
    this.isInitialized = false;
    
    console.log('🧹 Schema cache cleared');
  }

  /**
   * Get cache health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    message: string;
    details: any;
  } {
    if (!this.isInitialized) {
      return {
        status: 'critical',
        message: 'Cache not initialized',
        details: { initialized: false }
      };
    }

    const stats = this.getStats();
    
    if (stats.hitRate < 50 && stats.hitCount + stats.missCount > 100) {
      return {
        status: 'warning',
        message: 'Low cache hit rate',
        details: stats
      };
    }

    const timeSinceRefresh = this.lastRefresh ? Date.now() - this.lastRefresh.getTime() : 0;
    if (timeSinceRefresh > 24 * 60 * 60 * 1000) { // 24 hours
      return {
        status: 'warning',
        message: 'Cache data may be stale',
        details: { ...stats, timeSinceRefresh }
      };
    }

    return {
      status: 'healthy',
      message: 'Cache operating normally',
      details: stats
    };
  }
}

// Create singleton instance
const directSchemaCache = new DirectSchemaCache();

export default directSchemaCache;
export { directSchemaCache };
export type { BusinessRecord, ProfileRecord, CacheStats };