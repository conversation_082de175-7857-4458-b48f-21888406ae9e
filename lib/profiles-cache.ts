/**
 * Profiles Server-Side Cache Service
 * Implements server-side caching to reduce database connections for profile data
 */

import { getUserProfileDirect, executeQuery } from '@/lib/database-direct';

interface ProfileCacheEntry {
  id: string;
  first_name: string;
  last_name: string;
  user_email: string;
  phone: string | null;
  is_card_holder: boolean;
  is_business_applicant: boolean;
  card_tier: string;
  xrp_wallet_address: string | null;
  membership_start_date: string | null;
  membership_end_date: string | null;
  referring_business_id: string | null;
  wallet_connected_at: string | null;
  wallet_last_used: string | null;
  created_at: string;
  cachedAt: Date;
  expiresAt: Date;
}

interface ProfilesCacheStats {
  totalEntries: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  lastRefresh: Date | null;
}

class ProfilesCache {
  private cache = new Map<string, ProfileCacheEntry>();
  private businessOwnerCache = new Map<string, { isOwner: boolean; expiresAt: Date }>();
  private hitCount = 0;
  private missCount = 0;
  private lastRefresh: Date | null = null;
  private readonly cacheDuration = 15 * 60 * 1000; // 15 minutes for profiles
  private readonly businessOwnerCacheDuration = 30 * 60 * 1000; // 30 minutes for business owner status
  private readonly maxCacheSize = 5000; // Maximum profile entries
  private cleanupInterval?: NodeJS.Timeout;

  constructor() {
    this.startCleanupInterval();
  }

  private startCleanupInterval() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup() {
    const now = new Date();
    const expiredKeys: string[] = [];
    const expiredBusinessOwnerKeys: string[] = [];

    // Clean up profile cache
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt < now) {
        expiredKeys.push(key);
      }
    }
    expiredKeys.forEach(key => this.cache.delete(key));

    // Clean up business owner cache
    for (const [key, entry] of this.businessOwnerCache.entries()) {
      if (entry.expiresAt < now) {
        expiredBusinessOwnerKeys.push(key);
      }
    }
    expiredBusinessOwnerKeys.forEach(key => this.businessOwnerCache.delete(key));

    // Limit cache size
    if (this.cache.size > this.maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].cachedAt.getTime() - b[1].cachedAt.getTime());
      const toRemove = entries.slice(0, this.cache.size - this.maxCacheSize);
      toRemove.forEach(([key]) => this.cache.delete(key));
    }

    if (expiredKeys.length > 0 || expiredBusinessOwnerKeys.length > 0) {
      console.log(`🧹 Profiles Cache cleanup: removed ${expiredKeys.length} profile entries, ${expiredBusinessOwnerKeys.length} business owner entries`);
    }
  }

  private generateCacheKey(userId: string): string {
    return `profile_${userId}`;
  }

  private generateBusinessOwnerCacheKey(userId: string): string {
    return `business_owner_${userId}`;
  }

  private createCacheEntry(profileData: any): ProfileCacheEntry {
    const now = new Date();
    return {
      id: profileData.id,
      first_name: profileData.first_name || '',
      last_name: profileData.last_name || '',
      user_email: profileData.user_email || '',
      phone: profileData.phone || null,
      is_card_holder: profileData.is_card_holder || false,
      is_business_applicant: profileData.is_business_applicant || false,
      card_tier: profileData.card_tier || 'Premium',
      xrp_wallet_address: profileData.xrp_wallet_address || null,
      membership_start_date: profileData.membership_start_date || null,
      membership_end_date: profileData.membership_end_date || null,
      referring_business_id: profileData.referring_business_id || null,
      wallet_connected_at: profileData.wallet_connected_at || null,
      wallet_last_used: profileData.wallet_last_used || null,
      created_at: profileData.created_at,
      cachedAt: now,
      expiresAt: new Date(now.getTime() + this.cacheDuration)
    };
  }

  async getProfile(userId: string): Promise<ProfileCacheEntry | null> {
    if (!userId) {
      console.warn('getProfile: No userId provided');
      return null;
    }

    const cacheKey = this.generateCacheKey(userId);
    
    // Check cache first
    const cachedEntry = this.cache.get(cacheKey);
    if (cachedEntry && cachedEntry.expiresAt > new Date()) {
      this.hitCount++;
      console.log(`🎯 Profile Cache hit for user ${userId}`);
      return cachedEntry;
    }

    // Cache miss - fetch from database
    this.missCount++;
    console.log(`📊 Profile Cache miss for user ${userId}, fetching from database`);

    try {
      const profileData = await getUserProfileDirect(userId);
      
      if (profileData) {
        const entry = this.createCacheEntry(profileData);
        this.cache.set(cacheKey, entry);
        return entry;
      }

      return null;
    } catch (error) {
      console.error('Error fetching profile from database:', error);
      return null;
    }
  }

  async getProfileMinimal(userId: string): Promise<Partial<ProfileCacheEntry> | null> {
    if (!userId) {
      console.warn('getProfileMinimal: No userId provided');
      return null;
    }

    // Try to get from full cache first
    const fullProfile = await this.getProfile(userId);
    if (fullProfile) {
      return {
        id: fullProfile.id,
        first_name: fullProfile.first_name,
        last_name: fullProfile.last_name,
        user_email: fullProfile.user_email,
        is_card_holder: fullProfile.is_card_holder
      };
    }

    // Fallback to direct minimal query if not in cache
    try {
      const query = `
        SELECT id, first_name, last_name, user_email, is_card_holder
        FROM profiles
        WHERE id = $1
      `;
      
      const { data, error } = await executeQuery(query, [userId]);
      
      if (error) {
        console.error('getProfileMinimal query error:', error);
        return null;
      }

      if (data && data.length > 0) {
        console.log(`✅ Minimal profile found for user: ${userId}`);
        return data[0];
      }

      return null;
    } catch (error) {
      console.error('Error fetching minimal profile:', error);
      return null;
    }
  }

  async checkUserIsBusinessOwner(userId: string): Promise<boolean> {
    if (!userId) {
      console.warn('checkUserIsBusinessOwner: No userId provided');
      return false;
    }

    const cacheKey = this.generateBusinessOwnerCacheKey(userId);
    
    // Check cache first
    const cachedEntry = this.businessOwnerCache.get(cacheKey);
    if (cachedEntry && cachedEntry.expiresAt > new Date()) {
      this.hitCount++;
      console.log(`🎯 Business Owner Cache hit for user ${userId}: ${cachedEntry.isOwner}`);
      return cachedEntry.isOwner;
    }

    // Cache miss - check database
    this.missCount++;
    console.log(`📊 Business Owner Cache miss for user ${userId}, checking database`);

    try {
      const query = `
        SELECT 1
        FROM businesses
        WHERE user_id = $1 AND is_active = true
        LIMIT 1
      `;
      
      const { data, error } = await executeQuery(query, [userId]);
      
      if (error) {
        console.error('checkUserIsBusinessOwner query error:', error);
        return false;
      }

      const isOwner = data && data.length > 0;
      
      // Cache the result
      const now = new Date();
      this.businessOwnerCache.set(cacheKey, {
        isOwner,
        expiresAt: new Date(now.getTime() + this.businessOwnerCacheDuration)
      });

      console.log(`✅ User ${userId} business owner status cached: ${isOwner}`);
      return isOwner;
    } catch (error) {
      console.error('Error checking business owner status:', error);
      return false;
    }
  }

  async preloadProfiles(userIds: string[]): Promise<void> {
    try {
      console.log(`🔄 Preloading profiles for ${userIds.length} users`);
      
      // Filter out users already in cache
      const uncachedUserIds = userIds.filter(userId => {
        const cacheKey = this.generateCacheKey(userId);
        const cachedEntry = this.cache.get(cacheKey);
        return !cachedEntry || cachedEntry.expiresAt <= new Date();
      });

      if (uncachedUserIds.length === 0) {
        console.log('✅ All profiles already cached');
        return;
      }

      // Batch fetch profiles
      const query = `
        SELECT id, first_name, last_name, user_email, phone,
               is_card_holder, is_business_applicant, card_tier,
               xrp_wallet_address, membership_start_date, membership_end_date,
               referring_business_id, wallet_connected_at, wallet_last_used,
               created_at
        FROM profiles
        WHERE id = ANY($1)
      `;

      const { data, error } = await executeQuery(query, [uncachedUserIds]);
      
      if (error) {
        console.error('preloadProfiles query error:', error);
        return;
      }

      // Cache the results
      if (data) {
        data.forEach(profileData => {
          const entry = this.createCacheEntry(profileData);
          const cacheKey = this.generateCacheKey(profileData.id);
          this.cache.set(cacheKey, entry);
        });

        console.log(`✅ Preloaded ${data.length} profiles into cache`);
      }
    } catch (error) {
      console.error('Error preloading profiles:', error);
    }
  }

  updateWalletInCache(userId: string, walletAddress: string | null): void {
    const cacheKey = this.generateCacheKey(userId);
    const cachedEntry = this.cache.get(cacheKey);
    
    if (cachedEntry) {
      cachedEntry.xrp_wallet_address = walletAddress;
      cachedEntry.wallet_last_used = walletAddress ? new Date().toISOString() : null;
      if (walletAddress && !cachedEntry.wallet_connected_at) {
        cachedEntry.wallet_connected_at = new Date().toISOString();
      }
      
      this.cache.set(cacheKey, cachedEntry);
      console.log(`💳 Wallet address updated in cache for user ${userId}`);
    }
  }

  invalidateProfile(userId: string): void {
    const profileCacheKey = this.generateCacheKey(userId);
    const businessOwnerCacheKey = this.generateBusinessOwnerCacheKey(userId);
    
    const profileDeleted = this.cache.delete(profileCacheKey);
    const businessOwnerDeleted = this.businessOwnerCache.delete(businessOwnerCacheKey);
    
    if (profileDeleted || businessOwnerDeleted) {
      console.log(`🗑️ Invalidated profile cache for user ${userId}`);
    }
  }

  invalidateBusinessOwnerStatus(userId: string): void {
    const cacheKey = this.generateBusinessOwnerCacheKey(userId);
    const deleted = this.businessOwnerCache.delete(cacheKey);
    
    if (deleted) {
      console.log(`🗑️ Invalidated business owner cache for user ${userId}`);
    }
  }

  invalidateAll(): void {
    this.cache.clear();
    this.businessOwnerCache.clear();
    console.log('🧹 All profile cache invalidated');
  }

  async refresh(): Promise<void> {
    console.log('🔄 Refreshing profile cache...');
    this.invalidateAll();
    this.lastRefresh = new Date();
    console.log('✅ Profile cache refreshed');
  }

  clear(): void {
    this.cache.clear();
    this.businessOwnerCache.clear();
    this.hitCount = 0;
    this.missCount = 0;
    this.lastRefresh = null;
    console.log('🧹 Profiles Cache cleared');
  }

  getStats(): ProfilesCacheStats {
    const totalRequests = this.hitCount + this.missCount;
    return {
      totalEntries: this.cache.size,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: totalRequests > 0 ? (this.hitCount / totalRequests) * 100 : 0,
      lastRefresh: this.lastRefresh
    };
  }

  getCacheInfo(userId: string): {
    hasProfile: boolean;
    hasBusinessOwner: boolean;
    profileAge?: number;
    businessOwnerAge?: number;
  } {
    const profileCacheKey = this.generateCacheKey(userId);
    const businessOwnerCacheKey = this.generateBusinessOwnerCacheKey(userId);
    
    const profileEntry = this.cache.get(profileCacheKey);
    const businessOwnerEntry = this.businessOwnerCache.get(businessOwnerCacheKey);
    
    return {
      hasProfile: !!profileEntry,
      hasBusinessOwner: !!businessOwnerEntry,
      profileAge: profileEntry ? Date.now() - profileEntry.cachedAt.getTime() : undefined,
      businessOwnerAge: businessOwnerEntry ? Date.now() - businessOwnerEntry.expiresAt.getTime() + this.businessOwnerCacheDuration : undefined
    };
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Create singleton instance
const profilesCache = new ProfilesCache();

export default profilesCache;
export { profilesCache };
export type { ProfileCacheEntry, ProfilesCacheStats };