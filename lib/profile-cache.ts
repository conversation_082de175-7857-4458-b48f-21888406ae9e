/**
 * Profile Cache Service
 * Manages profile data caching for improved auth performance
 */

interface ProfileCacheEntry {
  id: string;
  user_email: string;
  first_name: string;
  last_name: string;
  phone: string;
  is_card_holder: boolean;
  is_business_applicant: boolean;
  card_tier: string;
  xrp_wallet_address: string | null;
  wallet_connected_at: string | null;
  wallet_last_used: string | null;
  created_at: string;
}

interface BusinessOwnerCache {
  isOwner: boolean;
  timestamp: number;
}

class ProfileCacheService {
  private readonly PROFILE_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly BUSINESS_OWNER_CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

  // Profile caching methods
  cacheProfile(userId: string, profile: ProfileCacheEntry): void {
    try {
      const cacheKey = `profile_${userId}`;
      const timestampKey = `profile_timestamp_${userId}`;
      
      localStorage.setItem(cacheKey, JSON.stringify(profile));
      localStorage.setItem(timestampKey, Date.now().toString());
      
      console.log(`📦 Profile cached for user ${userId}`);
    } catch (error) {
      console.warn('Failed to cache profile:', error);
    }
  }

  getCachedProfile(userId: string): ProfileCacheEntry | null {
    try {
      const cacheKey = `profile_${userId}`;
      const timestampKey = `profile_timestamp_${userId}`;
      
      const cachedProfile = localStorage.getItem(cacheKey);
      const cacheTimestamp = localStorage.getItem(timestampKey);
      
      if (cachedProfile && cacheTimestamp) {
        const cacheAge = Date.now() - parseInt(cacheTimestamp);
        
        if (cacheAge < this.PROFILE_CACHE_DURATION) {
          console.log(`🎯 Profile cache hit for user ${userId} (age: ${Math.round(cacheAge / 1000)}s)`);
          return JSON.parse(cachedProfile);
        } else {
          console.log(`⏰ Profile cache expired for user ${userId} (age: ${Math.round(cacheAge / 1000)}s)`);
          this.invalidateProfile(userId);
        }
      }
      
      return null;
    } catch (error) {
      console.warn('Failed to get cached profile:', error);
      return null;
    }
  }

  invalidateProfile(userId: string): void {
    try {
      const cacheKey = `profile_${userId}`;
      const timestampKey = `profile_timestamp_${userId}`;
      
      localStorage.removeItem(cacheKey);
      localStorage.removeItem(timestampKey);
      
      console.log(`🗑️ Profile cache invalidated for user ${userId}`);
    } catch (error) {
      console.warn('Failed to invalidate profile cache:', error);
    }
  }

  // Business owner caching methods
  cacheBusinessOwner(userId: string, isOwner: boolean): void {
    try {
      const cacheKey = `is_business_owner_optimized_${userId}`;
      const timestampKey = `business_owner_optimized_timestamp_${userId}`;
      
      localStorage.setItem(cacheKey, isOwner.toString());
      localStorage.setItem(timestampKey, Date.now().toString());
      
      console.log(`📦 Business owner status cached for user ${userId}: ${isOwner}`);
    } catch (error) {
      console.warn('Failed to cache business owner status:', error);
    }
  }

  getCachedBusinessOwner(userId: string): boolean | null {
    try {
      const cacheKey = `is_business_owner_optimized_${userId}`;
      const timestampKey = `business_owner_optimized_timestamp_${userId}`;
      
      const cachedStatus = localStorage.getItem(cacheKey);
      const cacheTimestamp = localStorage.getItem(timestampKey);
      
      if (cachedStatus && cacheTimestamp) {
        const cacheAge = Date.now() - parseInt(cacheTimestamp);
        
        if (cacheAge < this.BUSINESS_OWNER_CACHE_DURATION) {
          const isOwner = cachedStatus === 'true';
          console.log(`🎯 Business owner cache hit for user ${userId}: ${isOwner} (age: ${Math.round(cacheAge / 1000)}s)`);
          return isOwner;
        } else {
          console.log(`⏰ Business owner cache expired for user ${userId} (age: ${Math.round(cacheAge / 1000)}s)`);
          this.invalidateBusinessOwner(userId);
        }
      }
      
      return null;
    } catch (error) {
      console.warn('Failed to get cached business owner status:', error);
      return null;
    }
  }

  invalidateBusinessOwner(userId: string): void {
    try {
      const cacheKey = `is_business_owner_optimized_${userId}`;
      const timestampKey = `business_owner_optimized_timestamp_${userId}`;
      
      localStorage.removeItem(cacheKey);
      localStorage.removeItem(timestampKey);
      
      console.log(`🗑️ Business owner cache invalidated for user ${userId}`);
    } catch (error) {
      console.warn('Failed to invalidate business owner cache:', error);
    }
  }

  // Session caching methods
  cacheUserSession(userId: string, email: string): void {
    try {
      localStorage.setItem('user_id', userId);
      localStorage.setItem('user_email', email);
      
      console.log(`📦 User session cached for ${email}`);
    } catch (error) {
      console.warn('Failed to cache user session:', error);
    }
  }

  getCachedUserSession(): { userId: string; email: string } | null {
    try {
      const userId = localStorage.getItem('user_id');
      const email = localStorage.getItem('user_email');
      
      if (userId && email) {
        return { userId, email };
      }
      
      return null;
    } catch (error) {
      console.warn('Failed to get cached user session:', error);
      return null;
    }
  }

  clearUserSession(): void {
    try {
      localStorage.removeItem('user_id');
      localStorage.removeItem('user_email');
      
      console.log('🗑️ User session cache cleared');
    } catch (error) {
      console.warn('Failed to clear user session cache:', error);
    }
  }

  // Cache statistics
  getCacheStats(userId: string): {
    hasProfile: boolean;
    profileAge?: number;
    hasBusinessOwner: boolean;
    businessOwnerAge?: number;
    hasSession: boolean;
  } {
    const stats = {
      hasProfile: false,
      hasBusinessOwner: false,
      hasSession: false,
      profileAge: undefined as number | undefined,
      businessOwnerAge: undefined as number | undefined
    };

    try {
      // Check profile cache
      const profileTimestamp = localStorage.getItem(`profile_timestamp_${userId}`);
      if (profileTimestamp) {
        stats.hasProfile = true;
        stats.profileAge = Date.now() - parseInt(profileTimestamp);
      }

      // Check business owner cache
      const businessTimestamp = localStorage.getItem(`business_owner_optimized_timestamp_${userId}`);
      if (businessTimestamp) {
        stats.hasBusinessOwner = true;
        stats.businessOwnerAge = Date.now() - parseInt(businessTimestamp);
      }

      // Check session cache
      const session = this.getCachedUserSession();
      stats.hasSession = !!session;

    } catch (error) {
      console.warn('Failed to get cache stats:', error);
    }

    return stats;
  }

  // Wallet-specific caching methods
  updateWalletInCache(userId: string, walletAddress: string | null): void {
    try {
      const cachedProfile = this.getCachedProfile(userId);
      if (cachedProfile) {
        cachedProfile.xrp_wallet_address = walletAddress;
        cachedProfile.wallet_last_used = walletAddress ? new Date().toISOString() : null;
        if (walletAddress && !cachedProfile.wallet_connected_at) {
          cachedProfile.wallet_connected_at = new Date().toISOString();
        }
        
        this.cacheProfile(userId, cachedProfile);
        console.log(`💳 Wallet address updated in cache for user ${userId}: ${walletAddress ? `${walletAddress.substring(0, 6)}...${walletAddress.substring(walletAddress.length - 4)}` : 'disconnected'}`);
      }
    } catch (error) {
      console.warn('Failed to update wallet in cache:', error);
    }
  }

  getWalletFromCache(userId: string): string | null {
    try {
      const cachedProfile = this.getCachedProfile(userId);
      return cachedProfile?.xrp_wallet_address || null;
    } catch (error) {
      console.warn('Failed to get wallet from cache:', error);
      return null;
    }
  }

  isWalletConnected(userId: string): boolean {
    try {
      const cachedProfile = this.getCachedProfile(userId);
      return !!(cachedProfile?.xrp_wallet_address);
    } catch (error) {
      console.warn('Failed to check wallet connection status:', error);
      return false;
    }
  }

  // Clear all cache for a user
  clearUserCache(userId: string): void {
    this.invalidateProfile(userId);
    this.invalidateBusinessOwner(userId);
    this.clearUserSession();
    console.log(`🧹 All cache cleared for user ${userId}`);
  }
}

// Create singleton instance
const profileCache = new ProfileCacheService();

export default profileCache;
export { profileCache };
export type { ProfileCacheEntry };