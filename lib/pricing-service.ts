"use client"

// Real-time pricing service for FUSE/XRP/USD exchange rates
// Based on: 10 XRP = 13941 FUSE = $24

interface PriceData {
  xrpUsd: number
  fuseUsd: number
  xrpFuse: number
  fuseXrp: number
  lastUpdated: number
}

interface CachedPrices {
  data: PriceData
  expires: number
}

class PricingService {
  private cachedPrices: CachedPrices | null = null
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes in milliseconds
  private readonly BASE_RATES = {
    // Base rates from provided example: 10 XRP = 13941 FUSE = $24
    XRP_PER_USD: 10 / 24, // 0.4167 XRP per USD
    FUSE_PER_USD: 13941 / 24, // 580.875 FUSE per USD
    XRP_PER_FUSE: 10 / 13941, // 0.000717 XRP per FUSE
    FUSE_PER_XRP: 13941 / 10 // 1394.1 FUSE per XRP
  }

  // Get current prices with caching
  async getCurrentPrices(): Promise<PriceData> {
    // Check if cached prices are still valid
    if (this.cachedPrices && Date.now() < this.cachedPrices.expires) {
      return this.cachedPrices.data
    }

    // Fetch fresh prices
    const prices = await this.fetchCurrentPrices()
    
    // Cache the prices
    this.cachedPrices = {
      data: prices,
      expires: Date.now() + this.CACHE_DURATION
    }

    return prices
  }

  // Fetch current prices from external APIs and DEX data
  private async fetchCurrentPrices(): Promise<PriceData> {
    try {
      // Fetch current XRP/USD price from CoinGecko
      let xrpUsdPrice = 2.4 // Fallback

      try {
        const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=ripple&vs_currencies=usd', {
          next: { revalidate: 300 } // Cache for 5 minutes
        })
        const data = await response.json()
        if (data.ripple?.usd) {
          xrpUsdPrice = data.ripple.usd
        }
      } catch (error) {
        console.warn('Failed to fetch XRP price from CoinGecko, using fallback:', error)
      }

      // Fetch FUSE/XRP rate from our DEX API endpoint
      let fuseXrpRate = this.BASE_RATES.FUSE_PER_XRP // Fallback
      let xrpFuseRate = this.BASE_RATES.XRP_PER_FUSE // Fallback

      try {
        // Try to fetch directly from XMagnetic or use fallback
        let dexSuccess = false
        
        // Try XMagnetic API directly
        try {
          const xmagneticResponse = await fetch('https://api.xmagnetic.org/v1/orderbook/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP', {
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'Fuse-VIP/1.0'
            }
          })
          
          if (xmagneticResponse.ok) {
            const xmagneticData = await xmagneticResponse.json()
            console.log('XMagnetic response:', xmagneticData)
            
            // Try to extract rate from orderbook data
            if (xmagneticData.asks && xmagneticData.asks.length > 0) {
              const bestAsk = xmagneticData.asks[0]
              if (bestAsk.price || bestAsk.rate) {
                fuseXrpRate = parseFloat(bestAsk.price || bestAsk.rate)
                xrpFuseRate = 1 / fuseXrpRate
                dexSuccess = true
                console.log(`FUSE/XRP rate from XMagnetic orderbook: ${fuseXrpRate}`)
              }
            }
          }
        } catch (xmagneticError) {
          console.warn('XMagnetic API failed:', xmagneticError)
        }
        
        // If still no success, use client-side API in browser context only
        if (!dexSuccess && typeof window !== 'undefined') {
          try {
            const dexResponse = await fetch('/api/dex/fuse-rate')
            if (dexResponse.ok) {
              const dexResult = await dexResponse.json()
              if (dexResult.success && dexResult.data?.price) {
                fuseXrpRate = parseFloat(dexResult.data.price)
                xrpFuseRate = 1 / fuseXrpRate
                dexSuccess = true
                console.log(`FUSE/XRP rate from internal API: ${fuseXrpRate}`)
              }
            }
          } catch (apiError) {
            console.warn('Internal API failed:', apiError)
          }
        }
        
        if (!dexSuccess) {
          console.log('Using fallback FUSE/XRP rates')
        }
      } catch (error) {
        console.warn('Failed to fetch FUSE/XRP rate, using fallback:', error)
      }

      // Calculate FUSE/USD based on XRP/USD and FUSE/XRP rates
      const fuseUsdPrice = xrpUsdPrice * xrpFuseRate

      return {
        xrpUsd: xrpUsdPrice,
        fuseUsd: fuseUsdPrice,
        xrpFuse: xrpFuseRate,
        fuseXrp: fuseXrpRate,
        lastUpdated: Date.now()
      }
    } catch (error) {
      console.error('Error fetching prices:', error)
      
      // Return base rates as fallback
      return {
        xrpUsd: 2.4,
        fuseUsd: 24 / 13941,
        xrpFuse: this.BASE_RATES.XRP_PER_FUSE,
        fuseXrp: this.BASE_RATES.FUSE_PER_XRP,
        lastUpdated: Date.now()
      }
    }
  }

  // Calculate XRP amount needed for a USD value
  async calculateXRPAmount(usdAmount: string | number): Promise<string> {
    const prices = await this.getCurrentPrices()
    const usdValue = typeof usdAmount === 'string' ? parseFloat(usdAmount) : usdAmount
    const xrpAmount = usdValue / prices.xrpUsd
    return xrpAmount.toFixed(6)
  }

  // Calculate FUSE amount needed for a USD value
  async calculateFUSEAmount(usdAmount: string | number): Promise<string> {
    const prices = await this.getCurrentPrices()
    const usdValue = typeof usdAmount === 'string' ? parseFloat(usdAmount) : usdAmount
    const fuseAmount = usdValue / prices.fuseUsd
    return fuseAmount.toFixed(2)
  }

  // Convert XRP to FUSE
  async convertXRPToFUSE(xrpAmount: string | number): Promise<string> {
    const prices = await this.getCurrentPrices()
    const xrpValue = typeof xrpAmount === 'string' ? parseFloat(xrpAmount) : xrpAmount
    const fuseAmount = xrpValue * prices.fuseXrp
    return fuseAmount.toFixed(2)
  }

  // Convert FUSE to XRP
  async convertFUSEToXRP(fuseAmount: string | number): Promise<string> {
    const prices = await this.getCurrentPrices()
    const fuseValue = typeof fuseAmount === 'string' ? parseFloat(fuseAmount) : fuseAmount
    const xrpAmount = fuseValue * prices.xrpFuse
    return xrpAmount.toFixed(6)
  }

  // Get VIP card prices in all currencies
  async getVIPCardPrices() {
    try {
      console.log('Getting VIP card prices...')
      const prices = await this.getCurrentPrices()
      console.log('Current prices:', prices)
      
      const cards = [
        { name: 'Monthly VIP Card', usdPrice: 9.99 },
        { name: 'Premium Card', usdPrice: 100 },
        { name: 'Gold Card', usdPrice: 250 },
        { name: 'Platinum Card', usdPrice: 500 },
        { name: 'Diamond Card', usdPrice: 1000 },
        { name: 'Obsidian Card', usdPrice: 1500 }
      ]

      const cardPrices = await Promise.all(
        cards.map(async (card) => {
          const xrpPrice = await this.calculateXRPAmount(card.usdPrice)
          const fusePrice = await this.calculateFUSEAmount(card.usdPrice)
          console.log(`${card.name}: $${card.usdPrice} = ${xrpPrice} XRP = ${fusePrice} FUSE`)
          
          return {
            ...card,
            xrpPrice,
            fusePrice,
            rates: prices // Include rates for easy access
          }
        })
      )

      console.log('Final card prices:', cardPrices)

      return {
        cards: cardPrices,
        rates: prices,
        lastUpdated: new Date(prices.lastUpdated).toISOString()
      }
    } catch (error) {
      console.error('Error in getVIPCardPrices:', error)
      throw error
    }
  }

  // Force refresh prices (bypass cache)
  async refreshPrices(): Promise<PriceData> {
    this.cachedPrices = null
    return this.getCurrentPrices()
  }

  // Get cache status
  getCacheStatus() {
    if (!this.cachedPrices) {
      return { cached: false, expires: null, age: null }
    }

    const now = Date.now()
    const age = now - (this.cachedPrices.expires - this.CACHE_DURATION)
    const expiresIn = this.cachedPrices.expires - now

    return {
      cached: true,
      expires: new Date(this.cachedPrices.expires).toISOString(),
      expiresIn: Math.max(0, expiresIn),
      age: age,
      isExpired: expiresIn <= 0
    }
  }
}

// Singleton instance
export const pricingService = new PricingService()

// Utility functions for formatting
export const formatCurrency = {
  usd: (amount: string | number): string => {
    const value = typeof amount === 'string' ? parseFloat(amount) : amount
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value)
  },
  
  xrp: (amount: string | number): string => {
    const value = typeof amount === 'string' ? parseFloat(amount) : amount
    return `${value.toFixed(6)} XRP`
  },
  
  fuse: (amount: string | number): string => {
    const value = typeof amount === 'string' ? parseFloat(amount) : amount
    return `${value.toLocaleString()} FUSE`
  }
}

// Price validation helpers
export const validatePrices = {
  isValidAmount: (amount: string | number): boolean => {
    const value = typeof amount === 'string' ? parseFloat(amount) : amount
    return !isNaN(value) && value > 0
  },
  
  meetsMinimum: (amount: string | number, minimum: number = 0.000001): boolean => {
    const value = typeof amount === 'string' ? parseFloat(amount) : amount
    return value >= minimum
  }
}

export default pricingService