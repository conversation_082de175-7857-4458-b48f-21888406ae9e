/**
 * Profile Real-Time Cache Service
 * Enhanced profile caching with real-time Supabase subscriptions
 * Based on the business dashboard real-time pattern
 */

import { getSupabaseClient } from '@/lib/supabase';
import { ProfileCacheEntry } from './profile-cache';
import type { RealtimeChannel } from '@supabase/supabase-js';

export interface ProfileData extends ProfileCacheEntry {
  is_admin?: boolean;
  membership_start_date?: string;
  membership_end_date?: string;
  referring_business_id?: string;
  business_name?: string;
  updated_at?: string;
  is_profile_complete?: boolean;
}

export interface ProfileSyncStatus {
  status: 'idle' | 'syncing' | 'error';
  lastSync: Date | null;
  isOnline: boolean;
}

class ProfileRealtimeCache {
  private static instance: ProfileRealtimeCache;
  private cache = new Map<string, { data: ProfileData; timestamp: number }>();
  private subscriptions = new Map<string, RealtimeChannel>();
  private syncCallbacks = new Map<string, Set<(profile: ProfileData) => void>>();
  private statusCallbacks = new Map<string, Set<(status: ProfileSyncStatus) => void>>();
  private syncStatus = new Map<string, ProfileSyncStatus>();
  
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static getInstance(): ProfileRealtimeCache {
    if (!ProfileRealtimeCache.instance) {
      ProfileRealtimeCache.instance = new ProfileRealtimeCache();
    }
    return ProfileRealtimeCache.instance;
  }

  // Core cache operations
  get(userId: string): ProfileData | null {
    const cached = this.cache.get(userId);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      console.log(`✅ Profile real-time cache hit for user ${userId}`);
      return cached.data;
    }
    
    if (cached) {
      console.log(`⏰ Profile real-time cache expired for user ${userId}`);
      this.cache.delete(userId);
    }
    
    return null;
  }

  set(userId: string, data: ProfileData): void {
    this.cache.set(userId, { data, timestamp: Date.now() });
    console.log(`📦 Profile cached with real-time support for user ${userId}`);
    
    // Notify subscribers
    this.notifySubscribers(userId, data);
  }

  invalidate(userId: string): void {
    this.cache.delete(userId);
    console.log(`🗑️ Profile real-time cache invalidated for user ${userId}`);
  }

  // Real-time subscription management
  subscribe(userId: string, callback: (profile: ProfileData) => void): () => void {
    // Add callback to subscribers
    if (!this.syncCallbacks.has(userId)) {
      this.syncCallbacks.set(userId, new Set());
    }
    this.syncCallbacks.get(userId)!.add(callback);

    // Setup real-time subscription if not exists
    this.setupRealtimeSubscription(userId);

    // Return unsubscribe function
    return () => {
      const callbacks = this.syncCallbacks.get(userId);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.cleanupSubscription(userId);
        }
      }
    };
  }

  subscribeToStatus(userId: string, callback: (status: ProfileSyncStatus) => void): () => void {
    if (!this.statusCallbacks.has(userId)) {
      this.statusCallbacks.set(userId, new Set());
    }
    this.statusCallbacks.get(userId)!.add(callback);

    // Send current status immediately
    const currentStatus = this.getSyncStatus(userId);
    callback(currentStatus);

    return () => {
      const callbacks = this.statusCallbacks.get(userId);
      if (callbacks) {
        callbacks.delete(callback);
      }
    };
  }

  private setupRealtimeSubscription(userId: string): void {
    if (this.subscriptions.has(userId)) {
      console.log(`🔄 Real-time subscription already exists for user ${userId}`);
      return;
    }

    const supabase = getSupabaseClient();
    if (!supabase) {
      console.warn('Supabase client not available for real-time subscription');
      return;
    }

    console.log(`🔄 Setting up real-time subscription for profile updates: ${userId}`);

    const channel = supabase
      .channel(`profile_changes_${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'profiles',
          filter: `id=eq.${userId}`,
        },
        async (payload: any) => {
          console.log('🔄 Real-time profile update received:', payload);
          
          this.setSyncStatus(userId, 'syncing');

          try {
            // Invalidate cache
            this.invalidate(userId);

            // Extract updated data from payload
            let updatedProfile: ProfileData;
            
            if (payload.eventType === 'DELETE') {
              // Handle profile deletion (rare case)
              console.log('Profile deleted, cleaning up cache');
              this.cleanupSubscription(userId);
              return;
            } else {
              // Handle INSERT or UPDATE
              updatedProfile = payload.new as ProfileData;
            }

            // Update cache with fresh data
            this.set(userId, updatedProfile);
            
            this.setSyncStatus(userId, 'idle', new Date());
            
          } catch (error) {
            console.error('Error handling real-time profile update:', error);
            this.setSyncStatus(userId, 'error');
          }
        }
      )
      .subscribe((status: any) => {
        console.log(`Profile real-time subscription status for ${userId}:`, status);
        
        if (status === 'SUBSCRIBED') {
          this.setSyncStatus(userId, 'idle');
        } else if (status === 'CHANNEL_ERROR') {
          this.setSyncStatus(userId, 'error');
        }
      });

    this.subscriptions.set(userId, channel);
  }

  private cleanupSubscription(userId: string): void {
    const subscription = this.subscriptions.get(userId);
    if (subscription) {
      const supabase = getSupabaseClient();
      if (supabase) {
        console.log(`🔄 Cleaning up real-time subscription for user ${userId}`);
        supabase.removeChannel(subscription);
      }
      this.subscriptions.delete(userId);
    }

    // Clean up callbacks
    this.syncCallbacks.delete(userId);
    this.statusCallbacks.delete(userId);
    this.syncStatus.delete(userId);
  }

  private notifySubscribers(userId: string, profile: ProfileData): void {
    const callbacks = this.syncCallbacks.get(userId);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(profile);
        } catch (error) {
          console.error('Error in profile sync callback:', error);
        }
      });
    }
  }

  private setSyncStatus(userId: string, status: ProfileSyncStatus['status'], lastSync?: Date | null): void {
    const currentStatus = this.syncStatus.get(userId) || {
      status: 'idle',
      lastSync: null,
      isOnline: navigator.onLine
    };

    const newStatus: ProfileSyncStatus = {
      ...currentStatus,
      status,
      lastSync: lastSync || currentStatus.lastSync,
      isOnline: navigator.onLine
    };

    this.syncStatus.set(userId, newStatus);

    // Notify status subscribers
    const callbacks = this.statusCallbacks.get(userId);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(newStatus);
        } catch (error) {
          console.error('Error in status callback:', error);
        }
      });
    }
  }

  getSyncStatus(userId: string): ProfileSyncStatus {
    return this.syncStatus.get(userId) || {
      status: 'idle',
      lastSync: null,
      isOnline: navigator.onLine
    };
  }

  // Optimistic updates
  optimisticUpdate(userId: string, updates: Partial<ProfileData>): ProfileData | null {
    const cached = this.get(userId);
    if (!cached) {
      console.warn(`Cannot perform optimistic update: no cached profile for user ${userId}`);
      return null;
    }

    const optimisticProfile = { ...cached, ...updates };
    
    // Update cache with optimistic data
    this.cache.set(userId, { 
      data: optimisticProfile, 
      timestamp: Date.now() 
    });

    console.log(`✨ Optimistic profile update for user ${userId}:`, updates);
    
    // Notify subscribers immediately
    this.notifySubscribers(userId, optimisticProfile);
    
    return optimisticProfile;
  }

  // Revert optimistic updates on error
  revertOptimisticUpdate(userId: string, originalProfile: ProfileData): void {
    console.log(`↩️ Reverting optimistic update for user ${userId}`);
    
    this.cache.set(userId, { 
      data: originalProfile, 
      timestamp: Date.now() 
    });

    this.notifySubscribers(userId, originalProfile);
    this.setSyncStatus(userId, 'error');
  }

  // Network status management
  updateNetworkStatus(isOnline: boolean): void {
    this.syncStatus.forEach((status, userId) => {
      this.setSyncStatus(userId, status.status, status.lastSync);
    });
  }

  // Wallet-specific optimistic updates
  optimisticWalletUpdate(userId: string, walletAddress: string | null): ProfileData | null {
    const updates: Partial<ProfileData> = {
      xrp_wallet_address: walletAddress,
      wallet_last_used: walletAddress ? new Date().toISOString() : null,
    };

    if (walletAddress) {
      const cached = this.get(userId);
      if (cached && !cached.wallet_connected_at) {
        updates.wallet_connected_at = new Date().toISOString();
      }
    }

    return this.optimisticUpdate(userId, updates);
  }

  // VIP status optimistic updates
  optimisticVipUpdate(userId: string, isCardHolder: boolean, cardTier?: string): ProfileData | null {
    const updates: Partial<ProfileData> = {
      is_card_holder: isCardHolder,
    };

    if (cardTier) {
      updates.card_tier = cardTier;
    }

    if (isCardHolder) {
      updates.membership_start_date = new Date().toISOString();
    }

    return this.optimisticUpdate(userId, updates);
  }

  // Cache statistics
  getStats(): {
    totalCached: number;
    activeSubscriptions: number;
    totalCallbacks: number;
  } {
    return {
      totalCached: this.cache.size,
      activeSubscriptions: this.subscriptions.size,
      totalCallbacks: Array.from(this.syncCallbacks.values()).reduce(
        (total, callbacks) => total + callbacks.size, 
        0
      )
    };
  }

  // Clean up all subscriptions (for app shutdown)
  cleanup(): void {
    console.log('🧹 Cleaning up all profile real-time subscriptions');
    
    const supabase = getSupabaseClient();
    if (supabase) {
      this.subscriptions.forEach((channel) => {
        supabase.removeChannel(channel);
      });
    }

    this.subscriptions.clear();
    this.syncCallbacks.clear();
    this.statusCallbacks.clear();
    this.syncStatus.clear();
    this.cache.clear();
  }
}

// Create singleton instance
const profileRealtimeCache = ProfileRealtimeCache.getInstance();

export default profileRealtimeCache;
export { ProfileRealtimeCache };