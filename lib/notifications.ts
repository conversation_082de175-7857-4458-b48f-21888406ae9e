// Notification utilities for admin alerts

interface EmailNotification {
  to: string;
  subject: string;
  message: string;
  type: 'business_access_request' | 'new_business' | 'system_alert';
  data?: any;
}

interface PushNotification {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: any;
}

// Admin email for notifications
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_USER_ID = '58dc33cf-44eb-4fa2-9da2-789db8a12913';

// Email notification service (placeholder - would integrate with actual email service)
export async function sendEmailNotification(notification: EmailNotification): Promise<boolean> {
  try {
    // In a real implementation, you would integrate with:
    // - SendGrid
    // - AWS SES
    // - Mailgun
    // - Resend
    // - etc.
    
    console.log('Email notification would be sent:', {
      to: notification.to,
      subject: notification.subject,
      message: notification.message,
      type: notification.type,
      timestamp: new Date().toISOString(),
    });

    // For now, just log the notification
    // In production, replace this with actual email service integration
    
    // Example with fetch to a serverless function:
    /*
    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(notification),
    });
    
    return response.ok;
    */
    
    return true; // Simulate success
  } catch (error) {
    console.error('Failed to send email notification:', error);
    return false;
  }
}

// Browser push notification
export async function sendPushNotification(notification: PushNotification): Promise<boolean> {
  try {
    // Check if browser supports notifications
    if (!('Notification' in window)) {
      console.warn('Browser does not support notifications');
      return false;
    }

    // Check permission
    if (Notification.permission !== 'granted') {
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        console.warn('Notification permission denied');
        return false;
      }
    }

    // Send notification
    const browserNotification = new Notification(notification.title, {
      body: notification.body,
      icon: notification.icon || '/favicon.ico',
      badge: notification.badge || '/favicon.ico',
      data: notification.data,
      requireInteraction: true, // Keep notification visible until user interacts
    });

    // Handle notification click
    browserNotification.onclick = () => {
      window.focus();
      browserNotification.close();
      
      // Navigate to relevant page if data provided
      if (notification.data?.url) {
        window.location.href = notification.data.url;
      }
    };

    return true;
  } catch (error) {
    console.error('Failed to send push notification:', error);
    return false;
  }
}

// Notification templates
export const NotificationTemplates = {
  businessAccessRequest: (userName: string, businessName: string) => ({
    email: {
      subject: 'New Business Access Request - Fuse VIP',
      message: `
        Hello Admin,
        
        A new business access request has been submitted:
        
        User: ${userName}
        Business: ${businessName}
        Time: ${new Date().toLocaleString()}
        
        Please review this request in the admin dashboard.
        
        Best regards,
        Fuse VIP System
      `,
    },
    push: {
      title: 'New Business Access Request',
      body: `${userName} wants access to ${businessName}`,
      data: {
        url: '/admin/businesses',
        type: 'business_access_request',
      },
    },
  }),

  newBusiness: (businessName: string, ownerName: string) => ({
    email: {
      subject: 'New Business Registration - Fuse VIP',
      message: `
        Hello Admin,
        
        A new business has been registered:
        
        Business: ${businessName}
        Owner: ${ownerName}
        Time: ${new Date().toLocaleString()}
        
        Please review this business in the admin dashboard.
        
        Best regards,
        Fuse VIP System
      `,
    },
    push: {
      title: 'New Business Registration',
      body: `${businessName} registered by ${ownerName}`,
      data: {
        url: '/admin/businesses',
        type: 'new_business',
      },
    },
  }),

  systemAlert: (alertType: string, message: string) => ({
    email: {
      subject: `System Alert: ${alertType} - Fuse VIP`,
      message: `
        Hello Admin,
        
        A system alert has been triggered:
        
        Type: ${alertType}
        Message: ${message}
        Time: ${new Date().toLocaleString()}
        
        Please check the system status.
        
        Best regards,
        Fuse VIP System
      `,
    },
    push: {
      title: `System Alert: ${alertType}`,
      body: message,
      data: {
        url: '/admin',
        type: 'system_alert',
      },
    },
  }),
};

// Main notification service
export class NotificationService {
  private static instance: NotificationService;

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Send business access request notification
  async notifyBusinessAccessRequest(
    userName: string,
    userEmail: string,
    businessName: string
  ): Promise<void> {
    const templates = NotificationTemplates.businessAccessRequest(userName, businessName);

    // Send email notification
    await sendEmailNotification({
      to: ADMIN_EMAIL,
      subject: templates.email.subject,
      message: templates.email.message,
      type: 'business_access_request',
      data: { userName, userEmail, businessName },
    });

    // Send push notification (if admin is online)
    await sendPushNotification(templates.push);
  }

  // Send new business notification
  async notifyNewBusiness(businessName: string, ownerName: string, ownerEmail: string): Promise<void> {
    const templates = NotificationTemplates.newBusiness(businessName, ownerName);

    // Send email notification
    await sendEmailNotification({
      to: ADMIN_EMAIL,
      subject: templates.email.subject,
      message: templates.email.message,
      type: 'new_business',
      data: { businessName, ownerName, ownerEmail },
    });

    // Send push notification
    await sendPushNotification(templates.push);
  }

  // Send system alert
  async notifySystemAlert(alertType: string, message: string, data?: any): Promise<void> {
    const templates = NotificationTemplates.systemAlert(alertType, message);

    // Send email notification
    await sendEmailNotification({
      to: ADMIN_EMAIL,
      subject: templates.email.subject,
      message: templates.email.message,
      type: 'system_alert',
      data: { alertType, message, ...data },
    });

    // Send push notification
    await sendPushNotification(templates.push);
  }

  // Request notification permissions
  async requestPermissions(): Promise<boolean> {
    if (!('Notification' in window)) {
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }

    return false;
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();
