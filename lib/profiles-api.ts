/**
 * Utility functions for querying profiles with API key authentication
 */

const PROFILES_API_KEY = process.env.PROFILES_API_KEY || 'fuse-profiles-secure-2024'
const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001'

interface Profile {
  id: string
  first_name?: string
  last_name?: string
  user_email?: string
  phone?: string
  is_card_holder?: boolean
  is_business_applicant?: boolean
  xrp_wallet_address?: string
  membership_start_date?: string
  membership_end_date?: string
  card_tier?: string
  referring_business_id?: string
  created_at?: string
}

interface ProfilesResponse {
  profiles: Profile[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface SingleProfileResponse {
  profile: Profile
}

/**
 * Fetch a single profile by user ID
 */
export async function getProfile(userId: string): Promise<Profile | null> {
  try {
    const url = `${BASE_URL}/api/profiles?userId=${userId}&apiKey=${PROFILES_API_KEY}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': PROFILES_API_KEY
      }
    })

    if (!response.ok) {
      console.error('Failed to fetch profile:', response.status, response.statusText)
      return null
    }

    const data: SingleProfileResponse = await response.json()
    return data.profile
  } catch (error) {
    console.error('Error fetching profile:', error)
    return null
  }
}

/**
 * Fetch multiple profiles with pagination
 */
export async function getProfiles(page = 1, limit = 10): Promise<ProfilesResponse | null> {
  try {
    const url = `${BASE_URL}/api/profiles?page=${page}&limit=${limit}&apiKey=${PROFILES_API_KEY}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': PROFILES_API_KEY
      }
    })

    if (!response.ok) {
      console.error('Failed to fetch profiles:', response.status, response.statusText)
      return null
    }

    const data: ProfilesResponse = await response.json()
    return data
  } catch (error) {
    console.error('Error fetching profiles:', error)
    return null
  }
}

/**
 * Create a new profile
 */
export async function createProfile(profileData: Partial<Profile>): Promise<Profile | null> {
  try {
    const response = await fetch(`${BASE_URL}/api/profiles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': PROFILES_API_KEY
      },
      body: JSON.stringify(profileData)
    })

    if (!response.ok) {
      console.error('Failed to create profile:', response.status, response.statusText)
      return null
    }

    const data: SingleProfileResponse = await response.json()
    return data.profile
  } catch (error) {
    console.error('Error creating profile:', error)
    return null
  }
}

/**
 * Update an existing profile
 */
export async function updateProfile(userId: string, profileData: Partial<Profile>): Promise<Profile | null> {
  try {
    const url = `${BASE_URL}/api/profiles?userId=${userId}&apiKey=${PROFILES_API_KEY}`
    
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': PROFILES_API_KEY
      },
      body: JSON.stringify(profileData)
    })

    if (!response.ok) {
      console.error('Failed to update profile:', response.status, response.statusText)
      return null
    }

    const data: SingleProfileResponse = await response.json()
    return data.profile
  } catch (error) {
    console.error('Error updating profile:', error)
    return null
  }
}

/**
 * Fetch profiles using URL parameters (for external API calls)
 */
export function buildProfilesUrl(options: {
  userId?: string
  page?: number
  limit?: number
  apiKey?: string
}): string {
  const { userId, page = 1, limit = 10, apiKey = PROFILES_API_KEY } = options
  
  const params = new URLSearchParams({
    apiKey,
    ...(userId && { userId }),
    ...(page && { page: page.toString() }),
    ...(limit && { limit: limit.toString() })
  })
  
  return `${BASE_URL}/api/profiles?${params.toString()}`
}

/**
 * Example usage for external API calls with curl
 */
export function getCurlExamples() {
  return {
    getSingleProfile: `curl -H "x-api-key: ${PROFILES_API_KEY}" "${BASE_URL}/api/profiles?userId=USER_ID_HERE"`,
    getAllProfiles: `curl -H "x-api-key: ${PROFILES_API_KEY}" "${BASE_URL}/api/profiles?page=1&limit=10"`,
    createProfile: `curl -X POST -H "Content-Type: application/json" -H "x-api-key: ${PROFILES_API_KEY}" -d '{"first_name":"John","last_name":"Doe","user_email":"<EMAIL>"}' "${BASE_URL}/api/profiles"`,
    updateProfile: `curl -X PUT -H "Content-Type: application/json" -H "x-api-key: ${PROFILES_API_KEY}" -d '{"first_name":"Jane"}' "${BASE_URL}/api/profiles?userId=USER_ID_HERE"`
  }
}
