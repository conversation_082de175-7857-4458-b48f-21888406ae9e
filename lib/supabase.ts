import { createClient } from "@supabase/supabase-js"
import { connectionHealthMonitor, performanceMonitor } from "./performance"

let supabaseInstance: ReturnType<typeof createClient> | null = null

// Initialize Supabase client with environment variables (singleton pattern)
export function getSupabaseClient() {
  if (supabaseInstance) return supabaseInstance

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""

  if (!supabaseUrl || !supabaseAnonKey) {
    console.warn("Supabase environment variables are missing.")
    return null
  }

  // Ensure we never connect to localhost
  if (supabaseUrl.includes('localhost') || supabaseUrl.includes('127.0.0.1')) {
    console.error("Localhost URL detected in Supabase URL - this should not happen in production")
    return null
  }

  try {
    new URL(supabaseUrl)

    // Optimized client configuration to avoid schema cache issues
    const clientConfig = {
      auth: {
        persistSession: typeof window !== 'undefined',
        autoRefreshToken: typeof window !== 'undefined',
        detectSessionInUrl: typeof window !== 'undefined',
        storage: typeof window !== 'undefined' ? window.localStorage : undefined,
      },
      global: {
        headers: {
          'X-Client-Info': 'fuse-vip-web',
          'User-Agent': 'Fuse.vip/1.0',
          'Prefer': 'return=minimal', // Reduce response size
          'Accept': 'application/json'
        }
      },
      // Remove db.schema to use default configuration
      realtime: {
        params: {
          eventsPerSecond: 10 // Limit realtime events
        }
      }
    }

    supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, clientConfig)
    return supabaseInstance
  } catch (error) {
    console.error("Invalid Supabase URL:", error)
    return null
  }
}

// Reset function to clear singleton instance (useful for testing or debugging)
export function resetSupabaseClient() {
  supabaseInstance = null
}

// Note: Removed global export to prevent build-time issues with cookies
// Use getSupabaseClient() function instead of importing 'supabase' directly

// --------- AUTH HELPERS ---------
export async function signInWithEmail(email: string, password: string) {
  const client = getSupabaseClient()
  if (!client) throw new Error("Supabase client not initialized")
  return client.auth.signInWithPassword({ email, password })
}

export async function signUpWithEmail(email: string, password: string, metadata: object = {}) {
  const client = getSupabaseClient()
  if (!client) throw new Error("Supabase client not initialized")

  console.log('🔄 Supabase signUpWithEmail called:', { email, metadata })

  // Get the current origin for the redirect URL
  const origin = typeof window !== 'undefined' ? window.location.origin : 'https://fuse.vip'

  // Merge provided metadata with default data
  const userData = {
    confirmed_at: new Date().toISOString(),
    ...metadata
  }

  console.log('📝 User data for signup:', userData)

  // Call Supabase's signUp method with a redirect back to our app
  const result = await client.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/onboarding?confirmed=true`,
      data: userData
    }
  })

  console.log('📊 Supabase signup result:', {
    success: !result.error,
    userId: result.data?.user?.id,
    error: result.error?.message
  })

  return result
}

export async function signOut() {
  const client = getSupabaseClient()
  if (!client) throw new Error("Supabase client not initialized")
  return client.auth.signOut()
}

export async function getSession() {
  const client = getSupabaseClient()
  if (!client) return { data: { session: null } }
  return client.auth.getSession()
}

export function onAuthStateChange(callback: (event: string, session: any) => void) {
  const client = getSupabaseClient()
  if (!client) return { data: { subscription: { unsubscribe: () => { } } } }
  return client.auth.onAuthStateChange(callback)
}

// Unified session refresh function
export async function refreshSession() {
  const client = getSupabaseClient()
  if (!client) throw new Error("Supabase client not initialized")

  try {
    const { data, error } = await client.auth.refreshSession()
    if (error) {
      console.error("Error refreshing session:", error)
      return { data: null, error }
    }
    return { data, error: null }
  } catch (error) {
    console.error("Exception refreshing session:", error)
    return { data: null, error }
  }
}

// Alias for backward compatibility
export const updateSession = refreshSession

// Circuit breaker for profile requests
const profileRequestState = {
  lastRequestTime: 0,
  failureCount: 0,
  isCircuitOpen: false,
  cooldownPeriod: 30000, // 30 seconds
  requestDebounce: 2000, // 2 seconds between requests
}

// --------- PROFILE HELPERS ---------
export async function getUserProfile(userId: string) {
  const client = getSupabaseClient()
  if (!client || !userId) return null

  // Check circuit breaker
  const now = Date.now()
  if (profileRequestState.isCircuitOpen) {
    if (now - profileRequestState.lastRequestTime < profileRequestState.cooldownPeriod) {
      console.log("Circuit breaker is open, using cached data or returning null")
      const cached = localStorage.getItem(`profile_${userId}`)
      return cached ? JSON.parse(cached) : null
    } else {
      // Reset circuit breaker after cooldown
      profileRequestState.isCircuitOpen = false
      profileRequestState.failureCount = 0
    }
  }

  // Debounce requests - don't make request if one was made recently
  if (now - profileRequestState.lastRequestTime < profileRequestState.requestDebounce) {
    console.log("Request debounced, using cached data")
    const cached = localStorage.getItem(`profile_${userId}`)
    return cached ? JSON.parse(cached) : null
  }

  // Check aggressive cache first (5 minute cache)
  const cached = localStorage.getItem(`profile_${userId}`)
  const cacheTimestamp = localStorage.getItem(`profile_timestamp_${userId}`)
  if (cached && cacheTimestamp) {
    const cacheAge = now - parseInt(cacheTimestamp)
    if (cacheAge < 300000) { // 5 minutes
      console.log("Using cached profile (fresh)")
      return JSON.parse(cached)
    }
  }

  profileRequestState.lastRequestTime = now

  // Super simplified approach - just try to get basic profile data
  try {
    console.log("Making single profile request...")
    
    // Try simple query first
    const { data, error } = await client
      .from("profiles")
      .select("id, first_name, last_name, user_email, is_card_holder, is_business_applicant")
      .eq('id', userId)
      .single()

    if (error) {
      profileRequestState.failureCount++
      
      if (error.code === "PGRST116") {
        console.log("No profile found for user:", userId)
        return null
      }
      
      // If 503 or schema cache error, open circuit breaker
      if (error.message?.includes('503') || error.message?.includes('schema cache') || profileRequestState.failureCount >= 3) {
        console.error("Opening circuit breaker due to repeated failures")
        profileRequestState.isCircuitOpen = true
        localStorage.setItem('supabase_503_detected', 'true')
        localStorage.setItem('needs_profile_creation', userId)
        return null
      }
      
      console.error("Error fetching profile:", error)
      return null
    }

    // Success - cache aggressively and reset failure count
    profileRequestState.failureCount = 0
    localStorage.setItem(`profile_${userId}`, JSON.stringify(data))
    localStorage.setItem(`profile_timestamp_${userId}`, now.toString())
    localStorage.removeItem('supabase_503_detected')
    
    console.log("Profile fetched and cached successfully")
    return data

  } catch (exception) {
    profileRequestState.failureCount++
    console.error("Exception fetching profile:", exception)
    
    // Open circuit breaker on exception
    if (profileRequestState.failureCount >= 2) {
      profileRequestState.isCircuitOpen = true
      localStorage.setItem('supabase_503_detected', 'true')
      localStorage.setItem('needs_profile_creation', userId)
    }
    
    return null
  }
}

// Function to create profile via RPC (bypasses schema cache issues)
export async function createUserProfileRPC(
  userId: string, 
  email: string, 
  firstName?: string, 
  lastName?: string, 
  phone?: string, 
  isBusinessApplicant?: boolean, 
  referringBusinessId?: string
) {
  const client = getSupabaseClient()
  if (!client || !userId) return null

  try {
    console.log("Creating profile via RPC...")
    const { data, error } = await client.rpc('create_user_profile', {
      user_id: userId,
      user_email: email,
      first_name: firstName || null,
      last_name: lastName || null,
      phone: phone || null,
      is_business_applicant: isBusinessApplicant || false,
      referring_business_id: referringBusinessId || null
    })

    if (error) {
      console.error("RPC profile creation failed:", error)
      return null
    }

    console.log("RPC profile creation successful:", data)
    return data
  } catch (exception) {
    console.error("Exception in RPC profile creation:", exception)
    return null
  }
}

// Function to update profile via RPC (bypasses schema cache issues)
export async function updateUserProfileRPC(
  userId: string,
  updates: {
    firstName?: string,
    lastName?: string,
    phone?: string,
    isBusinessApplicant?: boolean,
    referringBusinessId?: string,
    cardTier?: string,
    isCardHolder?: boolean
  }
) {
  const client = getSupabaseClient()
  if (!client || !userId) return null

  try {
    console.log("Updating profile via RPC...")
    const { data, error } = await client.rpc('update_user_profile', {
      user_id: userId,
      first_name: updates.firstName || null,
      last_name: updates.lastName || null,
      phone: updates.phone || null,
      is_business_applicant: updates.isBusinessApplicant || null,
      referring_business_id: updates.referringBusinessId || null,
      card_tier: updates.cardTier || null,
      is_card_holder: updates.isCardHolder || null
    })

    if (error) {
      console.error("RPC profile update failed:", error)
      return null
    }

    console.log("RPC profile update successful:", data)
    return data
  } catch (exception) {
    console.error("Exception in RPC profile update:", exception)
    return null
  }
}




// --------- BUSINESS HELPERS ---------
export async function isBusinessOwner(userId: string): Promise<boolean> {
  const client = getSupabaseClient()
  if (!client || !userId) return false

  const { data, error } = await client.from("businesses").select("id").eq("user_id", userId).maybeSingle()
  return !!data && !error
}

export async function getApprovedBusinesses() {
  const client = getSupabaseClient()
  if (!client) return []

  const { data, error } = await client
    .from("businesses")
    .select("id, name, category")
    .eq("status", "approved")
    .order("name", { ascending: true })

  if (error) {
    console.error("Error fetching approved businesses:", error)
    return []
  }

  return data
}

// --------- USER ROLES HELPERS ---------
export async function getUserRoles(userId: string) {
  const client = getSupabaseClient()
  if (!client || !userId) return []

  try {
    const { data, error } = await client
      .from("user_roles")
      .select("role_id, portal_roles!inner(role_name)")
      .eq("user_id", userId)
      .eq("is_active", true)

    if (error) {
      console.error("Error fetching user roles:", error)
      return []
    }

    return data.map((item: any) => item.portal_roles?.role_name).filter(Boolean)
  } catch (error) {
    console.error("Exception fetching user roles:", error)
    return []
  }
}

// --------- ADMIN HELPERS ---------
export async function getAdminApplications() {
  const client = getSupabaseClient()
  if (!client) return []

  const { data, error } = await client
    .from("network_applications")
    .select("*")
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching network applications:", error)
    return []
  }

  return data
}

export async function updateAdminApplication(applicationId: string, status: string, reviewedBy: string) {
  const client = getSupabaseClient()
  if (!client) return false

  const { error } = await client
    .from("network_applications")
    .update({
      status,
      reviewed_by: reviewedBy,
      reviewed_at: new Date().toISOString(),
    })
    .eq("id", applicationId)

  if (error) {
    console.error("Error updating network application:", error)
    return false
  }

  return true
}

// --------- PURCHASE & REFERRAL HELPERS ---------
export async function getUserCards(userId: string) {
  const client = getSupabaseClient()
  if (!client) return []

  // Get user's card information from physical_cards and profile
  try {
    const [physicalCardsResult, profileResult] = await Promise.all([
      client.from("physical_cards").select("*").eq("user_id", userId),
      client.from("profiles").select("id, card_tier, is_card_holder, membership_start_date, membership_end_date").eq("id", userId).single()
    ])

    const cards = []
    
    // Add physical cards
    if (physicalCardsResult.data) {
      cards.push(...physicalCardsResult.data.map(card => ({
        ...card,
        type: 'physical',
        active: card.is_active
      })))
    }
    
    // Add digital card from profile if user is card holder
    if (profileResult.data?.is_card_holder) {
      cards.push({
        id: profileResult.data.id,
        user_id: userId,
        tier: profileResult.data.card_tier,
        type: 'digital',
        active: true,
        membership_start_date: profileResult.data.membership_start_date,
        membership_end_date: profileResult.data.membership_end_date
      })
    }
    
    return cards
  } catch (error) {
    console.error("Error fetching user cards:", error)
    return []
  }
}

// Subscribe to purchases for real-time updates
export function subscribeToPurchases(callback: (payload: any) => void) {
  const client = getSupabaseClient()
  if (!client) return { unsubscribe: () => {} }

  const subscription = client
    .channel('purchases')
    .on('postgres_changes', { event: '*', schema: 'public', table: 'purchases' }, callback)
    .subscribe()

  return {
    unsubscribe: () => {
      subscription.unsubscribe()
    }
  }
}

export async function getBusinessPurchases(businessId: string) {
  const client = getSupabaseClient()
  if (!client) return []

  const { data, error } = await client
    .from("purchases")
    .select(`
      *,
      profiles:user_id (
        first_name,
        last_name,
        user_email
      )
    `)
    .eq("business_id", businessId)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching purchases:", error)
    return []
  }

  // Process the data to include user information
  return data.map((purchase: any) => ({
    ...purchase,
    customer_name: purchase.profiles ?
      `${purchase.profiles.first_name || ''} ${purchase.profiles.last_name || ''}`.trim() :
      purchase.customer_name || '',
    user_email: purchase.profiles?.user_email || purchase.user_email || '',
  }))
}

export async function getBusinessReferrals(businessId: string) {
  const client = getSupabaseClient()
  if (!client) return []

  const { data, error } = await client
    .from("referrals")
    .select(`
      *,
      referrer:referrer_id (
        first_name,
        last_name,
        user_email
      ),
      referred:referred_id (
        first_name,
        last_name,
        user_email
      )
    `)
    .eq("business_id", businessId)
    .order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching referrals:", error)
    return []
  }

  // Process the data to include user information
  return data.map((referral: any) => ({
    ...referral,
    referrer_name: referral.referrer ?
      `${referral.referrer.first_name || ''} ${referral.referrer.last_name || ''}`.trim() :
      referral.referrer_name || '',
    referrer_email: referral.referrer?.user_email || referral.referrer_email || '',

    referred_name: referral.referred ?
      `${referral.referred.first_name || ''} ${referral.referred.last_name || ''}`.trim() :
      referral.referred_name || '',
    referred_email: referral.referred?.user_email || referral.referred_email || '',
  }))
}

// --------- DATABASE HEALTH & PERFORMANCE ---------

// Database health check
export async function checkDatabaseHealth(): Promise<{
  isHealthy: boolean;
  responseTime: number;
  error?: string;
}> {
  const startTime = performance.now();
  const client = getSupabaseClient();

  if (!client) {
    return {
      isHealthy: false,
      responseTime: 0,
      error: 'Supabase client not initialized'
    };
  }

  try {
    // Simple health check query
    const { error } = await client
      .from('profiles')
      .select('id')
      .limit(1);

    const responseTime = performance.now() - startTime;

    if (error) {
      connectionHealthMonitor.recordHealthCheck('main', false);
      return {
        isHealthy: false,
        responseTime,
        error: error.message
      };
    }

    connectionHealthMonitor.recordHealthCheck('main', true);
    return {
      isHealthy: true,
      responseTime
    };
  } catch (error) {
    const responseTime = performance.now() - startTime;
    connectionHealthMonitor.recordHealthCheck('main', false);

    return {
      isHealthy: false,
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Enhanced database operation with performance monitoring
export async function performDatabaseOperation<T>(
  operationName: string,
  operation: () => Promise<T>
): Promise<T> {
  const timerName = `db_${operationName}_${Date.now()}`;
  performanceMonitor.startTimer(timerName, { operation: operationName });

  try {
    const result = await operation();
    performanceMonitor.endTimer(timerName);
    return result;
  } catch (error) {
    performanceMonitor.endTimer(timerName);
    console.error(`Database operation '${operationName}' failed:`, error);
    throw error;
  }
}

// Connection cleanup for graceful shutdown
export function cleanupConnections(): void {
  resetSupabaseClient();
  console.log('Database connections cleaned up');
}
