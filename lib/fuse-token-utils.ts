// FUSE Token Gaming Utilities
// Handles trustline checking, token payments, and XRPL interactions

export const FUSE_TOKEN = {
  currency: 'FUSE',
  issuer: 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo',
  name: 'Fuse.vip Token'
} as const

export const GAMING_WALLET = 'rsdggftBzdLerbCzm8HGeeUeApzNnvfgtx' as const

export const MAGNETIC_DEX_URL = 'https://xmagnetic.org/dex/FUSE%2Brs2G9J95qwL3yw241JTRdgms2hhcLouVHo_XRP%2BXRP?network=mainnet' as const

// XRPL API endpoints for trustline checking
const XRPL_ENDPOINTS = [
  'https://xrplcluster.com',
  'https://s1.ripple.com:51234',
  'https://s2.ripple.com:51234'
] as const

// Interface for XRPL account_lines response
export interface XRPLTrustLine {
  account: string
  balance: string
  currency: string
  limit: string
  limit_peer: string
  no_ripple?: boolean
  no_ripple_peer?: boolean
  quality_in?: number
  quality_out?: number
}

export interface XRPLAccountLinesResponse {
  result: {
    account: string
    lines: XRPLTrustLine[]
    ledger_current_index?: number
    ledger_index?: number
    validated?: boolean
  }
  status: string
  type: string
}

// Check if user has FUSE trustline established
export async function checkFuseTrustline(walletAddress: string): Promise<{
  hasTrustline: boolean
  balance: string
  limit: string
  error?: string
}> {
  if (!walletAddress) {
    return { hasTrustline: false, balance: '0', limit: '0', error: 'No wallet address provided' }
  }

  for (const endpoint of XRPL_ENDPOINTS) {
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method: 'account_lines',
          params: [{
            account: walletAddress,
            peer: FUSE_TOKEN.issuer,
            ledger_index: 'validated'
          }]
        })
      })

      if (!response.ok) {
        continue // Try next endpoint
      }

      const data: XRPLAccountLinesResponse = await response.json()
      
      if (data.status !== 'success') {
        continue // Try next endpoint
      }

      // Look for FUSE trustline
      const fuseTrustline = data.result.lines.find(line => 
        line.currency === FUSE_TOKEN.currency && 
        line.account === FUSE_TOKEN.issuer
      )

      if (fuseTrustline) {
        return {
          hasTrustline: true,
          balance: fuseTrustline.balance,
          limit: fuseTrustline.limit
        }
      } else {
        return {
          hasTrustline: false,
          balance: '0',
          limit: '0'
        }
      }

    } catch (error) {
      console.error(`Error checking trustline with ${endpoint}:`, error)
      continue // Try next endpoint
    }
  }

  return { 
    hasTrustline: false, 
    balance: '0', 
    limit: '0', 
    error: 'Unable to check trustline - all XRPL endpoints failed' 
  }
}

// Create FUSE token payment payload for Xaman
export function createFuseTokenPayment(
  destination: string = GAMING_WALLET,
  amount: string = '1',
  memo?: string,
  returnUrl?: string
) {
  const payload = {
    txjson: {
      TransactionType: 'Payment',
      Destination: destination,
      Amount: {
        currency: FUSE_TOKEN.currency,
        issuer: FUSE_TOKEN.issuer,
        value: amount
      }
    },
    options: {
      return_url: returnUrl ? {
        app: returnUrl,
        web: returnUrl
      } : undefined,
      force_network: 'MAINNET' as const
    },
    custom_meta: {
      identifier: `fuse_gaming_${Date.now()}`,
      instruction: memo || `Send ${amount} FUSE tokens to enter the game`,
      blob: {
        appName: 'Fuse.vip Gaming',
        appIcon: 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/fuse-token-cVZqlFSRtwajwj78HxXV8wNGxxBtqg.png'
      }
    }
  }

  // Add memo if provided
  if (memo) {
    payload.txjson.Memos = [{
      Memo: {
        MemoType: Buffer.from('memo').toString('hex').toUpperCase(),
        MemoData: Buffer.from(memo).toString('hex').toUpperCase()
      }
    }]
  }

  return payload
}

// Generate return URL for gaming session
export function generateGamingReturnUrl(
  sessionId: string,
  gameType: string,
  baseUrl: string = typeof window !== 'undefined' ? window.location.origin : ''
): string {
  const params = new URLSearchParams({
    session: sessionId,
    game: gameType,
    payment: 'fuse'
  })
  
  return `${baseUrl}/games?${params.toString()}`
}

// Generate Magnetic DEX URL with return parameter
export function generateMagneticDexUrl(returnUrl: string): string {
  const params = new URLSearchParams({
    return_to: returnUrl
  })
  
  return `${MAGNETIC_DEX_URL}&${params.toString()}`
}

// Verify FUSE token transaction on XRPL
export async function verifyFuseTransaction(
  transactionHash: string,
  expectedAmount: string = '1',
  expectedDestination: string = GAMING_WALLET
): Promise<{
  verified: boolean
  amount?: string
  destination?: string
  source?: string
  error?: string
}> {
  for (const endpoint of XRPL_ENDPOINTS) {
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          method: 'tx',
          params: [{
            transaction: transactionHash,
            binary: false
          }]
        })
      })

      if (!response.ok) {
        continue
      }

      const data = await response.json()
      
      if (data.status !== 'success') {
        continue
      }

      const tx = data.result
      
      // Verify transaction details
      if (tx.TransactionType === 'Payment' &&
          tx.Destination === expectedDestination &&
          tx.Amount?.currency === FUSE_TOKEN.currency &&
          tx.Amount?.issuer === FUSE_TOKEN.issuer &&
          tx.Amount?.value === expectedAmount &&
          tx.meta?.TransactionResult === 'tesSUCCESS') {
        
        return {
          verified: true,
          amount: tx.Amount.value,
          destination: tx.Destination,
          source: tx.Account
        }
      }

      return {
        verified: false,
        error: 'Transaction details do not match expected values'
      }

    } catch (error) {
      console.error(`Error verifying transaction with ${endpoint}:`, error)
      continue
    }
  }

  return {
    verified: false,
    error: 'Unable to verify transaction - all XRPL endpoints failed'
  }
}

// Helper to format FUSE amount for display
export function formatFuseAmount(amount: string | number): string {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 8
  })
}

// Helper to check if amount is sufficient for gaming
export function hasSufficientFuseBalance(balance: string, required: string = '1'): boolean {
  const balanceNum = parseFloat(balance)
  const requiredNum = parseFloat(required)
  return balanceNum >= requiredNum
}
