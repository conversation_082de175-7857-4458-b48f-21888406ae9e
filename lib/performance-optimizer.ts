/**
 * Performance Optimization Utilities
 * Comprehensive performance enhancements for the FUSE VIP application
 */

import { NextRequest, NextResponse } from 'next/server'

// Cache configuration
export const CACHE_CONFIG = {
  // Static assets
  STATIC_ASSETS: 60 * 60 * 24 * 365, // 1 year
  // Images and logos
  IMAGES: 60 * 60 * 24 * 30, // 30 days
  // API responses
  API_SHORT: 60 * 5, // 5 minutes
  API_MEDIUM: 60 * 30, // 30 minutes
  API_LONG: 60 * 60 * 24, // 1 day
  // Database queries
  DB_FAST: 60 * 2, // 2 minutes
  DB_MEDIUM: 60 * 15, // 15 minutes
  DB_SLOW: 60 * 60, // 1 hour
} as const

// Performance headers for different content types
export const PERFORMANCE_HEADERS = {
  // Static assets with long cache
  STATIC: {
    'Cache-Control': `public, max-age=${CACHE_CONFIG.STATIC_ASSETS}, immutable`,
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
  },
  // Images with medium cache
  IMAGES: {
    'Cache-Control': `public, max-age=${CACHE_CONFIG.IMAGES}`,
    'X-Content-Type-Options': 'nosniff',
    'Vary': 'Accept-Encoding',
  },
  // API responses with short cache
  API: {
    'Cache-Control': `public, max-age=${CACHE_CONFIG.API_SHORT}`,
    'X-Content-Type-Options': 'nosniff',
    'Vary': 'Accept-Encoding',
    'X-RateLimit-Limit': '100',
    'X-RateLimit-Remaining': '99',
  },
  // No cache for dynamic content
  DYNAMIC: {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
  },
} as const

// Memory cache for frequently accessed data
class MemoryCache {
  private cache = new Map<string, { data: any; expires: number }>()
  private maxSize = 1000 // Maximum number of cached items

  set(key: string, data: any, ttlSeconds: number): void {
    // Clean up expired entries if cache is getting full
    if (this.cache.size >= this.maxSize) {
      this.cleanup()
    }

    const expires = Date.now() + (ttlSeconds * 1000)
    this.cache.set(key, { data, expires })
  }

  get(key: string): any | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    if (Date.now() > item.expires) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  private cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expires) {
        this.cache.delete(key)
      }
    }
  }

  getStats(): { size: number; maxSize: number } {
    return {
      size: this.cache.size,
      maxSize: this.maxSize
    }
  }
}

// Global memory cache instance
export const memoryCache = new MemoryCache()

// Database query optimization utilities
export class QueryOptimizer {
  private static queryCache = new Map<string, { result: any; expires: number }>()

  static async cachedQuery<T>(
    queryFn: () => Promise<T>,
    cacheKey: string,
    ttlSeconds: number = CACHE_CONFIG.DB_MEDIUM
  ): Promise<T> {
    // Check cache first
    const cached = this.queryCache.get(cacheKey)
    if (cached && Date.now() < cached.expires) {
      return cached.result
    }

    // Execute query
    const result = await queryFn()
    
    // Cache result
    this.queryCache.set(cacheKey, {
      result,
      expires: Date.now() + (ttlSeconds * 1000)
    })

    return result
  }

  static invalidateCache(pattern?: string): void {
    if (pattern) {
      // Remove entries matching pattern
      for (const key of this.queryCache.keys()) {
        if (key.includes(pattern)) {
          this.queryCache.delete(key)
        }
      }
    } else {
      // Clear all cache
      this.queryCache.clear()
    }
  }

  static getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.queryCache.size,
      keys: Array.from(this.queryCache.keys())
    }
  }
}

// Response optimization utilities
export class ResponseOptimizer {
  static addPerformanceHeaders(
    response: NextResponse,
    type: keyof typeof PERFORMANCE_HEADERS = 'API'
  ): NextResponse {
    const headers = PERFORMANCE_HEADERS[type]
    
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value)
    })

    return response
  }

  static compressResponse(data: any): string {
    // Simple JSON compression - remove unnecessary whitespace
    return JSON.stringify(data, null, 0)
  }

  static createOptimizedResponse(
    data: any,
    options: {
      type?: keyof typeof PERFORMANCE_HEADERS
      status?: number
      compress?: boolean
    } = {}
  ): NextResponse {
    const {
      type = 'API',
      status = 200,
      compress = true
    } = options

    const responseData = compress ? this.compressResponse(data) : JSON.stringify(data, null, 2)
    
    const response = new NextResponse(responseData, {
      status,
      headers: {
        'Content-Type': 'application/json',
        ...PERFORMANCE_HEADERS[type]
      }
    })

    return response
  }
}

// Image optimization utilities
export class ImageOptimizer {
  static getOptimizedImageUrl(
    originalUrl: string,
    options: {
      width?: number
      height?: number
      quality?: number
      format?: 'webp' | 'avif' | 'jpeg' | 'png'
    } = {}
  ): string {
    const {
      width,
      height,
      quality = 85,
      format = 'webp'
    } = options

    // If it's a processed logo, return as-is
    if (originalUrl.startsWith('/processed-logos/')) {
      return originalUrl
    }

    // For Next.js Image component optimization
    const params = new URLSearchParams()
    if (width) params.set('w', width.toString())
    if (height) params.set('h', height.toString())
    params.set('q', quality.toString())
    params.set('f', format)

    return `/_next/image?url=${encodeURIComponent(originalUrl)}&${params.toString()}`
  }

  static getResponsiveImageSizes(): string {
    return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>()

  static startTimer(label: string): () => number {
    const start = performance.now()
    
    return () => {
      const duration = performance.now() - start
      this.recordMetric(label, duration)
      return duration
    }
  }

  static recordMetric(label: string, value: number): void {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, [])
    }
    
    const values = this.metrics.get(label)!
    values.push(value)
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift()
    }
  }

  static getMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, any> = {}
    
    for (const [label, values] of this.metrics.entries()) {
      if (values.length > 0) {
        result[label] = {
          avg: values.reduce((a, b) => a + b, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          count: values.length
        }
      }
    }
    
    return result
  }

  static clearMetrics(): void {
    this.metrics.clear()
  }
}

// Utility function to measure async operations
export async function measureAsync<T>(
  operation: () => Promise<T>,
  label: string
): Promise<T> {
  const endTimer = PerformanceMonitor.startTimer(label)
  try {
    const result = await operation()
    return result
  } finally {
    endTimer()
  }
}

// Export all utilities
export {
  MemoryCache,
  QueryOptimizer,
  ResponseOptimizer,
  ImageOptimizer,
  PerformanceMonitor
}
