/**
 * Cache Initializer
 * Ensures schema cache is loaded on server startup and handles background refresh
 */

import { directSchemaCache } from '@/lib/schema-cache-direct';

let initializationPromise: Promise<void> | null = null;
let isInitialized = false;

/**
 * Initialize the schema cache on server startup
 */
export async function initializeSchemaCache(): Promise<void> {
  // Prevent multiple simultaneous initializations
  if (initializationPromise) {
    console.log('⏳ Schema cache initialization already in progress, waiting...');
    return initializationPromise;
  }

  if (isInitialized) {
    console.log('✅ Schema cache already initialized');
    return;
  }

  console.log('🚀 Starting schema cache initialization...');

  initializationPromise = (async () => {
    try {
      // Check if we're in a server environment
      if (typeof window !== 'undefined') {
        console.warn('⚠️ Schema cache can only be initialized on the server');
        return;
      }

      const startTime = Date.now();
      
      // Initialize the cache
      await directSchemaCache.initialize();
      
      const duration = Date.now() - startTime;
      console.log(`🎉 Schema cache initialization completed in ${duration}ms`);
      
      // Get initial stats
      const stats = directSchemaCache.getStats();
      console.log(`📊 Cache loaded: ${stats.businessesCount} businesses, ${stats.profilesCount} profiles`);
      
      isInitialized = true;
      
      // Set up periodic refresh (every 30 minutes)
      setupPeriodicRefresh();
      
    } catch (error) {
      console.error('❌ Schema cache initialization failed:', error);
      throw error;
    } finally {
      initializationPromise = null;
    }
  })();

  return initializationPromise;
}

/**
 * Set up periodic cache refresh
 */
function setupPeriodicRefresh(): void {
  // Refresh every 30 minutes (1800000 ms)
  const refreshInterval = 30 * 60 * 1000;
  
  setInterval(async () => {
    try {
      console.log('🔄 Starting periodic schema cache refresh...');
      await directSchemaCache.refresh();
      
      const stats = directSchemaCache.getStats();
      console.log(`✅ Periodic refresh completed - Cache: ${stats.businessesCount} businesses, ${stats.profilesCount} profiles`);
    } catch (error) {
      console.error('❌ Periodic schema cache refresh failed:', error);
    }
  }, refreshInterval);
  
  console.log(`⏰ Periodic cache refresh scheduled every ${refreshInterval / 60000} minutes`);
}

/**
 * Ensure the cache is ready before using it
 */
export async function ensureCacheReady(): Promise<void> {
  if (!isInitialized) {
    await initializeSchemaCache();
  }
}

/**
 * Get cache readiness status
 */
export function isCacheReady(): boolean {
  return isInitialized && directSchemaCache.isReady();
}

/**
 * Force refresh the cache
 */
export async function refreshCache(): Promise<void> {
  console.log('🔄 Force refreshing schema cache...');
  await directSchemaCache.refresh();
}

/**
 * Get cache health information
 */
export function getCacheHealth() {
  return {
    isInitialized,
    isReady: directSchemaCache.isReady(),
    stats: directSchemaCache.getStats(),
    health: directSchemaCache.getHealthStatus()
  };
}

// Auto-initialize on server startup in production
if (typeof window === 'undefined' && process.env.NODE_ENV === 'production') {
  // Use a small delay to ensure the server is ready
  setTimeout(() => {
    initializeSchemaCache().catch(error => {
      console.error('❌ Auto-initialization of schema cache failed:', error);
    });
  }, 2000); // 2 second delay
}

// Also initialize in development, but with a longer delay
if (typeof window === 'undefined' && process.env.NODE_ENV === 'development') {
  setTimeout(() => {
    initializeSchemaCache().catch(error => {
      console.error('❌ Auto-initialization of schema cache failed in development:', error);
    });
  }, 5000); // 5 second delay
}