/**
 * SuperUser Service
 * Uses Supabase service role key to bypass RLS policies and access all data
 * This service should only be used on the server side for administrative operations
 */

import { createClient } from '@supabase/supabase-js'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

interface BusinessData {
  id: string
  name: string
  logo_url?: string
  website?: string
  category?: string
  premium_discount?: string
  is_active: boolean
  business_spotlight?: boolean
  business_address?: string
  contact_name?: string
  contact_email?: string
  contact_phone?: string
  user_id?: string
  created_at: string
  updated_at?: string
}

interface ProfileData {
  id: string
  first_name?: string
  last_name?: string
  user_email?: string
  phone?: string
  is_card_holder: boolean
  is_business_applicant: boolean
  card_tier?: string
  xrp_wallet_address?: string
  membership_start_date?: string
  membership_end_date?: string
  referring_business_id?: string
  created_at: string
  updated_at?: string
}

class SuperUserService {
  private client: ReturnType<typeof createClient> | null = null
  private cache = new Map<string, CacheEntry<any>>()
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5 minutes

  constructor() {
    this.initializeClient()
  }

  private initializeClient() {
    try {
      // SECURITY: Ensure this only runs server-side
      if (typeof window !== 'undefined') {
        console.error('❌ SuperUser Service: SECURITY ERROR - Cannot run on client-side')
        throw new Error('SuperUser Service can only be used server-side')
      }

      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

      if (!supabaseUrl || !serviceRoleKey) {
        console.error('❌ SuperUser Service: Missing Supabase environment variables')
        return
      }

      this.client = createClient(supabaseUrl, serviceRoleKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        global: {
          headers: {
            'X-Client-Info': 'fuse-superuser-service',
            'User-Agent': 'Fuse.vip/SuperUser/1.0'
          }
        }
      })

      console.log('✅ SuperUser Service: Initialized with service role key')
    } catch (error) {
      console.error('❌ SuperUser Service: Failed to initialize client:', error)
      this.client = null
    }
  }

  private getClient() {
    if (!this.client) {
      this.initializeClient()
    }
    
    if (!this.client) {
      throw new Error('SuperUser Service: Client not available - initialization failed')
    }
    
    return this.client
  }

  private getCachedData<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  private setCachedData<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * Get all businesses (bypasses RLS)
   */
  async getAllBusinesses(useCache: boolean = true): Promise<BusinessData[]> {
    const cacheKey = 'all_businesses'
    
    if (useCache) {
      const cached = this.getCachedData<BusinessData[]>(cacheKey)
      if (cached) {
        console.log(`📋 SuperUser: Returning ${cached.length} businesses from cache`)
        return cached
      }
    }

    try {
      console.log('🔄 SuperUser: Fetching all businesses from database...')
      const client = this.getClient()

      const { data, error } = await client
        .from('businesses')
        .select(`
          id,
          name,
          logo_url,
          website,
          category,
          premium_discount,
          is_active,
          business_spotlight,
          business_address,
          contact_name,
          contact_email,
          contact_phone,
          user_id,
          created_at,
          updated_at
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('❌ SuperUser: Error fetching businesses:', error)
        throw new Error(`Failed to fetch businesses: ${error.message}`)
      }

      const businesses = data || []
      console.log(`✅ SuperUser: Successfully fetched ${businesses.length} businesses`)

      if (useCache) {
        this.setCachedData(cacheKey, businesses)
      }

      return businesses
    } catch (error) {
      console.error('❌ SuperUser: getAllBusinesses failed:', error)
      throw error
    }
  }

  /**
   * Get all profiles (bypasses RLS)
   */
  async getAllProfiles(useCache: boolean = true): Promise<ProfileData[]> {
    const cacheKey = 'all_profiles'
    
    if (useCache) {
      const cached = this.getCachedData<ProfileData[]>(cacheKey)
      if (cached) {
        console.log(`👥 SuperUser: Returning ${cached.length} profiles from cache`)
        return cached
      }
    }

    try {
      console.log('🔄 SuperUser: Fetching all profiles from database...')
      const client = this.getClient()

      const { data, error } = await client
        .from('profiles')
        .select(`
          id,
          first_name,
          last_name,
          user_email,
          phone,
          is_card_holder,
          is_business_applicant,
          card_tier,
          xrp_wallet_address,
          membership_start_date,
          membership_end_date,
          referring_business_id,
          created_at,
          updated_at
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('❌ SuperUser: Error fetching profiles:', error)
        throw new Error(`Failed to fetch profiles: ${error.message}`)
      }

      const profiles = data || []
      console.log(`✅ SuperUser: Successfully fetched ${profiles.length} profiles`)

      if (useCache) {
        this.setCachedData(cacheKey, profiles)
      }

      return profiles
    } catch (error) {
      console.error('❌ SuperUser: getAllProfiles failed:', error)
      throw error
    }
  }

  /**
   * Get active businesses only
   */
  async getActiveBusinesses(useCache: boolean = true): Promise<BusinessData[]> {
    const allBusinesses = await this.getAllBusinesses(useCache)
    return allBusinesses.filter(business => business.is_active)
  }

  /**
   * Get card holders only
   */
  async getCardHolders(useCache: boolean = true): Promise<ProfileData[]> {
    const allProfiles = await this.getAllProfiles(useCache)
    return allProfiles.filter(profile => profile.is_card_holder)
  }

  /**
   * Get business applicants only
   */
  async getBusinessApplicants(useCache: boolean = true): Promise<ProfileData[]> {
    const allProfiles = await this.getAllProfiles(useCache)
    return allProfiles.filter(profile => profile.is_business_applicant)
  }

  /**
   * Get businesses with owner profile data
   */
  async getBusinessesWithOwners(useCache: boolean = true): Promise<any[]> {
    const cacheKey = 'businesses_with_owners'
    
    if (useCache) {
      const cached = this.getCachedData<any[]>(cacheKey)
      if (cached) {
        console.log(`🏢👥 SuperUser: Returning ${cached.length} businesses with owners from cache`)
        return cached
      }
    }

    try {
      console.log('🔄 SuperUser: Fetching businesses with owner data...')
      const client = this.getClient()

      const { data, error } = await client
        .from('businesses')
        .select(`
          *,
          profiles!businesses_user_id_fkey (
            id,
            first_name,
            last_name,
            user_email,
            is_card_holder,
            card_tier
          )
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('❌ SuperUser: Error fetching businesses with owners:', error)
        throw new Error(`Failed to fetch businesses with owners: ${error.message}`)
      }

      const businessesWithOwners = data || []
      console.log(`✅ SuperUser: Successfully fetched ${businessesWithOwners.length} businesses with owners`)

      if (useCache) {
        this.setCachedData(cacheKey, businessesWithOwners)
      }

      return businessesWithOwners
    } catch (error) {
      console.error('❌ SuperUser: getBusinessesWithOwners failed:', error)
      throw error
    }
  }

  /**
   * Clear all cache
   */
  clearCache() {
    this.cache.clear()
    console.log('🗑️ SuperUser: Cache cleared')
  }

  /**
   * Clear specific cache entry
   */
  clearCacheEntry(key: string) {
    this.cache.delete(key)
    console.log(`🗑️ SuperUser: Cache entry '${key}' cleared`)
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    }
  }
}

// Export singleton instance
export const superUserService = new SuperUserService()
export default superUserService
