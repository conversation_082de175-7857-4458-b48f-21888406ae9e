/**
 * Direct Storage Service
 * Bypasses Supabase client connection limits by using direct HTTP API calls
 */

interface UploadResult {
  data?: any;
  error?: string;
}

interface StorageConfig {
  baseUrl: string;
  apiKey: string;
  projectId: string;
}

class DirectStorageService {
  private config: StorageConfig;
  private retryAttempts = 3;
  private retryDelay = 1000;

  constructor() {
    this.config = {
      baseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!,
      // Only use anon key for client-side operations
      // Service role operations should be handled server-side only
      apiKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      projectId: this.extractProjectId(process.env.NEXT_PUBLIC_SUPABASE_URL!)
    };
  }

  private extractProjectId(url: string): string {
    const match = url.match(/https:\/\/([^.]+)\.supabase\.co/);
    return match ? match[1] : '';
  }

  private async makeRequest(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<Response> {
    const url = `${this.config.baseUrl}/storage/v1${endpoint}`;
    
    const defaultHeaders = {
      'Authorization': `Bearer ${this.config.apiKey}`,
      'X-Client-Info': 'fuse-direct-storage',
    };

    const requestOptions: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    return fetch(url, requestOptions);
  }

  private async retry<T>(
    operation: () => Promise<T>,
    attempt = 1
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (attempt >= this.retryAttempts) {
        throw error;
      }

      // Exponential backoff with jitter
      const delay = this.retryDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
      
      console.warn(`Storage operation failed, retrying (attempt ${attempt + 1}/${this.retryAttempts}):`, error);
      return this.retry(operation, attempt + 1);
    }
  }

  async uploadFile(
    bucket: string,
    path: string,
    file: File,
    options?: {
      cacheControl?: string;
      contentType?: string;
      upsert?: boolean;
    }
  ): Promise<UploadResult> {
    try {
      return await this.retry(async () => {
        const formData = new FormData();
        formData.append('file', file);
        
        const params = new URLSearchParams();
        if (options?.upsert !== false) {
          params.append('upsert', 'true');
        }
        if (options?.cacheControl) {
          params.append('cacheControl', options.cacheControl);
        }

        const endpoint = `/object/${bucket}/${path}${params.toString() ? '?' + params.toString() : ''}`;
        
        const response = await this.makeRequest(endpoint, {
          method: 'POST',
          body: formData,
          headers: {
            // Don't set Content-Type - let browser set it with boundary for FormData
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Upload failed: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        return { data };
      });
    } catch (error) {
      console.error('Direct upload error:', error);
      return { 
        error: error instanceof Error ? error.message : 'Upload failed' 
      };
    }
  }

  async deleteFile(bucket: string, path: string): Promise<UploadResult> {
    try {
      return await this.retry(async () => {
        const endpoint = `/object/${bucket}/${path}`;
        
        const response = await this.makeRequest(endpoint, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Delete failed: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        return { data };
      });
    } catch (error) {
      console.error('Direct delete error:', error);
      return { 
        error: error instanceof Error ? error.message : 'Delete failed' 
      };
    }
  }

  getPublicUrl(bucket: string, path: string): string {
    return `${this.config.baseUrl}/storage/v1/object/public/${bucket}/${path}`;
  }

  async createSignedUrl(
    bucket: string, 
    path: string, 
    expiresIn: number = 3600
  ): Promise<UploadResult> {
    try {
      return await this.retry(async () => {
        const endpoint = `/object/sign/${bucket}/${path}`;
        
        const response = await this.makeRequest(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ expiresIn })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Signed URL creation failed: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        return { data };
      });
    } catch (error) {
      console.error('Direct signed URL error:', error);
      return { 
        error: error instanceof Error ? error.message : 'Signed URL creation failed' 
      };
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/bucket', {
        method: 'GET'
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  async listBuckets(): Promise<UploadResult> {
    try {
      const response = await this.makeRequest('/bucket', {
        method: 'GET'
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`List buckets failed: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error('List buckets error:', error);
      return { 
        error: error instanceof Error ? error.message : 'List buckets failed' 
      };
    }
  }

  getStats() {
    return {
      serviceType: 'direct-api',
      baseUrl: this.config.baseUrl,
      projectId: this.config.projectId,
      authConfigured: !!this.config.apiKey
    };
  }
}

// Create singleton instance
const directStorageService = new DirectStorageService();

export default directStorageService;

// Helper functions using direct storage
export async function uploadBusinessLogoDirect(
  businessId: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<{ url: string; error?: string }> {
  try {
    // Validate file
    if (!file) {
      throw new Error('No file provided');
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      throw new Error('File size must be less than 5MB');
    }

    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error('File must be an image (JPEG, PNG, WebP, or SVG)');
    }

    // Generate unique filename
    const timestamp = Date.now();
    const fileExt = file.name.split('.').pop();
    const fileName = `logo-${businessId}-${timestamp}.${fileExt}`;
    const filePath = `business-logos/${fileName}`;

    onProgress?.(10);

    // Upload file using direct API
    const { data, error } = await directStorageService.uploadFile(
      'business-assets',
      filePath,
      file,
      {
        contentType: file.type,
        upsert: true
      }
    );

    if (error) {
      throw new Error(error);
    }

    onProgress?.(90);

    // Get public URL
    const publicUrl = directStorageService.getPublicUrl('business-assets', filePath);
    
    onProgress?.(100);

    return { url: publicUrl };
  } catch (error) {
    console.error('Direct logo upload error:', error);
    return { 
      url: '', 
      error: error instanceof Error ? error.message : 'Upload failed' 
    };
  }
}

export async function deleteBusinessLogoDirect(logoUrl: string): Promise<{ success: boolean; error?: string }> {
  try {
    // Extract path from URL
    const urlParts = logoUrl.split('/business-assets/');
    if (urlParts.length !== 2) {
      throw new Error('Invalid logo URL format');
    }

    const filePath = urlParts[1];
    const { error } = await directStorageService.deleteFile('business-assets', filePath);

    if (error) {
      throw new Error(error);
    }

    return { success: true };
  } catch (error) {
    console.error('Direct logo delete error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Delete failed' 
    };
  }
}

// Export direct storage service instance
export { directStorageService };