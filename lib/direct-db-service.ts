/**
 * Direct Database Service
 * Bypasses PostgREST/Supabase client when schema cache is corrupted
 * Uses direct PostgreSQL connections for reliable data access
 */

import { Pool, PoolClient } from 'pg'

interface Business {
  id: string
  name: string
  logo_url?: string
  website?: string
  category: string
  premium_discount: string
  is_active: boolean
  business_spotlight?: boolean
  contact_name?: string
  contact_email?: string
  contact_phone?: string
  business_address?: string
  created_at: string
  updated_at: string
}

class DirectDatabaseService {
  private pool: Pool | null = null
  private connectionString: string

  constructor() {
    // Use direct connection since pooler was having timeout issues
    this.connectionString = process.env.DATABASE_URL || `postgresql://postgres:${process.env.SUPABASE_DB_PASSWORD}@db.haqbtbpmyadkocakqnew.supabase.co:5432/postgres`
  }

  private async getPool(): Promise<Pool> {
    if (!this.pool) {
      this.pool = new Pool({
        connectionString: this.connectionString,
        ssl: { rejectUnauthorized: false },
        max: 10,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 10000,
      })
    }
    return this.pool
  }

  private async getClient(): Promise<PoolClient> {
    const pool = await this.getPool()
    return pool.connect()
  }

  async getActiveBusinesses(): Promise<Business[]> {
    const client = await this.getClient()
    try {
      const result = await client.query(`
        SELECT 
          id, name, logo_url, website, category, premium_discount, 
          is_active, business_spotlight, contact_name, contact_email, 
          contact_phone, business_address, created_at, updated_at
        FROM businesses 
        WHERE is_active = true 
        ORDER BY created_at DESC
      `)
      return result.rows
    } finally {
      client.release()
    }
  }

  async getBusinessById(id: string): Promise<Business | null> {
    const client = await this.getClient()
    try {
      const result = await client.query(`
        SELECT 
          id, name, logo_url, website, category, premium_discount, 
          is_active, business_spotlight, contact_name, contact_email, 
          contact_phone, business_address, created_at, updated_at
        FROM businesses 
        WHERE id = $1
      `, [id])
      return result.rows[0] || null
    } finally {
      client.release()
    }
  }

  async getBusinessCount(): Promise<number> {
    const client = await this.getClient()
    try {
      const result = await client.query('SELECT COUNT(*) as count FROM businesses WHERE is_active = true')
      return parseInt(result.rows[0].count)
    } finally {
      client.release()
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const client = await this.getClient()
      await client.query('SELECT 1')
      client.release()
      return true
    } catch (error) {
      console.error('Direct DB connection test failed:', error)
      return false
    }
  }

  async getUserProfile(userId: string): Promise<any> {
    const client = await this.getClient()
    try {
      const result = await client.query(`
        SELECT 
          id, first_name, last_name, phone, user_email, 
          is_card_holder, is_business_applicant, xrp_wallet_address,
          membership_start_date, membership_end_date, card_tier,
          created_at
        FROM profiles 
        WHERE id = $1
      `, [userId])
      return result.rows[0] || null
    } finally {
      client.release()
    }
  }

  async createBusinessVisit(userId: string, businessId: string, fuseEarned: number = 100): Promise<void> {
    const client = await this.getClient()
    try {
      await client.query(`
        INSERT INTO business_visits (user_id, business_id, fuse_earned, scanned_at)
        VALUES ($1, $2, $3, NOW())
      `, [userId, businessId, fuseEarned])
    } finally {
      client.release()
    }
  }

  async close(): Promise<void> {
    if (this.pool) {
      await this.pool.end()
      this.pool = null
    }
  }
}

// Singleton instance
let directDbService: DirectDatabaseService | null = null

export function getDirectDbService(): DirectDatabaseService {
  if (!directDbService) {
    directDbService = new DirectDatabaseService()
  }
  return directDbService
}

export type { Business }
export { DirectDatabaseService }
