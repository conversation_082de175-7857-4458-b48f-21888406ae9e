/**
 * Xaman Payment URL Generator
 * Creates payment URLs for XRP and FUSE tokens
 */

export interface XamanPaymentConfig {
  amount: number
  currency: 'XRP' | 'FUSE'
  destination: string
  destinationTag?: number
  issuer?: string
  currencyCode?: string
  memo?: string
}

export interface TokenConfig {
  issuer: string
  currencyCode: string
}

// Fuse.vip wallet address
export const FUSE_WALLET_ADDRESS = 'rHMKAoZT33VAQKzXxB7EdfDQ5pdPJ3sbU'

// Token configurations - FUSE token on XRPL
export const FUSE_TOKEN_CONFIG: TokenConfig = {
  issuer: 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo', // FUSE token issuer
  currencyCode: '4655534500000000000000000000000000000000' // FUSE currency code
}

/**
 * Generate Xaman payment URL
 */
export function generateXamanPaymentUrl(config: XamanPaymentConfig): string {
  const baseUrl = 'https://xaman.app/detect/request'
  const params = new URLSearchParams()
  
  // Add amount
  params.append('amount', config.amount.toString())
  
  // Add network (always XRPL for both XRP and FUSE)
  params.append('network', 'XRPL')
  
  // Add destination
  params.append('destination', config.destination)
  
  // Add destination tag if provided
  if (config.destinationTag) {
    params.append('dt', config.destinationTag.toString())
  }
  
  // For FUSE tokens, add issuer and currency
  if (config.currency === 'FUSE') {
    params.append('issuer', config.issuer || FUSE_TOKEN_CONFIG.issuer)
    params.append('currency', config.currencyCode || FUSE_TOKEN_CONFIG.currencyCode)
  }
  
  // Add memo if provided
  if (config.memo) {
    params.append('memo', config.memo)
  }
  
  // Generate a unique request ID (placeholder - in real app this would be generated by Xaman)
  const requestId = `fuse-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  
  return `${baseUrl}:${requestId}?${params.toString()}`
}

/**
 * Generate XRP payment URL
 */
export function generateXRPPaymentUrl(amount: number, memo?: string): string {
  return generateXamanPaymentUrl({
    amount,
    currency: 'XRP',
    destination: FUSE_WALLET_ADDRESS,
    memo
  })
}

/**
 * Generate FUSE payment URL
 */
export function generateFUSEPaymentUrl(amount: number, memo?: string): string {
  return generateXamanPaymentUrl({
    amount,
    currency: 'FUSE',
    destination: FUSE_WALLET_ADDRESS,
    issuer: FUSE_TOKEN_CONFIG.issuer,
    currencyCode: FUSE_TOKEN_CONFIG.currencyCode,
    memo
  })
}

/**
 * Generate payment URLs for VIP tiers
 */
export function generateVIPPaymentUrls(tier: 'monthly' | 'annual', pricing: any) {
  const tierName = tier === 'monthly' ? 'Monthly VIP Card' : 'Premium Card'
  const card = pricing.cards?.find((c: any) => c.name === tierName)
  
  if (!card) {
    throw new Error(`Pricing data not found for ${tierName}`)
  }
  
  const xrpAmount = parseFloat(card.xrpPrice)
  const fuseAmount = parseInt(card.fusePrice.replace(/,/g, ''))
  
  return {
    xrp: generateXRPPaymentUrl(xrpAmount, `VIP-${tier}-${Date.now()}`),
    fuse: generateFUSEPaymentUrl(fuseAmount, `VIP-${tier}-${Date.now()}`),
    amounts: {
      xrp: xrpAmount,
      fuse: fuseAmount,
      usd: card.usdPrice
    }
  }
}