// Server-side only direct PostgreSQL connection
import { serverEnv } from './env'

let Client: any = null
let Pool: any = null
let connectionPool: any = null

// Dynamically import pg only on server side
async function initPgClient() {
  if (typeof window !== 'undefined') {
    throw new Error('Direct database connection is server-side only')
  }

  if (!Client || !Pool) {
    const pg = await import('pg')
    Client = pg.Client
    Pool = pg.Pool
  }

  return { Client, Pool }
}

// Connection configuration with security best practices
const getConnectionConfig = () => {
  const config = {
    connectionString: serverEnv.DATABASE_URL ||
      `postgresql://postgres:${serverEnv.SUPABASE_DB_PASSWORD}@db.haqbtbpmyadkocakqnew.supabase.co:5432/postgres`,
    ssl: {
      rejectUnauthorized: false,
      // In production, use proper SSL certificates
    },
    connectionTimeoutMillis: 30000, // 30 seconds
    idleTimeoutMillis: 60000, // 60 seconds
    max: 5, // Maximum number of connections in pool
    min: 1, // Minimum number of connections in pool
    statement_timeout: 30000, // 30 seconds for query execution
  }

  if (!config.connectionString) {
    throw new Error('Database connection string not configured')
  }

  return config
}

// Connection retry logic with exponential backoff
async function connectWithRetry(clientInstance: any, maxRetries = 3): Promise<void> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await clientInstance.connect()
      console.log('Database connection established')
      return
    } catch (error) {
      console.error(`Database connection attempt ${attempt} failed:`, error)

      if (attempt === maxRetries) {
        throw new Error(`Failed to connect to database after ${maxRetries} attempts`)
      }

      // Exponential backoff: wait 2^attempt seconds
      const delay = Math.pow(2, attempt) * 1000
      console.log(`Retrying in ${delay}ms...`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
}

// Get connection pool (recommended for production)
export async function getConnectionPool() {
  if (typeof window !== 'undefined') {
    throw new Error('Direct database connection is server-side only')
  }

  if (!connectionPool) {
    const { Pool } = await initPgClient()
    const config = getConnectionConfig()

    // Enhanced pool configuration
    const poolConfig = {
      ...config,
      max: 8, // Reduced max connections
      min: 2, // Minimum connections
      acquireTimeoutMillis: 60000, // Time to wait for connection
      createTimeoutMillis: 30000, // Time to wait for new connection creation
      destroyTimeoutMillis: 5000, // Time to wait for connection destruction
      reapIntervalMillis: 1000, // How often to check for idle connections
      createRetryIntervalMillis: 200, // Retry interval for failed connections
    }

    connectionPool = new Pool(poolConfig)

    // Enhanced error handling
    connectionPool.on('error', (err: Error) => {
      console.error('❌ Unexpected error on idle client:', err)
      // Don't reset pool here, let it handle recovery
    })

    connectionPool.on('connect', (client: any) => {
      console.log('✅ New client connected to pool')
    })

    connectionPool.on('remove', (client: any) => {
      console.log('🔌 Client removed from pool')
    })

    // Test the connection with retry logic
    let retries = 3
    while (retries > 0) {
      try {
        const testClient = await connectionPool.connect()
        await testClient.query('SELECT NOW()')
        testClient.release()
        console.log('✅ Database pool initialized successfully')
        break
      } catch (error) {
        retries--
        console.error(`❌ Failed to initialize database pool (${3 - retries}/3):`, error)
        if (retries === 0) {
          throw error
        }
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
  }

  return connectionPool
}

// Get single client from pool (recommended approach)
export async function getDirectClient() {
  if (typeof window !== 'undefined') {
    throw new Error('Direct database connection is server-side only')
  }

  // Use pool instead of single client to prevent reuse issues
  const pool = await getConnectionPool()
  return await pool.connect()
}

export async function releaseDirectClient(client: any) {
  if (client && typeof client.release === 'function') {
    try {
      client.release()
      console.log('✅ Direct database client released to pool')
    } catch (error) {
      console.error('❌ Error releasing direct client:', error)
    }
  }
}

export async function closeConnectionPool() {
  if (connectionPool) {
    try {
      await connectionPool.end()
      console.log('Database connection pool closed')
    } catch (error) {
      console.error('Error closing connection pool:', error)
    } finally {
      connectionPool = null
    }
  }
}

// Health check function
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    const pool = await getConnectionPool()
    const client = await pool.connect()

    const result = await client.query('SELECT 1 as health_check')
    client.release()

    return result.rows[0]?.health_check === 1
  } catch (error) {
    console.error('Database health check failed:', error)
    return false
  }
}

// Direct database queries for critical operations
export async function getBusinessesDirect() {
  let client: any = null
  try {
    client = await getDirectClient()
    const result = await client.query(`
      SELECT
        id,
        name,
        category,
        logo_url,
        website,
        premium_discount,
        is_active,
        display_order,
        created_at,
        updated_at,
        contact_info,
        user_id,
        business_address,
        contact_name,
        contact_email,
        contact_phone,
        latitude,
        longitude,
        business_referral,
        referring_business_id,
        business_spotlight,
        loyalty_reward_frequency
      FROM businesses
      WHERE is_active = true
      -- No logo_url filtering - businesses display regardless of logo availability
      ORDER BY display_order ASC, name ASC
    `)
    return { data: result.rows, error: null }
  } catch (error) {
    console.error('❌ Direct business query error:', error)
    return { data: null, error }
  } finally {
    if (client) {
      await releaseDirectClient(client)
    }
  }
}

export async function getProfileDirect(userId: string) {
  let client: any = null
  try {
    client = await getDirectClient()
    const result = await client.query(`
      SELECT * FROM profiles WHERE id = $1
    `, [userId])
    return { data: result.rows[0] || null, error: null }
  } catch (error) {
    console.error('❌ Direct profile query error:', error)
    return { data: null, error }
  } finally {
    if (client) {
      await releaseDirectClient(client)
    }
  }
}

export async function getBusinessCountDirect() {
  try {
    const client = await getDirectClient()
    const result = await client.query(`
      SELECT COUNT(*) as count FROM businesses WHERE is_active = true
    `)
    return { data: parseInt(result.rows[0].count), error: null }
  } catch (error) {
    console.error('Direct business count error:', error)
    return { data: 0, error }
  }
}

// Test if direct connection is working
export async function testDirectConnection() {
  try {
    const client = await getDirectClient()
    await client.query('SELECT 1')
    return true
  } catch (error) {
    console.error('Direct connection test failed:', error)
    return false
  }
}

// Direct auth operations to bypass Supabase auth service 503 errors
export async function getUserByEmailDirect(email: string) {
  try {
    const client = await getDirectClient()
    const result = await client.query(`
      SELECT
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        created_at,
        updated_at,
        last_sign_in_at,
        raw_app_meta_data,
        raw_user_meta_data,
        is_super_admin,
        role
      FROM auth.users
      WHERE email = $1
    `, [email])
    return { data: result.rows[0] || null, error: null }
  } catch (error) {
    console.error('Direct user query error:', error)
    return { data: null, error }
  }
}

export async function createUserDirect(email: string, hashedPassword: string, userData: any = {}) {
  try {
    const client = await getDirectClient()
    const userId = crypto.randomUUID()

    const result = await client.query(`
      INSERT INTO auth.users (
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        created_at,
        updated_at,
        raw_app_meta_data,
        raw_user_meta_data,
        aud,
        role
      ) VALUES (
        $1, $2, $3, NOW(), NOW(), NOW(),
        '{"provider": "email", "providers": ["email"]}',
        $4,
        'authenticated',
        'authenticated'
      )
      RETURNING id, email, created_at
    `, [userId, email, hashedPassword, JSON.stringify(userData)])

    return { data: result.rows[0], error: null }
  } catch (error) {
    console.error('Direct user creation error:', error)
    return { data: null, error }
  }
}

export async function verifyPasswordDirect(email: string, password: string) {
  try {
    const { data: user, error } = await getUserByEmailDirect(email)
    if (error || !user) {
      return { valid: false, user: null, error: error || new Error('User not found') }
    }

    // Import bcrypt for password verification
    const bcrypt = await import('bcrypt')
    const isValid = await bcrypt.compare(password, user.encrypted_password)

    if (isValid) {
      // Update last sign in
      const client = await getDirectClient()
      await client.query(`
        UPDATE auth.users
        SET last_sign_in_at = NOW(), updated_at = NOW()
        WHERE id = $1
      `, [user.id])
    }

    return { valid: isValid, user: isValid ? user : null, error: null }
  } catch (error) {
    console.error('Direct password verification error:', error)
    return { valid: false, user: null, error }
  }
}

export async function updateUserPasswordDirect(userId: string, hashedPassword: string) {
  try {
    const client = await getDirectClient()
    const result = await client.query(`
      UPDATE auth.users
      SET encrypted_password = $1, updated_at = NOW()
      WHERE id = $2
      RETURNING id, email, updated_at
    `, [hashedPassword, userId])

    return { data: result.rows[0] || null, error: null }
  } catch (error) {
    console.error('Direct password update error:', error)
    return { data: null, error }
  }
}

export async function confirmUserEmailDirect(userId: string) {
  try {
    const client = await getDirectClient()
    const result = await client.query(`
      UPDATE auth.users
      SET email_confirmed_at = NOW(), updated_at = NOW()
      WHERE id = $1
      RETURNING id, email, email_confirmed_at
    `, [userId])

    return { data: result.rows[0] || null, error: null }
  } catch (error) {
    console.error('Direct email confirmation error:', error)
    return { data: null, error }
  }
}
