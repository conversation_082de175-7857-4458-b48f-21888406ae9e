{"buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "framework": "nextjs", "functions": {"app/api/businesses-static/route.ts": {"maxDuration": 30}}, "headers": [{"source": "/static-data/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=3600"}, {"key": "Content-Type", "value": "application/json"}]}, {"source": "/api/businesses-static", "headers": [{"key": "Cache-Control", "value": "public, max-age=300, s-maxage=300"}]}], "rewrites": [{"source": "/api/businesses", "destination": "/api/businesses-static"}]}