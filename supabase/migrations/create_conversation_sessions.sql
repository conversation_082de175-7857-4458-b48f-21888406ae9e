-- Create conversation_sessions table for enhanced chat bot
CREATE TABLE IF NOT EXISTS conversation_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  session_id TEXT UNIQUE NOT NULL,
  messages J<PERSON><PERSON><PERSON> DEFAULT '[]'::jsonb,
  context JSONB DEFAULT '{}'::jsonb,
  last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for efficient lookups
CREATE INDEX IF NOT EXISTS idx_conversation_sessions_session_id ON conversation_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_sessions_user_id ON conversation_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_sessions_last_activity ON conversation_sessions(last_activity);

-- <PERSON><PERSON> updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_conversation_sessions_updated_at
  BEFORE UPDATE ON conversation_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE conversation_sessions ENABLE ROW LEVEL SECURITY;

-- Policy for authenticated users to access their own sessions
CREATE POLICY "Users can access their own conversation sessions"
  ON conversation_sessions FOR ALL
  USING (auth.uid() = user_id);

-- Policy for service role to access all sessions (for bot management)
CREATE POLICY "Service role can access all conversation sessions"
  ON conversation_sessions FOR ALL
  USING (auth.role() = 'service_role');

-- Policy for anonymous users to access sessions without user_id
CREATE POLICY "Anonymous users can access their sessions"
  ON conversation_sessions FOR ALL
  USING (user_id IS NULL);