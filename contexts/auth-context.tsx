"use client"

import { create<PERSON>ontext, use<PERSON>ontex<PERSON>, useEffect, useState, type ReactNode } from "react"
import {
  onAuthStateChange,
  signInWithEmail,
  signUpWithEmail,
  signOut as supabaseSignOut,
  updateSession as supabaseUpdateSession,
  getSupabaseClient,
} from "@/lib/supabase"
import type { User, Session } from "@supabase/supabase-js"
import { profileCache } from "@/lib/profile-cache"

interface UserProfile {
  id: string
  first_name?: string
  last_name?: string
  user_email?: string
  phone?: string
  is_card_holder?: boolean
  is_business_applicant?: boolean
  card_tier?: string
  xrp_wallet_address?: string
  membership_start_date?: string
  membership_end_date?: string
  referring_business_id?: string
  created_at?: string
  updated_at?: string
}

interface AuthContextType {
  user: User | null
  session: Session | null
  profile: UserProfile | null
  isLoading: boolean
  isAdmin: boolean
  isBusinessOwner: boolean
  roles: string[]
  portalRoles: string[]
  referralCount: number
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  signUp: (email: string, password: string, metadata?: object) => Promise<{ data: any; error: any }>
  signOut: () => Promise<void>
  refreshSession: () => Promise<void>
  getSession: () => Promise<{ data: any; error: any }>
  extendSession: () => Promise<boolean>
}

// Create a default context value to prevent the "must be used within a provider" error
const defaultContextValue: AuthContextType = {
  user: null,
  session: null,
  profile: null,
  isLoading: false,
  isAdmin: false,
  isBusinessOwner: false,
  roles: [],
  portalRoles: [],
  referralCount: 0,
  signIn: async () => ({ data: null, error: new Error("Auth context not initialized") }),
  signUp: async () => ({ data: null, error: new Error("Auth context not initialized") }),
  signOut: async () => {},
  refreshSession: async () => {},
  getSession: async () => ({ data: null, error: new Error("Auth context not initialized") }),
  extendSession: async () => false,
}

// Update the context creation line to use the default value:
const AuthContext = createContext<AuthContextType>(defaultContextValue)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(false) // Start with false to avoid unnecessary loading
  const [roles, setRoles] = useState<string[]>([])
  const [portalRoles, setPortalRoles] = useState<string[]>([])
  const [isAdmin, setIsAdmin] = useState(false)
  const [isBusinessOwnerState, setIsBusinessOwner] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const [referralCount, setReferralCount] = useState(0)
  const [isLoadingProfile, setIsLoadingProfile] = useState(false)
  // Initialize auth state
  useEffect(() => {
    // Initialize Supabase auth state
    const {
      data: { subscription },
    } = onAuthStateChange(async (_event, session) => {
      try {
        setSession(session)
        setUser(session?.user ?? null)

        if (session?.user) {
          await loadUserData(session.user)
        } else {
          setProfile(null)
          setRoles([])
          setPortalRoles([])
          setIsAdmin(false)
          setIsBusinessOwner(false)
          setReferralCount(0)
          setIsLoading(false)
        }
      } catch (error) {
        console.error("Error in auth state change:", error)
        setIsLoading(false)
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])


  // Optimized business ownership check using new database functions
  async function checkBusinessOwnershipOptimized(userId: string, retryCount = 0) {
    try {
      console.log("🔍 Checking business ownership (optimized) for user:", userId)

      // Check cache first using profile cache service
      const cachedStatus = profileCache.getCachedBusinessOwner(userId)
      if (cachedStatus !== null) {
        setIsBusinessOwner(cachedStatus)
        console.log("✅ Using cached business owner status:", cachedStatus)
        return
      }

      // Add a small delay on first attempt to ensure session is established
      if (retryCount === 0) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      // Use optimized API endpoint with database functions (user comes from auth context)
      const response = await fetch('/api/business-access-optimized', {
        credentials: 'include' // Include authentication cookies
      })
      const result = await response.json()

      if (response.ok && result.success) {
        const hasAccess = result.data.has_business_access === true
        setIsBusinessOwner(hasAccess)

        // Cache the result using profile cache service
        profileCache.cacheBusinessOwner(userId, hasAccess)

        console.log(`✅ Business ownership check completed in ${result.performance?.queryTime || 'unknown'}ms:`, hasAccess)
      } else if (response.status === 401 && retryCount < 2) {
        // If authentication failed, retry after a longer delay
        console.log(`⏳ Auth not ready, retrying business ownership check (attempt ${retryCount + 1})...`)
        await new Promise(resolve => setTimeout(resolve, 1000))
        return checkBusinessOwnershipOptimized(userId, retryCount + 1)
      } else {
        console.error("❌ Failed to check business ownership:", result.error)
        setIsBusinessOwner(false)
      }
    } catch (error) {
      if (retryCount < 2) {
        console.log(`⏳ Network error, retrying business ownership check (attempt ${retryCount + 1})...`)
        await new Promise(resolve => setTimeout(resolve, 1000))
        return checkBusinessOwnershipOptimized(userId, retryCount + 1)
      }
      console.error("❌ Error checking business ownership:", error)
      setIsBusinessOwner(false)
    }
  }

  async function loadUserData(user: User) {
    // Prevent multiple simultaneous loads
    if (isLoadingProfile) {
      console.log("Profile load already in progress, skipping")
      return
    }

    setIsLoading(true)
    setIsLoadingProfile(true)

    try {
      // Check cache first using profile cache service
      const cachedProfile = profileCache.getCachedProfile(user.id)
      
      if (cachedProfile) {
        setProfile(cachedProfile)
        setIsAdmin(false) // Default values
        setReferralCount(0)

        // Use optimized business ownership check
        await checkBusinessOwnershipOptimized(user.id)

        console.log("✅ Using cached profile with optimized business check")
        return
      }

      // Simplified profile loading - since users are automatically in profiles after signup
      console.log("Loading profile data...")

      // Try simple profiles API first
      try {
        console.log("🔄 Auth context trying simple profiles API...")
        const response = await fetch(`/api/profiles-simple?userId=${user.id}`)
        const result = await response.json()

        if (response.ok && result.success && result.profile) {
          const simpleProfile = result.profile
          console.log("✅ Auth context simple profiles API success")

          // Create a more complete profile object for auth context
          const authProfile: UserProfile = {
            id: simpleProfile.id,
            user_email: simpleProfile.user_email,
            first_name: simpleProfile.first_name || '',
            last_name: simpleProfile.last_name || '',
            phone: simpleProfile.phone || '',
            is_card_holder: simpleProfile.is_card_holder || false,
            is_business_applicant: simpleProfile.is_business_applicant || false,
            card_tier: simpleProfile.card_tier || null,
            xrp_wallet_address: simpleProfile.xrp_wallet_address || '',
            created_at: simpleProfile.created_at || new Date().toISOString()
          }

          setProfile(authProfile)
          profileCache.setCachedProfile(user.id, authProfile)
          await checkBusinessOwnershipOptimized(user.id)
          console.log("✅ Profile loaded successfully via simple API")
          return
        } else {
          console.error("❌ Simple profiles API failed:", result.error)
        }
      } catch (error) {
        console.error("❌ Simple profiles API error:", error)
      }

      // Fallback to optimized API
      try {
        console.log("🔄 Auth context trying optimized profiles API...")
        const response = await fetch(`/api/auth-profile-optimized?user_id=${user.id}`)
        const result = await response.json()

        if (response.ok && result.success) {
          const optimizedProfile = result.data
          console.log("✅ Auth context optimized profiles API success")

          // Create a more complete profile object for auth context
          const authProfile: UserProfile = {
            id: optimizedProfile.id,
            user_email: optimizedProfile.user_email,
            first_name: optimizedProfile.first_name || '',
            last_name: optimizedProfile.last_name || '',
            phone: optimizedProfile.phone || '',
            is_card_holder: optimizedProfile.is_card_holder || false,
            is_business_applicant: optimizedProfile.is_business_applicant || false,
            card_tier: optimizedProfile.card_tier || null,
            xrp_wallet_address: optimizedProfile.xrp_wallet_address || '',
            created_at: new Date().toISOString()
          }

          setProfile(authProfile)

          // Set business ownership from optimized response
          setIsBusinessOwner(optimizedProfile.has_business_access || false)

          console.log(`✅ Profile loaded successfully in ${result.performance?.queryTime || 'unknown'}ms`)

          // Cache the profile using profile cache service
          profileCache.cacheProfile(user.id, authProfile)

          // Set minimal defaults
          setIsAdmin(false)
          setReferralCount(0)

          // Store minimal user session info using cache service
          profileCache.cacheUserSession(user.id, user.email || '')
        } else {
          // Since users are automatically in profiles, treat as temporary error
          console.warn("Profile loading failed, but user should exist. Using minimal profile.")
          
          // Create minimal profile object from user data
          const minimalProfile: UserProfile = {
            id: user.id,
            user_email: user.email || '',
            first_name: '',
            last_name: '',
            created_at: new Date().toISOString()
          }
          
          setProfile(minimalProfile)
          setIsAdmin(false)
          setReferralCount(0)
          setIsBusinessOwner(false)

          // Store minimal user session info using cache service
          profileCache.cacheUserSession(user.id, user.email || '')
          
          console.log("Using minimal profile, components will handle their own data loading")
        }
      } catch (error) {
        console.error("Error loading profile:", error)
        
        // Since users are automatically in profiles, treat as temporary error
        console.log("Profile loading failed, using minimal profile from user data")
        
        // Create minimal profile object from user data
        const minimalProfile: UserProfile = {
          id: user.id,
          user_email: user.email || '',
          first_name: '',
          last_name: '',
          created_at: new Date().toISOString()
        }
        
        setProfile(minimalProfile)
        setIsAdmin(false)
        setReferralCount(0)
        setIsBusinessOwner(false)

        // Store minimal user session info using cache service
        profileCache.cacheUserSession(user.id, user.email || '')
        
        console.log("Using minimal profile, components will handle their own data loading")
      }

    } catch (error) {
      console.error("Error loading user data:", error)
      
      // Since users are automatically in profiles, create minimal profile from user data
      const minimalProfile: UserProfile = {
        id: user.id,
        user_email: user.email || '',
        first_name: '',
        last_name: '',
        created_at: new Date().toISOString()
      }
      
      setProfile(minimalProfile)
      setIsAdmin(false)
      setReferralCount(0)
      setIsBusinessOwner(false)

      // Store minimal user session info using cache service
      profileCache.cacheUserSession(user.id, user.email || '')
      
      console.log("Using minimal profile from user data, components will handle their own data loading")
    } finally {
      setIsLoading(false)
      setIsLoadingProfile(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    // Initialize auth state when user tries to sign in
    setIsInitialized(true)
    setIsLoading(true)

    try {
      const { data, error } = await signInWithEmail(email, password)
      
      // If sign in was successful and we have pending wallet data, process it
      if (data?.user && !error) {
        await processPendingWalletConnection(data.user.id)
      }
      
      setIsLoading(false)
      return { data, error }
    } catch (error) {
      console.error("Error signing in:", error)
      setIsLoading(false)
      return { data: null, error }
    }
  }

  // Helper function to process pending wallet connection after authentication
  const processPendingWalletConnection = async (userId: string) => {
    try {
      const pendingWalletAddress = localStorage.getItem('pending_wallet_address')
      const pendingWalletTimestamp = localStorage.getItem('pending_wallet_timestamp')
      
      if (pendingWalletAddress && pendingWalletTimestamp) {
        // Check if the pending data is not too old (max 10 minutes)
        const timestampAge = Date.now() - new Date(pendingWalletTimestamp).getTime()
        const maxAge = 10 * 60 * 1000 // 10 minutes in milliseconds
        
        if (timestampAge > maxAge) {
          console.log('🕐 Pending wallet data expired, cleaning up')
          localStorage.removeItem('pending_wallet_address')
          localStorage.removeItem('pending_wallet_timestamp')
          return
        }

        console.log('🔗 Processing pending wallet connection for authenticated user')
        
        const supabase = getSupabaseClient()
        if (!supabase) {
          console.warn('⚠️ Supabase client not available for pending wallet processing')
          return
        }

        // Update user profile with the pending wallet address
        const { error } = await supabase
          .from('profiles')
          .update({
            xrp_wallet_address: pendingWalletAddress,
            wallet_connected_at: new Date().toISOString(),
            wallet_last_used: new Date().toISOString()
          })
          .eq('id', userId)

        if (error) {
          console.error('❌ Error updating profile with pending wallet:', error)
        } else {
          console.log('✅ Successfully processed pending wallet connection')
          
          // Update cache
          profileCache.updateWalletInCache(userId, pendingWalletAddress)
          
          // Store wallet connection state
          localStorage.setItem('fuse_wallet_address', pendingWalletAddress)
          localStorage.setItem('fuse_wallet_connected', 'true')
        }

        // Clean up pending data
        localStorage.removeItem('pending_wallet_address')
        localStorage.removeItem('pending_wallet_timestamp')
      }
    } catch (error) {
      console.error('❌ Error processing pending wallet connection:', error)
    }
  }

  const signUp = async (email: string, password: string, metadata: object = {}) => {
    // Initialize auth state when user tries to sign up
    setIsInitialized(true)
    setIsLoading(true)

    console.log('🔄 Auth context signUp called:', { email, metadata })

    try {
      // Use the enhanced signUpWithEmail function that accepts metadata
      const { data, error } = await signUpWithEmail(email, password, metadata)
      setIsLoading(false)

      if (error) {
        console.error('❌ SignUp error:', error)
      } else {
        console.log('✅ SignUp successful:', data)
      }

      return { data, error }
    } catch (error) {
      console.error("❌ Exception during sign up:", error)
      setIsLoading(false)
      return { data: null, error }
    }
  }

  const signOut = async () => {
    setIsLoading(true)
    try {
      // Get current user ID for cache cleanup
      const currentUserId = user?.id

      // Sign out from both JWT and Supabase
      // Temporarily disable JWT signout until auth-client is created
      // await signOutWithJWT()
      await supabaseSignOut()

      // Clear local state
      setUser(null)
      setSession(null)
      setProfile(null)
      setRoles([])
      setPortalRoles([])
      setIsAdmin(false)
      setIsBusinessOwner(false)
      setReferralCount(0)

      // Clear all cached data using profile cache service
      if (currentUserId) {
        profileCache.clearUserCache(currentUserId)
      }

      // Clear any remaining localStorage items
      localStorage.clear()

      console.log("User signed out and all cache cleared")
      setIsLoading(false)
    } catch (error) {
      console.error("Error signing out:", error)
      setIsLoading(false)
      throw error
    }
  }

  const refreshSession = async () => {
    // Simplified refresh - just use cached data if available
    if (user && !isLoading) {
      console.log("Quick refresh using existing user data")
      return
    }

    try {
      setIsLoading(true)

      // Try to get current session first
      const supabase = getSupabaseClient()
      if (!supabase) throw new Error("Supabase client not initialized")

      const { data: sessionData } = await supabase.auth.getSession()

      if (sessionData?.session?.user) {
        console.log("Found existing session, updating context")
        setSession(sessionData.session)
        setUser(sessionData.session.user)

        // Load user data if profile is missing
        if (!profile) {
          await loadUserData(sessionData.session.user)
        }
      } else {
        // Try to refresh the session if no current session
        console.log("No current session, attempting refresh")
        const { data: refreshData, error } = await supabase.auth.refreshSession()

        if (refreshData?.session?.user && !error) {
          console.log("Session refreshed successfully")
          setSession(refreshData.session)
          setUser(refreshData.session.user)

          if (!profile) {
            await loadUserData(refreshData.session.user)
          }
        } else {
          console.log("Session refresh failed:", error)
          // Clear auth state if refresh fails
          setSession(null)
          setUser(null)
          setProfile(null)
        }
      }
    } catch (error) {
      console.error("Error refreshing session:", error)
      // Clear auth state on error
      setSession(null)
      setUser(null)
      setProfile(null)
    } finally {
      setIsLoading(false)
    }
  }

  const getSession = async () => {
    try {
      const { data, error } = await supabase!.auth.getSession()
      if (error) {
        console.error("Error updating session:", error)
        return { data: null, error }
      }
      
      // If session was successfully updated, refresh user data
      if (data?.session?.user) {
        setSession(data.session)
        setUser(data.session.user)
        await loadUserData(data.session.user)
      }
      
      return { data, error: null }
    } catch (error) {
      console.error("Exception getting session:", error)
      return { data: null, error }
    }
  }

  // Add a more robust session extension function
  const extendSession = async () => {
    try {
      console.log("Attempting to extend session...")
      
      // First try to refresh the session
      const { data, error } = await supabase!.auth.refreshSession()
      
      if (error) {
        console.error("Error extending session:", error)
        
        // If refresh fails, try to get the current session
        const { data: currentSession } = await supabase!.auth.getSession()
        
        if (currentSession?.session) {
          console.log("Retrieved current session")
          setSession(currentSession.session)
          return true
        }
        
        return false
      }
      
      if (data?.session) {
        console.log("Session extended successfully")
        setSession(data.session)
        setUser(data.user)
        return true
      }
      
      return false
    } catch (error) {
      console.error("Exception extending session:", error)
      return false
    }
  }

  const value = {
    user,
    session,
    profile,
    isLoading,
    isAdmin,
    isBusinessOwner: isBusinessOwnerState,
    roles,
    portalRoles,
    referralCount,
    signIn,
    signUp,
    signOut,
    refreshSession,
    getSession,
    extendSession, // Add this to the context
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
