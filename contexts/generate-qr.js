const QRCode = require("qrcode");
const fs = require("fs");
const path = require("path");

const businessId = "815ee22d-ca1d-4a10-a958-929c025ae95a"; // Just the UUID

const qrData = businessId; // <-- NO URL, just the UUID string

const outputDir = path.join(__dirname, "..", "public", "qr");

// Make sure the output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

const outputPath = path.join(outputDir, `${businessId}.png`);

QRCode.toFile(
  outputPath,
  qrData,
  {
    color: {
      dark: "#000000",
      light: "#ffffff",
    },
  },
  function (err) {
    if (err) throw err;
    console.log(`✅ QR Code saved to ${outputPath}`);
  }
);