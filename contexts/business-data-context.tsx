"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { getSupabaseClient } from "@/lib/supabase";
import { useAuth } from "@/contexts/auth-context";
import { usePurchasesSubscription } from "@/hooks/use-realtime-subscription";

interface BusinessDataContextType {
  purchases: any[];
  referrals: any[];
  loading: boolean;
}

const BusinessDataContext = createContext<BusinessDataContextType | undefined>(undefined);

export function BusinessDataProvider({ children }: { children: React.ReactNode }) {
  const { user, isBusinessOwner } = useAuth();
  const [purchases, setPurchases] = useState<any[]>([]);
  const [referrals, setReferrals] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchBusinessData() {
      const supabase = getSupabaseClient();
      if (!user || !isBusinessOwner || !supabase) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // First, get the user's business(es) to get the actual business_id
        const { data: businessesData, error: businessesError } = await supabase
          .from("businesses")
          .select("id")
          .eq("user_id", user.id)
          .eq("is_active", true);

        if (businessesError) {
          console.error("Error fetching user businesses:", businessesError);
          setLoading(false);
          return;
        }

        if (!businessesData || businessesData.length === 0) {
          console.log("User has no active businesses");
          setPurchases([]);
          setReferrals([]);
          setLoading(false);
          return;
        }

        // Use the actual business IDs from the businesses table
        const businessIds = businessesData.map(b => b.id);

        // Fetch purchases using proper business_id
        const { data: purchasesData, error: purchasesError } = await supabase
          .from("purchases")
          .select("*")
          .in("business_id", businessIds);

        if (purchasesError) {
          console.error("Error fetching purchases:", purchasesError);
        } else {
          setPurchases(purchasesData || []);
        }

        // Fetch referrals using proper business_id
        const { data: referralsData, error: referralsError } = await supabase
          .from("referrals")
          .select("*")
          .in("business_id", businessIds);

        if (referralsError) {
          console.error("Error fetching referrals:", referralsError);
        } else {
          setReferrals(referralsData || []);
        }
      } catch (error) {
        console.error("Error fetching business data:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchBusinessData();
  }, [user, isBusinessOwner]);

  // Subscribe to real-time purchases updates
  // Note: Real-time subscriptions should be managed at the component level
  // for proper business_id filtering based on actual business ownership
  usePurchasesSubscription(
    (payload) => {
      console.log('Purchase real-time update:', payload);

      // Only process updates for purchases that belong to this user's businesses
      // This requires the component to know the actual business IDs
      if (payload.eventType === 'INSERT') {
        setPurchases(prev => [payload.new, ...prev]);
      } else if (payload.eventType === 'UPDATE') {
        setPurchases(prev =>
          prev.map(purchase =>
            purchase.id === payload.new.id ? payload.new : purchase
          )
        );
      } else if (payload.eventType === 'DELETE') {
        setPurchases(prev =>
          prev.filter(purchase => purchase.id !== payload.old.id)
        );
      }
    },
    undefined, // Remove user.id filter - let the RLS policies handle access control
    isBusinessOwner && !!user
  );

  return (
    <BusinessDataContext.Provider value={{ purchases, referrals, loading }}>
      {children}
    </BusinessDataContext.Provider>
  );
}

export function useBusinessData() {
  const context = useContext(BusinessDataContext);
  if (!context) {
    throw new Error("useBusinessData must be used within a BusinessDataProvider");
  }
  return context;
}

