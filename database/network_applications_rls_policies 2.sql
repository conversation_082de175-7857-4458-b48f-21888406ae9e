-- Comprehensive RLS Policies for Network Applications Table
-- Allows normal users to submit applications while giving admins full access

-- First, ensure R<PERSON> is enabled on network_applications table
ALTER TABLE network_applications ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to start fresh
DROP POLICY IF EXISTS "Allow authenticated users to insert network applications" ON network_applications;
DROP POLICY IF EXISTS "Allow authenticated users to view their own applications" ON network_applications;
DROP POLICY IF EXISTS "Allow admins to view all applications" ON network_applications;
DROP POLICY IF EXISTS "Service role can access all applications" ON network_applications;
DROP POLICY IF EXISTS "Allow Realtime" ON network_applications;

-- CREATE NEW COMPREHENSIVE RLS POLICIES

-- 1. SELECT Policies

-- Allow authenticated users to view their own applications
CREATE POLICY "user_select_own_applications" ON network_applications
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

-- Allow admins (role 1) to view ALL applications
CREATE POLICY "admin_select_all_applications" ON network_applications
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- 2. INSERT Policies

-- Allow authenticated users to insert their own applications
CREATE POLICY "user_insert_own_applications" ON network_applications
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

-- Allow admins to insert applications for any user
CREATE POLICY "admin_insert_any_applications" ON network_applications
    FOR INSERT
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- 3. UPDATE Policies

-- Allow users to update their own PENDING applications only
CREATE POLICY "user_update_own_pending_applications" ON network_applications
    FOR UPDATE
    TO authenticated
    USING (
        user_id = auth.uid() 
        AND status = 'pending'
    )
    WITH CHECK (
        user_id = auth.uid() 
        AND status = 'pending'
    );

-- Allow admins to update any application (for approval/rejection)
CREATE POLICY "admin_update_all_applications" ON network_applications
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- 4. DELETE Policies

-- Users cannot delete their applications (preserve audit trail)
-- Only admins can delete applications if absolutely necessary
CREATE POLICY "admin_delete_applications" ON network_applications
    FOR DELETE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- 5. Service Role Access (for backend operations)
CREATE POLICY "service_role_full_access" ON network_applications
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- 6. Realtime Access (for real-time subscriptions)
CREATE POLICY "realtime_select_access" ON network_applications
    FOR SELECT
    TO authenticated
    USING (
        -- Users can subscribe to their own applications
        user_id = auth.uid()
        OR
        -- Admins can subscribe to all applications
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- Create index for performance on user_id lookups
CREATE INDEX IF NOT EXISTS idx_network_applications_user_id ON network_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_network_applications_status ON network_applications(status);
CREATE INDEX IF NOT EXISTS idx_network_applications_created_at ON network_applications(created_at);