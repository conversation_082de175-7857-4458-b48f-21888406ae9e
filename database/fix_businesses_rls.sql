-- Fix businesses table RLS policies to allow users to access their own businesses
-- without requiring specific roles in the user_roles table

-- This fixes the issue where users can't access their businesses because 
-- they don't have the business_owner role (role_id = 2) assigned

-- Allow authenticated users to view their own businesses (without role requirement)
CREATE POLICY IF NOT EXISTS "authenticated_user_select_own_businesses" ON businesses
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

-- Allow authenticated users to insert their own businesses (without role requirement)
CREATE POLICY IF NOT EXISTS "authenticated_user_insert_own_businesses" ON businesses
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

-- Allow authenticated users to update their own businesses (without role requirement)
CREATE POLICY IF NOT EXISTS "authenticated_user_update_own_businesses" ON businesses
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Allow authenticated users to delete their own businesses (without role requirement)
CREATE POLICY IF NOT EXISTS "authenticated_user_delete_own_businesses" ON businesses
    FOR DELETE
    TO authenticated
    USING (user_id = auth.uid());

-- Verify the policies were created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'businesses' 
AND policyname LIKE 'authenticated_user_%'
ORDER BY policyname;