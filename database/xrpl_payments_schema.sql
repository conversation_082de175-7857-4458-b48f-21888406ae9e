-- XRPL Payments table for tracking cryptocurrency payments
CREATE TABLE IF NOT EXISTS xrpl_payments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    transaction_hash VARCHAR(64) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    user_email VARCHAR(255),
    card_type VARCHAR(100) NOT NULL,
    amount VARCHAR(50) NOT NULL,
    currency VARCHAR(10) NOT NULL, -- 'XRP' or 'FUSE'
    sender_address VARCHAR(35) NOT NULL,
    destination_address VARCHAR(35) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'completed', 'failed'
    verified_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- VIP Card Upgrades table for tracking upgrade transactions
CREATE TABLE IF NOT EXISTS vip_card_upgrades (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    from_card_type VARCHAR(100),
    to_card_type VARCHAR(100) NOT NULL,
    transaction_hash VARCHAR(64) REFERENCES xrpl_payments(transaction_hash),
    upgrade_date TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add XRPL-related columns to users table if they don't exist
DO $$ 
BEGIN
    -- Add VIP card payment method tracking
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'vip_card_payment_method') THEN
        ALTER TABLE users ADD COLUMN vip_card_payment_method VARCHAR(20) DEFAULT 'stripe'; -- 'stripe' or 'xrpl'
    END IF;
    
    -- Add XRPL wallet address for users who pay with crypto
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'xrpl_wallet_address') THEN
        ALTER TABLE users ADD COLUMN xrpl_wallet_address VARCHAR(35);
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_xrpl_payments_transaction_hash ON xrpl_payments(transaction_hash);
CREATE INDEX IF NOT EXISTS idx_xrpl_payments_user_id ON xrpl_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_xrpl_payments_status ON xrpl_payments(status);
CREATE INDEX IF NOT EXISTS idx_xrpl_payments_created_at ON xrpl_payments(created_at);
CREATE INDEX IF NOT EXISTS idx_vip_card_upgrades_user_id ON vip_card_upgrades(user_id);
CREATE INDEX IF NOT EXISTS idx_vip_card_upgrades_upgrade_date ON vip_card_upgrades(upgrade_date);

-- Enable Row Level Security (RLS)
ALTER TABLE xrpl_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE vip_card_upgrades ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for xrpl_payments
CREATE POLICY "Users can view their own XRPL payments" ON xrpl_payments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage XRPL payments" ON xrpl_payments
    FOR ALL USING (auth.role() = 'service_role');

-- Create RLS policies for vip_card_upgrades  
CREATE POLICY "Users can view their own upgrades" ON vip_card_upgrades
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage upgrades" ON vip_card_upgrades
    FOR ALL USING (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON xrpl_payments TO authenticated;
GRANT SELECT, INSERT, UPDATE ON vip_card_upgrades TO authenticated;
GRANT ALL ON xrpl_payments TO service_role;
GRANT ALL ON vip_card_upgrades TO service_role;