-- =============================================
-- RLS POLICIES FOR BUSINESS ACCESS REQUESTS
-- =============================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own business access requests" ON business_access_requests;
DROP POLICY IF EXISTS "Users can create business access requests" ON business_access_requests;
DROP POLICY IF EXISTS "Admins can view all business access requests" ON business_access_requests;
DROP POLICY IF EXISTS "Admins can update business access requests" ON business_access_requests;
DROP POLICY IF EXISTS "Business owners can view requests for their businesses" ON business_access_requests;
DROP POLICY IF EXISTS "Business owners can update requests for their businesses" ON business_access_requests;

-- Policy 1: Users can view their own business access requests
CREATE POLICY "Users can view their own business access requests"
ON business_access_requests FOR SELECT
USING (
  auth.uid() = user_id
);

-- Policy 2: Users can create business access requests
CREATE POLICY "Users can create business access requests"
ON business_access_requests FOR INSERT
WITH CHECK (
  auth.uid() = user_id
);

-- Policy 3: Admins can view all business access requests
CREATE POLICY "Admins can view all business access requests"
ON business_access_requests FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM portal_roles pr
    JOIN user_roles ur ON pr.id = ur.role_id
    WHERE ur.user_id = auth.uid()
    AND pr.role_name = 'admin'
    AND ur.is_active = true
  )
);

-- Policy 4: Admins can update business access requests (approve/reject)
CREATE POLICY "Admins can update business access requests"
ON business_access_requests FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM portal_roles pr
    JOIN user_roles ur ON pr.id = ur.role_id
    WHERE ur.user_id = auth.uid()
    AND pr.role_name = 'admin'
    AND ur.is_active = true
  )
);

-- Policy 5: Business owners can view requests for their businesses
CREATE POLICY "Business owners can view requests for their businesses"
ON business_access_requests FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM businesses b
    WHERE b.id = business_access_requests.business_id
    AND b.user_id = auth.uid()
  )
);

-- Policy 6: Business owners can update requests for their businesses (approve/reject)
CREATE POLICY "Business owners can update requests for their businesses"
ON business_access_requests FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM businesses b
    WHERE b.id = business_access_requests.business_id
    AND b.user_id = auth.uid()
  )
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_access_requests_user_id ON business_access_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_business_access_requests_business_id ON business_access_requests(business_id);
CREATE INDEX IF NOT EXISTS idx_business_access_requests_status ON business_access_requests(status);
CREATE INDEX IF NOT EXISTS idx_business_access_requests_created_at ON business_access_requests(created_at);

-- Add comments for documentation
COMMENT ON TABLE business_access_requests IS 'Stores requests from users to gain access to manage specific businesses';
COMMENT ON COLUMN business_access_requests.status IS 'Status of the request: pending, approved, rejected';
COMMENT ON COLUMN business_access_requests.message IS 'Optional message from user explaining their relationship to the business';
COMMENT ON COLUMN business_access_requests.reviewed_by IS 'User ID of admin or business owner who reviewed the request';
COMMENT ON COLUMN business_access_requests.admin_notes IS 'Internal notes from reviewer about the decision';