-- Comprehensive RLS Policies for Businesses Table
-- Role 1 (admin) gets full access to all businesses
-- Role 2 (business_owner) gets full access only to their own businesses

-- First, ensure RLS is enabled on businesses table
ALTER TABLE businesses ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to start fresh
DROP POLICY IF EXISTS "Allow authenticated users to insert businesses" ON businesses;
DROP POLICY IF EXISTS "Allow authenticated users to update their own businesses" ON businesses;
DROP POLICY IF EXISTS "Public can view active businesses" ON businesses;
DROP POLICY IF EXISTS "Service role can access all businesses" ON businesses;
DROP POLICY IF EXISTS "Allow Realtime" ON businesses;
DROP POLICY IF EXISTS "Allow anonymous users to view all businesses" ON businesses;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON businesses;
DROP POLICY IF EXISTS "public_business_read_access" ON businesses;

-- CREATE NEW COMPREHENSIVE RLS POLICIES

-- 1. SELECT Policies
-- Allow public/anonymous users to view active businesses (for browsing)
CREATE POLICY "public_select_active_businesses" ON businesses
    FOR SELECT
    TO public
    USING (is_active = true);

-- Allow authenticated users to view active businesses
CREATE POLICY "authenticated_select_active_businesses" ON businesses
    FOR SELECT
    TO authenticated
    USING (is_active = true);

-- Allow admins (role 1) to view ALL businesses
CREATE POLICY "admin_select_all_businesses" ON businesses
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- Allow business owners (role 2) to view their own businesses
CREATE POLICY "business_owner_select_own_businesses" ON businesses
    FOR SELECT
    TO authenticated
    USING (
        user_id = auth.uid() 
        AND EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 2 
            AND ur.is_active = true
        )
    );

-- Allow authenticated users to view their own businesses (without role requirement)
CREATE POLICY "authenticated_user_select_own_businesses" ON businesses
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

-- 2. INSERT Policies
-- Allow admins to insert any business
CREATE POLICY "admin_insert_businesses" ON businesses
    FOR INSERT
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- Allow business owners to insert businesses (they own)
CREATE POLICY "business_owner_insert_own_businesses" ON businesses
    FOR INSERT
    TO authenticated
    WITH CHECK (
        user_id = auth.uid() 
        AND EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 2 
            AND ur.is_active = true
        )
    );

-- Allow authenticated users to insert their own businesses (without role requirement)
CREATE POLICY "authenticated_user_insert_own_businesses" ON businesses
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

-- 3. UPDATE Policies
-- Allow admins to update any business
CREATE POLICY "admin_update_all_businesses" ON businesses
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- Allow business owners to update their own businesses
CREATE POLICY "business_owner_update_own_businesses" ON businesses
    FOR UPDATE
    TO authenticated
    USING (
        user_id = auth.uid() 
        AND EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 2 
            AND ur.is_active = true
        )
    )
    WITH CHECK (
        user_id = auth.uid() 
        AND EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 2 
            AND ur.is_active = true
        )
    );

-- Allow authenticated users to update their own businesses (without role requirement)
CREATE POLICY "authenticated_user_update_own_businesses" ON businesses
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- 4. DELETE Policies
-- Allow admins to delete any business
CREATE POLICY "admin_delete_all_businesses" ON businesses
    FOR DELETE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 1 
            AND ur.is_active = true
        )
    );

-- Allow business owners to delete their own businesses
CREATE POLICY "business_owner_delete_own_businesses" ON businesses
    FOR DELETE
    TO authenticated
    USING (
        user_id = auth.uid() 
        AND EXISTS (
            SELECT 1 FROM user_roles ur 
            WHERE ur.user_id = auth.uid() 
            AND ur.role_id = 2 
            AND ur.is_active = true
        )
    );

-- Allow authenticated users to delete their own businesses (without role requirement)
CREATE POLICY "authenticated_user_delete_own_businesses" ON businesses
    FOR DELETE
    TO authenticated
    USING (user_id = auth.uid());

-- 5. Service Role Access (for backend operations)
CREATE POLICY "service_role_full_access" ON businesses
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);

-- 6. Realtime Access (for real-time subscriptions)
CREATE POLICY "realtime_select_access" ON businesses
    FOR SELECT
    TO authenticated
    USING (true);
