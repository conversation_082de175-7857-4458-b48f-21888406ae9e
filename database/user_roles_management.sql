-- User Roles Management System
-- Ensures proper role assignment when businesses are created or ownership changes

-- Function to assign business_owner role to a user
CREATE OR REPLACE FUNCTION assign_business_owner_role(target_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    business_owner_role_id INTEGER := 2; -- business_owner role ID from portal_roles
    existing_role_count INTEGER;
BEGIN
    -- Check if user already has business_owner role
    SELECT COUNT(*) INTO existing_role_count
    FROM user_roles 
    WHERE user_id = target_user_id 
    AND role_id = business_owner_role_id 
    AND is_active = true;
    
    -- If user doesn't have the role, assign it
    IF existing_role_count = 0 THEN
        INSERT INTO user_roles (user_id, role_id, assigned_at, is_active)
        VALUES (target_user_id, business_owner_role_id, NOW(), true);
        
        RAISE NOTICE 'Assigned business_owner role to user %', target_user_id;
        RETURN true;
    ELSE
        RAISE NOTICE 'User % already has business_owner role', target_user_id;
        RETUR<PERSON> false;
    <PERSON>ND IF;
    
EXCE<PERSON><PERSON>ON
    WHEN OTHERS THEN
        RAISE WARNING 'Failed to assign business_owner role to user %: %', target_user_id, SQLERRM;
        <PERSON><PERSON><PERSON><PERSON> false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove business_owner role if user has no businesses
CREATE OR REPLACE FUNCTION check_and_remove_business_owner_role(target_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    business_owner_role_id INTEGER := 2; -- business_owner role ID from portal_roles
    business_count INTEGER;
    admin_role_count INTEGER;
BEGIN
    -- Count how many businesses this user owns
    SELECT COUNT(*) INTO business_count
    FROM businesses 
    WHERE user_id = target_user_id 
    AND is_active = true;
    
    -- Check if user has admin role (admins should keep business_owner role)
    SELECT COUNT(*) INTO admin_role_count
    FROM user_roles 
    WHERE user_id = target_user_id 
    AND role_id = 1 -- admin role
    AND is_active = true;
    
    -- If user has no businesses and is not an admin, remove business_owner role
    IF business_count = 0 AND admin_role_count = 0 THEN
        UPDATE user_roles 
        SET is_active = false, 
            revoked_at = NOW(),
            updated_at = NOW()
        WHERE user_id = target_user_id 
        AND role_id = business_owner_role_id 
        AND is_active = true;
        
        RAISE NOTICE 'Removed business_owner role from user % (no businesses owned)', target_user_id;
        RETURN true;
    ELSE
        RAISE NOTICE 'User % keeps business_owner role (businesses: %, admin: %)', target_user_id, business_count, admin_role_count;
        RETURN false;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Failed to check/remove business_owner role for user %: %', target_user_id, SQLERRM;
        RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function for business INSERT operations
CREATE OR REPLACE FUNCTION handle_business_role_assignment()
RETURNS TRIGGER AS $$
BEGIN
    -- When a business is created with a user_id, assign business_owner role
    IF TG_OP = 'INSERT' AND NEW.user_id IS NOT NULL THEN
        PERFORM assign_business_owner_role(NEW.user_id);
    END IF;
    
    -- When a business user_id is updated
    IF TG_OP = 'UPDATE' THEN
        -- If user_id changed from NULL to a value, assign role to new user
        IF OLD.user_id IS NULL AND NEW.user_id IS NOT NULL THEN
            PERFORM assign_business_owner_role(NEW.user_id);
        END IF;
        
        -- If user_id changed from one user to another
        IF OLD.user_id IS NOT NULL AND NEW.user_id IS NOT NULL AND OLD.user_id != NEW.user_id THEN
            -- Assign role to new user
            PERFORM assign_business_owner_role(NEW.user_id);
            -- Check if old user should lose the role
            PERFORM check_and_remove_business_owner_role(OLD.user_id);
        END IF;
        
        -- If user_id changed from a value to NULL, check if old user should lose role
        IF OLD.user_id IS NOT NULL AND NEW.user_id IS NULL THEN
            PERFORM check_and_remove_business_owner_role(OLD.user_id);
        END IF;
    END IF;
    
    -- When a business is deleted, check if user should lose role
    IF TG_OP = 'DELETE' AND OLD.user_id IS NOT NULL THEN
        PERFORM check_and_remove_business_owner_role(OLD.user_id);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for businesses table
DROP TRIGGER IF EXISTS trigger_business_role_assignment ON businesses;
CREATE TRIGGER trigger_business_role_assignment
    AFTER INSERT OR UPDATE OR DELETE ON businesses
    FOR EACH ROW
    EXECUTE FUNCTION handle_business_role_assignment();

-- Function to sync existing business owners with user_roles
CREATE OR REPLACE FUNCTION sync_existing_business_owners()
RETURNS TEXT AS $$
DECLARE
    business_record RECORD;
    assigned_count INTEGER := 0;
    total_count INTEGER := 0;
BEGIN
    -- Get all businesses with user_id that are active
    FOR business_record IN 
        SELECT DISTINCT user_id, COUNT(*) as business_count
        FROM businesses 
        WHERE user_id IS NOT NULL 
        AND is_active = true
        GROUP BY user_id
    LOOP
        total_count := total_count + 1;
        
        -- Assign business_owner role if not already assigned
        IF assign_business_owner_role(business_record.user_id) THEN
            assigned_count := assigned_count + 1;
        END IF;
    END LOOP;
    
    RETURN format('Processed %s business owners, assigned %s new roles', total_count, assigned_count);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get role assignment status for debugging
CREATE OR REPLACE FUNCTION get_role_assignment_status()
RETURNS TABLE (
    user_id UUID,
    user_email TEXT,
    business_count BIGINT,
    has_admin_role BOOLEAN,
    has_business_owner_role BOOLEAN,
    business_names TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id as user_id,
        p.user_email,
        COALESCE(b.business_count, 0) as business_count,
        COALESCE(admin_roles.has_admin, false) as has_admin_role,
        COALESCE(business_roles.has_business_owner, false) as has_business_owner_role,
        COALESCE(b.business_names, '') as business_names
    FROM profiles p
    LEFT JOIN (
        SELECT
            user_id,
            COUNT(*) as business_count,
            STRING_AGG(name, ', ') as business_names
        FROM businesses
        WHERE is_active = true
        GROUP BY user_id
    ) b ON p.id = b.user_id
    LEFT JOIN (
        SELECT user_id, true as has_admin
        FROM user_roles
        WHERE role_id = 1 AND is_active = true
    ) admin_roles ON p.id = admin_roles.user_id
    LEFT JOIN (
        SELECT user_id, true as has_business_owner
        FROM user_roles
        WHERE role_id = 2 AND is_active = true
    ) business_roles ON p.id = business_roles.user_id
    WHERE b.business_count > 0 OR admin_roles.has_admin = true OR business_roles.has_business_owner = true
    ORDER BY business_count DESC, p.user_email;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has business access (owns any business)
CREATE OR REPLACE FUNCTION has_business_access(target_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    business_count INTEGER;
BEGIN
    -- Count active businesses owned by the user
    SELECT COUNT(*) INTO business_count
    FROM businesses
    WHERE user_id = target_user_id
    AND is_active = true;

    RETURN business_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(target_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    admin_count INTEGER;
BEGIN
    -- Check if user has admin role (role_id = 1)
    SELECT COUNT(*) INTO admin_count
    FROM user_roles
    WHERE user_id = target_user_id
    AND role_id = 1
    AND is_active = true;

    RETURN admin_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has specific role
CREATE OR REPLACE FUNCTION user_has_portal_role(target_user_id UUID, target_role_id INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    role_count INTEGER;
BEGIN
    -- Check if user has the specified role
    SELECT COUNT(*) INTO role_count
    FROM user_roles
    WHERE user_id = target_user_id
    AND role_id = target_role_id
    AND is_active = true;

    RETURN role_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
