#!/usr/bin/env node

/**
 * Static Data Generation Script
 * 
 * This script runs during build time to fetch all businesses data
 * and generate static JSON files, eliminating runtime database calls
 * and connection pool issues.
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL:', !!SUPABASE_URL);
  console.error('- SUPABASE_SERVICE_ROLE_KEY:', !!SUPABASE_SERVICE_KEY);
  process.exit(1);
}

// Create Supabase client with service role key for build-time access
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false
  },
  global: {
    headers: {
      'X-Client-Info': 'static-data-generator',
      'User-Agent': 'Fuse.vip-StaticGen/1.0'
    }
  }
});

// Output directory for static data
const STATIC_DATA_DIR = path.join(process.cwd(), 'public', 'static-data');

// Ensure static data directory exists
if (!fs.existsSync(STATIC_DATA_DIR)) {
  fs.mkdirSync(STATIC_DATA_DIR, { recursive: true });
}

/**
 * Retry function for database operations
 */
async function retryOperation(operation, maxRetries = 3, delay = 2000) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      console.log(`❌ Attempt ${attempt} failed:`, error.message)

      if (attempt === maxRetries) {
        throw error
      }

      console.log(`⏳ Retrying in ${delay}ms...`)
      await new Promise(resolve => setTimeout(resolve, delay))
      delay *= 1.5 // Exponential backoff
    }
  }
}

/**
 * Fetch all businesses data with different field sets
 */
async function fetchBusinessesData() {
  console.log('🔄 Fetching businesses data for static generation...');

  return await retryOperation(async () => {
    console.log('📡 Attempting to connect to Supabase...')

    // Test connection first
    const { data: testData, error: testError } = await supabase
      .from('businesses')
      .select('count')
      .limit(1)

    if (testError) {
      console.error('❌ Connection test failed:', testError)
      throw new Error(`Database connection failed: ${testError.message}`)
    }

    console.log('✅ Database connection successful')

    // Full businesses data for admin and detailed views
    const { data: fullBusinesses, error: fullError } = await supabase
      .from('businesses')
      .select(`
        id,
        name,
        logo_url,
        website,
        category,
        premium_discount,
        is_active,
        created_at,
        updated_at,
        display_order,
        contact_info,
        user_id,
        business_address,
        contact_name,
        contact_email,
        contact_phone,
        latitude,
        longitude,
        business_referral,
        referring_business_id,
        business_spotlight,
        loyalty_reward_frequency,
        logo_optimized_url,
        logo_alt_text,
        logo_width,
        logo_height,
        logo_file_size,
        logo_mime_type
      `)
      .eq('is_active', true)
      .order('display_order', { ascending: true, nullsLast: true })
      .order('created_at', { ascending: false });

    if (fullError) {
      throw new Error(`Failed to fetch full businesses: ${fullError.message}`);
    }

    // Minimal businesses data for fast loading (carousels, home page)
    const minimalBusinesses = fullBusinesses.map(business => ({
      id: business.id,
      name: business.name,
      logo_url: business.logo_url,
      website: business.website,
      premium_discount: business.premium_discount,
      business_spotlight: business.business_spotlight
    }));

    // Standard businesses data for most UI components
    const standardBusinesses = fullBusinesses.map(business => ({
      id: business.id,
      name: business.name,
      logo_url: business.logo_url,
      website: business.website,
      category: business.category,
      premium_discount: business.premium_discount,
      is_active: business.is_active,
      business_spotlight: business.business_spotlight,
      business_address: business.business_address,
      contact_name: business.contact_name,
      contact_email: business.contact_email,
      contact_phone: business.contact_phone,
      created_at: business.created_at
    }));

    // Active businesses only (most common use case)
    const activeBusinesses = fullBusinesses.filter(b => b.is_active);

    // Spotlight businesses
    const spotlightBusinesses = fullBusinesses.filter(b => b.business_spotlight);

    // Categories for filtering
    const categories = [...new Set(fullBusinesses.map(b => b.category).filter(Boolean))];

    console.log(`✅ Fetched ${fullBusinesses.length} businesses successfully`);
    console.log(`📊 Categories: ${categories.length}`);
    console.log(`⭐ Spotlight businesses: ${spotlightBusinesses.length}`);

    return {
      full: fullBusinesses,
      standard: standardBusinesses,
      minimal: minimalBusinesses,
      active: activeBusinesses,
      spotlight: spotlightBusinesses,
      categories,
      metadata: {
        total: fullBusinesses.length,
        active: activeBusinesses.length,
        spotlight: spotlightBusinesses.length,
        categories: categories.length,
        generated_at: new Date().toISOString(),
        version: '1.0.0'
      }
    };
  }); // End of retryOperation
}

/**
 * Generate static JSON files
 */
async function generateStaticFiles(businessesData) {
  console.log('📝 Generating static JSON files...');

  const files = [
    { name: 'businesses-full.json', data: businessesData.full },
    { name: 'businesses-standard.json', data: businessesData.standard },
    { name: 'businesses-minimal.json', data: businessesData.minimal },
    { name: 'businesses-active.json', data: businessesData.active },
    { name: 'businesses-spotlight.json', data: businessesData.spotlight },
    { name: 'categories.json', data: businessesData.categories },
    { name: 'metadata.json', data: businessesData.metadata }
  ];

  for (const file of files) {
    const filePath = path.join(STATIC_DATA_DIR, file.name);
    const jsonContent = JSON.stringify(file.data, null, 2);
    
    fs.writeFileSync(filePath, jsonContent, 'utf8');
    console.log(`✅ Generated: ${file.name} (${file.data.length || 'N/A'} items)`);
  }

  // Generate index file for easy access
  const indexData = {
    files: files.map(f => f.name),
    metadata: businessesData.metadata,
    usage: {
      'businesses-minimal.json': 'For carousels, home page, fast loading',
      'businesses-standard.json': 'For industry page, listings, most UI',
      'businesses-full.json': 'For admin, detailed views, editing',
      'businesses-active.json': 'All active businesses (most common)',
      'businesses-spotlight.json': 'Featured/spotlight businesses only',
      'categories.json': 'List of all business categories',
      'metadata.json': 'Generation metadata and stats'
    }
  };

  fs.writeFileSync(
    path.join(STATIC_DATA_DIR, 'index.json'),
    JSON.stringify(indexData, null, 2),
    'utf8'
  );

  console.log('✅ Generated: index.json');
}

/**
 * Main execution
 */
async function main() {
  console.log('🚀 Starting static data generation...');
  console.log(`📁 Output directory: ${STATIC_DATA_DIR}`);

  try {
    const businessesData = await fetchBusinessesData();
    await generateStaticFiles(businessesData);

    console.log('\n🎉 Static data generation completed successfully!');
    console.log(`📊 Generated ${businessesData.metadata.total} businesses in multiple formats`);
    console.log(`📁 Files saved to: ${STATIC_DATA_DIR}`);
    
  } catch (error) {
    console.error('\n❌ Static data generation failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, fetchBusinessesData, generateStaticFiles };
