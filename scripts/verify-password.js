#!/usr/bin/env node

/**
 * Database Password Verification Tool
 * 
 * This script verifies if the current database password is correct
 * by attempting different connection methods.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Color console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function main() {
  log('🔑 Database Password Verification Tool', 'cyan');
  log('====================================', 'cyan');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const dbPassword = process.env.SUPABASE_DB_PASSWORD;
  
  log('\n1. Environment Variables:', 'blue');
  log(`   URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`, supabaseUrl ? 'green' : 'red');
  log(`   Anon Key: ${supabaseAnonKey ? '✅ Set' : '❌ Missing'}`, supabaseAnonKey ? 'green' : 'red');
  log(`   Service Role Key: ${serviceRoleKey ? '✅ Set' : '❌ Missing'}`, serviceRoleKey ? 'green' : 'red');
  log(`   DB Password: ${dbPassword ? '✅ Set' : '❌ Missing'}`, dbPassword ? 'green' : 'red');
  
  if (!supabaseUrl || !supabaseAnonKey || !dbPassword) {
    log('\n❌ Required environment variables missing', 'red');
    process.exit(1);
  }
  
  log('\n2. Testing Connection with Current Password:', 'blue');
  
  // Test 1: Basic Supabase client creation
  try {
    const client = createClient(supabaseUrl, supabaseAnonKey);
    log('   ✅ Client creation: Success', 'green');
    
    // Test 2: Auth service (this should work even with wrong password)
    const { data: session, error: authError } = await client.auth.getSession();
    log(`   ${authError ? '❌' : '✅'} Auth service: ${authError ? 'Error' : 'Success'}`, authError ? 'red' : 'green');
    
    // Test 3: Simple database query (this will fail with wrong password)
    const { data, error } = await client.from('profiles').select('id').limit(1);
    log(`   ${error ? '❌' : '✅'} Database query: ${error ? 'Error' : 'Success'}`, error ? 'red' : 'green');
    
    if (error) {
      log(`      Error: ${error.message}`, 'red');
      log(`      Code: ${error.code || 'N/A'}`, 'red');
    }
    
    // Test 4: Service role client (if available)
    if (serviceRoleKey) {
      const serviceClient = createClient(supabaseUrl, serviceRoleKey);
      const { data: serviceData, error: serviceError } = await serviceClient.from('profiles').select('id').limit(1);
      log(`   ${serviceError ? '❌' : '✅'} Service role query: ${serviceError ? 'Error' : 'Success'}`, serviceError ? 'red' : 'green');
      
      if (serviceError) {
        log(`      Error: ${serviceError.message}`, 'red');
      }
    }
    
  } catch (err) {
    log(`   ❌ Connection test failed: ${err.message}`, 'red');
  }
  
  log('\n3. Password Analysis:', 'blue');
  log(`   Current password: ${dbPassword}`, 'yellow');
  log(`   Password length: ${dbPassword.length} characters`, 'yellow');
  log(`   Contains special chars: ${/[!@#$%^&*(),.?":{}|<>]/.test(dbPassword) ? 'Yes' : 'No'}`, 'yellow');
  log(`   Contains numbers: ${/\d/.test(dbPassword) ? 'Yes' : 'No'}`, 'yellow');
  log(`   Contains uppercase: ${/[A-Z]/.test(dbPassword) ? 'Yes' : 'No'}`, 'yellow');
  log(`   Contains lowercase: ${/[a-z]/.test(dbPassword) ? 'Yes' : 'No'}`, 'yellow');
  
  log('\n4. Recommendations:', 'blue');
  log('   To fix password issues:', 'yellow');
  log('   1. Go to Supabase Dashboard → Settings → Database', 'yellow');
  log('   2. Click "Reset database password"', 'yellow');
  log('   3. Copy the new password to .env.local', 'yellow');
  log('   4. Restart your development server', 'yellow');
  log('   5. Run this script again to verify', 'yellow');
  
  log('\n🏁 Password verification complete!', 'cyan');
}

main().catch(err => {
  log(`\n💥 Script failed: ${err.message}`, 'red');
  process.exit(1);
});