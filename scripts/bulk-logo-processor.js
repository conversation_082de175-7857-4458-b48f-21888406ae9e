#!/usr/bin/env node

/**
 * Bulk Logo Processing Script
 * Processes multiple business logos at once using the Docker logo processor service
 * Updated to process logos from public/images/BusinessLogos/FuseLogos
 */

const fs = require('fs').promises;
const path = require('path');
const FormData = require('form-data');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// Configuration
const LOGO_SERVICE_URL = 'http://localhost:3001';
const SUPPORTED_FORMATS = ['.jpg', '.jpeg', '.png', '.svg', '.webp'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const LOGOS_DIRECTORY = './public/images/BusinessLogos/FuseLogos';

class BulkLogoProcessor {
  constructor(options = {}) {
    this.serviceUrl = options.serviceUrl || LOGO_SERVICE_URL;
    this.outputDir = options.outputDir || './processed-logos';
    this.logFile = options.logFile || './logo-processing.log';
    this.results = [];
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    console.log(message);
    await fs.appendFile(this.logFile, logMessage).catch(() => {});
  }

  async checkServiceHealth() {
    try {
      const response = await fetch(`${this.serviceUrl}/health`);
      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status}`);
      }
      const health = await response.json();
      await this.log(`✅ Logo service is healthy: ${health.status}`);
      return true;
    } catch (error) {
      await this.log(`❌ Logo service health check failed: ${error.message}`);
      return false;
    }
  }

  async validateFile(filePath) {
    try {
      const stats = await fs.stat(filePath);
      const ext = path.extname(filePath).toLowerCase();
      
      if (!SUPPORTED_FORMATS.includes(ext)) {
        return { valid: false, error: `Unsupported format: ${ext}` };
      }
      
      if (stats.size > MAX_FILE_SIZE) {
        return { valid: false, error: `File too large: ${(stats.size / 1024 / 1024).toFixed(2)}MB` };
      }
      
      return { valid: true, size: stats.size };
    } catch (error) {
      return { valid: false, error: `File access error: ${error.message}` };
    }
  }

  async processLogo(filePath, businessName = null) {
    const fileName = path.basename(filePath);
    await this.log(`🔄 Processing: ${fileName}`);

    try {
      // Validate file
      const validation = await this.validateFile(filePath);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Read file
      const fileBuffer = await fs.readFile(filePath);
      
      // Create form data
      const formData = new FormData();
      formData.append('logo', fileBuffer, {
        filename: fileName,
        contentType: this.getMimeType(path.extname(fileName))
      });

      if (businessName) {
        formData.append('businessName', businessName);
      }

      // Send to processing service
      const response = await fetch(`${this.serviceUrl}/api/process-logo`, {
        method: 'POST',
        body: formData,
        headers: formData.getHeaders()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Processing failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Processing failed');
      }

      await this.log(`✅ Successfully processed: ${fileName} (ID: ${result.fileId})`);
      
      return {
        success: true,
        fileName,
        filePath,
        businessName,
        fileId: result.fileId,
        processedImages: result.processedImages,
        metadata: result.metadata,
        originalSize: validation.size
      };

    } catch (error) {
      await this.log(`❌ Failed to process ${fileName}: ${error.message}`);
      return {
        success: false,
        fileName,
        filePath,
        businessName,
        error: error.message
      };
    }
  }

  getMimeType(ext) {
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.svg': 'image/svg+xml',
      '.webp': 'image/webp'
    };
    return mimeTypes[ext.toLowerCase()] || 'application/octet-stream';
  }

  async findLogos(directory) {
    try {
      const files = await fs.readdir(directory);
      const logoFiles = [];

      for (const file of files) {
        const filePath = path.join(directory, file);
        const stats = await fs.stat(filePath);
        
        if (stats.isFile()) {
          const ext = path.extname(file).toLowerCase();
          if (SUPPORTED_FORMATS.includes(ext)) {
            logoFiles.push(filePath);
          }
        }
      }

      return logoFiles;
    } catch (error) {
      throw new Error(`Failed to read directory ${directory}: ${error.message}`);
    }
  }

  async processDirectory(directory, businessMapping = {}) {
    await this.log(`🚀 Starting bulk logo processing from: ${directory}`);
    
    // Check service health
    const isHealthy = await this.checkServiceHealth();
    if (!isHealthy) {
      throw new Error('Logo processing service is not available');
    }

    // Find logo files
    const logoFiles = await this.findLogos(directory);
    await this.log(`📁 Found ${logoFiles.length} logo files`);

    if (logoFiles.length === 0) {
      await this.log('⚠️  No logo files found in the specified directory');
      return { processed: 0, failed: 0, results: [] };
    }

    // Process each logo
    const results = [];
    let processed = 0;
    let failed = 0;

    for (const filePath of logoFiles) {
      const fileName = path.basename(filePath, path.extname(filePath));
      const businessName = businessMapping[fileName] || fileName;
      
      const result = await this.processLogo(filePath, businessName);
      results.push(result);
      
      if (result.success) {
        processed++;
      } else {
        failed++;
      }

      // Small delay between processing
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.results = results;
    
    await this.log(`\n📊 Processing Summary:`);
    await this.log(`   ✅ Successfully processed: ${processed}`);
    await this.log(`   ❌ Failed: ${failed}`);
    await this.log(`   📁 Total files: ${logoFiles.length}`);

    return { processed, failed, results };
  }

  async generateReport() {
    const reportPath = path.join(this.outputDir, 'processing-report.json');
    await fs.mkdir(this.outputDir, { recursive: true });
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.results.length,
        successful: this.results.filter(r => r.success).length,
        failed: this.results.filter(r => !r.success).length
      },
      results: this.results
    };

    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    await this.log(`📄 Report saved to: ${reportPath}`);
    
    return reportPath;
  }
}

// CLI Usage
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
Usage: node bulk-logo-processor.js <directory> [options]

Arguments:
  directory     Path to directory containing logo files

Options:
  --output      Output directory for reports (default: ./processed-logos)
  --service     Logo service URL (default: http://localhost:3001)
  --mapping     JSON file with filename to business name mapping

Examples:
  node bulk-logo-processor.js ./logos
  node bulk-logo-processor.js ./logos --output ./reports
  node bulk-logo-processor.js ./logos --mapping ./business-mapping.json
    `);
    process.exit(1);
  }

  const directory = args[0];
  const options = {};
  
  // Parse options
  for (let i = 1; i < args.length; i += 2) {
    const option = args[i];
    const value = args[i + 1];
    
    switch (option) {
      case '--output':
        options.outputDir = value;
        break;
      case '--service':
        options.serviceUrl = value;
        break;
      case '--mapping':
        try {
          const mappingData = await fs.readFile(value, 'utf8');
          options.businessMapping = JSON.parse(mappingData);
        } catch (error) {
          console.error(`Failed to load mapping file: ${error.message}`);
          process.exit(1);
        }
        break;
    }
  }

  try {
    const processor = new BulkLogoProcessor(options);
    const result = await processor.processDirectory(directory, options.businessMapping || {});
    await processor.generateReport();
    
    console.log('\n🎉 Bulk logo processing completed!');
    process.exit(result.failed > 0 ? 1 : 0);
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    process.exit(1);
  }
}

// Export for use as module
module.exports = BulkLogoProcessor;

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
