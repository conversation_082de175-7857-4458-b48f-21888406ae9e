#!/usr/bin/env node

const path = require('path');
const fs = require('fs').promises;

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

const { Pool } = require('pg');

class CorrectLogoMatcher {
  constructor() {
    this.pool = null;
    this.logFile = path.join(__dirname, `correct-logo-match-${Date.now()}.log`);
    this.desktopLogoDirectory = '/Users/<USER>/Desktop/BusinessLogos/FuseLogos';
    this.publicLogoDirectory = path.join(__dirname, '..', 'public', 'images', 'BusinessLogos', 'FuseLogos');
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    await fs.appendFile(this.logFile, logMessage + '\n').catch(() => {});
  }

  async initDatabase() {
    if (this.pool) return this.pool;

    const dbPassword = process.env.SUPABASE_DB_PASSWORD;
    if (!dbPassword) {
      throw new Error('SUPABASE_DB_PASSWORD environment variable is required');
    }

    const config = {
      host: 'db.haqbtbpmyadkocakqnew.supabase.co',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: dbPassword,
      ssl: { rejectUnauthorized: false },
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 30000,
      max: 4,
      min: 1
    };

    this.pool = new Pool(config);
    const client = await this.pool.connect();
    await client.query('SELECT 1');
    client.release();
    
    await this.log('✅ Database connection established');
    return this.pool;
  }

  async getBusinesses() {
    const client = await this.pool.connect();
    try {
      const result = await client.query(`
        SELECT id, name, logo_url, category, user_id, created_at
        FROM businesses 
        WHERE is_active = true
        ORDER BY name
      `);
      
      await this.log(`📊 Found ${result.rows.length} businesses in database`);
      return result.rows;
    } finally {
      client.release();
    }
  }

  async getDesktopLogoFiles() {
    try {
      const files = await fs.readdir(this.desktopLogoDirectory);
      const logoFiles = files.filter(file => 
        file.toLowerCase().endsWith('.png') || 
        file.toLowerCase().endsWith('.jpg') || 
        file.toLowerCase().endsWith('.jpeg') || 
        file.toLowerCase().endsWith('.svg') ||
        file.toLowerCase().endsWith('.avif')
      );
      
      await this.log(`📁 Found ${logoFiles.length} logo files in Desktop directory`);
      return logoFiles;
    } catch (error) {
      await this.log(`❌ Error reading Desktop logo directory: ${error.message}`);
      return [];
    }
  }

  // Normalize text for matching
  normalizeText(text) {
    return text.toLowerCase()
      .replace(/[^a-z0-9]/g, '')  // Remove all non-alphanumeric
      .replace(/\s+/g, '');       // Remove spaces
  }

  // Smart matching algorithm for descriptive filenames
  matchBusinessToLogo(business, logoFiles) {
    const businessName = this.normalizeText(business.name);
    
    // Direct mapping for known matches
    const directMappings = {
      'aandb3d.png': 'A&B 3D printing',
      'aandb3d.PNG': 'A&B 3D printing',
      'ACG.png': 'AC Greatness Ministry',
      'AmsConstruction.png': 'AMS Construction',
      'ArrowOccupational.png': 'Arrow Occupational Medicine Center',
      'ArtofPitching.png': 'The Art of Pitching Corp',
      'AudleyCuteHats.png': 'AudleyCuteHats!',
      'BayworthTrimming.jpg': 'Bayworth Trimming',
      'beemans.png': 'Beeman\'s Pharmacy',
      'CaravelHealth.png': 'Caravel Health DPC',
      'CarlsbadChiropractor.png': 'Carlsbad Chiropractic',
      'clarendonShop.png': 'Clarendon Shop',
      'compassExec.jpeg': 'Compass Executive',
      'CoveSanclemente.png': 'Cove San Clemente',
      'culinaryCollective.png': 'The Culinary Collective',
      'directFitness.avif': 'Direct Fitness Collab',
      'DrSchumacher\'s.png': 'Dr. Schumacher\'s Chiropractic & Massage Clinic',
      'EastHighland.png': 'East Highland Chiropractic',
      'ElocTattoo.png': 'E.Loc.Tattoo',
      'Guadamuz.png': 'Guadamuz Chiropractic Inc',
      'homesource.png': 'Home Source Real Estate',
      'HoneyBelle.avif': 'Honey Belle',
      'JaynesBikinis.PNG': 'Jayne Bikinis',
      'Jeffbarlow.png': 'Jeff Barlow DDS',
      'JorritsmaTherapeutics.png': 'Jorritsma Therapeutics Shockwave Therapy',
      'JRWFoundations.png': 'JRW Foundation Systems',
      'KingsMeat.jpeg': 'Kings Meat & BBQ Supply',
      'KoreRehab.png': 'Kore Rehab',
      'Lptrealty.png': 'LPT Realty',
      'MikkisOnDelMar.jpg': 'Mikii\'s On Del Mar',
      'missiontaxandaccounting.avif': 'MISSION TAX & ACCOUNTING',
      'MonocleMedia.png': 'Monocle Media',
      'NorthernChiro.png': 'Northern Chiropractic',
      'OptimalTraining .png': 'Optimal Training',
      'OROsLogo.PNG': 'Oros Detailing',
      'Outlaw.png': 'Outlaw BBQ',
      'PrideAutoRepair.png': 'Pride automotive repair',
      'Protein plus.png': 'Protein Plus',
      'principaledLeader.png': 'The PrincipalED Leader, LLC',
      'RakeshPatel.png': 'Rakesh Patel DDS Inc.',
      'redlands-chiropractic-logo.png': 'Redlands Chiropractic',
      'redlands-massage-therapy.png': 'Redlands Massage Therapy',
      'RedlandsThriftLog.PNG': 'Redlands Thrift Store',
      'ResetKetamine.png': 'Reset Ketamine',
      'RimChiro.png': 'Rim Chiropractic',
      'Shootsafe-Academy.avif': 'Shootsafe Academy',
      'SIXTY+THREE+INK.jpg': 'Sixty Three Ink',
      'SLPRiley.png': 'SLP Riley',
      'Sorry.png': 'Sorry',
      'staffordsDiscounts.jpeg': 'Stanford\'s Discount Carpets Inc.',
      'TabTaxes.png': 'TAB - Business and Tax Services',
      'tadeas.png': 'Tadea\'s Home Visit Medical Massage',
      'TheBlues.png': 'The Blues',
      'theChoiceChiro.png': 'The Choice Chiropractic',
      'TricommunityChiro.png': 'Tri-Community Chiropractic Office',
      'TulareChiro.png': 'Tulare Chiropractic',
      'UKIntSoccer.png': 'UK International Soccer',
      'UnitedUniversityClinic.jpg': 'United University Clinic',
      'VintageFinds.png': 'Vintage Finds Me',
      'VitaNova.avif': 'Vitanova',
      'WavesBeyondSkin.png': 'Waves Beyond Skin Care',
      'Weebee\'s Sweet Designs.avif': 'Weebee\'s Sweet Designs',
      'WellnessAdvocate.jpg': 'Wellness Advocate'
    };

    // Check direct mappings first
    for (const [logoFile, businessNameMapped] of Object.entries(directMappings)) {
      if (logoFiles.includes(logoFile)) {
        const normalizedMapped = this.normalizeText(businessNameMapped);
        if (normalizedMapped === businessName) {
          return logoFile;
        }
      }
    }

    // Fallback to algorithmic matching
    for (const logoFile of logoFiles) {
      const logoName = this.normalizeText(logoFile.replace(/\.(png|jpg|jpeg|svg|avif)$/i, ''));
      
      // Direct match
      if (logoName === businessName) {
        return logoFile;
      }
      
      // Contains match
      if (logoName.includes(businessName) || businessName.includes(logoName)) {
        return logoFile;
      }
      
      // Word-based matching
      const businessWords = business.name.toLowerCase().split(/\s+/);
      for (const word of businessWords) {
        if (word.length > 3 && logoName.includes(this.normalizeText(word))) {
          return logoFile;
        }
      }
    }
    
    return null;
  }

  async copyLogoToPublic(logoFile) {
    try {
      const sourcePath = path.join(this.desktopLogoDirectory, logoFile);
      const destPath = path.join(this.publicLogoDirectory, logoFile);
      
      const logoBuffer = await fs.readFile(sourcePath);
      await fs.writeFile(destPath, logoBuffer);
      
      await this.log(`📋 Copied ${logoFile} to public directory`);
      return `/images/BusinessLogos/FuseLogos/${logoFile}`;
    } catch (error) {
      await this.log(`❌ Failed to copy ${logoFile}: ${error.message}`);
      // Return desktop path as fallback
      return `/Users/<USER>/Desktop/BusinessLogos/FuseLogos/${logoFile}`;
    }
  }

  async updateBusinessLogo(businessId, logoUrl) {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        'UPDATE businesses SET logo_url = $1, updated_at = NOW() WHERE id = $2 RETURNING name',
        [logoUrl, businessId]
      );
      
      if (result.rows.length > 0) {
        await this.log(`✅ Updated logo for business: ${result.rows[0].name}`);
        return true;
      }
      
      return false;
    } finally {
      client.release();
    }
  }

  async processCorrectLogoMatching() {
    await this.log('🎯 Starting correct logo matching process...');
    
    try {
      await this.initDatabase();
      
      const businesses = await this.getBusinesses();
      const logoFiles = await this.getDesktopLogoFiles();
      
      if (logoFiles.length === 0) {
        await this.log('❌ No logo files found in Desktop directory');
        return;
      }
      
      const matches = [];
      const unmatched = [];
      
      // Match businesses to logos
      for (const business of businesses) {
        const matchedLogo = this.matchBusinessToLogo(business, logoFiles);
        
        if (matchedLogo) {
          matches.push({
            business,
            logoFile: matchedLogo,
            currentLogoUrl: business.logo_url
          });
        } else {
          unmatched.push(business);
        }
      }
      
      await this.log(`🎯 Matched ${matches.length} businesses to logos`);
      await this.log(`❓ ${unmatched.length} businesses without logo matches`);
      
      // List all matches for review
      await this.log('\\n📋 MATCHED BUSINESSES:');
      for (const match of matches) {
        await this.log(`   • ${match.business.name} → ${match.logoFile}`);
      }
      
      // Process uploads and updates
      let successCount = 0;
      let errorCount = 0;
      let skippedCount = 0;
      
      for (const match of matches) {
        try {
          // Copy logo to public directory and update database
          const logoUrl = await this.copyLogoToPublic(match.logoFile);
          const success = await this.updateBusinessLogo(match.business.id, logoUrl);
          
          if (success) {
            successCount++;
          } else {
            errorCount++;
          }
          
          // Add small delay to avoid overwhelming the system
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (error) {
          await this.log(`❌ Error processing ${match.business.name}: ${error.message}`);
          errorCount++;
        }
      }
      
      await this.log(`\\n📊 FINAL RESULTS:`);
      await this.log(`✅ Successfully updated: ${successCount} businesses`);
      await this.log(`❌ Errors: ${errorCount}`);
      await this.log(`⏭️  Skipped: ${skippedCount}`);
      await this.log(`❓ Unmatched businesses: ${unmatched.length}`);
      
      // List unmatched businesses for manual review
      if (unmatched.length > 0) {
        await this.log('\\n❓ UNMATCHED BUSINESSES (need manual logo assignment):');
        for (const business of unmatched) {
          await this.log(`   • ${business.name} (ID: ${business.id})`);
        }
      }
      
    } catch (error) {
      await this.log(`❌ Fatal error: ${error.message}`);
      throw error;
    } finally {
      if (this.pool) {
        await this.pool.end();
      }
    }
  }
}

// Run the script
async function main() {
  const matcher = new CorrectLogoMatcher();
  await matcher.processCorrectLogoMatching();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = CorrectLogoMatcher;