#!/usr/bin/env node

/**
 * Create Data URL for Redlands Chiropractic Logo
 * This script creates a data URL that can be used directly in the database
 */

const fs = require('fs');
const path = require('path');

// Business ID for Redlands Chiropractic
const REDLANDS_BUSINESS_ID = '26641328-6b07-4a56-b7f1-3550bb5ffb1f';

function createLogoDataUrl() {
  try {
    console.log('🚀 Creating data URL for Redlands Chiropractic logo...');

    // Read the SVG file
    const logoPath = path.join(__dirname, '../temp/redlands-chiropractic-logo.svg');
    
    if (!fs.existsSync(logoPath)) {
      throw new Error(`Logo file not found at: ${logoPath}`);
    }

    const logoContent = fs.readFileSync(logoPath, 'utf8');
    console.log(`📁 Read logo file: ${logoContent.length} characters`);

    // Create data URL
    const base64Content = Buffer.from(logoContent).toString('base64');
    const dataUrl = `data:image/svg+xml;base64,${base64Content}`;
    
    console.log('✅ Data URL created successfully');
    console.log(`📏 Data URL length: ${dataUrl.length} characters`);
    
    // Output the data URL
    console.log('\n🔗 Data URL:');
    console.log(dataUrl);
    
    // Output SQL update command
    console.log('\n📝 SQL Update Command:');
    console.log(`UPDATE businesses SET logo_url = '${dataUrl}', logo_optimized_url = '${dataUrl}', logo_file_size = ${logoContent.length}, logo_mime_type = 'image/svg+xml', logo_width = 400, logo_height = 100, updated_at = NOW() WHERE id = '${REDLANDS_BUSINESS_ID}';`);
    
    // Save to file for easy copying
    const outputPath = path.join(__dirname, '../temp/redlands-logo-data-url.txt');
    fs.writeFileSync(outputPath, dataUrl);
    console.log(`\n💾 Data URL saved to: ${outputPath}`);

    console.log('\n🎉 Logo data URL creation completed successfully!');

  } catch (error) {
    console.error('❌ Error creating data URL:', error.message);
    process.exit(1);
  }
}

// Run the script
createLogoDataUrl();
