#!/usr/bin/env node

/**
 * Automated Logo Processor
 * Processes logos from network_applications and updates businesses table
 * Can be used for both immediate fixes and automated approval workflow
 */

const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

const { createClient } = require('@supabase/supabase-js');
const FormData = require('form-data');
const fetch = require('node-fetch');
const fs = require('fs').promises;

class AutomatedLogoProcessor {
  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    this.dockerServiceUrl = process.env.DOCKER_LOGO_SERVICE_URL || 'http://localhost:3001';
    this.processed = 0;
    this.failed = 0;
    this.logFile = path.join(__dirname, `logo-processing-${Date.now()}.log`);
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    await fs.appendFile(this.logFile, logMessage + '\n').catch(() => {});
  }

  /**
   * Process logo for a specific business ID
   */
  async processBusinessLogo(businessId) {
    await this.log(`🔄 Processing logo for business: ${businessId}`);

    try {
      // First, check if business exists
      const { data: business, error: businessError } = await this.supabase
        .from('businesses')
        .select('id, name, logo_url')
        .eq('id', businessId)
        .single();

      if (businessError || !business) {
        await this.log(`❌ Business not found: ${businessId}`);
        return false;
      }

      // Look for network application with logo data for this business
      const { data: applications, error: appError } = await this.supabase
        .from('network_applications')
        .select('id, business_name, logo_data, user_id')
        .eq('user_id', business.user_id)
        .not('logo_data', 'is', null);

      if (appError || !applications || applications.length === 0) {
        await this.log(`⚠️  No network application with logo found for business: ${business.name}`);
        return false;
      }

      // Process the first application with logo data
      const application = applications[0];
      const logoResult = await this.processLogoFromApplication(application);

      if (logoResult.success) {
        // Update business with processed logo
        const { error: updateError } = await this.supabase
          .from('businesses')
          .update({
            logo_url: logoResult.logoUrl,
            logo_optimized_url: logoResult.logoUrl,
            logo_file_size: logoResult.fileSize,
            logo_mime_type: logoResult.mimeType,
            logo_width: logoResult.width,
            logo_height: logoResult.height,
            updated_at: new Date().toISOString()
          })
          .eq('id', businessId);

        if (updateError) {
          await this.log(`❌ Failed to update business logo: ${updateError.message}`);
          return false;
        }

        await this.log(`✅ Successfully updated business logo: ${business.name} -> ${logoResult.logoUrl}`);
        this.processed++;
        return true;
      } else {
        await this.log(`❌ Failed to process logo for business: ${business.name} - ${logoResult.error}`);
        this.failed++;
        return false;
      }

    } catch (error) {
      await this.log(`❌ Error processing business logo: ${error.message}`);
      this.failed++;
      return false;
    }
  }

  /**
   * Process logo from network application data
   */
  async processLogoFromApplication(application) {
    try {
      if (!application.logo_data) {
        return { success: false, error: 'No logo data found' };
      }

      // Parse logo data JSON
      const logoData = JSON.parse(application.logo_data);
      const { filename, mimetype, size, data } = logoData;

      if (!data) {
        return { success: false, error: 'No logo file data found' };
      }

      await this.log(`📁 Processing logo: ${filename} (${this.formatFileSize(size)})`);

      // Convert base64 to buffer
      const logoBuffer = Buffer.from(data, 'base64');

      // Try Docker processing first
      const dockerResult = await this.processWithDocker(logoBuffer, filename, mimetype);
      if (dockerResult.success) {
        return dockerResult;
      }

      // Fallback to direct upload
      await this.log(`⚠️  Docker processing failed, using fallback upload`);
      return await this.fallbackUpload(logoBuffer, filename, mimetype);

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Process logo using Docker service
   */
  async processWithDocker(logoBuffer, filename, mimetype) {
    try {
      // Check if Docker service is available
      const healthCheck = await fetch(`${this.dockerServiceUrl}/health`).catch(() => null);
      if (!healthCheck || !healthCheck.ok) {
        return { success: false, error: 'Docker service unavailable' };
      }

      // Create form data
      const formData = new FormData();
      formData.append('logo', logoBuffer, {
        filename: filename,
        contentType: mimetype
      });

      // Send to Docker processor
      const response = await fetch(`${this.dockerServiceUrl}/api/process-logo`, {
        method: 'POST',
        body: formData,
        headers: formData.getHeaders()
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { success: false, error: `Docker processing failed: ${response.status} - ${errorText}` };
      }

      const result = await response.json();
      
      if (result.success) {
        return {
          success: true,
          logoUrl: result.processedImages?.original?.path || result.logoUrl,
          fileSize: result.metadata?.size || logoBuffer.length,
          mimeType: result.metadata?.format || mimetype,
          width: result.metadata?.width,
          height: result.metadata?.height
        };
      } else {
        return { success: false, error: result.error || 'Docker processing failed' };
      }

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Fallback upload to Supabase storage
   */
  async fallbackUpload(logoBuffer, filename, mimetype) {
    try {
      // Generate unique filename
      const timestamp = Date.now();
      const sanitizedName = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
      const storageFileName = `business-logos/${timestamp}_${sanitizedName}`;

      // Upload to Supabase storage
      const { data, error } = await this.supabase.storage
        .from('business-assets')
        .upload(storageFileName, logoBuffer, {
          contentType: mimetype,
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        return { success: false, error: `Storage upload failed: ${error.message}` };
      }

      // Get public URL
      const { data: urlData } = this.supabase.storage
        .from('business-assets')
        .getPublicUrl(storageFileName);

      return {
        success: true,
        logoUrl: urlData.publicUrl,
        fileSize: logoBuffer.length,
        mimeType: mimetype,
        width: null,
        height: null
      };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Process all pending network applications with logos
   */
  async processAllPendingLogos() {
    await this.log(`🚀 Starting automated logo processing for all pending applications`);

    try {
      // Get all approved network applications with logo data that don't have corresponding businesses
      const { data: applications, error } = await this.supabase
        .from('network_applications')
        .select(`
          id, 
          business_name, 
          logo_data, 
          user_id,
          category,
          website,
          contact_name,
          contact_email,
          contact_phone,
          business_address,
          proposed_discount,
          loyalty_reward_frequency
        `)
        .eq('status', 'approved')
        .not('logo_data', 'is', null);

      if (error) {
        await this.log(`❌ Failed to fetch applications: ${error.message}`);
        return false;
      }

      if (!applications || applications.length === 0) {
        await this.log(`ℹ️  No approved applications with logos found`);
        return true;
      }

      await this.log(`📋 Found ${applications.length} approved applications with logos to process`);

      for (const application of applications) {
        await this.processApplicationWithLogo(application);
      }

      await this.log(`🎉 Completed processing. Processed: ${this.processed}, Failed: ${this.failed}`);
      return true;

    } catch (error) {
      await this.log(`❌ Error in batch processing: ${error.message}`);
      return false;
    }
  }

  /**
   * Process a single application and create/update business
   */
  async processApplicationWithLogo(application) {
    try {
      await this.log(`🔄 Processing application: ${application.business_name}`);

      // Check if business already exists for this user
      const { data: existingBusiness } = await this.supabase
        .from('businesses')
        .select('id, name')
        .eq('user_id', application.user_id)
        .eq('name', application.business_name)
        .single();

      let businessId;

      if (existingBusiness) {
        businessId = existingBusiness.id;
        await this.log(`📝 Found existing business: ${existingBusiness.name}`);
      } else {
        // Create new business
        const { data: newBusiness, error: createError } = await this.supabase
          .from('businesses')
          .insert({
            user_id: application.user_id,
            name: application.business_name,
            category: application.category,
            website: application.website,
            contact_name: application.contact_name,
            contact_email: application.contact_email,
            contact_phone: application.contact_phone,
            business_address: application.business_address,
            premium_discount: application.proposed_discount,
            loyalty_reward_frequency: application.loyalty_reward_frequency,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select('id')
          .single();

        if (createError) {
          await this.log(`❌ Failed to create business: ${createError.message}`);
          this.failed++;
          return false;
        }

        businessId = newBusiness.id;
        await this.log(`✅ Created new business: ${application.business_name}`);
      }

      // Process and update logo
      const logoResult = await this.processLogoFromApplication(application);

      if (logoResult.success) {
        const { error: updateError } = await this.supabase
          .from('businesses')
          .update({
            logo_url: logoResult.logoUrl,
            logo_optimized_url: logoResult.logoUrl,
            logo_file_size: logoResult.fileSize,
            logo_mime_type: logoResult.mimeType,
            logo_width: logoResult.width,
            logo_height: logoResult.height,
            updated_at: new Date().toISOString()
          })
          .eq('id', businessId);

        if (updateError) {
          await this.log(`❌ Failed to update business logo: ${updateError.message}`);
          this.failed++;
          return false;
        }

        await this.log(`✅ Successfully processed: ${application.business_name} -> ${logoResult.logoUrl}`);
        this.processed++;
        return true;
      } else {
        await this.log(`❌ Logo processing failed for: ${application.business_name} - ${logoResult.error}`);
        this.failed++;
        return false;
      }

    } catch (error) {
      await this.log(`❌ Error processing application: ${error.message}`);
      this.failed++;
      return false;
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// CLI interface
async function main() {
  const processor = new AutomatedLogoProcessor();
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.log(`
Usage:
  node automated-logo-processor.js <command> [options]

Commands:
  process-business <business-id>  Process logo for specific business
  process-all                     Process all pending applications with logos
  
Examples:
  node automated-logo-processor.js process-business 123774fb-3fab-403e-ad39-acc0b15ec78d
  node automated-logo-processor.js process-all
    `);
    process.exit(1);
  }

  const command = args[0];

  try {
    if (command === 'process-business') {
      const businessId = args[1];
      if (!businessId) {
        console.error('❌ Business ID required');
        process.exit(1);
      }
      
      const success = await processor.processBusinessLogo(businessId);
      process.exit(success ? 0 : 1);
      
    } else if (command === 'process-all') {
      const success = await processor.processAllPendingLogos();
      process.exit(success ? 0 : 1);
      
    } else {
      console.error(`❌ Unknown command: ${command}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Fatal error: ${error.message}`);
    process.exit(1);
  }
}

// Export for use as module
module.exports = { AutomatedLogoProcessor };

// Run CLI if called directly
if (require.main === module) {
  main();
}
