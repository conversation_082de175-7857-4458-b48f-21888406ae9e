#!/usr/bin/env node

const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

const { Pool } = require('pg');
const fs = require('fs').promises;

class NetworkApplicationLogoProcessor {
  constructor() {
    this.pool = null;
    this.logFile = path.join(__dirname, `network-app-logos-${Date.now()}.log`);
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    await fs.appendFile(this.logFile, logMessage + '\n').catch(() => {});
  }

  async initDatabase() {
    if (this.pool) return this.pool;

    const dbPassword = process.env.SUPABASE_DB_PASSWORD;
    if (!dbPassword) {
      throw new Error('SUPABASE_DB_PASSWORD environment variable is required');
    }

    const config = {
      host: 'db.haqbtbpmyadkocakqnew.supabase.co',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: dbPassword,
      ssl: { rejectUnauthorized: false },
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 30000,
      max: 4,  // Appropriate for 44 users
      min: 1   // Single connection minimum
    };

    this.pool = new Pool(config);

    // Test connection
    const client = await this.pool.connect();
    await client.query('SELECT 1');
    client.release();

    await this.log('✅ Database connection established');
    return this.pool;
  }

  async findAllApplicationsWithLogos() {
    await this.initDatabase();

    const query = `
      SELECT 
        id, 
        business_name, 
        user_id, 
        status, 
        logo_data,
        category,
        website,
        contact_name,
        contact_email,
        contact_phone,
        business_address,
        proposed_discount,
        loyalty_reward_frequency,
        created_at
      FROM network_applications 
      WHERE logo_data IS NOT NULL 
      ORDER BY created_at DESC
    `;

    const result = await this.pool.query(query);
    
    await this.log(`✅ Found ${result.rows.length} network applications with logo data`);
    
    for (const app of result.rows) {
      await this.log(`  📋 ${app.business_name} (${app.id}) - Status: ${app.status} - User: ${app.user_id}`);
      
      try {
        const logoData = JSON.parse(app.logo_data);
        await this.log(`    📁 Logo: ${logoData.filename} (${this.formatFileSize(logoData.size)}) - Type: ${logoData.mimetype}`);
        
        // Check if this is the img_2123 file
        if (logoData.filename && logoData.filename.toLowerCase().includes('img_2123')) {
          await this.log(`    🎯 FOUND img_2123 file!`);
        }
      } catch (e) {
        await this.log(`    ⚠️  Could not parse logo data: ${e.message}`);
      }
    }

    return result.rows;
  }

  async findBusinessesForUsers(userIds) {
    await this.initDatabase();

    const query = `
      SELECT 
        id, 
        name, 
        user_id, 
        logo_url,
        category,
        website,
        contact_email
      FROM businesses 
      WHERE user_id = ANY($1)
      ORDER BY created_at DESC
    `;

    const result = await this.pool.query(query, [userIds]);
    
    await this.log(`✅ Found ${result.rows.length} businesses for the users with logo applications`);
    
    for (const business of result.rows) {
      await this.log(`  🏢 ${business.name} (${business.id}) - User: ${business.user_id} - Logo: ${business.logo_url ? '✅' : '❌'}`);
    }

    return result.rows;
  }

  async extractLogoFile(application, outputDir = null) {
    try {
      if (!application.logo_data) {
        throw new Error('No logo data found');
      }

      const logoData = JSON.parse(application.logo_data);
      const { filename, mimetype, size, data } = logoData;

      if (!data) {
        throw new Error('No logo file data found');
      }

      await this.log(`📁 Extracting logo: ${filename} (${this.formatFileSize(size)})`);

      // Convert base64 to buffer
      const logoBuffer = Buffer.from(data, 'base64');

      // Determine output directory
      const extractDir = outputDir || path.join(__dirname, 'extracted-logos');
      await fs.mkdir(extractDir, { recursive: true });

      // Create safe filename
      const timestamp = Date.now();
      const safeName = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
      const outputPath = path.join(extractDir, `${application.business_name}_${timestamp}_${safeName}`);

      // Write file
      await fs.writeFile(outputPath, logoBuffer);

      await this.log(`✅ Logo extracted to: ${outputPath}`);

      return {
        success: true,
        outputPath,
        filename,
        size,
        mimetype,
        logoBuffer
      };

    } catch (error) {
      await this.log(`❌ Failed to extract logo: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async processLogoToPublicDirectory(application) {
    try {
      const extraction = await this.extractLogoFile(application);
      if (!extraction.success) {
        return extraction;
      }

      // Copy to public directory for web access
      const publicDir = path.join(__dirname, '..', 'public', 'business-logos');
      await fs.mkdir(publicDir, { recursive: true });

      const timestamp = Date.now();
      const safeName = extraction.filename.replace(/[^a-zA-Z0-9.-]/g, '_');
      const publicFileName = `${application.business_name.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}_${safeName}`;
      const publicPath = path.join(publicDir, publicFileName);

      await fs.writeFile(publicPath, extraction.logoBuffer);

      const publicUrl = `/business-logos/${publicFileName}`;

      await this.log(`✅ Logo copied to public directory: ${publicUrl}`);

      return {
        success: true,
        publicUrl,
        publicPath,
        filename: extraction.filename,
        size: extraction.size,
        mimetype: extraction.mimetype
      };

    } catch (error) {
      await this.log(`❌ Failed to process logo to public directory: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async updateBusinessWithLogo(businessId, logoResult) {
    await this.initDatabase();

    const updateQuery = `
      UPDATE businesses 
      SET 
        logo_url = $1,
        logo_optimized_url = $1,
        logo_file_size = $2,
        logo_mime_type = $3,
        updated_at = NOW()
      WHERE id = $4
      RETURNING id, name
    `;

    const params = [
      logoResult.publicUrl,
      logoResult.size,
      logoResult.mimetype,
      businessId
    ];

    const result = await this.pool.query(updateQuery, params);
    
    if (result.rows.length > 0) {
      await this.log(`✅ Updated business logo: ${result.rows[0].name} -> ${logoResult.publicUrl}`);
      return true;
    } else {
      await this.log(`❌ Failed to update business logo for ID: ${businessId}`);
      return false;
    }
  }

  async processSpecificBusiness(businessId) {
    await this.log(`🎯 Processing specific business: ${businessId}`);

    try {
      await this.initDatabase();

      // Find the business
      const businessQuery = `
        SELECT id, name, user_id, logo_url
        FROM businesses
        WHERE id = $1
      `;

      const businessResult = await this.pool.query(businessQuery, [businessId]);
      
      if (businessResult.rows.length === 0) {
        await this.log(`❌ Business not found: ${businessId}`);
        return false;
      }

      const business = businessResult.rows[0];
      await this.log(`✅ Found business: ${business.name} (User: ${business.user_id})`);

      // Find network applications for this user with logo data
      const appQuery = `
        SELECT * FROM network_applications 
        WHERE user_id = $1 AND logo_data IS NOT NULL
        ORDER BY created_at DESC
        LIMIT 1
      `;

      const appResult = await this.pool.query(appQuery, [business.user_id]);
      
      if (appResult.rows.length === 0) {
        await this.log(`❌ No network application with logo found for user: ${business.user_id}`);
        return false;
      }

      const application = appResult.rows[0];
      await this.log(`✅ Found application: ${application.business_name} with logo data`);

      // Process the logo
      const logoResult = await this.processLogoToPublicDirectory(application);
      
      if (logoResult.success) {
        // Update the business with the logo URL
        const updated = await this.updateBusinessWithLogo(businessId, logoResult);
        
        if (updated) {
          await this.log(`🎉 Successfully processed logo for business: ${business.name}`);
          return true;
        }
      } else {
        await this.log(`❌ Logo processing failed: ${logoResult.error}`);
      }

      return false;

    } catch (error) {
      await this.log(`❌ Error processing specific business: ${error.message}`);
      return false;
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async cleanup() {
    if (this.pool) {
      await this.pool.end();
      await this.log('🔌 Database connection closed');
    }
  }
}

// CLI interface
async function main() {
  const processor = new NetworkApplicationLogoProcessor();
  const args = process.argv.slice(2);

  try {
    if (args.length === 0) {
      console.log(`
Usage:
  node process-network-application-logos.js <command> [options]

Commands:
  list-all                        List all network applications with logos
  process-business <business-id>  Process logo for specific business
  extract-all                     Extract all logos to files
  
Examples:
  node process-network-application-logos.js list-all
  node process-network-application-logos.js process-business 123774fb-3fab-403e-ad39-acc0b15ec78d
  node process-network-application-logos.js extract-all
      `);
      process.exit(1);
    }

    const command = args[0];

    if (command === 'list-all') {
      const applications = await processor.findAllApplicationsWithLogos();
      
      if (applications.length > 0) {
        const userIds = applications.map(app => app.user_id);
        await processor.findBusinessesForUsers(userIds);
      }
      
      await processor.cleanup();
      process.exit(0);
      
    } else if (command === 'process-business') {
      const businessId = args[1];
      if (!businessId) {
        console.error('❌ Business ID required');
        process.exit(1);
      }
      
      const success = await processor.processSpecificBusiness(businessId);
      await processor.cleanup();
      process.exit(success ? 0 : 1);
      
    } else if (command === 'extract-all') {
      const applications = await processor.findAllApplicationsWithLogos();
      
      for (const app of applications) {
        await processor.extractLogoFile(app);
      }
      
      await processor.cleanup();
      process.exit(0);
      
    } else {
      console.error(`❌ Unknown command: ${command}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Fatal error: ${error.message}`);
    await processor.cleanup();
    process.exit(1);
  }
}

// Run CLI if called directly
if (require.main === module) {
  main();
}

module.exports = { NetworkApplicationLogoProcessor };
