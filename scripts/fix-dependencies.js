#!/usr/bin/env node

/**
 * <PERSON>ript to fix package.json dependencies by replacing "latest" with specific versions
 * This helps prevent version conflicts in CI/CD pipelines
 */

const fs = require('fs');
const path = require('path');

const packageJsonPath = path.join(process.cwd(), 'package.json');

console.log('🔧 Fixing package.json dependencies...');

try {
  // Read package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Dependencies that should have specific versions instead of "latest"
  const specificVersions = {
    // Radix UI components - use stable versions
    '@radix-ui/react-accordion': '^1.1.2',
    '@radix-ui/react-alert-dialog': '^1.0.5',
    '@radix-ui/react-aspect-ratio': '^1.0.3',
    '@radix-ui/react-avatar': '^1.0.4',
    '@radix-ui/react-checkbox': '^1.0.4',
    '@radix-ui/react-collapsible': '^1.0.3',
    '@radix-ui/react-context-menu': '^2.1.5',
    '@radix-ui/react-dialog': '^1.0.5',
    '@radix-ui/react-dropdown-menu': '^2.0.6',
    '@radix-ui/react-hover-card': '^1.0.7',
    '@radix-ui/react-label': '^2.0.2',
    '@radix-ui/react-menubar': '^1.0.4',
    '@radix-ui/react-navigation-menu': '^1.1.4',
    '@radix-ui/react-popover': '^1.0.7',
    '@radix-ui/react-progress': '^1.0.3',
    '@radix-ui/react-radio-group': '^1.1.3',
    '@radix-ui/react-scroll-area': '^1.0.5',
    '@radix-ui/react-select': '^2.0.0',
    '@radix-ui/react-separator': '^1.0.3',
    '@radix-ui/react-slider': '^1.1.2',
    '@radix-ui/react-slot': '^1.0.2',
    '@radix-ui/react-switch': '^1.0.3',
    '@radix-ui/react-tabs': '^1.0.4',
    '@radix-ui/react-toast': '^1.1.5',
    '@radix-ui/react-toggle': '^1.0.3',
    '@radix-ui/react-toggle-group': '^1.0.4',
    '@radix-ui/react-tooltip': '^1.0.7',
    
    // Other UI components
    '@lottiefiles/dotlottie-react': '^0.8.6',
    'canvas-confetti': '^1.9.2',
    'cmdk': '^0.2.0',
    'embla-carousel-react': '^8.0.0',
    'framer-motion': '^11.0.0',
    'input-otp': '^1.2.4',
    'next-themes': '^0.2.1',
    'react-day-picker': '^8.10.0',
    'react-hook-form': '^7.49.0',
    'react-resizable-panels': '^2.0.0',
    'recharts': '^2.12.0',
    'vaul': '^0.9.0'
  };

  let hasChanges = false;

  // Fix dependencies
  if (packageJson.dependencies) {
    for (const [dep, version] of Object.entries(packageJson.dependencies)) {
      if (version === 'latest' && specificVersions[dep]) {
        console.log(`📦 Updating ${dep}: latest → ${specificVersions[dep]}`);
        packageJson.dependencies[dep] = specificVersions[dep];
        hasChanges = true;
      }
    }
  }

  // Fix devDependencies
  if (packageJson.devDependencies) {
    for (const [dep, version] of Object.entries(packageJson.devDependencies)) {
      if (version === 'latest' && specificVersions[dep]) {
        console.log(`📦 Updating ${dep}: latest → ${specificVersions[dep]}`);
        packageJson.devDependencies[dep] = specificVersions[dep];
        hasChanges = true;
      }
    }
  }

  if (hasChanges) {
    // Write updated package.json
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    console.log('✅ package.json updated successfully!');
    console.log('🔄 Run "npm install" to update package-lock.json');
  } else {
    console.log('✅ No changes needed - all dependencies already have specific versions');
  }

} catch (error) {
  console.error('❌ Error fixing dependencies:', error.message);
  process.exit(1);
}
