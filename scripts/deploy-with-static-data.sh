#!/bin/bash

# Deploy with Static Data Generation Script
# 
# This script ensures static businesses data is generated before deployment
# and handles any errors gracefully.

set -e  # Exit on any error

echo "🚀 Starting deployment with static data generation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required environment variables are set
check_env_vars() {
    print_status "Checking environment variables..."
    
    if [ -z "$NEXT_PUBLIC_SUPABASE_URL" ]; then
        print_error "NEXT_PUBLIC_SUPABASE_URL is not set"
        exit 1
    fi
    
    if [ -z "$SUPABASE_SERVICE_ROLE_KEY" ]; then
        print_error "SUPABASE_SERVICE_ROLE_KEY is not set"
        exit 1
    fi
    
    print_success "Environment variables are properly configured"
}

# Generate static data
generate_static_data() {
    print_status "Generating static businesses data..."
    
    # Create static data directory if it doesn't exist
    mkdir -p public/static-data
    
    # Run the static data generation script
    if node scripts/generate-static-data.js; then
        print_success "Static data generated successfully"
        
        # Verify generated files
        if [ -f "public/static-data/businesses-standard.json" ]; then
            BUSINESS_COUNT=$(node -e "console.log(JSON.parse(require('fs').readFileSync('public/static-data/businesses-standard.json', 'utf8')).length)")
            print_success "Generated data for $BUSINESS_COUNT businesses"
        else
            print_error "Static data files not found after generation"
            exit 1
        fi
    else
        print_error "Failed to generate static data"
        exit 1
    fi
}

# Validate static data
validate_static_data() {
    print_status "Validating static data files..."
    
    REQUIRED_FILES=(
        "public/static-data/businesses-minimal.json"
        "public/static-data/businesses-standard.json"
        "public/static-data/businesses-full.json"
        "public/static-data/businesses-active.json"
        "public/static-data/categories.json"
        "public/static-data/metadata.json"
    )
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$file" ]; then
            print_success "✓ $file exists"
        else
            print_error "✗ $file is missing"
            exit 1
        fi
    done
    
    # Validate JSON format
    for file in "${REQUIRED_FILES[@]}"; do
        if node -e "JSON.parse(require('fs').readFileSync('$file', 'utf8'))" 2>/dev/null; then
            print_success "✓ $file is valid JSON"
        else
            print_error "✗ $file contains invalid JSON"
            exit 1
        fi
    done
}

# Build the application
build_application() {
    print_status "Building Next.js application..."
    
    if npm run build; then
        print_success "Application built successfully"
    else
        print_error "Application build failed"
        exit 1
    fi
}

# Test static data endpoints
test_static_endpoints() {
    print_status "Testing static data endpoints..."
    
    # Start the application in background for testing
    npm run start &
    SERVER_PID=$!
    
    # Wait for server to start
    sleep 10
    
    # Test static API endpoint
    if curl -f http://localhost:3000/api/businesses-static?fields=minimal > /dev/null 2>&1; then
        print_success "Static API endpoint is working"
    else
        print_warning "Static API endpoint test failed (this might be normal if server isn't ready)"
    fi
    
    # Test static file access
    if curl -f http://localhost:3000/static-data/businesses-standard.json > /dev/null 2>&1; then
        print_success "Static data files are accessible"
    else
        print_warning "Static data files test failed (this might be normal if server isn't ready)"
    fi
    
    # Stop the test server
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
}

# Main deployment process
main() {
    print_status "=== Fuse VIP Static Data Deployment ==="
    
    # Step 1: Check environment
    check_env_vars
    
    # Step 2: Generate static data
    generate_static_data
    
    # Step 3: Validate generated data
    validate_static_data
    
    # Step 4: Build application
    build_application
    
    # Step 5: Test endpoints (optional, might not work in all environments)
    if [ "$SKIP_ENDPOINT_TEST" != "true" ]; then
        test_static_endpoints
    fi
    
    print_success "=== Deployment completed successfully! ==="
    print_status "Your application is ready to deploy with static businesses data"
    print_status "Static data files are located in: public/static-data/"
    
    # Show summary
    if [ -f "public/static-data/metadata.json" ]; then
        METADATA=$(cat public/static-data/metadata.json)
        TOTAL=$(echo $METADATA | node -e "console.log(JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8')).total)")
        GENERATED_AT=$(echo $METADATA | node -e "console.log(JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8')).generated_at)")
        
        print_status "Summary:"
        print_status "  - Total businesses: $TOTAL"
        print_status "  - Generated at: $GENERATED_AT"
        print_status "  - Static files ready for deployment"
    fi
}

# Handle script interruption
trap 'print_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
