#!/usr/bin/env node

/**
 * Emergency PGRST002 Fix Script
 * This script directly executes schema reload commands to fix PostgREST cache errors
 */

const { createClient } = require('@supabase/supabase-js')
const { Pool } = require('pg')

// Emergency pool configuration optimized for 44 monthly active users
const EMERGENCY_POOL_CONFIG = {
  max: 10,              // Sufficient for emergency operations with 44 users
  min: 2,               // Minimal connections for immediate availability
  connectionTimeoutMillis: 15000,  // 15 seconds
  idleTimeoutMillis: 45000,        // 45 seconds
  acquireTimeoutMillis: 60000,     // 60 seconds
  createTimeoutMillis: 25000,      // 25 seconds
  destroyTimeoutMillis: 3000,      // 3 seconds
  reapIntervalMillis: 1000,        // 1 second
  createRetryIntervalMillis: 200,  // 200ms
  statement_timeout: 45000,        // 45 seconds
  query_timeout: 40000,            // 40 seconds
  application_name: 'fuse-vip-emergency-fix'
}

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://haqbtbpmyadkocakqnew.supabase.co'
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY
const DB_PASSWORD = process.env.SUPABASE_DB_PASSWORD

if (!SUPABASE_SERVICE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required')
  process.exit(1)
}

if (!DB_PASSWORD) {
  console.error('❌ SUPABASE_DB_PASSWORD environment variable is required')
  process.exit(1)
}

// Initialize Supabase admin client
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Direct PostgreSQL connection for emergency operations
const pgPool = new Pool({
  host: 'db.haqbtbpmyadkocakqnew.supabase.co',
  port: 5432,
  database: 'postgres',
  user: 'postgres',
  password: DB_PASSWORD,
  ssl: { rejectUnauthorized: false },
  ...EMERGENCY_POOL_CONFIG
})

async function executeSchemaReload() {
  console.log('🚨 EMERGENCY PGRST002 FIX STARTING...')
  console.log('=' .repeat(50))
  
  let success = false
  const methods = []

  // Method 1: Supabase RPC
  try {
    console.log('🔄 Method 1: Supabase RPC notify...')
    
    const { data, error } = await supabaseAdmin.rpc('exec_sql', {
      sql: "NOTIFY pgrst, 'reload schema'"
    })
    
    if (error) {
      console.warn('⚠️  RPC method failed:', error.message)
      methods.push({ method: 'supabase_rpc', success: false, error: error.message })
    } else {
      console.log('✅ RPC method succeeded')
      methods.push({ method: 'supabase_rpc', success: true })
      success = true
    }
  } catch (error) {
    console.warn('⚠️  RPC method error:', error.message)
    methods.push({ method: 'supabase_rpc', success: false, error: error.message })
  }

  // Method 2: Direct PostgreSQL NOTIFY
  try {
    console.log('🔄 Method 2: Direct PostgreSQL NOTIFY...')
    
    const client = await pgPool.connect()
    
    try {
      await client.query("NOTIFY pgrst, 'reload schema'")
      console.log('✅ Direct PostgreSQL NOTIFY succeeded')
      methods.push({ method: 'direct_pg_notify', success: true })
      success = true
    } finally {
      client.release()
    }
  } catch (error) {
    console.warn('⚠️  Direct PostgreSQL method failed:', error.message)
    methods.push({ method: 'direct_pg_notify', success: false, error: error.message })
  }

  // Method 3: pg_notify function
  try {
    console.log('🔄 Method 3: pg_notify function...')
    
    const client = await pgPool.connect()
    
    try {
      await client.query("SELECT pg_notify('pgrst', 'reload schema')")
      console.log('✅ pg_notify function succeeded')
      methods.push({ method: 'pg_notify_function', success: true })
      success = true
    } finally {
      client.release()
    }
  } catch (error) {
    console.warn('⚠️  pg_notify function failed:', error.message)
    methods.push({ method: 'pg_notify_function', success: false, error: error.message })
  }

  // Method 4: Multiple reload commands
  try {
    console.log('🔄 Method 4: Multiple reload commands...')
    
    const client = await pgPool.connect()
    
    try {
      const commands = [
        "NOTIFY pgrst, 'reload schema'",
        "NOTIFY pgrst, 'reload config'",
        "SELECT pg_notify('pgrst', 'reload schema')",
        "SELECT pg_notify('pgrst', 'reload config')"
      ]
      
      for (const cmd of commands) {
        await client.query(cmd)
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      
      console.log('✅ Multiple reload commands succeeded')
      methods.push({ method: 'multiple_commands', success: true })
      success = true
    } finally {
      client.release()
    }
  } catch (error) {
    console.warn('⚠️  Multiple commands failed:', error.message)
    methods.push({ method: 'multiple_commands', success: false, error: error.message })
  }

  // Wait for changes to propagate
  if (success) {
    console.log('⏳ Waiting 5 seconds for schema reload to propagate...')
    await new Promise(resolve => setTimeout(resolve, 5000))
  }

  // Test the fix
  console.log('🧪 Testing schema reload effectiveness...')
  try {
    const { data: testData, error: testError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .limit(1)

    if (testError) {
      if (testError.message.includes('schema cache') || testError.message.includes('503')) {
        console.error('❌ Schema reload test failed - PGRST002 still present')
        console.error('Error:', testError.message)
        methods.push({ method: 'test_after_reload', success: false, error: testError.message })
      } else {
        console.log('✅ Schema reload test passed - different error (not PGRST002)')
        methods.push({ method: 'test_after_reload', success: true, note: 'Different error, not schema cache' })
      }
    } else {
      console.log('✅ Schema reload test passed - no errors detected')
      methods.push({ method: 'test_after_reload', success: true })
    }
  } catch (error) {
    console.error('❌ Schema reload test error:', error.message)
    methods.push({ method: 'test_after_reload', success: false, error: error.message })
  }

  // Create schema management functions if they don't exist
  try {
    console.log('🔧 Creating schema management functions...')
    
    const client = await pgPool.connect()
    
    try {
      // Create the notify function
      await client.query(`
        CREATE OR REPLACE FUNCTION notify_schema_reload()
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          PERFORM pg_notify('pgrst', 'reload schema');
        END;
        $$;
      `)
      
      // Create the exec_sql function
      await client.query(`
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          IF sql LIKE '%NOTIFY%' OR sql LIKE '%pg_notify%' THEN
            EXECUTE sql;
          ELSE
            RAISE EXCEPTION 'Only NOTIFY operations are allowed through this function';
          END IF;
        END;
        $$;
      `)
      
      console.log('✅ Schema management functions created')
      methods.push({ method: 'create_functions', success: true })
    } finally {
      client.release()
    }
  } catch (error) {
    console.warn('⚠️  Function creation failed:', error.message)
    methods.push({ method: 'create_functions', success: false, error: error.message })
  }

  // Pool health check
  try {
    console.log('🏥 Checking pool health...')
    console.log(`📊 Pool Stats:`)
    console.log(`   Total Connections: ${pgPool.totalCount || 'N/A'}`)
    console.log(`   Active Connections: ${(pgPool.totalCount || 0) - (pgPool.idleCount || 0)}`)
    console.log(`   Idle Connections: ${pgPool.idleCount || 'N/A'}`)
    console.log(`   Waiting Clients: ${pgPool.waitingCount || 'N/A'}`)

    const utilizationRate = pgPool.totalCount ? ((pgPool.totalCount - pgPool.idleCount) / pgPool.totalCount) : 0
    console.log(`   Utilization Rate: ${(utilizationRate * 100).toFixed(1)}%`)

    if (utilizationRate > 0.8) {
      console.log('⚠️  High pool utilization detected - consider increasing pool size')
    }
  } catch (error) {
    console.warn('⚠️  Could not retrieve pool stats:', error.message)
  }

  // Summary
  console.log('\n' + '=' .repeat(50))
  console.log('📊 PGRST002 FIX SUMMARY')
  console.log('=' .repeat(50))
  
  methods.forEach((method, index) => {
    const status = method.success ? '✅' : '❌'
    console.log(`${status} ${index + 1}. ${method.method}`)
    if (method.error) {
      console.log(`   Error: ${method.error}`)
    }
    if (method.note) {
      console.log(`   Note: ${method.note}`)
    }
  })

  const successCount = methods.filter(m => m.success).length
  console.log(`\n📈 Success Rate: ${successCount}/${methods.length} methods succeeded`)

  if (success) {
    console.log('\n🎉 SCHEMA RELOAD COMPLETED!')
    console.log('✅ At least one reload method succeeded')
    console.log('⏳ Changes may take 2-3 minutes to fully propagate')
    console.log('\n📋 NEXT STEPS:')
    console.log('1. Test your app for PGRST002 errors')
    console.log('2. If errors persist, go to Supabase Dashboard → Settings → API')
    console.log('3. Change "Exposed schemas" to only include "public"')
    console.log('4. Save and wait 2-3 minutes')
    console.log('5. Run this script again if needed')
  } else {
    console.log('\n❌ ALL RELOAD METHODS FAILED')
    console.log('🚨 Manual intervention required:')
    console.log('1. Go to Supabase Dashboard: https://supabase.com/dashboard/project/haqbtbpmyadkocakqnew')
    console.log('2. Navigate to Settings → API')
    console.log('3. Change "Exposed schemas" from multiple schemas to only "public"')
    console.log('4. Save configuration and wait 2-3 minutes')
    console.log('5. Contact Supabase support if issues persist')
  }

  console.log('\n🔗 Useful Links:')
  console.log('- Supabase Dashboard: https://supabase.com/dashboard/project/haqbtbpmyadkocakqnew')
  console.log('- Schema Management API: /api/schema-management')
  console.log('- Documentation: SCHEMA_FIX_GUIDE.md')
  
  return { success, methods, successCount }
}

// Run the fix
executeSchemaReload()
  .then(result => {
    process.exit(result.success ? 0 : 1)
  })
  .catch(error => {
    console.error('\n💥 CRITICAL ERROR:', error.message)
    console.error('Stack:', error.stack)
    process.exit(1)
  })
  .finally(() => {
    pgPool.end()
  })
