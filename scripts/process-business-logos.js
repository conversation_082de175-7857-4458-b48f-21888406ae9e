#!/usr/bin/env node

/**
 * Business Logo Processing Script
 * Processes the 74 logos from public/images/BusinessLogos/FuseLogos
 * Uploads to Supabase storage and updates database records
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'
import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

// Load environment variables
config({ path: '.env.local' })

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Configuration
const LOGOS_DIRECTORY = path.join(__dirname, '../public/images/BusinessLogos/FuseLogos')
const SUPPORTED_FORMATS = ['.jpg', '.jpeg', '.png', '.svg', '.webp']
const STORAGE_BUCKET = 'business-logos'

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

class BusinessLogoProcessor {
  constructor() {
    this.results = []
    this.processed = 0
    this.failed = 0
  }

  async log(message) {
    console.log(message)
  }

  async ensureStorageBucket() {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await supabase.storage.listBuckets()
      
      if (listError) {
        throw new Error(`Failed to list buckets: ${listError.message}`)
      }

      const bucketExists = buckets.some(bucket => bucket.name === STORAGE_BUCKET)
      
      if (!bucketExists) {
        await this.log(`📦 Creating storage bucket: ${STORAGE_BUCKET}`)
        const { error: createError } = await supabase.storage.createBucket(STORAGE_BUCKET, {
          public: true,
          allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'],
          fileSizeLimit: 5242880 // 5MB
        })
        
        if (createError) {
          throw new Error(`Failed to create bucket: ${createError.message}`)
        }
        
        await this.log(`✅ Storage bucket created successfully`)
      } else {
        await this.log(`✅ Storage bucket already exists`)
      }
    } catch (error) {
      await this.log(`❌ Storage bucket error: ${error.message}`)
      throw error
    }
  }

  async findLogos() {
    try {
      const files = await fs.readdir(LOGOS_DIRECTORY)
      const logoFiles = []

      for (const file of files) {
        const filePath = path.join(LOGOS_DIRECTORY, file)
        const stats = await fs.stat(filePath)
        
        if (stats.isFile()) {
          const ext = path.extname(file).toLowerCase()
          if (SUPPORTED_FORMATS.includes(ext)) {
            logoFiles.push({
              filename: file,
              path: filePath,
              size: stats.size,
              ext: ext
            })
          }
        }
      }

      await this.log(`📁 Found ${logoFiles.length} logo files`)
      return logoFiles
    } catch (error) {
      throw new Error(`Failed to read directory ${LOGOS_DIRECTORY}: ${error.message}`)
    }
  }

  async uploadLogo(logoFile) {
    try {
      // Read file
      const fileBuffer = await fs.readFile(logoFile.path)
      
      // Generate unique filename
      const timestamp = Date.now()
      const sanitizedName = logoFile.filename.replace(/[^a-zA-Z0-9.-]/g, '_')
      const storageFileName = `${timestamp}_${sanitizedName}`
      
      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from(STORAGE_BUCKET)
        .upload(storageFileName, fileBuffer, {
          contentType: this.getMimeType(logoFile.ext),
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        throw new Error(`Upload failed: ${error.message}`)
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(STORAGE_BUCKET)
        .getPublicUrl(storageFileName)

      return {
        success: true,
        filename: logoFile.filename,
        storageFileName: storageFileName,
        publicUrl: urlData.publicUrl,
        size: logoFile.size,
        mimeType: this.getMimeType(logoFile.ext)
      }
    } catch (error) {
      return {
        success: false,
        filename: logoFile.filename,
        error: error.message
      }
    }
  }

  getMimeType(ext) {
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.svg': 'image/svg+xml',
      '.webp': 'image/webp'
    }
    return mimeTypes[ext.toLowerCase()] || 'application/octet-stream'
  }

  async processAllLogos() {
    await this.log('🚀 Starting business logo processing...')
    
    // Ensure storage bucket exists
    await this.ensureStorageBucket()
    
    // Find all logo files
    const logoFiles = await this.findLogos()
    
    if (logoFiles.length === 0) {
      await this.log('❌ No logo files found')
      return { processed: 0, failed: 0, results: [] }
    }

    await this.log(`📊 Processing ${logoFiles.length} logos...`)
    
    const results = []
    let processed = 0
    let failed = 0

    for (const logoFile of logoFiles) {
      await this.log(`🔄 Processing: ${logoFile.filename}`)
      
      const result = await this.uploadLogo(logoFile)
      results.push(result)
      
      if (result.success) {
        processed++
        await this.log(`✅ Uploaded: ${logoFile.filename} -> ${result.publicUrl}`)
      } else {
        failed++
        await this.log(`❌ Failed: ${logoFile.filename} - ${result.error}`)
      }

      // Small delay between uploads
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    this.results = results
    this.processed = processed
    this.failed = failed
    
    await this.log(`\n📊 Processing Summary:`)
    await this.log(`   ✅ Successfully processed: ${processed}`)
    await this.log(`   ❌ Failed: ${failed}`)
    await this.log(`   📁 Total files: ${logoFiles.length}`)

    return { processed, failed, results }
  }

  async generateReport() {
    const reportPath = path.join(__dirname, '../processed-logos/business-logos-report.json')
    await fs.mkdir(path.dirname(reportPath), { recursive: true })
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.results.length,
        successful: this.processed,
        failed: this.failed
      },
      results: this.results,
      storageInfo: {
        bucket: STORAGE_BUCKET,
        baseUrl: `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${STORAGE_BUCKET}/`
      }
    }

    await fs.writeFile(reportPath, JSON.stringify(report, null, 2))
    await this.log(`📄 Report saved to: ${reportPath}`)
    
    return reportPath
  }
}

// Main execution
async function main() {
  try {
    const processor = new BusinessLogoProcessor()
    const result = await processor.processAllLogos()
    await processor.generateReport()
    
    console.log('\n🎉 Business logo processing completed!')
    process.exit(result.failed > 0 ? 1 : 0)
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`)
    console.error(error.stack)
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export default BusinessLogoProcessor
