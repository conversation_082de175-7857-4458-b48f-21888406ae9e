#!/usr/bin/env node

const path = require('path');
const fs = require('fs').promises;

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

const { Pool } = require('pg');

class BulkBusinessLogoUploader {
  constructor() {
    this.pool = null;
    this.logFile = path.join(__dirname, `bulk-logo-upload-${Date.now()}.log`);
    this.logoDirectory = path.join(__dirname, '..', 'public', 'images', 'BusinessLogos', 'FuseLogos');
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    await fs.appendFile(this.logFile, logMessage + '\n').catch(() => {});
  }

  async initDatabase() {
    if (this.pool) return this.pool;

    const dbPassword = process.env.SUPABASE_DB_PASSWORD;
    if (!dbPassword) {
      throw new Error('SUPABASE_DB_PASSWORD environment variable is required');
    }

    const config = {
      host: 'db.haqbtbpmyadkocakqnew.supabase.co',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: dbPassword,
      ssl: { rejectUnauthorized: false },
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 30000,
      max: 4,
      min: 1
    };

    this.pool = new Pool(config);
    
    // Test connection
    const client = await this.pool.connect();
    await client.query('SELECT 1');
    client.release();
    
    await this.log('✅ Database connection established');
    return this.pool;
  }

  async getBusinesses() {
    const client = await this.pool.connect();
    try {
      const result = await client.query(`
        SELECT id, name, logo_url, category, user_id, created_at
        FROM businesses 
        WHERE is_active = true 
        ORDER BY created_at DESC
      `);
      
      await this.log(`📊 Found ${result.rows.length} businesses in database`);
      return result.rows;
    } finally {
      client.release();
    }
  }

  async getLogoFiles() {
    try {
      const files = await fs.readdir(this.logoDirectory);
      const logoFiles = files.filter(file => 
        file.toLowerCase().endsWith('.png') || 
        file.toLowerCase().endsWith('.jpg') || 
        file.toLowerCase().endsWith('.jpeg') || 
        file.toLowerCase().endsWith('.svg')
      );
      
      await this.log(`📁 Found ${logoFiles.length} logo files in directory`);
      return logoFiles;
    } catch (error) {
      await this.log(`❌ Error reading logo directory: ${error.message}`);
      return [];
    }
  }

  // Smart matching algorithm
  matchBusinessToLogo(business, logoFiles) {
    const businessName = business.name.toLowerCase();
    const businessWords = businessName.split(/\s+/);
    
    // Try exact matches first
    for (const logoFile of logoFiles) {
      const logoName = logoFile.toLowerCase().replace(/\.(png|jpg|jpeg|svg)$/, '');
      
      // Direct name match
      if (logoName === businessName || businessName.includes(logoName)) {
        return logoFile;
      }
      
      // Reverse match
      if (logoName.includes(businessName)) {
        return logoFile;
      }
      
      // Word-based matching
      for (const word of businessWords) {
        if (word.length > 3 && logoName.includes(word)) {
          return logoFile;
        }
      }
    }
    
    return null;
  }

  async uploadLogoToVercel(logoFile) {
    try {
      const logoPath = path.join(this.logoDirectory, logoFile);
      const logoBuffer = await fs.readFile(logoPath);
      
      // Upload to Vercel Blob
      const response = await fetch('https://api.vercel.com/v1/blob', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.BLOB_READ_WRITE_TOKEN}`,
          'Content-Type': 'application/octet-stream',
        },
        body: logoBuffer,
      });
      
      if (!response.ok) {
        throw new Error(`Vercel upload failed: ${response.statusText}`);
      }
      
      const result = await response.json();
      await this.log(`🔗 Uploaded ${logoFile} to Vercel: ${result.url}`);
      return result.url;
    } catch (error) {
      await this.log(`❌ Failed to upload ${logoFile} to Vercel: ${error.message}`);
      
      // Fallback to local path
      const localPath = `/images/BusinessLogos/FuseLogos/${logoFile}`;
      await this.log(`📁 Using local path fallback: ${localPath}`);
      return localPath;
    }
  }

  async updateBusinessLogo(businessId, logoUrl) {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        'UPDATE businesses SET logo_url = $1, updated_at = NOW() WHERE id = $2 RETURNING name',
        [logoUrl, businessId]
      );
      
      if (result.rows.length > 0) {
        await this.log(`✅ Updated logo for business: ${result.rows[0].name}`);
        return true;
      }
      
      return false;
    } finally {
      client.release();
    }
  }

  async processLogos() {
    await this.log('🚀 Starting bulk logo upload process...');
    
    try {
      await this.initDatabase();
      
      const businesses = await this.getBusinesses();
      const logoFiles = await this.getLogoFiles();
      
      if (logoFiles.length === 0) {
        await this.log('❌ No logo files found in directory');
        return;
      }
      
      const matches = [];
      const unmatched = [];
      
      // Match businesses to logos
      for (const business of businesses) {
        const matchedLogo = this.matchBusinessToLogo(business, logoFiles);
        
        if (matchedLogo) {
          matches.push({
            business,
            logoFile: matchedLogo,
            currentLogoUrl: business.logo_url
          });
        } else {
          unmatched.push(business);
        }
      }
      
      await this.log(`🎯 Matched ${matches.length} businesses to logos`);
      await this.log(`❓ ${unmatched.length} businesses without logo matches`);
      
      // List all matches for review
      await this.log('\n📋 MATCHED BUSINESSES:');
      for (const match of matches) {
        await this.log(`   • ${match.business.name} → ${match.logoFile}`);
      }
      
      // Process uploads and updates
      let successCount = 0;
      let errorCount = 0;
      
      for (const match of matches) {
        try {
          // Only update if business doesn't already have a logo
          if (!match.currentLogoUrl || match.currentLogoUrl === 'placeholder.svg') {
            const logoUrl = await this.uploadLogoToVercel(match.logoFile);
            const success = await this.updateBusinessLogo(match.business.id, logoUrl);
            
            if (success) {
              successCount++;
            } else {
              errorCount++;
            }
          } else {
            await this.log(`⏭️  Skipped ${match.business.name} - already has logo`);
          }
          
          // Add small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (error) {
          await this.log(`❌ Error processing ${match.business.name}: ${error.message}`);
          errorCount++;
        }
      }
      
      await this.log(`\n📊 FINAL RESULTS:`);
      await this.log(`✅ Successfully updated: ${successCount} businesses`);
      await this.log(`❌ Errors: ${errorCount}`);
      await this.log(`⏭️  Skipped (already have logos): ${matches.length - successCount - errorCount}`);
      await this.log(`❓ Unmatched businesses: ${unmatched.length}`);
      
      // List unmatched businesses for manual review
      if (unmatched.length > 0) {
        await this.log('\n❓ UNMATCHED BUSINESSES (need manual logo assignment):');
        for (const business of unmatched) {
          await this.log(`   • ${business.name} (ID: ${business.id})`);
        }
      }
      
    } catch (error) {
      await this.log(`❌ Fatal error: ${error.message}`);
      throw error;
    } finally {
      if (this.pool) {
        await this.pool.end();
      }
    }
  }
}

// Run the script
async function main() {
  const uploader = new BulkBusinessLogoUploader();
  await uploader.processLogos();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = BulkBusinessLogoUploader;