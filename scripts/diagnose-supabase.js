#!/usr/bin/env node

/**
 * Supabase Connection and Businesses Table Diagnostic Script
 * 
 * This script tests the Supabase connection and specifically diagnoses
 * issues with the businesses table that's causing 503 errors.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Color console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function main() {
  log('🔍 Supabase Connection Diagnostic Tool', 'cyan');
  log('=====================================', 'cyan');
  
  // Check environment variables
  log('\n1. Environment Variables Check:', 'blue');
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const dbPassword = process.env.SUPABASE_DB_PASSWORD;
  
  log(`   SUPABASE_URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`, supabaseUrl ? 'green' : 'red');
  log(`   ANON_KEY: ${supabaseAnonKey ? '✅ Set' : '❌ Missing'}`, supabaseAnonKey ? 'green' : 'red');
  log(`   SERVICE_ROLE_KEY: ${serviceRoleKey ? '✅ Set' : '❌ Missing'}`, serviceRoleKey ? 'green' : 'red');
  log(`   DB_PASSWORD: ${dbPassword ? '✅ Set' : '❌ Missing'}`, dbPassword ? 'green' : 'red');
  
  if (!supabaseUrl || !supabaseAnonKey) {
    log('\n❌ Required environment variables missing. Cannot proceed.', 'red');
    process.exit(1);
  }
  
  // Create clients
  const anonClient = createClient(supabaseUrl, supabaseAnonKey);
  const serviceClient = serviceRoleKey ? createClient(supabaseUrl, serviceRoleKey) : null;
  
  log('\n2. Basic Connection Tests:', 'blue');
  
  // Test 1: Simple auth check
  try {
    const { data, error } = await anonClient.auth.getSession();
    log(`   Auth Service: ${error ? '❌ Error' : '✅ Connected'}`, error ? 'red' : 'green');
    if (error) log(`      Error: ${error.message}`, 'red');
  } catch (err) {
    log(`   Auth Service: ❌ Exception - ${err.message}`, 'red');
  }
  
  // Test 2: Database connection
  try {
    const { data, error } = await anonClient.from('profiles').select('id').limit(1);
    log(`   Database: ${error ? '❌ Error' : '✅ Connected'}`, error ? 'red' : 'green');
    if (error) log(`      Error: ${error.message}`, 'red');
  } catch (err) {
    log(`   Database: ❌ Exception - ${err.message}`, 'red');
  }
  
  log('\n3. Businesses Table Specific Tests:', 'blue');
  
  // Test 3: Businesses table structure
  const businessesTests = [
    {
      name: 'Basic SELECT',
      query: () => anonClient.from('businesses').select('id').limit(1)
    },
    {
      name: 'Count active businesses',
      query: () => anonClient.from('businesses').select('*', { count: 'exact', head: true }).eq('is_active', true)
    },
    {
      name: 'Select with all columns',
      query: () => anonClient.from('businesses').select('*').eq('is_active', true).limit(1)
    },
    {
      name: 'Check RLS policies',
      query: () => anonClient.from('businesses').select('id, name, category, is_active').eq('is_active', true).limit(5)
    }
  ];
  
  for (const test of businessesTests) {
    try {
      const startTime = Date.now();
      const { data, error, count } = await test.query();
      const responseTime = Date.now() - startTime;
      
      if (error) {
        log(`   ${test.name}: ❌ Error (${responseTime}ms)`, 'red');
        log(`      Error: ${error.message}`, 'red');
        log(`      Code: ${error.code || 'N/A'}`, 'red');
        log(`      Details: ${error.details || 'N/A'}`, 'red');
      } else {
        log(`   ${test.name}: ✅ Success (${responseTime}ms)`, 'green');
        if (count !== undefined) log(`      Count: ${count}`, 'green');
        if (data && data.length > 0) log(`      Sample data: ${JSON.stringify(data[0], null, 2)}`, 'green');
      }
    } catch (err) {
      log(`   ${test.name}: ❌ Exception - ${err.message}`, 'red');
    }
  }
  
  // Test 4: Service role tests (if available)
  if (serviceClient) {
    log('\n4. Service Role Tests:', 'blue');
    
    try {
      const { data, error } = await serviceClient.from('businesses').select('*').limit(1);
      log(`   Service Role Query: ${error ? '❌ Error' : '✅ Success'}`, error ? 'red' : 'green');
      if (error) log(`      Error: ${error.message}`, 'red');
    } catch (err) {
      log(`   Service Role Query: ❌ Exception - ${err.message}`, 'red');
    }
  }
  
  // Test 5: Database functions test
  log('\n5. Database Functions Test:', 'blue');
  
  const functionTests = [
    'get_businesses_minimal',
    'get_businesses_standard', 
    'get_businesses_full'
  ];
  
  for (const functionName of functionTests) {
    try {
      const { data, error } = await anonClient.rpc(functionName);
      log(`   ${functionName}(): ${error ? '❌ Error' : '✅ Success'}`, error ? 'red' : 'green');
      if (error) log(`      Error: ${error.message}`, 'red');
    } catch (err) {
      log(`   ${functionName}(): ❌ Exception - ${err.message}`, 'red');
    }
  }
  
  // Test 6: Schema information
  log('\n6. Schema Information:', 'blue');
  
  if (serviceClient) {
    try {
      // Check if businesses table exists and get its structure
      const { data: tableInfo, error: tableError } = await serviceClient
        .from('information_schema.tables')
        .select('*')
        .eq('table_schema', 'public')
        .eq('table_name', 'businesses');
      
      if (tableError) {
        log(`   Table existence check: ❌ Error - ${tableError.message}`, 'red');
      } else if (tableInfo && tableInfo.length > 0) {
        log(`   Table exists: ✅ Yes`, 'green');
        
        // Get column information
        const { data: columns, error: columnsError } = await serviceClient
          .from('information_schema.columns')
          .select('column_name, data_type, is_nullable')
          .eq('table_schema', 'public')
          .eq('table_name', 'businesses')
          .order('ordinal_position');
        
        if (!columnsError && columns) {
          log(`   Columns (${columns.length}):`, 'green');
          columns.forEach(col => {
            log(`      ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`, 'green');
          });
        }
      } else {
        log(`   Table exists: ❌ No`, 'red');
      }
    } catch (err) {
      log(`   Schema check: ❌ Exception - ${err.message}`, 'red');
    }
  }
  
  // Test 7: Connection health over time
  log('\n7. Connection Stability Test (5 rapid requests):', 'blue');
  
  const stabilityResults = [];
  for (let i = 0; i < 5; i++) {
    const startTime = Date.now();
    try {
      const { error } = await anonClient.from('businesses').select('id').limit(1);
      const responseTime = Date.now() - startTime;
      stabilityResults.push({ success: !error, time: responseTime, error: error?.message });
    } catch (err) {
      const responseTime = Date.now() - startTime;
      stabilityResults.push({ success: false, time: responseTime, error: err.message });
    }
  }
  
  const successRate = (stabilityResults.filter(r => r.success).length / stabilityResults.length) * 100;
  const avgResponseTime = stabilityResults.reduce((sum, r) => sum + r.time, 0) / stabilityResults.length;
  
  log(`   Success Rate: ${successRate}% (${stabilityResults.filter(r => r.success).length}/5)`, successRate === 100 ? 'green' : 'yellow');
  log(`   Average Response Time: ${avgResponseTime.toFixed(2)}ms`, 'green');
  
  stabilityResults.forEach((result, index) => {
    if (!result.success) {
      log(`      Request ${index + 1}: ❌ ${result.error} (${result.time}ms)`, 'red');
    }
  });
  
  // Summary
  log('\n8. Summary & Recommendations:', 'magenta');
  
  if (successRate < 100) {
    log('   ⚠️  Connection instability detected', 'yellow');
    log('   Recommendations:', 'yellow');
    log('   - Check Supabase project status', 'yellow');
    log('   - Verify database password is correct', 'yellow');
    log('   - Consider implementing retry logic', 'yellow');
  } else {
    log('   ✅ All tests passed successfully', 'green');
  }
  
  log('\n🏁 Diagnostic complete!', 'cyan');
}

main().catch(err => {
  log(`\n💥 Diagnostic script failed: ${err.message}`, 'red');
  console.error(err);
  process.exit(1);
});