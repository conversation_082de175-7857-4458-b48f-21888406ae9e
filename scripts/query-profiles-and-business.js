/**
 * <PERSON><PERSON>t to query profiles table and examine business ownership for "The Art of Pitching Corp"
 * This script will help identify the current authenticated user and resolve business ownership
 */

// Use dynamic import for TypeScript module
let executeQuery;

async function initializeDatabase() {
  if (!executeQuery) {
    const databaseModule = await import('../lib/database-direct.ts');
    executeQuery = databaseModule.executeQuery;
  }
  return executeQuery;
}

async function queryAllProfiles() {
  await initializeDatabase();
  console.log('👥 Querying ALL user profiles from database...\n');
  
  const query = `
    SELECT 
      id,
      first_name,
      last_name,
      user_email,
      is_card_holder,
      is_business_applicant,
      phone,
      xrp_wallet_address,
      created_at,
      updated_at
    FROM profiles 
    ORDER BY created_at DESC
  `;
  
  const { data, error } = await executeQuery(query, []);
  
  if (error) {
    console.error('❌ Error querying profiles:', error);
    return null;
  }
  
  if (!data || data.length === 0) {
    console.log('📭 No profiles found in database');
    return null;
  }
  
  console.log(`✅ Found ${data.length} profile(s) in database:\n`);
  
  data.forEach((profile, index) => {
    console.log(`--- Profile ${index + 1} ---`);
    console.log(`ID: ${profile.id}`);
    console.log(`Name: ${profile.first_name || ''} ${profile.last_name || ''}`);
    console.log(`Email: ${profile.user_email || 'N/A'}`);
    console.log(`Phone: ${profile.phone || 'N/A'}`);
    console.log(`XRP Wallet: ${profile.xrp_wallet_address || 'N/A'}`);
    console.log(`Card Holder: ${profile.is_card_holder}`);
    console.log(`Business Applicant: ${profile.is_business_applicant}`);
    console.log(`Created: ${profile.created_at}`);
    console.log(`Updated: ${profile.updated_at}`);
    console.log('');
  });
  
  return data;
}

async function queryArtOfPitchingBusiness() {
  await initializeDatabase();
  console.log('🎯 Querying "The Art of Pitching" business details...\n');
  
  const query = `
    SELECT 
      id,
      name,
      user_id,
      category,
      website,
      contact_email,
      contact_name,
      contact_phone,
      business_address,
      premium_discount,
      logo_url,
      is_active,
      created_at,
      updated_at
    FROM businesses 
    WHERE name ILIKE $1
    ORDER BY created_at DESC
  `;
  
  const { data, error } = await executeQuery(query, ['%Art of Pitching%']);
  
  if (error) {
    console.error('❌ Error querying business:', error);
    return null;
  }
  
  if (!data || data.length === 0) {
    console.log('📭 No business found with name containing "Art of Pitching"');
    return null;
  }
  
  console.log(`✅ Found ${data.length} business(es) matching "Art of Pitching":\n`);
  
  data.forEach((business, index) => {
    console.log(`--- Business ${index + 1} ---`);
    console.log(`ID: ${business.id}`);
    console.log(`Name: ${business.name}`);
    console.log(`Current User ID: ${business.user_id || 'NULL'}`);
    console.log(`Category: ${business.category || 'N/A'}`);
    console.log(`Website: ${business.website || 'N/A'}`);
    console.log(`Contact Email: ${business.contact_email || 'N/A'}`);
    console.log(`Contact Name: ${business.contact_name || 'N/A'}`);
    console.log(`Contact Phone: ${business.contact_phone || 'N/A'}`);
    console.log(`Address: ${business.business_address || 'N/A'}`);
    console.log(`Premium Discount: ${business.premium_discount || 'N/A'}%`);
    console.log(`Logo URL: ${business.logo_url || 'N/A'}`);
    console.log(`Active: ${business.is_active}`);
    console.log(`Created: ${business.created_at}`);
    console.log(`Updated: ${business.updated_at}`);
    console.log('');
  });
  
  return data;
}

async function querySpecificUser(userId) {
  await initializeDatabase();
  console.log(`🔍 Querying specific user: ${userId}...\n`);
  
  const query = `
    SELECT 
      id,
      first_name,
      last_name,
      user_email,
      is_card_holder,
      is_business_applicant,
      phone,
      xrp_wallet_address,
      created_at,
      updated_at
    FROM profiles 
    WHERE id = $1
  `;
  
  const { data, error } = await executeQuery(query, [userId]);
  
  if (error) {
    console.error('❌ Error querying specific user:', error);
    return null;
  }
  
  if (!data || data.length === 0) {
    console.log(`📭 No profile found for user ID: ${userId}`);
    return null;
  }
  
  const profile = data[0];
  console.log(`✅ Found profile for user: ${userId}\n`);
  console.log(`ID: ${profile.id}`);
  console.log(`Name: ${profile.first_name || ''} ${profile.last_name || ''}`);
  console.log(`Email: ${profile.user_email || 'N/A'}`);
  console.log(`Phone: ${profile.phone || 'N/A'}`);
  console.log(`XRP Wallet: ${profile.xrp_wallet_address || 'N/A'}`);
  console.log(`Card Holder: ${profile.is_card_holder}`);
  console.log(`Business Applicant: ${profile.is_business_applicant}`);
  console.log(`Created: ${profile.created_at}`);
  console.log(`Updated: ${profile.updated_at}`);
  console.log('');
  
  return profile;
}

async function searchProfilesByEmail(email) {
  await initializeDatabase();
  console.log(`📧 Searching profiles by email: ${email}...\n`);
  
  const query = `
    SELECT 
      id,
      first_name,
      last_name,
      user_email,
      is_card_holder,
      is_business_applicant,
      phone,
      xrp_wallet_address,
      created_at,
      updated_at
    FROM profiles 
    WHERE user_email ILIKE $1
  `;
  
  const { data, error } = await executeQuery(query, [`%${email}%`]);
  
  if (error) {
    console.error('❌ Error searching profiles by email:', error);
    return null;
  }
  
  if (!data || data.length === 0) {
    console.log(`📭 No profiles found for email: ${email}`);
    return null;
  }
  
  console.log(`✅ Found ${data.length} profile(s) matching email: ${email}\n`);
  
  data.forEach((profile, index) => {
    console.log(`--- Profile ${index + 1} ---`);
    console.log(`ID: ${profile.id}`);
    console.log(`Name: ${profile.first_name || ''} ${profile.last_name || ''}`);
    console.log(`Email: ${profile.user_email || 'N/A'}`);
    console.log(`Phone: ${profile.phone || 'N/A'}`);
    console.log(`XRP Wallet: ${profile.xrp_wallet_address || 'N/A'}`);
    console.log(`Card Holder: ${profile.is_card_holder}`);
    console.log(`Business Applicant: ${profile.is_business_applicant}`);
    console.log(`Created: ${profile.created_at}`);
    console.log(`Updated: ${profile.updated_at}`);
    console.log('');
  });
  
  return data;
}

async function main() {
  try {
    console.log('🔍 FUSE VIP - Profile and Business Ownership Query');
    console.log('=================================================\n');
    
    // Step 1: Query all profiles
    console.log('STEP 1: Querying all user profiles...');
    const profiles = await queryAllProfiles();
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Step 2: Query "The Art of Pitching" business
    console.log('STEP 2: Querying "The Art of Pitching" business...');
    const businesses = await queryArtOfPitchingBusiness();
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Step 3: Query the current user ID assigned to the business
    if (businesses && businesses.length > 0) {
      const currentUserId = businesses[0].user_id;
      console.log(`STEP 3: Querying current business owner (${currentUserId})...`);
      await querySpecificUser(currentUserId);
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Step 4: Search for profiles with "lam10studios" email
    console.log('STEP 4: Searching for profiles with "lam10studios" email...');
    await searchProfilesByEmail('lam10studios');
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Step 5: Search for profiles with "fuse.vip" email
    console.log('STEP 5: Searching for profiles with "fuse.vip" email...');
    await searchProfilesByEmail('fuse.vip');
    
    console.log('\n📝 SUMMARY:');
    console.log('===========');
    console.log('• Review the profiles above to identify the current authenticated user');
    console.log('• Check which user should actually own "The Art of Pitching Corp" business');
    console.log('• The business is currently assigned to user ID: 58dc33cf-44eb-4fa2-9da2-789db8a12913');
    console.log('• If you need to update the business ownership, use the update script');
    console.log('');
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run the script
main().catch(console.error);