#!/usr/bin/env node

/**
 * Static Data Generation Script - API Based
 * 
 * This script generates static data by calling your existing API endpoints
 * instead of direct database access, avoiding connection pool issues.
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Import fetch for Node.js
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Output directory for static data
const STATIC_DATA_DIR = path.join(process.cwd(), 'public', 'static-data');

// Ensure static data directory exists
if (!fs.existsSync(STATIC_DATA_DIR)) {
  fs.mkdirSync(STATIC_DATA_DIR, { recursive: true });
}

/**
 * Start Next.js dev server temporarily for API access
 */
async function startDevServer() {
  return new Promise((resolve, reject) => {
    console.log('🚀 Starting temporary Next.js dev server...');

    const server = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      env: { ...process.env, NODE_ENV: 'development' }
    });

    let serverReady = false;
    let serverPort = 3000;
    let startupTimeout;

    const cleanup = () => {
      if (startupTimeout) clearTimeout(startupTimeout);
      server.kill('SIGTERM');
    };

    server.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('📡 Server:', output.trim());

      // Extract port from output
      const portMatch = output.match(/localhost:(\d+)/);
      if (portMatch) {
        serverPort = parseInt(portMatch[1]);
      }

      if (output.includes('Ready in') || output.includes('Local:') || output.includes('localhost:')) {
        if (!serverReady) {
          serverReady = true;
          console.log(`✅ Dev server is ready on port ${serverPort}`);
          resolve({ server, cleanup, port: serverPort });
        }
      }
    });

    server.stderr.on('data', (data) => {
      console.log('📡 Server Error:', data.toString().trim());
    });

    server.on('error', (error) => {
      cleanup();
      reject(new Error(`Failed to start dev server: ${error.message}`));
    });

    server.on('exit', (code) => {
      if (!serverReady) {
        cleanup();
        reject(new Error(`Dev server exited with code ${code}`));
      }
    });

    // Timeout after 60 seconds
    startupTimeout = setTimeout(() => {
      if (!serverReady) {
        cleanup();
        reject(new Error('Dev server startup timeout'));
      }
    }, 60000);
  });
}

/**
 * Fetch data from API endpoint with retry
 */
async function fetchFromAPI(endpoint, port = 3000, maxRetries = 5) {
  const baseUrl = `http://localhost:${port}`;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`📡 Fetching ${endpoint} (attempt ${attempt})`);
      
      const response = await fetch(`${baseUrl}${endpoint}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(`API Error: ${data.error || 'Unknown error'}`);
      }
      
      console.log(`✅ Successfully fetched ${endpoint}`);
      return data.data;
      
    } catch (error) {
      console.log(`❌ Attempt ${attempt} failed for ${endpoint}:`, error.message);
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Exponential backoff with longer delays for database connection issues
      const delay = Math.min(Math.pow(2, attempt) * 3000, 30000); // Max 30 seconds
      console.log(`⏳ Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

/**
 * Generate businesses data from API endpoints
 */
async function generateBusinessesData(port = 3000) {
  console.log('🔄 Fetching businesses data from API endpoints...');

  try {
    // Fetch different data sets from consolidated API
    const [fullBusinesses, activeBusinesses] = await Promise.all([
      fetchFromAPI('/api/businesses?fields=full&limit=500', port),
      fetchFromAPI('/api/businesses?fields=standard&limit=500', port)
    ]);

    console.log(`📊 Fetched ${fullBusinesses.length} full businesses`);
    console.log(`📊 Fetched ${activeBusinesses.length} active businesses`);

    // Generate minimal businesses data
    const minimalBusinesses = fullBusinesses.map(business => ({
      id: business.id,
      name: business.name,
      logo_url: business.logo_url,
      website: business.website,
      premium_discount: business.premium_discount,
      business_spotlight: business.business_spotlight
    }));

    // Generate standard businesses data (use activeBusinesses as it's already in standard format)
    const standardBusinesses = activeBusinesses;

    // Filter spotlight businesses
    const spotlightBusinesses = fullBusinesses.filter(b => b.business_spotlight);

    // Extract categories
    const categories = [...new Set(fullBusinesses.map(b => b.category).filter(Boolean))];

    console.log(`✅ Processed ${fullBusinesses.length} businesses successfully`);
    console.log(`📊 Categories: ${categories.length}`);
    console.log(`⭐ Spotlight businesses: ${spotlightBusinesses.length}`);

    return {
      full: fullBusinesses,
      standard: standardBusinesses,
      minimal: minimalBusinesses,
      active: activeBusinesses,
      spotlight: spotlightBusinesses,
      categories,
      metadata: {
        total: fullBusinesses.length,
        active: activeBusinesses.length,
        spotlight: spotlightBusinesses.length,
        categories: categories.length,
        generated_at: new Date().toISOString(),
        version: '1.0.0',
        source: 'api_endpoints'
      }
    };

  } catch (error) {
    console.error('❌ Error fetching businesses data from API:', error);
    throw error;
  }
}

/**
 * Generate static JSON files
 */
async function generateStaticFiles(businessesData) {
  console.log('📝 Generating static JSON files...');

  const files = [
    { name: 'businesses-full.json', data: businessesData.full },
    { name: 'businesses-standard.json', data: businessesData.standard },
    { name: 'businesses-minimal.json', data: businessesData.minimal },
    { name: 'businesses-active.json', data: businessesData.active },
    { name: 'businesses-spotlight.json', data: businessesData.spotlight },
    { name: 'categories.json', data: businessesData.categories },
    { name: 'metadata.json', data: businessesData.metadata }
  ];

  for (const file of files) {
    const filePath = path.join(STATIC_DATA_DIR, file.name);
    const jsonContent = JSON.stringify(file.data, null, 2);
    
    fs.writeFileSync(filePath, jsonContent, 'utf8');
    console.log(`✅ Generated: ${file.name} (${Array.isArray(file.data) ? file.data.length : 'N/A'} items)`);
  }

  // Generate index file
  const indexData = {
    files: files.map(f => f.name),
    metadata: businessesData.metadata,
    usage: {
      'businesses-minimal.json': 'For carousels, home page, fast loading',
      'businesses-standard.json': 'For industry page, listings, most UI',
      'businesses-full.json': 'For admin, detailed views, editing',
      'businesses-active.json': 'All active businesses (most common)',
      'businesses-spotlight.json': 'Featured/spotlight businesses only',
      'categories.json': 'List of all business categories',
      'metadata.json': 'Generation metadata and stats'
    }
  };

  fs.writeFileSync(
    path.join(STATIC_DATA_DIR, 'index.json'),
    JSON.stringify(indexData, null, 2),
    'utf8'
  );

  console.log('✅ Generated: index.json');
}

/**
 * Main execution
 */
async function main() {
  console.log('🚀 Starting API-based static data generation...');
  console.log(`📁 Output directory: ${STATIC_DATA_DIR}`);

  let serverInfo = null;

  try {
    // Start dev server
    serverInfo = await startDevServer();
    
    // Wait longer for server to fully initialize and database connections to stabilize
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Generate data
    const businessesData = await generateBusinessesData(serverInfo.port);
    await generateStaticFiles(businessesData);

    console.log('\n🎉 Static data generation completed successfully!');
    console.log(`📊 Generated ${businessesData.metadata.total} businesses in multiple formats`);
    console.log(`📁 Files saved to: ${STATIC_DATA_DIR}`);
    
  } catch (error) {
    console.error('\n❌ Static data generation failed:', error);
    process.exit(1);
  } finally {
    // Clean up server
    if (serverInfo) {
      console.log('🧹 Shutting down dev server...');
      serverInfo.cleanup();
      
      // Wait for cleanup
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, generateBusinessesData, generateStaticFiles };
