#!/usr/bin/env node

/**
 * Get Public Schema from Supabase
 * 
 * This script retrieves the complete public schema structure from Supabase
 * including all tables, columns, functions, and other schema objects.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Color console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function main() {
  log('📋 Supabase Public Schema Retrieval Tool', 'cyan');
  log('=========================================', 'cyan');
  
  // Check environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !serviceRoleKey) {
    log('❌ Missing required environment variables (SUPABASE_URL or SERVICE_ROLE_KEY)', 'red');
    process.exit(1);
  }
  
  // Create service role client for admin operations
  const serviceClient = createClient(supabaseUrl, serviceRoleKey);
  
  log('\n🔍 Retrieving Public Schema Information...', 'blue');
  
  try {
    // Get all tables in public schema
    log('\n1. Tables in Public Schema:', 'magenta');
    const { data: tables, error: tablesError } = await serviceClient
      .from('information_schema.tables')
      .select('table_name, table_type')
      .eq('table_schema', 'public')
      .order('table_name');
    
    if (tablesError) {
      log(`❌ Error retrieving tables: ${tablesError.message}`, 'red');
    } else {
      log(`Found ${tables.length} tables:`, 'green');
      tables.forEach(table => {
        log(`   📄 ${table.table_name} (${table.table_type})`, 'green');
      });
    }
    
    // Get all columns for each table
    log('\n2. Table Structures:', 'magenta');
    if (tables && !tablesError) {
      for (const table of tables) {
        if (table.table_type === 'BASE TABLE') {
          log(`\n   📋 ${table.table_name}:`, 'yellow');
          
          const { data: columns, error: columnsError } = await serviceClient
            .from('information_schema.columns')
            .select('column_name, data_type, is_nullable, column_default')
            .eq('table_schema', 'public')
            .eq('table_name', table.table_name)
            .order('ordinal_position');
          
          if (columnsError) {
            log(`      ❌ Error getting columns: ${columnsError.message}`, 'red');
          } else {
            columns.forEach(col => {
              const nullable = col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
              const defaultVal = col.column_default ? ` DEFAULT ${col.column_default}` : '';
              log(`      🔸 ${col.column_name}: ${col.data_type} ${nullable}${defaultVal}`, 'green');
            });
          }
        }
      }
    }
    
    // Get all functions in public schema
    log('\n3. Functions in Public Schema:', 'magenta');
    const { data: functions, error: functionsError } = await serviceClient
      .from('information_schema.routines')
      .select('routine_name, routine_type, data_type')
      .eq('routine_schema', 'public')
      .order('routine_name');
    
    if (functionsError) {
      log(`❌ Error retrieving functions: ${functionsError.message}`, 'red');
    } else {
      log(`Found ${functions.length} functions:`, 'green');
      functions.forEach(func => {
        log(`   ⚙️  ${func.routine_name}() -> ${func.data_type || 'void'}`, 'green');
      });
    }
    
    // Get all indexes
    log('\n4. Indexes in Public Schema:', 'magenta');
    const { data: indexes, error: indexesError } = await serviceClient
      .from('pg_indexes')
      .select('tablename, indexname, indexdef')
      .eq('schemaname', 'public')
      .order('tablename, indexname');
    
    if (indexesError) {
      log(`❌ Error retrieving indexes: ${indexesError.message}`, 'red');
    } else {
      log(`Found ${indexes.length} indexes:`, 'green');
      let currentTable = '';
      indexes.forEach(index => {
        if (index.tablename !== currentTable) {
          currentTable = index.tablename;
          log(`   📊 ${currentTable}:`, 'yellow');
        }
        log(`      🔗 ${index.indexname}`, 'green');
      });
    }
    
    // Get RLS policies
    log('\n5. Row Level Security Policies:', 'magenta');
    const { data: policies, error: policiesError } = await serviceClient
      .from('pg_policies')
      .select('tablename, policyname, permissive, roles, cmd, qual, with_check')
      .eq('schemaname', 'public')
      .order('tablename, policyname');
    
    if (policiesError) {
      log(`❌ Error retrieving policies: ${policiesError.message}`, 'red');
    } else {
      log(`Found ${policies.length} RLS policies:`, 'green');
      let currentTable = '';
      policies.forEach(policy => {
        if (policy.tablename !== currentTable) {
          currentTable = policy.tablename;
          log(`   🔐 ${currentTable}:`, 'yellow');
        }
        log(`      📝 ${policy.policyname} (${policy.cmd})`, 'green');
      });
    }
    
    // Generate schema export
    log('\n6. Generating Schema Export:', 'magenta');
    const schemaExport = {
      timestamp: new Date().toISOString(),
      tables: tables || [],
      functions: functions || [],
      indexes: indexes || [],
      policies: policies || []
    };
    
    // Save to file
    const fs = require('fs');
    const schemaPath = './public-schema-export.json';
    fs.writeFileSync(schemaPath, JSON.stringify(schemaExport, null, 2));
    log(`✅ Schema exported to: ${schemaPath}`, 'green');
    
    // Summary
    log('\n7. Schema Summary:', 'cyan');
    log(`   📄 Tables: ${tables?.length || 0}`, 'green');
    log(`   ⚙️  Functions: ${functions?.length || 0}`, 'green');
    log(`   🔗 Indexes: ${indexes?.length || 0}`, 'green');
    log(`   🔐 RLS Policies: ${policies?.length || 0}`, 'green');
    
  } catch (err) {
    log(`💥 Schema retrieval failed: ${err.message}`, 'red');
    console.error(err);
    process.exit(1);
  }
  
  log('\n🏁 Schema retrieval complete!', 'cyan');
}

main().catch(err => {
  log(`\n💥 Script failed: ${err.message}`, 'red');
  console.error(err);
  process.exit(1);
});