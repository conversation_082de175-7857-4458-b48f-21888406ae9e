#!/usr/bin/env node

/**
 * Storage Diagnostics and Rate Limit Recovery Tool
 * Helps diagnose and resolve Supabase Storage rate limiting issues
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

class StorageDiagnostics {
  constructor() {
    this.results = {
      buckets: [],
      recentUploads: [],
      errors: [],
      recommendations: []
    }
  }

  async log(message) {
    console.log(`[${new Date().toISOString()}] ${message}`)
  }

  async checkStorageHealth() {
    await this.log('🔍 Checking Supabase Storage health...')
    
    try {
      const start = Date.now()
      const { data: buckets, error } = await supabase.storage.listBuckets()
      const duration = Date.now() - start
      
      if (error) {
        this.results.errors.push(`Storage health check failed: ${error.message}`)
        await this.log(`❌ Storage health check failed: ${error.message}`)
        return false
      }
      
      this.results.buckets = buckets
      await this.log(`✅ Storage health check passed (${duration}ms)`)
      await this.log(`📦 Found ${buckets.length} storage buckets`)
      
      return true
    } catch (error) {
      this.results.errors.push(`Storage health check error: ${error.message}`)
      await this.log(`❌ Storage health check error: ${error.message}`)
      return false
    }
  }

  async checkBusinessAssetsBucket() {
    await this.log('🔍 Checking business-assets bucket...')
    
    try {
      const { data: files, error } = await supabase.storage
        .from('business-assets')
        .list('business-logos', {
          limit: 10,
          sortBy: { column: 'created_at', order: 'desc' }
        })
      
      if (error) {
        this.results.errors.push(`Business assets bucket check failed: ${error.message}`)
        await this.log(`❌ Business assets bucket check failed: ${error.message}`)
        return false
      }
      
      this.results.recentUploads = files || []
      await this.log(`✅ Business assets bucket accessible`)
      await this.log(`📁 Found ${files?.length || 0} recent logo files`)
      
      return true
    } catch (error) {
      this.results.errors.push(`Business assets bucket error: ${error.message}`)
      await this.log(`❌ Business assets bucket error: ${error.message}`)
      return false
    }
  }

  async testUploadCapacity() {
    await this.log('🔍 Testing upload capacity with small test file...')
    
    try {
      // Create a small test file
      const testContent = new Blob(['test'], { type: 'text/plain' })
      const testFile = new File([testContent], 'test.txt', { type: 'text/plain' })
      const testPath = `test-uploads/diagnostic-${Date.now()}.txt`
      
      const start = Date.now()
      const { data, error } = await supabase.storage
        .from('business-assets')
        .upload(testPath, testFile)
      const duration = Date.now() - start
      
      if (error) {
        if (error.message?.includes('429') || error.message?.includes('rate limit')) {
          this.results.errors.push('Rate limiting detected during test upload')
          await this.log(`⚠️  Rate limiting detected: ${error.message}`)
          return false
        } else {
          this.results.errors.push(`Test upload failed: ${error.message}`)
          await this.log(`❌ Test upload failed: ${error.message}`)
          return false
        }
      }
      
      // Clean up test file
      await supabase.storage.from('business-assets').remove([testPath])
      
      await this.log(`✅ Test upload successful (${duration}ms)`)
      return true
    } catch (error) {
      this.results.errors.push(`Test upload error: ${error.message}`)
      await this.log(`❌ Test upload error: ${error.message}`)
      return false
    }
  }

  async analyzeRecentActivity() {
    await this.log('🔍 Analyzing recent upload activity...')
    
    try {
      const { data: files, error } = await supabase.storage
        .from('business-assets')
        .list('business-logos', {
          limit: 50,
          sortBy: { column: 'created_at', order: 'desc' }
        })
      
      if (error) {
        await this.log(`⚠️  Could not analyze recent activity: ${error.message}`)
        return
      }
      
      const now = Date.now()
      const oneHourAgo = now - (60 * 60 * 1000)
      const recentFiles = files?.filter(file => {
        const fileTime = new Date(file.created_at).getTime()
        return fileTime > oneHourAgo
      }) || []
      
      await this.log(`📊 Found ${recentFiles.length} uploads in the last hour`)
      
      if (recentFiles.length > 20) {
        this.results.recommendations.push('High upload volume detected in the last hour - this may contribute to rate limiting')
      }
      
      // Check for temp files that might indicate failed uploads
      const tempFiles = files?.filter(file => file.name.includes('temp-')) || []
      if (tempFiles.length > 0) {
        await this.log(`⚠️  Found ${tempFiles.length} temporary files - may indicate failed uploads`)
        this.results.recommendations.push('Consider cleaning up temporary files from failed uploads')
      }
      
    } catch (error) {
      await this.log(`❌ Error analyzing recent activity: ${error.message}`)
    }
  }

  generateRecommendations() {
    const hasRateLimit = this.results.errors.some(error => 
      error.includes('429') || error.includes('rate limit')
    )
    
    if (hasRateLimit) {
      this.results.recommendations.push(
        'Rate limiting detected - wait 5-10 minutes before retrying uploads',
        'Consider implementing upload queuing to prevent overwhelming the service',
        'Reduce concurrent upload operations',
        'Implement exponential backoff for failed uploads'
      )
    }
    
    if (this.results.errors.length > 0) {
      this.results.recommendations.push(
        'Check Supabase project status and quotas',
        'Verify storage bucket permissions and policies',
        'Monitor upload file sizes and formats'
      )
    }
  }

  async runDiagnostics() {
    await this.log('🚀 Starting storage diagnostics...')
    
    const healthOk = await this.checkStorageHealth()
    const bucketOk = await this.checkBusinessAssetsBucket()
    const uploadOk = await this.testUploadCapacity()
    
    await this.analyzeRecentActivity()
    this.generateRecommendations()
    
    await this.log('\n📋 Diagnostic Summary:')
    await this.log(`   Storage Health: ${healthOk ? '✅ OK' : '❌ FAILED'}`)
    await this.log(`   Bucket Access: ${bucketOk ? '✅ OK' : '❌ FAILED'}`)
    await this.log(`   Upload Capacity: ${uploadOk ? '✅ OK' : '❌ RATE LIMITED'}`)
    
    if (this.results.errors.length > 0) {
      await this.log('\n❌ Errors Found:')
      this.results.errors.forEach(error => this.log(`   • ${error}`))
    }
    
    if (this.results.recommendations.length > 0) {
      await this.log('\n💡 Recommendations:')
      this.results.recommendations.forEach(rec => this.log(`   • ${rec}`))
    }
    
    return {
      healthy: healthOk && bucketOk && uploadOk,
      results: this.results
    }
  }
}

// Main execution
async function main() {
  try {
    const diagnostics = new StorageDiagnostics()
    const result = await diagnostics.runDiagnostics()
    
    console.log('\n🎯 Diagnostic completed!')
    
    if (!result.healthy) {
      console.log('\n⚠️  Issues detected. Please review the recommendations above.')
      process.exit(1)
    } else {
      console.log('\n✅ All systems operational!')
      process.exit(0)
    }
    
  } catch (error) {
    console.error(`❌ Diagnostic failed: ${error.message}`)
    process.exit(1)
  }
}

// Export for use as module
export default StorageDiagnostics

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}
