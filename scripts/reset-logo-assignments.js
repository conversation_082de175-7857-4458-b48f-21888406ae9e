#!/usr/bin/env node

const path = require('path');
const fs = require('fs').promises;

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

const { Pool } = require('pg');

class LogoAssignmentReset {
  constructor() {
    this.pool = null;
    this.logFile = path.join(__dirname, `logo-reset-${Date.now()}.log`);
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    await fs.appendFile(this.logFile, logMessage + '\n').catch(() => {});
  }

  async initDatabase() {
    if (this.pool) return this.pool;

    const dbPassword = process.env.SUPABASE_DB_PASSWORD;
    if (!dbPassword) {
      throw new Error('SUPABASE_DB_PASSWORD environment variable is required');
    }

    const config = {
      host: 'db.haqbtbpmyadkocakqnew.supabase.co',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: dbPassword,
      ssl: { rejectUnauthorized: false },
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 30000,
      max: 4,
      min: 1
    };

    this.pool = new Pool(config);
    const client = await this.pool.connect();
    await client.query('SELECT 1');
    client.release();
    
    await this.log('✅ Database connection established');
    return this.pool;
  }

  async resetWrongAssignments() {
    await this.log('🔄 Resetting incorrect logo assignments...');
    
    const client = await this.pool.connect();
    
    try {
      // Reset all businesses that have logo_url starting with '/images/BusinessLogos/FuseLogos/logo_r'
      // This preserves the manually assigned ones (JaynesBikinis.PNG, Guadamuz.png, Jeffbarlow.png)
      const resetQuery = `
        UPDATE businesses 
        SET logo_url = NULL, updated_at = NOW() 
        WHERE logo_url LIKE '/images/BusinessLogos/FuseLogos/logo_r%'
        RETURNING name, logo_url
      `;
      
      const result = await client.query(resetQuery);
      
      await this.log(`✅ Reset ${result.rows.length} incorrect logo assignments`);
      
      // Also show what's left (should be the correct ones)
      const remainingQuery = `
        SELECT name, logo_url 
        FROM businesses 
        WHERE logo_url IS NOT NULL 
        AND logo_url != '' 
        AND logo_url != 'placeholder.svg'
        ORDER BY name
      `;
      
      const remaining = await client.query(remainingQuery);
      
      await this.log(`\n📋 Businesses that still have logos (these should be correct):`);
      for (const business of remaining.rows) {
        await this.log(`   • ${business.name} → ${business.logo_url}`);
      }
      
      await this.log(`\n📊 Summary:`);
      await this.log(`   • Reset: ${result.rows.length} businesses`);
      await this.log(`   • Preserved: ${remaining.rows.length} businesses`);
      
    } finally {
      client.release();
    }
  }

  async showAvailableLogos() {
    await this.log('\n📁 Available logo files:');
    
    const logoDirectory = path.join(__dirname, '..', 'public', 'images', 'BusinessLogos', 'FuseLogos');
    const files = await fs.readdir(logoDirectory);
    const logoFiles = files.filter(file => 
      file.toLowerCase().endsWith('.png') || 
      file.toLowerCase().endsWith('.jpg') || 
      file.toLowerCase().endsWith('.jpeg') || 
      file.toLowerCase().endsWith('.svg')
    );
    
    logoFiles.sort();
    for (const file of logoFiles) {
      await this.log(`   • ${file}`);
    }
    
    await this.log(`\n📊 Total available logos: ${logoFiles.length}`);
  }

  async showBusinessesNeedingLogos() {
    await this.log('\n🏢 Businesses needing logos:');
    
    const client = await this.pool.connect();
    
    try {
      const query = `
        SELECT name, id, category, website
        FROM businesses 
        WHERE is_active = true 
        AND (logo_url IS NULL OR logo_url = '' OR logo_url = 'placeholder.svg')
        ORDER BY name
      `;
      
      const result = await client.query(query);
      
      for (const business of result.rows) {
        await this.log(`   • ${business.name} (${business.category})`);
      }
      
      await this.log(`\n📊 Total businesses needing logos: ${result.rows.length}`);
      
    } finally {
      client.release();
    }
  }
}

// CLI interface
async function main() {
  const resetter = new LogoAssignmentReset();
  
  try {
    await resetter.initDatabase();
    await resetter.resetWrongAssignments();
    await resetter.showAvailableLogos();
    await resetter.showBusinessesNeedingLogos();
    
    console.log('\n✅ Reset complete! Ready for correct logo assignments.');
    console.log('Please provide the correct mapping between logo files and business names.');
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
  } finally {
    if (resetter.pool) {
      await resetter.pool.end();
    }
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = LogoAssignmentReset;