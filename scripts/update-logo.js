const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function updateBusinessLogo(businessId, logoPath, businessName) {
  try {
    console.log(`🔄 Processing logo for ${businessName}...`);
    
    // Check if logo file exists
    if (!fs.existsSync(logoPath)) {
      throw new Error(`Logo file not found: ${logoPath}`);
    }

    // Read the logo file
    const logoBuffer = fs.readFileSync(logoPath);
    
    // Create form data for upload
    const formData = new FormData();
    formData.append('logo', logoBuffer, {
      filename: `${businessName.toLowerCase().replace(/\s+/g, '-')}-logo.png`,
      contentType: 'image/png'
    });
    formData.append('businessId', businessId);
    formData.append('businessName', businessName);

    // Upload to logo processor service
    console.log('📤 Uploading to logo processor...');
    const response = await fetch('http://localhost:3001/api/process-logo', {
      method: 'POST',
      body: formData,
      headers: formData.getHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Logo processor failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Logo processed successfully:', result);

    return result;
  } catch (error) {
    console.error('❌ Error updating logo:', error);
    throw error;
  }
}

// Export for use in other scripts
module.exports = { updateBusinessLogo };

// If run directly
if (require.main === module) {
  const businessId = process.argv[2];
  const logoPath = process.argv[3];
  const businessName = process.argv[4];

  if (!businessId || !logoPath || !businessName) {
    console.log('Usage: node update-logo.js <businessId> <logoPath> <businessName>');
    process.exit(1);
  }

  updateBusinessLogo(businessId, logoPath, businessName)
    .then(result => {
      console.log('🎉 Logo update completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Logo update failed:', error);
      process.exit(1);
    });
}
