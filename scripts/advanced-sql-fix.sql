-- Advanced PostgREST Schema Cache Recovery
-- This script performs a comprehensive fix for PGRST002 errors

-- Step 1: Force reload PostgREST schema cache
NOTIFY pgrst, 'reload schema';

-- Step 2: Ensure postgres user has all necessary permissions
GRANT USAGE ON SCHEMA public TO postgres;
GRANT USAGE ON SCHEMA information_schema TO postgres;
GRANT USAGE ON SCHEMA pg_catalog TO postgres;

-- Step 3: Grant comprehensive permissions to API roles
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Step 4: Ensure PostgREST can access system tables
GRANT SELECT ON information_schema.tables TO anon, authenticated;
GRANT SELECT ON information_schema.columns TO anon, authenticated;
GRANT SELECT ON information_schema.routines TO anon, authenticated;
GRANT SELECT ON information_schema.parameters TO anon, authenticated;

-- Step 5: Fix any potential role issues
DO $$
BEGIN
    -- Ensure anon and authenticated roles exist
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'anon') THEN
        CREATE ROLE anon NOLOGIN;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'authenticated') THEN
        CREATE ROLE authenticated NOLOGIN;
    END IF;
    
    -- Ensure web_anon role exists (sometimes needed)
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'web_anon') THEN
        CREATE ROLE web_anon NOLOGIN;
        GRANT anon TO web_anon;
    END IF;
END $$;

-- Step 6: Clear any potential locks
SELECT pg_advisory_unlock_all();

-- Step 7: Update statistics
ANALYZE;

-- Step 8: Reload schema cache again
NOTIFY pgrst, 'reload schema';

-- Step 9: Create a test function to verify PostgREST access
CREATE OR REPLACE FUNCTION test_postgrest_access()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN 'PostgREST access test successful at ' || now();
END;
$$;

-- Grant execute permission on test function
GRANT EXECUTE ON FUNCTION test_postgrest_access() TO anon, authenticated;

-- Step 10: Final schema cache reload
NOTIFY pgrst, 'reload schema';

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Advanced SQL fix completed successfully';
    RAISE NOTICE 'PostgREST schema cache has been reloaded';
    RAISE NOTICE 'All permissions have been granted';
    RAISE NOTICE 'Run your diagnostic script to verify the fix';
END $$;