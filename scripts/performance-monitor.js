#!/usr/bin/env node

/**
 * Comprehensive Performance Monitoring and Optimization Script
 * Monitors database performance, connection health, and application metrics
 */

import { config } from 'dotenv'
import sql from '../lib/postgres-direct.js'
import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

// Load environment variables
config({ path: '.env.local' })

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

class PerformanceMonitor {
  constructor() {
    this.startTime = Date.now()
    this.metrics = {
      database: {},
      application: {},
      system: {}
    }
  }

  async log(message, level = 'info') {
    const timestamp = new Date().toISOString()
    const elapsed = ((Date.now() - this.startTime) / 1000).toFixed(2)
    const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : '📊'
    console.log(`${prefix} [${elapsed}s] ${message}`)
  }

  async checkDatabaseHealth() {
    try {
      await this.log('🔍 Checking database health...')
      
      // Connection test
      const connectionStart = Date.now()
      const version = await sql`SELECT version(), current_database(), current_user`
      const connectionTime = Date.now() - connectionStart
      
      this.metrics.database.connectionTime = connectionTime
      this.metrics.database.version = version[0].version
      this.metrics.database.database = version[0].current_database
      this.metrics.database.user = version[0].current_user
      
      await this.log(`✅ Database connection: ${connectionTime}ms`)
      
      // Check active connections
      const connections = await sql`
        SELECT count(*) as active_connections,
               max(extract(epoch from (now() - query_start))) as longest_query_seconds
        FROM pg_stat_activity 
        WHERE state = 'active'
      `
      
      this.metrics.database.activeConnections = parseInt(connections[0].active_connections)
      this.metrics.database.longestQuerySeconds = parseFloat(connections[0].longest_query_seconds) || 0
      
      await this.log(`📈 Active connections: ${this.metrics.database.activeConnections}`)
      await this.log(`⏱️  Longest query: ${this.metrics.database.longestQuerySeconds.toFixed(2)}s`)
      
      // Check table sizes and performance
      const tableStats = await sql`
        SELECT 
          schemaname,
          tablename,
          n_tup_ins as inserts,
          n_tup_upd as updates,
          n_tup_del as deletes,
          n_live_tup as live_tuples,
          n_dead_tup as dead_tuples,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
        FROM pg_stat_user_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 10
      `
      
      this.metrics.database.tableStats = tableStats
      
      await this.log(`📋 Top tables by size:`)
      tableStats.forEach(table => {
        this.log(`   ${table.tablename}: ${table.size} (${table.live_tuples} rows)`)
      })
      
      return true
    } catch (error) {
      await this.log(`Database health check failed: ${error.message}`, 'error')
      this.metrics.database.error = error.message
      return false
    }
  }

  async checkApplicationPerformance() {
    try {
      await this.log('🚀 Checking application performance...')
      
      // Check key business metrics
      const businessMetrics = await sql`
        SELECT 
          COUNT(*) as total_businesses,
          COUNT(CASE WHEN is_active = true THEN 1 END) as active_businesses,
          COUNT(CASE WHEN logo_url IS NOT NULL THEN 1 END) as businesses_with_logos,
          COUNT(CASE WHEN logo_optimized_url IS NOT NULL THEN 1 END) as businesses_with_optimized_logos
        FROM businesses
      `
      
      const userMetrics = await sql`
        SELECT 
          COUNT(*) as total_profiles,
          COUNT(CASE WHEN created_at > NOW() - INTERVAL '7 days' THEN 1 END) as new_users_week,
          COUNT(CASE WHEN created_at > NOW() - INTERVAL '30 days' THEN 1 END) as new_users_month
        FROM profiles
      `
      
      this.metrics.application.businesses = businessMetrics[0]
      this.metrics.application.users = userMetrics[0]
      
      await this.log(`🏢 Businesses: ${businessMetrics[0].total_businesses} total, ${businessMetrics[0].active_businesses} active`)
      await this.log(`🖼️  Logos: ${businessMetrics[0].businesses_with_logos} uploaded, ${businessMetrics[0].businesses_with_optimized_logos} optimized`)
      await this.log(`👥 Users: ${userMetrics[0].total_profiles} total, ${userMetrics[0].new_users_week} new this week`)
      
      // Check for potential performance issues
      const slowQueries = await sql`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows
        FROM pg_stat_statements 
        WHERE mean_time > 100
        ORDER BY mean_time DESC
        LIMIT 5
      `.catch(() => {
        this.log('pg_stat_statements extension not available', 'warn')
        return []
      })
      
      if (slowQueries.length > 0) {
        await this.log('🐌 Slow queries detected:')
        slowQueries.forEach(query => {
          this.log(`   ${query.mean_time.toFixed(2)}ms avg: ${query.query.substring(0, 100)}...`)
        })
      }
      
      return true
    } catch (error) {
      await this.log(`Application performance check failed: ${error.message}`, 'error')
      this.metrics.application.error = error.message
      return false
    }
  }

  async checkSystemResources() {
    try {
      await this.log('💻 Checking system resources...')
      
      // Check database size and usage
      const dbSize = await sql`
        SELECT 
          pg_size_pretty(pg_database_size(current_database())) as database_size,
          pg_size_pretty(sum(pg_total_relation_size(schemaname||'.'||tablename))) as tables_size
        FROM pg_stat_user_tables 
        WHERE schemaname = 'public'
      `
      
      this.metrics.system.databaseSize = dbSize[0].database_size
      this.metrics.system.tablesSize = dbSize[0].tables_size
      
      await this.log(`💾 Database size: ${dbSize[0].database_size}`)
      await this.log(`📊 Tables size: ${dbSize[0].tables_size}`)
      
      // Check for unused indexes
      const unusedIndexes = await sql`
        SELECT 
          schemaname,
          tablename,
          indexname,
          pg_size_pretty(pg_relation_size(indexname::regclass)) as size
        FROM pg_stat_user_indexes 
        WHERE idx_scan = 0 
        AND schemaname = 'public'
        ORDER BY pg_relation_size(indexname::regclass) DESC
        LIMIT 5
      `
      
      if (unusedIndexes.length > 0) {
        await this.log('🗂️  Unused indexes found:')
        unusedIndexes.forEach(index => {
          this.log(`   ${index.indexname} on ${index.tablename}: ${index.size}`)
        })
        this.metrics.system.unusedIndexes = unusedIndexes
      }
      
      return true
    } catch (error) {
      await this.log(`System resource check failed: ${error.message}`, 'error')
      this.metrics.system.error = error.message
      return false
    }
  }

  async generateOptimizationRecommendations() {
    const recommendations = []
    
    // Database recommendations
    if (this.metrics.database.activeConnections > 20) {
      recommendations.push({
        type: 'database',
        priority: 'high',
        issue: 'High connection count',
        recommendation: 'Consider implementing connection pooling or reducing concurrent connections'
      })
    }
    
    if (this.metrics.database.longestQuerySeconds > 10) {
      recommendations.push({
        type: 'database',
        priority: 'medium',
        issue: 'Long-running queries detected',
        recommendation: 'Review and optimize slow queries, consider adding indexes'
      })
    }
    
    // Application recommendations
    const logoOptimizationRate = this.metrics.application.businesses?.businesses_with_optimized_logos / 
                                 this.metrics.application.businesses?.businesses_with_logos
    
    if (logoOptimizationRate < 0.8) {
      recommendations.push({
        type: 'application',
        priority: 'medium',
        issue: 'Low logo optimization rate',
        recommendation: 'Run logo optimization script to improve image loading performance'
      })
    }
    
    // System recommendations
    if (this.metrics.system.unusedIndexes?.length > 0) {
      recommendations.push({
        type: 'system',
        priority: 'low',
        issue: 'Unused indexes detected',
        recommendation: 'Consider dropping unused indexes to save storage space'
      })
    }
    
    return recommendations
  }

  async generateReport() {
    const reportPath = path.join(__dirname, '../performance-reports')
    await fs.mkdir(reportPath, { recursive: true })
    
    const recommendations = await this.generateOptimizationRecommendations()
    
    const report = {
      timestamp: new Date().toISOString(),
      executionTime: ((Date.now() - this.startTime) / 1000).toFixed(2) + 's',
      metrics: this.metrics,
      recommendations: recommendations,
      summary: {
        databaseHealthy: !this.metrics.database.error,
        applicationHealthy: !this.metrics.application.error,
        systemHealthy: !this.metrics.system.error,
        totalRecommendations: recommendations.length,
        highPriorityIssues: recommendations.filter(r => r.priority === 'high').length
      }
    }
    
    const reportFile = path.join(reportPath, `performance-report-${Date.now()}.json`)
    await fs.writeFile(reportFile, JSON.stringify(report, null, 2))
    
    await this.log(`📄 Performance report saved: ${reportFile}`)
    
    // Display summary
    await this.log('\n📋 Performance Summary:')
    await this.log(`   Database: ${report.summary.databaseHealthy ? '✅ Healthy' : '❌ Issues detected'}`)
    await this.log(`   Application: ${report.summary.applicationHealthy ? '✅ Healthy' : '❌ Issues detected'}`)
    await this.log(`   System: ${report.summary.systemHealthy ? '✅ Healthy' : '❌ Issues detected'}`)
    
    if (recommendations.length > 0) {
      await this.log('\n💡 Optimization Recommendations:')
      recommendations.forEach((rec, index) => {
        const priority = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢'
        this.log(`   ${index + 1}. ${priority} ${rec.issue}: ${rec.recommendation}`)
      })
    } else {
      await this.log('\n🎉 No optimization recommendations - system is performing well!')
    }
    
    return reportFile
  }

  async runFullCheck() {
    await this.log('🚀 Starting comprehensive performance monitoring...')
    
    const dbHealth = await this.checkDatabaseHealth()
    const appPerf = await this.checkApplicationPerformance()
    const sysRes = await this.checkSystemResources()
    
    const reportFile = await this.generateReport()
    
    await this.log(`\n⏱️  Total execution time: ${((Date.now() - this.startTime) / 1000).toFixed(2)}s`)
    
    return {
      success: dbHealth && appPerf && sysRes,
      reportFile: reportFile,
      metrics: this.metrics
    }
  }
}

// Main execution
async function main() {
  try {
    const monitor = new PerformanceMonitor()
    const result = await monitor.runFullCheck()
    
    console.log('\n🎉 Performance monitoring completed!')
    process.exit(result.success ? 0 : 1)
    
  } catch (error) {
    console.error(`❌ Performance monitoring failed: ${error.message}`)
    console.error(error.stack)
    process.exit(1)
  } finally {
    try {
      await sql.end()
    } catch (closeError) {
      console.error('Failed to close database connection:', closeError.message)
    }
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export default PerformanceMonitor
