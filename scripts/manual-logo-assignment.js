#!/usr/bin/env node

const path = require('path');
const fs = require('fs').promises;

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

const { Pool } = require('pg');

class ManualLogoAssignment {
  constructor() {
    this.pool = null;
    this.logFile = path.join(__dirname, `manual-logo-${Date.now()}.log`);
    this.desktopLogoDirectory = '/Users/<USER>/Desktop/BusinessLogos/FuseLogos';
    this.publicLogoDirectory = path.join(__dirname, '..', 'public', 'images', 'BusinessLogos', 'FuseLogos');
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    await fs.appendFile(this.logFile, logMessage + '\n').catch(() => {});
  }

  async initDatabase() {
    if (this.pool) return this.pool;

    const dbPassword = process.env.SUPABASE_DB_PASSWORD;
    if (!dbPassword) {
      throw new Error('SUPABASE_DB_PASSWORD environment variable is required');
    }

    const config = {
      host: 'db.haqbtbpmyadkocakqnew.supabase.co',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: dbPassword,
      ssl: { rejectUnauthorized: false },
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 30000,
      max: 4,
      min: 1
    };

    this.pool = new Pool(config);
    const client = await this.pool.connect();
    await client.query('SELECT 1');
    client.release();
    
    await this.log('✅ Database connection established');
    return this.pool;
  }

  async copyLogoToPublic(logoFile) {
    try {
      const sourcePath = path.join(this.desktopLogoDirectory, logoFile);
      const destPath = path.join(this.publicLogoDirectory, logoFile);
      
      const logoBuffer = await fs.readFile(sourcePath);
      await fs.writeFile(destPath, logoBuffer);
      
      await this.log(`📋 Copied ${logoFile} to public directory`);
      return `/images/BusinessLogos/FuseLogos/${logoFile}`;
    } catch (error) {
      await this.log(`❌ Failed to copy ${logoFile}: ${error.message}`);
      return null;
    }
  }

  async updateBusinessLogo(businessId, logoUrl, businessName) {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        'UPDATE businesses SET logo_url = $1, updated_at = NOW() WHERE id = $2 RETURNING name',
        [logoUrl, businessId]
      );
      
      if (result.rows.length > 0) {
        await this.log(`✅ Updated logo for business: ${result.rows[0].name}`);
        return true;
      }
      
      return false;
    } finally {
      client.release();
    }
  }

  async assignSpecificLogos() {
    await this.log('🎯 Starting manual logo assignment...');
    
    try {
      await this.initDatabase();
      
      // Manual assignments based on your input
      const manualAssignments = [
        {
          businessId: 'f1a241c4-ab58-40f9-a419-06a64cdc19cf',
          businessName: 'JPRAD-Radiology Interpretation & Analysis',
          logoFile: 'Jprad.png'
        }
      ];
      
      let successCount = 0;
      let errorCount = 0;
      
      for (const assignment of manualAssignments) {
        try {
          // Copy logo to public directory
          const logoUrl = await this.copyLogoToPublic(assignment.logoFile);
          
          if (logoUrl) {
            // Update database
            const success = await this.updateBusinessLogo(
              assignment.businessId, 
              logoUrl, 
              assignment.businessName
            );
            
            if (success) {
              successCount++;
              await this.log(`✅ ${assignment.businessName} → ${assignment.logoFile}`);
            } else {
              errorCount++;
            }
          } else {
            errorCount++;
          }
          
          await new Promise(resolve => setTimeout(resolve, 100));
          
        } catch (error) {
          await this.log(`❌ Error processing ${assignment.businessName}: ${error.message}`);
          errorCount++;
        }
      }
      
      await this.log(`\\n📊 FINAL RESULTS:`);
      await this.log(`✅ Successfully updated: ${successCount} businesses`);
      await this.log(`❌ Errors: ${errorCount}`);
      
    } catch (error) {
      await this.log(`❌ Fatal error: ${error.message}`);
      throw error;
    } finally {
      if (this.pool) {
        await this.pool.end();
      }
    }
  }
}

// Run the script
async function main() {
  const assigner = new ManualLogoAssignment();
  await assigner.assignSpecificLogos();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = ManualLogoAssignment;