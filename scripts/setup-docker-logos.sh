#!/bin/bash

# Setup script for Docker-based logo processing
set -e

echo "🚀 Setting up Docker-based logo processing for Fuse.vip"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p docker/volumes/logos
mkdir -p docker/nginx
mkdir -p docker/logo-processor/uploads
mkdir -p docker/logo-processor/processed
mkdir -p docker/logo-processor/temp

# Set permissions
chmod 755 docker/volumes/logos
chmod 755 docker/logo-processor/uploads
chmod 755 docker/logo-processor/processed
chmod 755 docker/logo-processor/temp

# Create .env file for Docker services if it doesn't exist
if [ ! -f .env.docker ]; then
    echo "📝 Creating .env.docker file..."
    cat > .env.docker << EOF
# Docker Logo Service Configuration
DOCKER_LOGO_SERVICE_URL=http://localhost:3001
NODE_ENV=production
ALLOWED_ORIGINS=http://localhost:3000,https://fuse.vip,https://www.fuse.vip

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379

# Nginx Configuration (for production)
NGINX_HOST=localhost
NGINX_PORT=80
EOF
    echo "✅ Created .env.docker file. Please review and update as needed."
fi

# Create a simple nginx configuration
if [ ! -f docker/nginx/nginx.conf ]; then
    echo "📝 Creating nginx configuration..."
    cat > docker/nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream logo_processor {
        server logo-processor:3001;
    }

    server {
        listen 80;
        server_name localhost;

        # Logo processing API
        location /api/ {
            proxy_pass http://logo_processor;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Increase timeouts for file uploads
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Increase max body size for file uploads
            client_max_body_size 10M;
        }

        # Serve processed logos directly
        location /logos/ {
            alias /var/www/logos/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
        }

        # Health check
        location /health {
            proxy_pass http://logo_processor/health;
        }
    }
}
EOF
    echo "✅ Created nginx configuration."
fi

# Build and start the services
echo "🔨 Building Docker images..."
docker-compose build logo-processor

echo "🚀 Starting logo processing service..."
docker-compose up -d logo-processor

# Wait for service to be ready
echo "⏳ Waiting for logo processing service to be ready..."
sleep 10

# Test the service
echo "🧪 Testing logo processing service..."
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "✅ Logo processing service is running and healthy!"
    echo ""
    echo "🎉 Setup complete!"
    echo ""
    echo "📋 Service Information:"
    echo "   - Logo Processor: http://localhost:3001"
    echo "   - Health Check: http://localhost:3001/health"
    echo "   - API Endpoint: http://localhost:3001/api/process-logo"
    echo ""
    echo "🔧 Management Commands:"
    echo "   - View logs: docker-compose logs -f logo-processor"
    echo "   - Stop service: docker-compose down"
    echo "   - Restart service: docker-compose restart logo-processor"
    echo "   - Update service: docker-compose build logo-processor && docker-compose up -d logo-processor"
    echo ""
    echo "📁 Processed logos are stored in: ./docker/volumes/logos"
else
    echo "❌ Logo processing service failed to start properly."
    echo "Check logs with: docker-compose logs logo-processor"
    exit 1
fi

# Add environment variable to .env.local if it exists
if [ -f .env.local ]; then
    if ! grep -q "DOCKER_LOGO_SERVICE_URL" .env.local; then
        echo "" >> .env.local
        echo "# Docker Logo Processing Service" >> .env.local
        echo "DOCKER_LOGO_SERVICE_URL=http://localhost:3001" >> .env.local
        echo "✅ Added DOCKER_LOGO_SERVICE_URL to .env.local"
    fi
fi

echo ""
echo "🎯 Next Steps:"
echo "1. Update your Next.js app to use the Docker logo service"
echo "2. Test logo uploads in your application"
echo "3. For production, consider using the nginx proxy: docker-compose --profile production up -d"
echo ""
echo "📚 For more information, see the documentation in docs/DOCKER_LOGO_SETUP.md"
