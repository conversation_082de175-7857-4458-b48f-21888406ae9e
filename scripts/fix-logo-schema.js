#!/usr/bin/env node

/**
 * Database Schema Fix for Logo Processing
 * Adds missing logo columns to businesses table
 */

import { config } from 'dotenv'
import sql from '../lib/postgres-direct.js'

// Load environment variables
config({ path: '.env.local' })

async function fixLogoSchema() {
  try {
    console.log('🔧 Fixing database schema for logo processing...')
    
    // Check if columns exist
    const columnCheck = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'businesses' 
      AND column_name IN ('logo_optimized_url', 'logo_alt_text', 'logo_width', 'logo_height', 'logo_file_size', 'logo_mime_type')
    `
    
    const existingColumns = columnCheck.map(row => row.column_name)
    console.log('📊 Existing logo columns:', existingColumns)
    
    // Add missing columns
    const columnsToAdd = [
      { name: 'logo_optimized_url', type: 'TEXT' },
      { name: 'logo_alt_text', type: 'TEXT' },
      { name: 'logo_width', type: 'INTEGER' },
      { name: 'logo_height', type: 'INTEGER' },
      { name: 'logo_file_size', type: 'INTEGER' },
      { name: 'logo_mime_type', type: 'TEXT' }
    ]
    
    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ Adding column: ${column.name}`)
        await sql.unsafe(`ALTER TABLE businesses ADD COLUMN ${column.name} ${column.type}`)
        console.log(`✅ Added column: ${column.name}`)
      } else {
        console.log(`✓ Column already exists: ${column.name}`)
      }
    }
    
    // Update existing businesses with placeholder logo URLs for the processed logos
    console.log('🔄 Updating businesses with processed logo URLs...')
    
    // Update specific businesses we know about
    const logoUpdates = [
      { name: 'Guadamuz', logoUrl: '/processed-logos/Guadamuz.png' },
      { name: 'Jayne Bikinis', logoUrl: '/processed-logos/JaynesBikinis.PNG' },
      { name: 'Jeff Barlow', logoUrl: '/processed-logos/Jeffbarlow.png' }
    ]
    
    for (const update of logoUpdates) {
      const result = await sql`
        UPDATE businesses 
        SET 
          logo_url = ${update.logoUrl},
          logo_optimized_url = ${update.logoUrl},
          logo_mime_type = 'image/png',
          updated_at = NOW()
        WHERE LOWER(name) LIKE LOWER(${'%' + update.name + '%'})
        RETURNING id, name
      `
      
      if (result.length > 0) {
        console.log(`✅ Updated logo for: ${result[0].name}`)
      } else {
        console.log(`⚠️  Business not found: ${update.name}`)
      }
    }
    
    console.log('🎉 Schema fix completed successfully!')
    
  } catch (error) {
    console.error('❌ Schema fix failed:', error.message)
    throw error
  } finally {
    await sql.end()
  }
}

// Run the fix
fixLogoSchema().catch(console.error)
