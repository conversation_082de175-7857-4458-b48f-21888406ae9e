#!/usr/bin/env node

/**
 * Direct Database Connection to Get Public Schema
 * 
 * This script connects directly to the PostgreSQL database to retrieve
 * the public schema information, bypassing the PostgREST API layer.
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

// Color console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function main() {
  log('🔗 Direct Database Schema Retrieval', 'cyan');
  log('===================================', 'cyan');
  
  const dbPassword = process.env.SUPABASE_DB_PASSWORD;
  const connectionString = `postgresql://postgres:${dbPassword}@db.haqbtbpmyadkocakqnew.supabase.co:5432/postgres`;
  
  if (!dbPassword) {
    log('❌ Missing SUPABASE_DB_PASSWORD environment variable', 'red');
    process.exit(1);
  }
  
  log('\n🔌 Connecting to database...', 'blue');
  
  const client = new Client({
    connectionString,
    ssl: { rejectUnauthorized: false },
    connectionTimeoutMillis: 10000,
    query_timeout: 10000
  });
  
  try {
    await client.connect();
    log('✅ Connected to database successfully', 'green');
    
    // Get all tables in public schema
    log('\n📋 Tables in Public Schema:', 'magenta');
    const tablesQuery = `
      SELECT 
        table_name, 
        table_type,
        is_insertable_into,
        is_typed
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `;
    
    const tablesResult = await client.query(tablesQuery);
    log(`Found ${tablesResult.rows.length} tables:`, 'green');
    
    tablesResult.rows.forEach(table => {
      const insertable = table.is_insertable_into === 'YES' ? '✏️' : '📖';
      log(`   ${insertable} ${table.table_name} (${table.table_type})`, 'green');
    });
    
    // Get detailed column information for each table
    log('\n🏗️  Table Structures:', 'magenta');
    
    for (const table of tablesResult.rows) {
      if (table.table_type === 'BASE TABLE') {
        log(`\n   📊 ${table.table_name}:`, 'yellow');
        
        const columnsQuery = `
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale,
            ordinal_position
          FROM information_schema.columns
          WHERE table_schema = 'public' AND table_name = $1
          ORDER BY ordinal_position;
        `;
        
        const columnsResult = await client.query(columnsQuery, [table.table_name]);
        
        columnsResult.rows.forEach(col => {
          const nullable = col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
          const defaultVal = col.column_default ? ` DEFAULT ${col.column_default}` : '';
          const length = col.character_maximum_length ? `(${col.character_maximum_length})` : '';
          const precision = col.numeric_precision ? `(${col.numeric_precision}${col.numeric_scale ? `,${col.numeric_scale}` : ''})` : '';
          
          log(`      🔸 ${col.column_name}: ${col.data_type}${length}${precision} ${nullable}${defaultVal}`, 'green');
        });
      }
    }
    
    // Get functions and procedures
    log('\n⚙️  Functions and Procedures:', 'magenta');
    const functionsQuery = `
      SELECT 
        routine_name,
        routine_type,
        data_type,
        is_deterministic,
        security_type,
        routine_definition
      FROM information_schema.routines
      WHERE routine_schema = 'public'
      ORDER BY routine_name;
    `;
    
    const functionsResult = await client.query(functionsQuery);
    log(`Found ${functionsResult.rows.length} functions/procedures:`, 'green');
    
    functionsResult.rows.forEach(func => {
      const security = func.security_type === 'DEFINER' ? '🔐' : '🔓';
      log(`   ${security} ${func.routine_name}() -> ${func.data_type || 'void'} (${func.routine_type})`, 'green');
    });
    
    // Get indexes
    log('\n🔗 Indexes:', 'magenta');
    const indexesQuery = `
      SELECT 
        schemaname,
        tablename,
        indexname,
        indexdef
      FROM pg_indexes
      WHERE schemaname = 'public'
      ORDER BY tablename, indexname;
    `;
    
    const indexesResult = await client.query(indexesQuery);
    log(`Found ${indexesResult.rows.length} indexes:`, 'green');
    
    let currentTable = '';
    indexesResult.rows.forEach(index => {
      if (index.tablename !== currentTable) {
        currentTable = index.tablename;
        log(`   📊 ${currentTable}:`, 'yellow');
      }
      log(`      🔗 ${index.indexname}`, 'green');
    });
    
    // Get constraints
    log('\n🔒 Constraints:', 'magenta');
    const constraintsQuery = `
      SELECT 
        tc.table_name,
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name,
        rc.referenced_table_name,
        rc.referenced_column_name
      FROM information_schema.table_constraints tc
      LEFT JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      LEFT JOIN information_schema.referential_constraints rc
        ON tc.constraint_name = rc.constraint_name
        AND tc.table_schema = rc.constraint_schema
      WHERE tc.table_schema = 'public'
      ORDER BY tc.table_name, tc.constraint_type, tc.constraint_name;
    `;
    
    const constraintsResult = await client.query(constraintsQuery);
    log(`Found ${constraintsResult.rows.length} constraints:`, 'green');
    
    currentTable = '';
    constraintsResult.rows.forEach(constraint => {
      if (constraint.table_name !== currentTable) {
        currentTable = constraint.table_name;
        log(`   🔒 ${currentTable}:`, 'yellow');
      }
      
      const typeIcon = {
        'PRIMARY KEY': '🔑',
        'FOREIGN KEY': '🔗',
        'UNIQUE': '⭐',
        'CHECK': '✅'
      }[constraint.constraint_type] || '📋';
      
      const ref = constraint.referenced_table_name ? 
        ` -> ${constraint.referenced_table_name}(${constraint.referenced_column_name})` : '';
      
      log(`      ${typeIcon} ${constraint.constraint_name}: ${constraint.constraint_type} (${constraint.column_name})${ref}`, 'green');
    });
    
    // Get RLS policies
    log('\n🛡️  Row Level Security Policies:', 'magenta');
    const rlsQuery = `
      SELECT 
        schemaname,
        tablename,
        policyname,
        permissive,
        roles,
        cmd,
        qual,
        with_check
      FROM pg_policies
      WHERE schemaname = 'public'
      ORDER BY tablename, policyname;
    `;
    
    const rlsResult = await client.query(rlsQuery);
    log(`Found ${rlsResult.rows.length} RLS policies:`, 'green');
    
    currentTable = '';
    rlsResult.rows.forEach(policy => {
      if (policy.tablename !== currentTable) {
        currentTable = policy.tablename;
        log(`   🛡️  ${currentTable}:`, 'yellow');
      }
      
      const permissive = policy.permissive === 'PERMISSIVE' ? '✅' : '❌';
      log(`      ${permissive} ${policy.policyname} (${policy.cmd}) - Roles: ${policy.roles ? policy.roles.join(', ') : 'ALL'}`, 'green');
    });
    
    // Generate comprehensive schema export
    log('\n💾 Generating Schema Export:', 'magenta');
    const schemaExport = {
      timestamp: new Date().toISOString(),
      database: 'postgres',
      schema: 'public',
      tables: tablesResult.rows,
      functions: functionsResult.rows,
      indexes: indexesResult.rows,
      constraints: constraintsResult.rows,
      rls_policies: rlsResult.rows
    };
    
    const fs = require('fs');
    const schemaPath = './public-schema-direct.json';
    fs.writeFileSync(schemaPath, JSON.stringify(schemaExport, null, 2));
    log(`✅ Complete schema exported to: ${schemaPath}`, 'green');
    
    // Summary
    log('\n📊 Schema Summary:', 'cyan');
    log(`   📄 Tables: ${tablesResult.rows.length}`, 'green');
    log(`   ⚙️  Functions: ${functionsResult.rows.length}`, 'green');
    log(`   🔗 Indexes: ${indexesResult.rows.length}`, 'green');
    log(`   🔒 Constraints: ${constraintsResult.rows.length}`, 'green');
    log(`   🛡️  RLS Policies: ${rlsResult.rows.length}`, 'green');
    
  } catch (err) {
    log(`💥 Database connection or query failed: ${err.message}`, 'red');
    console.error('Full error:', err);
    process.exit(1);
  } finally {
    await client.end();
    log('\n🔌 Database connection closed', 'blue');
  }
  
  log('\n🏁 Direct schema retrieval complete!', 'cyan');
}

main().catch(err => {
  log(`\n💥 Script failed: ${err.message}`, 'red');
  console.error(err);
  process.exit(1);
});