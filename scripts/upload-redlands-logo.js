#!/usr/bin/env node

/**
 * Upload Redlands Chiropractic Logo
 * This script uploads the Redlands Chiropractic logo to Supabase storage
 * and updates the business record with the logo URL.
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://haqbtbpmyadkocakqnew.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Business ID for Redlands Chiropractic (from database query)
const REDLANDS_BUSINESS_ID = '26641328-6b07-4a56-b7f1-3550bb5ffb1f';

async function uploadRedlandsLogo() {
  try {
    console.log('🚀 Starting Redlands Chiropractic logo upload...');

    // Read the SVG file
    const logoPath = path.join(__dirname, '../temp/redlands-chiropractic-logo.svg');
    
    if (!fs.existsSync(logoPath)) {
      throw new Error(`Logo file not found at: ${logoPath}`);
    }

    const logoBuffer = fs.readFileSync(logoPath);
    console.log(`📁 Read logo file: ${logoBuffer.length} bytes`);

    // Generate filename
    const fileName = `business-logos/redlands-chiropractic-${Date.now()}.svg`;
    
    console.log(`📤 Uploading to: ${fileName}`);

    // Upload to Supabase storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('business-assets')
      .upload(fileName, logoBuffer, {
        contentType: 'image/svg+xml',
        upsert: true,
        cacheControl: '3600'
      });

    if (uploadError) {
      throw new Error(`Upload failed: ${uploadError.message}`);
    }

    console.log('✅ Upload successful:', uploadData);

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('business-assets')
      .getPublicUrl(fileName);

    const publicUrl = urlData.publicUrl;
    console.log(`🔗 Public URL: ${publicUrl}`);

    // Update business record
    console.log(`📝 Updating business record: ${REDLANDS_BUSINESS_ID}`);
    
    const { data: updateData, error: updateError } = await supabase
      .from('businesses')
      .update({
        logo_url: publicUrl,
        logo_optimized_url: publicUrl,
        logo_file_size: logoBuffer.length,
        logo_mime_type: 'image/svg+xml',
        logo_width: 400,
        logo_height: 100,
        updated_at: new Date().toISOString()
      })
      .eq('id', REDLANDS_BUSINESS_ID)
      .select('id, name, logo_url');

    if (updateError) {
      throw new Error(`Database update failed: ${updateError.message}`);
    }

    if (!updateData || updateData.length === 0) {
      throw new Error('No business record was updated');
    }

    console.log('✅ Business record updated successfully:');
    console.log(`   Business: ${updateData[0].name}`);
    console.log(`   Logo URL: ${updateData[0].logo_url}`);

    // Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('businesses')
      .select('id, name, logo_url, logo_file_size, logo_mime_type')
      .eq('id', REDLANDS_BUSINESS_ID)
      .single();

    if (verifyError) {
      console.warn('⚠️ Verification failed:', verifyError.message);
    } else {
      console.log('🔍 Verification successful:');
      console.log(`   Name: ${verifyData.name}`);
      console.log(`   Logo URL: ${verifyData.logo_url}`);
      console.log(`   File Size: ${verifyData.logo_file_size} bytes`);
      console.log(`   MIME Type: ${verifyData.logo_mime_type}`);
    }

    console.log('🎉 Redlands Chiropractic logo upload completed successfully!');

  } catch (error) {
    console.error('❌ Error uploading logo:', error.message);
    process.exit(1);
  }
}

// Run the upload
uploadRedlandsLogo();
