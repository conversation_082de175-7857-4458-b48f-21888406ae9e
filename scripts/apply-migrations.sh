#!/bin/bash

# Apply Database Migrations and Fix Schema Cache Issues
# This script applies pending migrations and fixes PGRST002 errors

set -e

echo "🔧 Applying Database Migrations and Fixing Schema Cache..."

# Check if supabase CLI is available
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Not in a Supabase project directory. Please run from project root."
    exit 1
fi

echo "📋 Checking current migration status..."
supabase db diff --use-migra

echo "🚀 Applying pending migrations..."

# Apply the essential schema migration
if [ -f "supabase/migrations/20250707000000_essential_schema.sql" ]; then
    echo "   Applying essential schema..."
    supabase db push
else
    echo "   Essential schema migration not found"
fi

# Apply the businesses optimization migration
if [ -f "supabase/migrations/20250709000000_optimize_businesses_table.sql" ]; then
    echo "   Applying businesses table optimization..."
    supabase db push
else
    echo "   Businesses optimization migration not found"
fi

echo "🛠️  Fixing schema cache issues..."

# Apply the schema cache fix
if [ -f "scripts/fix-schema-cache.sql" ]; then
    echo "   Running schema cache fix..."
    supabase db reset --debug
    supabase db push
    
    # Execute the fix script
    cat scripts/fix-schema-cache.sql | supabase db psql
else
    echo "   Schema cache fix script not found"
fi

echo "🔍 Verifying fixes..."

# Test the connection
node scripts/diagnose-supabase.js

echo "✅ Migration and fix process complete!"
echo ""
echo "📝 Next steps:"
echo "1. Check if the diagnostic shows success"
echo "2. If issues persist, try restarting your Supabase project"
echo "3. Deploy to production if local tests pass"