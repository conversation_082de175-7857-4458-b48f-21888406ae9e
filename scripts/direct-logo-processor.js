#!/usr/bin/env node

const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

const { Pool } = require('pg');
const FormData = require('form-data');
const fetch = require('node-fetch');
const fs = require('fs').promises;

class DirectLogoProcessor {
  constructor() {
    this.pool = null;
    this.dockerServiceUrl = process.env.DOCKER_LOGO_SERVICE_URL || 'http://localhost:3001';
    this.logFile = path.join(__dirname, `direct-logo-processing-${Date.now()}.log`);
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    await fs.appendFile(this.logFile, logMessage + '\n').catch(() => {});
  }

  async initDatabase() {
    if (this.pool) return this.pool;

    const dbPassword = process.env.SUPABASE_DB_PASSWORD;
    if (!dbPassword) {
      throw new Error('SUPABASE_DB_PASSWORD environment variable is required');
    }

    const config = {
      host: 'db.haqbtbpmyadkocakqnew.supabase.co',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: dbPassword,
      ssl: { rejectUnauthorized: false },
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 30000,
      max: 4,  // Appropriate for 44 users
      min: 1   // Single connection minimum
    };

    this.pool = new Pool(config);

    // Test connection
    const client = await this.pool.connect();
    await client.query('SELECT 1');
    client.release();

    await this.log('✅ Database connection established');
    return this.pool;
  }

  async findBusinessAndApplication(businessId) {
    await this.initDatabase();

    // First check if business exists
    const businessQuery = `
      SELECT id, name, logo_url, user_id 
      FROM businesses 
      WHERE id = $1
    `;

    const businessResult = await this.pool.query(businessQuery, [businessId]);
    
    if (businessResult.rows.length === 0) {
      await this.log(`❌ Business not found: ${businessId}`);
      return null;
    }

    const business = businessResult.rows[0];
    await this.log(`✅ Found business: ${business.name} (User: ${business.user_id})`);

    // Look for network applications with logo data for this user
    const appQuery = `
      SELECT id, business_name, logo_data, user_id, status
      FROM network_applications 
      WHERE user_id = $1 AND logo_data IS NOT NULL
      ORDER BY created_at DESC
      LIMIT 5
    `;

    const appResult = await this.pool.query(appQuery, [business.user_id]);
    
    if (appResult.rows.length === 0) {
      await this.log(`❌ No network applications with logo found for user: ${business.user_id}`);
      return null;
    }

    await this.log(`✅ Found ${appResult.rows.length} applications with logos for user`);
    
    return {
      business,
      applications: appResult.rows
    };
  }

  async searchForImg2123() {
    await this.initDatabase();

    const query = `
      SELECT id, business_name, logo_data, user_id, status
      FROM network_applications 
      WHERE logo_data LIKE '%img_2123%'
      ORDER BY created_at DESC
    `;

    const result = await this.pool.query(query);
    
    if (result.rows.length === 0) {
      await this.log('❌ No applications found with img_2123');
      return [];
    }

    await this.log(`✅ Found ${result.rows.length} applications with img_2123`);
    
    for (const app of result.rows) {
      await this.log(`  - ${app.business_name} (${app.id}) - Status: ${app.status}`);
      
      try {
        const logoData = JSON.parse(app.logo_data);
        await this.log(`    📁 Logo: ${logoData.filename} (${this.formatFileSize(logoData.size)})`);
      } catch (e) {
        await this.log(`    ⚠️  Could not parse logo data`);
      }
    }

    return result.rows;
  }

  async processApplicationLogo(application) {
    try {
      if (!application.logo_data) {
        return { success: false, error: 'No logo data found' };
      }

      const logoData = JSON.parse(application.logo_data);
      const { filename, mimetype, size, data } = logoData;

      if (!data) {
        return { success: false, error: 'No logo file data found' };
      }

      await this.log(`📁 Processing logo: ${filename} (${this.formatFileSize(size)})`);

      // Convert base64 to buffer
      const logoBuffer = Buffer.from(data, 'base64');

      // Try Docker processing first
      const dockerResult = await this.processWithDocker(logoBuffer, filename, mimetype);
      if (dockerResult.success) {
        return dockerResult;
      }

      // Fallback to simple storage
      await this.log(`⚠️  Docker processing failed, using fallback`);
      return await this.fallbackUpload(logoBuffer, filename, mimetype);

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async processWithDocker(logoBuffer, filename, mimetype) {
    try {
      // Check if Docker service is available
      const healthCheck = await fetch(`${this.dockerServiceUrl}/health`).catch(() => null);
      if (!healthCheck || !healthCheck.ok) {
        return { success: false, error: 'Docker service unavailable' };
      }

      const formData = new FormData();
      formData.append('logo', logoBuffer, {
        filename: filename,
        contentType: mimetype
      });

      const response = await fetch(`${this.dockerServiceUrl}/api/process-logo`, {
        method: 'POST',
        body: formData,
        headers: formData.getHeaders()
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { success: false, error: `Docker processing failed: ${response.status} - ${errorText}` };
      }

      const result = await response.json();
      
      if (result.success) {
        return {
          success: true,
          logoUrl: result.processedImages?.original?.path || result.logoUrl,
          fileSize: result.metadata?.size || logoBuffer.length,
          mimeType: result.metadata?.format || mimetype,
          width: result.metadata?.width,
          height: result.metadata?.height
        };
      } else {
        return { success: false, error: result.error || 'Docker processing failed' };
      }

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async fallbackUpload(logoBuffer, filename, mimetype) {
    // For now, just create a data URL or save to public directory
    // This is a simplified fallback - in production you'd upload to storage
    try {
      const timestamp = Date.now();
      const sanitizedName = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
      const publicPath = path.join(__dirname, '..', 'public', 'business-logos');
      
      // Ensure directory exists
      await fs.mkdir(publicPath, { recursive: true });
      
      const filePath = path.join(publicPath, `${timestamp}_${sanitizedName}`);
      await fs.writeFile(filePath, logoBuffer);
      
      const publicUrl = `/business-logos/${timestamp}_${sanitizedName}`;
      
      return {
        success: true,
        logoUrl: publicUrl,
        fileSize: logoBuffer.length,
        mimeType: mimetype,
        width: null,
        height: null
      };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async updateBusinessLogo(businessId, logoResult) {
    await this.initDatabase();

    const updateQuery = `
      UPDATE businesses 
      SET 
        logo_url = $1,
        logo_optimized_url = $1,
        logo_file_size = $2,
        logo_mime_type = $3,
        logo_width = $4,
        logo_height = $5,
        updated_at = NOW()
      WHERE id = $6
      RETURNING id, name
    `;

    const params = [
      logoResult.logoUrl,
      logoResult.fileSize,
      logoResult.mimeType,
      logoResult.width,
      logoResult.height,
      businessId
    ];

    const result = await this.pool.query(updateQuery, params);
    
    if (result.rows.length > 0) {
      await this.log(`✅ Updated business logo: ${result.rows[0].name} -> ${logoResult.logoUrl}`);
      return true;
    } else {
      await this.log(`❌ Failed to update business logo`);
      return false;
    }
  }

  async processBusinessLogo(businessId) {
    try {
      await this.log(`🔄 Processing logo for business: ${businessId}`);

      const data = await this.findBusinessAndApplication(businessId);
      if (!data) {
        return false;
      }

      const { business, applications } = data;

      // Process the first application with logo data
      const application = applications[0];
      const logoResult = await this.processApplicationLogo(application);

      if (logoResult.success) {
        const updated = await this.updateBusinessLogo(businessId, logoResult);
        if (updated) {
          await this.log(`🎉 Successfully processed logo for: ${business.name}`);
          return true;
        }
      } else {
        await this.log(`❌ Logo processing failed: ${logoResult.error}`);
      }

      return false;

    } catch (error) {
      await this.log(`❌ Error processing business logo: ${error.message}`);
      return false;
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async cleanup() {
    if (this.pool) {
      await this.pool.end();
      await this.log('🔌 Database connection closed');
    }
  }
}

// CLI interface
async function main() {
  const processor = new DirectLogoProcessor();
  const args = process.argv.slice(2);

  try {
    if (args.length === 0) {
      console.log(`
Usage:
  node direct-logo-processor.js <command> [options]

Commands:
  process-business <business-id>  Process logo for specific business
  search-img2123                  Search for applications with img_2123
  
Examples:
  node direct-logo-processor.js process-business 123774fb-3fab-403e-ad39-acc0b15ec78d
  node direct-logo-processor.js search-img2123
      `);
      process.exit(1);
    }

    const command = args[0];

    if (command === 'process-business') {
      const businessId = args[1];
      if (!businessId) {
        console.error('❌ Business ID required');
        process.exit(1);
      }
      
      const success = await processor.processBusinessLogo(businessId);
      await processor.cleanup();
      process.exit(success ? 0 : 1);
      
    } else if (command === 'search-img2123') {
      const applications = await processor.searchForImg2123();
      await processor.cleanup();
      process.exit(0);
      
    } else {
      console.error(`❌ Unknown command: ${command}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Fatal error: ${error.message}`);
    await processor.cleanup();
    process.exit(1);
  }
}

// Run CLI if called directly
if (require.main === module) {
  main();
}

module.exports = { DirectLogoProcessor };
