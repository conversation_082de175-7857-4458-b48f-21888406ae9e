#!/usr/bin/env node

const path = require('path');
const fs = require('fs').promises;

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

const { Pool } = require('pg');

class SmartLogoMatcher {
  constructor() {
    this.pool = null;
    this.logFile = path.join(__dirname, `smart-logo-match-${Date.now()}.log`);
    this.logoDirectory = path.join(__dirname, '..', 'public', 'images', 'BusinessLogos', 'FuseLogos');
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    await fs.appendFile(this.logFile, logMessage + '\n').catch(() => {});
  }

  async initDatabase() {
    if (this.pool) return this.pool;

    const dbPassword = process.env.SUPABASE_DB_PASSWORD;
    if (!dbPassword) {
      throw new Error('SUPABASE_DB_PASSWORD environment variable is required');
    }

    const config = {
      host: 'db.haqbtbpmyadkocakqnew.supabase.co',
      port: 5432,
      database: 'postgres',
      user: 'postgres',
      password: dbPassword,
      ssl: { rejectUnauthorized: false },
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 30000,
      max: 4,
      min: 1
    };

    this.pool = new Pool(config);
    const client = await this.pool.connect();
    await client.query('SELECT 1');
    client.release();
    
    await this.log('✅ Database connection established');
    return this.pool;
  }

  async getBusinesses() {
    const client = await this.pool.connect();
    try {
      const result = await client.query(`
        SELECT id, name, logo_url, category, user_id, created_at, contact_email, website
        FROM businesses 
        WHERE is_active = true AND (logo_url IS NULL OR logo_url = '' OR logo_url = 'placeholder.svg')
        ORDER BY created_at DESC
      `);
      
      await this.log(`📊 Found ${result.rows.length} businesses without logos`);
      return result.rows;
    } finally {
      client.release();
    }
  }

  async getLogoFiles() {
    try {
      const files = await fs.readdir(this.logoDirectory);
      const logoFiles = files.filter(file => 
        file.toLowerCase().endsWith('.png') || 
        file.toLowerCase().endsWith('.jpg') || 
        file.toLowerCase().endsWith('.jpeg') || 
        file.toLowerCase().endsWith('.svg')
      );
      
      await this.log(`📁 Found ${logoFiles.length} logo files`);
      return logoFiles;
    } catch (error) {
      await this.log(`❌ Error reading logo directory: ${error.message}`);
      return [];
    }
  }

  // Generate interactive assignment options
  async generateAssignmentOptions() {
    await this.log('🎯 Generating interactive logo assignment options...');
    
    await this.initDatabase();
    const businesses = await this.getBusinesses();
    const logoFiles = await this.getLogoFiles();
    
    // Sort businesses by name for easier manual review
    businesses.sort((a, b) => a.name.localeCompare(b.name));
    
    // Create different assignment approaches
    const approaches = {
      manual: [],
      sequential: [],
      random: []
    };
    
    // Manual assignment template
    for (let i = 0; i < Math.min(businesses.length, logoFiles.length); i++) {
      approaches.manual.push({
        businessId: businesses[i].id,
        businessName: businesses[i].name,
        suggestedLogo: logoFiles[i],
        logoOptions: logoFiles.slice(0, 5) // Show first 5 as options
      });
    }
    
    // Sequential assignment (first business gets first logo, etc.)
    for (let i = 0; i < Math.min(businesses.length, logoFiles.length); i++) {
      approaches.sequential.push({
        businessId: businesses[i].id,
        businessName: businesses[i].name,
        assignedLogo: logoFiles[i]
      });
    }
    
    // Random assignment
    const shuffledLogos = [...logoFiles].sort(() => Math.random() - 0.5);
    for (let i = 0; i < Math.min(businesses.length, shuffledLogos.length); i++) {
      approaches.random.push({
        businessId: businesses[i].id,
        businessName: businesses[i].name,
        assignedLogo: shuffledLogos[i]
      });
    }
    
    await this.log(`\n📋 ASSIGNMENT OPTIONS GENERATED:`);
    await this.log(`📊 Businesses needing logos: ${businesses.length}`);
    await this.log(`📁 Available logos: ${logoFiles.length}`);
    await this.log(`🎯 Max assignments possible: ${Math.min(businesses.length, logoFiles.length)}`);
    
    // Write assignment files
    const assignmentDir = path.join(__dirname, 'logo-assignments');
    await fs.mkdir(assignmentDir, { recursive: true });
    
    // Write sequential assignment (easiest to execute)
    await fs.writeFile(
      path.join(assignmentDir, 'sequential-assignment.json'),
      JSON.stringify(approaches.sequential, null, 2)
    );
    
    // Write random assignment
    await fs.writeFile(
      path.join(assignmentDir, 'random-assignment.json'),
      JSON.stringify(approaches.random, null, 2)
    );
    
    // Create SQL script for quick execution
    const sqlScript = approaches.sequential.map(item => 
      `UPDATE businesses SET logo_url = '/images/BusinessLogos/FuseLogos/${item.assignedLogo}', updated_at = NOW() WHERE id = '${item.businessId}'; -- ${item.businessName}`
    ).join('\n');
    
    await fs.writeFile(
      path.join(assignmentDir, 'bulk-logo-update.sql'),
      `-- Bulk logo assignment script
-- Generated: ${new Date().toISOString()}
-- This will assign logos sequentially to businesses

${sqlScript}
`
    );
    
    await this.log(`\n📁 Files created in ${assignmentDir}:`);
    await this.log(`   • sequential-assignment.json - Business->Logo mapping (sequential)`);
    await this.log(`   • random-assignment.json - Business->Logo mapping (random)`);
    await this.log(`   • bulk-logo-update.sql - Ready-to-run SQL script`);
    
    return {
      businesses,
      logoFiles,
      assignments: approaches.sequential,
      assignmentDir
    };
  }

  async executeAssignment(assignmentFile) {
    await this.log(`🚀 Executing assignment from: ${assignmentFile}`);
    
    const assignments = JSON.parse(await fs.readFile(assignmentFile, 'utf8'));
    const client = await this.pool.connect();
    
    let successCount = 0;
    let errorCount = 0;
    
    try {
      for (const assignment of assignments) {
        try {
          const logoUrl = `/images/BusinessLogos/FuseLogos/${assignment.assignedLogo}`;
          
          const result = await client.query(
            'UPDATE businesses SET logo_url = $1, updated_at = NOW() WHERE id = $2 RETURNING name',
            [logoUrl, assignment.businessId]
          );
          
          if (result.rows.length > 0) {
            await this.log(`✅ ${result.rows[0].name} → ${assignment.assignedLogo}`);
            successCount++;
          } else {
            await this.log(`❌ Business not found: ${assignment.businessId}`);
            errorCount++;
          }
          
          // Small delay to avoid overwhelming the database
          await new Promise(resolve => setTimeout(resolve, 50));
          
        } catch (error) {
          await this.log(`❌ Error updating ${assignment.businessName}: ${error.message}`);
          errorCount++;
        }
      }
    } finally {
      client.release();
    }
    
    await this.log(`\n📊 ASSIGNMENT RESULTS:`);
    await this.log(`✅ Successfully assigned: ${successCount} logos`);
    await this.log(`❌ Errors: ${errorCount}`);
    
    return { successCount, errorCount };
  }
}

// CLI interface
async function main() {
  const matcher = new SmartLogoMatcher();
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
📋 Smart Logo Matcher Usage:

1. Generate assignment options:
   node smart-logo-matcher.js generate

2. Execute assignment:
   node smart-logo-matcher.js execute <assignment-file>

3. Quick sequential assignment:
   node smart-logo-matcher.js quick-assign

Examples:
   node smart-logo-matcher.js generate
   node smart-logo-matcher.js execute scripts/logo-assignments/sequential-assignment.json
   node smart-logo-matcher.js quick-assign
`);
    return;
  }
  
  const command = args[0];
  
  try {
    await matcher.initDatabase();
    
    switch (command) {
      case 'generate':
        await matcher.generateAssignmentOptions();
        break;
        
      case 'execute':
        if (!args[1]) {
          console.log('❌ Please provide assignment file path');
          return;
        }
        await matcher.executeAssignment(args[1]);
        break;
        
      case 'quick-assign':
        const { assignmentDir } = await matcher.generateAssignmentOptions();
        const assignmentFile = path.join(assignmentDir, 'sequential-assignment.json');
        await matcher.executeAssignment(assignmentFile);
        break;
        
      default:
        console.log(`❌ Unknown command: ${command}`);
    }
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
  } finally {
    if (matcher.pool) {
      await matcher.pool.end();
    }
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = SmartLogoMatcher;