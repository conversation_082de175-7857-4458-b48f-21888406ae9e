#!/bin/bash

# =============================================
# FUSE.VIP OPTIMIZED DOCKER BUILD SCRIPT
# SOAR Architecture Review - Docker Phase
# =============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="fuse-vip"
LOGO_PROCESSOR_IMAGE="fuse-logo-processor"
BUILD_CACHE_DIR=".docker-cache"
REGISTRY_URL=${REGISTRY_URL:-""}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Create necessary directories
setup_directories() {
    log_info "Setting up build directories..."
    
    mkdir -p docker/volumes/logos
    mkdir -p docker/volumes/uploads
    mkdir -p $BUILD_CACHE_DIR
    
    log_success "Directories created"
}

# Build main application with optimizations
build_main_app() {
    log_info "Building main Fuse.vip application..."
    
    local start_time=$(date +%s)
    
    # Build with BuildKit for better caching and performance
    DOCKER_BUILDKIT=1 docker build \
        --target runtime \
        --cache-from $IMAGE_NAME:cache \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --tag $IMAGE_NAME:latest \
        --tag $IMAGE_NAME:$(date +%Y%m%d-%H%M%S) \
        .
    
    local end_time=$(date +%s)
    local build_time=$((end_time - start_time))
    
    log_success "Main application built in ${build_time} seconds"
    
    # Get image size
    local image_size=$(docker images $IMAGE_NAME:latest --format "table {{.Size}}" | tail -n 1)
    log_info "Image size: $image_size"
}

# Build logo processor with optimizations
build_logo_processor() {
    log_info "Building logo processor service..."
    
    local start_time=$(date +%s)
    
    DOCKER_BUILDKIT=1 docker build \
        --target runtime \
        --cache-from $LOGO_PROCESSOR_IMAGE:cache \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --tag $LOGO_PROCESSOR_IMAGE:latest \
        --tag $LOGO_PROCESSOR_IMAGE:$(date +%Y%m%d-%H%M%S) \
        docker/logo-processor/
    
    local end_time=$(date +%s)
    local build_time=$((end_time - start_time))
    
    log_success "Logo processor built in ${build_time} seconds"
    
    # Get image size
    local image_size=$(docker images $LOGO_PROCESSOR_IMAGE:latest --format "table {{.Size}}" | tail -n 1)
    log_info "Image size: $image_size"
}

# Clean up old images and containers
cleanup() {
    log_info "Cleaning up old images and containers..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old tagged images (keep last 3)
    docker images $IMAGE_NAME --format "table {{.Tag}}" | grep -E '^[0-9]{8}-[0-9]{6}$' | sort -r | tail -n +4 | xargs -r docker rmi $IMAGE_NAME: 2>/dev/null || true
    docker images $LOGO_PROCESSOR_IMAGE --format "table {{.Tag}}" | grep -E '^[0-9]{8}-[0-9]{6}$' | sort -r | tail -n +4 | xargs -r docker rmi $LOGO_PROCESSOR_IMAGE: 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Push to registry if configured
push_images() {
    if [ -n "$REGISTRY_URL" ]; then
        log_info "Pushing images to registry: $REGISTRY_URL"
        
        # Tag for registry
        docker tag $IMAGE_NAME:latest $REGISTRY_URL/$IMAGE_NAME:latest
        docker tag $LOGO_PROCESSOR_IMAGE:latest $REGISTRY_URL/$LOGO_PROCESSOR_IMAGE:latest
        
        # Push images
        docker push $REGISTRY_URL/$IMAGE_NAME:latest
        docker push $REGISTRY_URL/$LOGO_PROCESSOR_IMAGE:latest
        
        log_success "Images pushed to registry"
    else
        log_info "No registry URL configured, skipping push"
    fi
}

# Performance analysis
analyze_performance() {
    log_info "Analyzing build performance..."
    
    # Get image sizes
    local main_size=$(docker images $IMAGE_NAME:latest --format "{{.Size}}")
    local logo_size=$(docker images $LOGO_PROCESSOR_IMAGE:latest --format "{{.Size}}")
    
    echo ""
    echo "=== BUILD PERFORMANCE SUMMARY ==="
    echo "Main Application Image: $main_size"
    echo "Logo Processor Image: $logo_size"
    echo ""
    
    # Check for security vulnerabilities (if trivy is installed)
    if command -v trivy &> /dev/null; then
        log_info "Running security scan with Trivy..."
        trivy image --severity HIGH,CRITICAL $IMAGE_NAME:latest
        trivy image --severity HIGH,CRITICAL $LOGO_PROCESSOR_IMAGE:latest
    else
        log_warning "Trivy not installed, skipping security scan"
    fi
}

# Main execution
main() {
    local start_time=$(date +%s)
    
    log_info "Starting optimized Docker build process..."
    
    check_docker
    setup_directories
    
    # Build images in parallel for faster builds
    build_main_app &
    build_logo_processor &
    wait
    
    cleanup
    push_images
    analyze_performance
    
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    
    log_success "Build process completed in ${total_time} seconds"
    
    echo ""
    echo "=== NEXT STEPS ==="
    echo "1. Run: docker-compose up -d"
    echo "2. Test health: curl http://localhost:3000/api/health"
    echo "3. Monitor logs: docker-compose logs -f"
}

# Handle script arguments
case "${1:-}" in
    "main")
        check_docker
        build_main_app
        ;;
    "logo")
        check_docker
        build_logo_processor
        ;;
    "clean")
        cleanup
        ;;
    "analyze")
        analyze_performance
        ;;
    *)
        main
        ;;
esac
