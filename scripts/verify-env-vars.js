#!/usr/bin/env node

/**
 * Environment Variables Verification Script
 * 
 * This script checks that all required environment variables are properly set
 * for production deployment, especially for Stripe functionality.
 */

const requiredEnvVars = {
  // Supabase
  'NEXT_PUBLIC_SUPABASE_URL': 'Supabase project URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY': 'Supabase anonymous key',
  'SUPABASE_SERVICE_ROLE_KEY': 'Supabase service role key',
  
  // Stripe (Critical for payments)
  'STRIPE_SECRET_KEY': 'Stripe secret key for server-side operations',
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY': 'Stripe publishable key for client-side',
  'STRIPE_WEBHOOK_SECRET': 'Stripe webhook endpoint secret',
  
  // Database
  'SUPABASE_DB_PASSWORD': 'Database password',
  
  // Xaman OAuth2 (Required for wallet integration)
  'NEXT_PUBLIC_XAMAN_API_KEY': 'Xaman API key (client-side)',
  'XUMM_API_SECRET': 'Xaman API secret (server-side)',
}

const optionalEnvVars = {
  'PERPLEXITY_API_KEY': 'Perplexity AI API key',
  'S3_ACCESS_KEY_ID': 'S3 access key',
  'S3_SECRET_ACCESS_KEY': 'S3 secret key',
}

console.log('🔍 Verifying Environment Variables...\n')

let hasErrors = false
let hasWarnings = false

// Check required variables
console.log('📋 Required Variables:')
for (const [varName, description] of Object.entries(requiredEnvVars)) {
  const value = process.env[varName]
  
  if (!value) {
    console.log(`❌ ${varName}: MISSING - ${description}`)
    hasErrors = true
  } else if (value.length < 10) {
    console.log(`⚠️  ${varName}: TOO SHORT (${value.length} chars) - ${description}`)
    hasWarnings = true
  } else {
    const maskedValue = value.substring(0, 8) + '...' + value.substring(value.length - 4)
    console.log(`✅ ${varName}: ${maskedValue}`)
  }
}

console.log('\n📋 Optional Variables:')
for (const [varName, description] of Object.entries(optionalEnvVars)) {
  const value = process.env[varName]
  
  if (!value) {
    console.log(`⚪ ${varName}: Not set - ${description}`)
  } else {
    const maskedValue = value.substring(0, 8) + '...' + value.substring(value.length - 4)
    console.log(`✅ ${varName}: ${maskedValue}`)
  }
}

// Specific Stripe validation
console.log('\n🔐 Stripe Configuration Validation:')
const stripeSecret = process.env.STRIPE_SECRET_KEY
const stripePublishable = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
const stripeWebhook = process.env.STRIPE_WEBHOOK_SECRET

if (stripeSecret) {
  if (stripeSecret.startsWith('sk_live_')) {
    console.log('✅ Stripe Secret Key: Live mode detected')
  } else if (stripeSecret.startsWith('sk_test_')) {
    console.log('⚠️  Stripe Secret Key: Test mode detected')
    hasWarnings = true
  } else {
    console.log('❌ Stripe Secret Key: Invalid format')
    hasErrors = true
  }
}

if (stripePublishable) {
  if (stripePublishable.startsWith('pk_live_')) {
    console.log('✅ Stripe Publishable Key: Live mode detected')
  } else if (stripePublishable.startsWith('pk_test_')) {
    console.log('⚠️  Stripe Publishable Key: Test mode detected')
    hasWarnings = true
  } else {
    console.log('❌ Stripe Publishable Key: Invalid format')
    hasErrors = true
  }
}

if (stripeWebhook) {
  if (stripeWebhook.startsWith('whsec_')) {
    console.log('✅ Stripe Webhook Secret: Valid format')
  } else {
    console.log('❌ Stripe Webhook Secret: Invalid format (should start with whsec_)')
    hasErrors = true
  }
}

// Summary
console.log('\n📊 Summary:')
if (hasErrors) {
  console.log('❌ ERRORS FOUND: Some required environment variables are missing or invalid')
  console.log('   Please set these variables in your deployment environment (Vercel dashboard)')
  process.exit(1)
} else if (hasWarnings) {
  console.log('⚠️  WARNINGS: Some variables may need attention')
  console.log('   Review the warnings above, especially Stripe test/live mode settings')
  process.exit(0)
} else {
  console.log('✅ ALL CHECKS PASSED: Environment variables are properly configured')
  process.exit(0)
}
