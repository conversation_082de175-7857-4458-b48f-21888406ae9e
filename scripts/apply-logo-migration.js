#!/usr/bin/env node

/**
 * Apply Logo Data Migration to network_applications table
 * Adds missing columns needed for logo file storage during application process
 */

const { config } = require('dotenv')
const { Pool } = require('pg')

// Load environment variables
config({ path: '.env.local' })

// Create database connection
const pool = new Pool({
  connectionString: `postgresql://postgres:${process.env.SUPABASE_DB_PASSWORD}@db.haqbtbpmyadkocakqnew.supabase.co:5432/postgres`,
  ssl: { rejectUnauthorized: false },
  max: 3,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000,
})

async function executeQuery(query, params = []) {
  const client = await pool.connect()
  try {
    const result = await client.query(query, params)
    return { data: result.rows, error: null }
  } catch (error) {
    return { data: null, error }
  } finally {
    client.release()
  }
}

async function applyLogoMigration() {
  try {
    console.log('🔧 Applying logo data migration to network_applications table...')
    
    // Check current table structure
    console.log('📊 Checking current table structure...')
    const { data: columns } = await executeQuery(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'network_applications' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `, [])
    
    console.log('Current columns:', columns?.map(c => c.column_name) || [])
    
    // Add missing columns
    const columnsToAdd = [
      { name: 'logo_data', type: 'TEXT', description: 'JSON string containing logo file data' },
      { name: 'logo_url', type: 'TEXT', description: 'URL to uploaded logo' },
      { name: 'logo_path', type: 'TEXT', description: 'Path to logo file' },
      { name: 'category', type: 'TEXT', description: 'Business category' },
      { name: 'contact_name', type: 'TEXT', description: 'Contact person name' },
      { name: 'contact_email', type: 'TEXT', description: 'Contact email' },
      { name: 'contact_phone', type: 'TEXT', description: 'Contact phone' },
      { name: 'proposed_discount', type: 'TEXT', description: 'Proposed discount percentage' },
      { name: 'loyalty_reward_frequency', type: 'TEXT', description: 'Loyalty reward frequency' },
      { name: 'website', type: 'TEXT', description: 'Business website' }
    ]
    
    const existingColumns = columns?.map(c => c.column_name) || []
    
    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ Adding column: ${column.name}`)
        
        const defaultValue = column.name === 'loyalty_reward_frequency' ? " DEFAULT 'monthly'" : ''
        
        await executeQuery(`
          ALTER TABLE network_applications 
          ADD COLUMN ${column.name} ${column.type}${defaultValue}
        `, [])
        
        console.log(`✅ Added column: ${column.name}`)
      } else {
        console.log(`✓ Column already exists: ${column.name}`)
      }
    }
    
    // Add comment for logo_data column
    console.log('📝 Adding column comments...')
    await executeQuery(`
      COMMENT ON COLUMN network_applications.logo_data IS 'JSON string containing logo file data: {filename, mimetype, size, data(base64)}'
    `, [])
    
    // Update schema metadata if table exists
    try {
      await executeQuery(`
        INSERT INTO schema_metadata (key, value) VALUES
          ('logo_data_migration', '2025-07-01'),
          ('logo_data_description', 'Added logo_data column to network_applications for file storage during application process')
        ON CONFLICT (key) DO UPDATE SET
          value = EXCLUDED.value,
          updated_at = NOW()
      `, [])
      console.log('📊 Updated schema metadata')
    } catch (error) {
      console.log('⚠️ Schema metadata table not found, skipping metadata update')
    }
    
    // Verify the migration
    console.log('🔍 Verifying migration...')
    const { data: finalColumns } = await executeQuery(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'network_applications' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `, [])
    
    console.log('Final columns:', finalColumns?.map(c => c.column_name) || [])
    
    console.log('🎉 Logo data migration completed successfully!')

  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    throw error
  } finally {
    await pool.end()
  }
}

// Run the migration
applyLogoMigration().catch(console.error)
