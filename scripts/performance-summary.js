#!/usr/bin/env node

/**
 * Performance Summary and Optimization Report
 * Comprehensive overview of all performance improvements implemented
 */

import { config } from 'dotenv'
import sql from '../lib/postgres-direct.js'
import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

// Load environment variables
config({ path: '.env.local' })

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

class PerformanceSummary {
  constructor() {
    this.startTime = Date.now()
    this.improvements = []
  }

  async log(message, level = 'info') {
    const timestamp = new Date().toISOString()
    const prefix = level === 'success' ? '✅' : level === 'error' ? '❌' : level === 'warn' ? '⚠️' : '📊'
    console.log(`${prefix} ${message}`)
  }

  async checkImplementedImprovements() {
    await this.log('🚀 Checking implemented performance improvements...')

    // Check PostgREST version
    try {
      const restVersionExists = await fs.access('supabase/.temp/rest_version').then(() => true).catch(() => false)
      if (restVersionExists) {
        const version = await fs.readFile('supabase/.temp/rest_version', 'utf8')
        this.improvements.push({
          category: 'Database',
          improvement: 'PostgREST Version Control',
          status: 'Implemented',
          details: `Set to version ${version.trim()}`,
          impact: 'High - Better database performance and stability'
        })
        await this.log(`PostgREST version set to: ${version.trim()}`, 'success')
      }
    } catch (error) {
      await this.log('PostgREST version check failed', 'warn')
    }

    // Check direct PostgreSQL connection
    try {
      const postgresDirectExists = await fs.access('lib/postgres-direct.js').then(() => true).catch(() => false)
      if (postgresDirectExists) {
        this.improvements.push({
          category: 'Database',
          improvement: 'Direct PostgreSQL Connection',
          status: 'Implemented',
          details: 'Bypasses connection pooling overhead for critical operations',
          impact: 'High - Reduced latency and connection issues'
        })
        await this.log('Direct PostgreSQL connection implemented', 'success')
      }
    } catch (error) {
      await this.log('PostgreSQL connection check failed', 'warn')
    }

    // Check logo processing optimization
    try {
      const processedLogosExists = await fs.access('public/processed-logos').then(() => true).catch(() => false)
      if (processedLogosExists) {
        const logoFiles = await fs.readdir('public/processed-logos')
        this.improvements.push({
          category: 'Assets',
          improvement: 'Logo Processing and Optimization',
          status: 'Implemented',
          details: `Processed ${logoFiles.length} business logos with optimized delivery`,
          impact: 'Medium - Faster image loading and better user experience'
        })
        await this.log(`Logo optimization: ${logoFiles.length} logos processed`, 'success')
      }
    } catch (error) {
      await this.log('Logo processing check failed', 'warn')
    }

    // Check Next.js configuration optimizations
    try {
      const nextConfigContent = await fs.readFile('next.config.mjs', 'utf8')
      const hasOptimizations = nextConfigContent.includes('optimizePackageImports') && 
                              nextConfigContent.includes('deviceSizes') &&
                              nextConfigContent.includes('poweredByHeader: false')
      
      if (hasOptimizations) {
        this.improvements.push({
          category: 'Frontend',
          improvement: 'Next.js Configuration Optimization',
          status: 'Implemented',
          details: 'Enhanced image optimization, package imports, and caching strategies',
          impact: 'High - Improved bundle size and runtime performance'
        })
        await this.log('Next.js configuration optimized', 'success')
      }
    } catch (error) {
      await this.log('Next.js config check failed', 'warn')
    }

    // Check performance monitoring tools
    try {
      const perfMonitorExists = await fs.access('scripts/performance-monitor.js').then(() => true).catch(() => false)
      const perfOptimizerExists = await fs.access('lib/performance-optimizer.ts').then(() => true).catch(() => false)
      
      if (perfMonitorExists && perfOptimizerExists) {
        this.improvements.push({
          category: 'Monitoring',
          improvement: 'Performance Monitoring and Optimization Tools',
          status: 'Implemented',
          details: 'Comprehensive monitoring, caching, and optimization utilities',
          impact: 'Medium - Ongoing performance insights and optimization capabilities'
        })
        await this.log('Performance monitoring tools implemented', 'success')
      }
    } catch (error) {
      await this.log('Performance tools check failed', 'warn')
    }

    // Check database schema optimizations
    try {
      const schemaColumns = await sql`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'businesses' 
        AND column_name IN ('logo_optimized_url', 'logo_alt_text', 'logo_width', 'logo_height', 'logo_file_size', 'logo_mime_type')
      `
      
      if (schemaColumns.length >= 6) {
        this.improvements.push({
          category: 'Database',
          improvement: 'Database Schema Optimization',
          status: 'Implemented',
          details: 'Added logo metadata columns for better performance and caching',
          impact: 'Medium - Improved data structure and query efficiency'
        })
        await this.log('Database schema optimized with logo metadata', 'success')
      }
    } catch (error) {
      await this.log('Database schema check failed', 'warn')
    }
  }

  async generatePerformanceMetrics() {
    await this.log('📊 Generating performance metrics...')

    try {
      // Database performance metrics
      const dbMetrics = await sql`
        SELECT 
          COUNT(*) as total_businesses,
          COUNT(CASE WHEN logo_url IS NOT NULL THEN 1 END) as businesses_with_logos,
          COUNT(CASE WHEN logo_optimized_url IS NOT NULL THEN 1 END) as optimized_logos,
          AVG(CASE WHEN logo_file_size IS NOT NULL THEN logo_file_size END) as avg_logo_size
        FROM businesses
      `

      const userMetrics = await sql`
        SELECT COUNT(*) as total_users FROM profiles
      `

      return {
        database: {
          totalBusinesses: parseInt(dbMetrics[0].total_businesses),
          businessesWithLogos: parseInt(dbMetrics[0].businesses_with_logos),
          optimizedLogos: parseInt(dbMetrics[0].optimized_logos),
          avgLogoSize: Math.round(parseFloat(dbMetrics[0].avg_logo_size) || 0),
          totalUsers: parseInt(userMetrics[0].total_users)
        },
        optimization: {
          logoOptimizationRate: (parseInt(dbMetrics[0].optimized_logos) / parseInt(dbMetrics[0].businesses_with_logos) * 100).toFixed(1) + '%',
          totalImprovements: this.improvements.length,
          highImpactImprovements: this.improvements.filter(i => i.impact.startsWith('High')).length
        }
      }
    } catch (error) {
      await this.log(`Metrics generation failed: ${error.message}`, 'error')
      return null
    }
  }

  async generateReport() {
    const metrics = await this.generatePerformanceMetrics()
    
    const report = {
      timestamp: new Date().toISOString(),
      executionTime: ((Date.now() - this.startTime) / 1000).toFixed(2) + 's',
      summary: {
        totalImprovements: this.improvements.length,
        implementedImprovements: this.improvements.filter(i => i.status === 'Implemented').length,
        categories: [...new Set(this.improvements.map(i => i.category))],
        highImpactCount: this.improvements.filter(i => i.impact.startsWith('High')).length
      },
      improvements: this.improvements,
      metrics: metrics,
      recommendations: [
        {
          priority: 'High',
          action: 'Monitor database connection usage',
          description: 'Use the performance monitoring script regularly to track database health'
        },
        {
          priority: 'Medium',
          action: 'Implement CDN for static assets',
          description: 'Consider using a CDN for processed logos and static assets'
        },
        {
          priority: 'Low',
          action: 'Enable service worker caching',
          description: 'Implement service worker for offline functionality and better caching'
        }
      ]
    }

    // Save report
    const reportPath = path.join(__dirname, '../performance-reports')
    await fs.mkdir(reportPath, { recursive: true })
    
    const reportFile = path.join(reportPath, `performance-summary-${Date.now()}.json`)
    await fs.writeFile(reportFile, JSON.stringify(report, null, 2))

    return { report, reportFile }
  }

  async displaySummary() {
    const { report, reportFile } = await this.generateReport()

    await this.log('\n🎯 PERFORMANCE OPTIMIZATION SUMMARY', 'success')
    await this.log('=' * 50)
    
    await this.log(`📊 Total Improvements: ${report.summary.totalImprovements}`)
    await this.log(`✅ Implemented: ${report.summary.implementedImprovements}`)
    await this.log(`🔥 High Impact: ${report.summary.highImpactCount}`)
    await this.log(`📂 Categories: ${report.summary.categories.join(', ')}`)

    if (report.metrics) {
      await this.log('\n📈 Current Metrics:')
      await this.log(`   🏢 Businesses: ${report.metrics.database.totalBusinesses}`)
      await this.log(`   🖼️  Logo Optimization Rate: ${report.metrics.optimization.logoOptimizationRate}`)
      await this.log(`   👥 Users: ${report.metrics.database.totalUsers}`)
    }

    await this.log('\n🚀 Key Improvements Implemented:')
    this.improvements.forEach((improvement, index) => {
      const impact = improvement.impact.startsWith('High') ? '🔴' : 
                    improvement.impact.startsWith('Medium') ? '🟡' : '🟢'
      this.log(`   ${index + 1}. ${impact} ${improvement.improvement} (${improvement.category})`)
      this.log(`      ${improvement.details}`)
    })

    await this.log(`\n📄 Detailed report saved: ${reportFile}`)
    await this.log(`⏱️  Total analysis time: ${report.executionTime}`)

    return report
  }
}

// Main execution
async function main() {
  try {
    const summary = new PerformanceSummary()
    await summary.checkImplementedImprovements()
    const report = await summary.displaySummary()
    
    console.log('\n🎉 Performance optimization analysis completed!')
    process.exit(0)
    
  } catch (error) {
    console.error(`❌ Performance summary failed: ${error.message}`)
    console.error(error.stack)
    process.exit(1)
  } finally {
    try {
      await sql.end()
    } catch (closeError) {
      console.error('Failed to close database connection:', closeError.message)
    }
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export default PerformanceSummary
