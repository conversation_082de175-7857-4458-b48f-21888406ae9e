#!/bin/bash

# =============================================
# SAFE MIGRATION DEPLOYMENT SCRIPT
# Deploys migrations in phases to avoid connection pool issues
# =============================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if supabase CLI is available
check_supabase_cli() {
    if ! command -v supabase &> /dev/null; then
        log_error "Supabase CLI not found. Please install it first:"
        echo "npm install -g supabase"
        exit 1
    fi
    log_success "Supabase CLI found"
}

# Check if we're in a Supabase project
check_supabase_project() {
    if [ ! -f "supabase/config.toml" ]; then
        log_error "Not in a Supabase project directory"
        exit 1
    fi
    log_success "Supabase project detected"
}

# Deploy a single migration with retry logic
deploy_migration() {
    local migration_file=$1
    local phase_name=$2
    local max_retries=3
    local retry_count=0
    
    log_info "Deploying $phase_name: $migration_file"
    
    while [ $retry_count -lt $max_retries ]; do
        if supabase db push --include-all --file "$migration_file"; then
            log_success "$phase_name deployed successfully"
            return 0
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                log_warning "Deployment failed, retrying in 10 seconds... (attempt $retry_count/$max_retries)"
                sleep 10
            else
                log_error "$phase_name deployment failed after $max_retries attempts"
                return 1
            fi
        fi
    done
}

# Wait for database to be ready
wait_for_database() {
    log_info "Waiting for database to be ready..."
    local max_wait=30
    local wait_count=0
    
    while [ $wait_count -lt $max_wait ]; do
        if supabase db ping; then
            log_success "Database is ready"
            return 0
        else
            wait_count=$((wait_count + 1))
            sleep 2
        fi
    done
    
    log_error "Database not ready after ${max_wait} attempts"
    return 1
}

# Main deployment function
deploy_all_migrations() {
    log_info "Starting phased migration deployment..."
    
    # Phase 1: Indexes
    log_info "=== PHASE 1: PERFORMANCE INDEXES ==="
    if deploy_migration "supabase/migrations/20250630000000_indexes_only.sql" "Phase 1 - Indexes"; then
        sleep 5 # Wait between phases
    else
        log_error "Phase 1 failed, stopping deployment"
        exit 1
    fi
    
    # Phase 2: Materialized Views
    log_info "=== PHASE 2: MATERIALIZED VIEWS ==="
    if deploy_migration "supabase/migrations/20250630000001_materialized_views.sql" "Phase 2 - Materialized Views"; then
        sleep 5
    else
        log_error "Phase 2 failed, stopping deployment"
        exit 1
    fi
    
    # Phase 3: Functions
    log_info "=== PHASE 3: FUNCTIONS AND PROCEDURES ==="
    if deploy_migration "supabase/migrations/20250630000002_functions.sql" "Phase 3 - Functions"; then
        sleep 5
    else
        log_error "Phase 3 failed, stopping deployment"
        exit 1
    fi
    
    # Phase 4: Pagination
    log_info "=== PHASE 4: PAGINATION FUNCTIONS ==="
    if deploy_migration "supabase/migrations/20250630000003_pagination.sql" "Phase 4 - Pagination"; then
        sleep 5
    else
        log_error "Phase 4 failed, stopping deployment"
        exit 1
    fi
    
    # Phase 5: Monitoring
    log_info "=== PHASE 5: MONITORING AND PERFORMANCE TRACKING ==="
    if deploy_migration "supabase/migrations/20250630000004_monitoring.sql" "Phase 5 - Monitoring"; then
        log_success "All phases completed successfully!"
    else
        log_error "Phase 5 failed"
        exit 1
    fi
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check if materialized views exist
    if supabase db query "SELECT COUNT(*) FROM dashboard_businesses;" > /dev/null 2>&1; then
        log_success "dashboard_businesses view is accessible"
    else
        log_warning "dashboard_businesses view not accessible"
    fi
    
    if supabase db query "SELECT COUNT(*) FROM industry_businesses;" > /dev/null 2>&1; then
        log_success "industry_businesses view is accessible"
    else
        log_warning "industry_businesses view not accessible"
    fi
    
    # Check if functions exist
    if supabase db query "SELECT get_auth_profile('00000000-0000-0000-0000-000000000000');" > /dev/null 2>&1; then
        log_success "get_auth_profile function is working"
    else
        log_warning "get_auth_profile function not accessible"
    fi
    
    log_success "Deployment verification completed"
}

# Rollback function (if needed)
rollback_migrations() {
    log_warning "Rolling back migrations..."
    
    # Drop in reverse order
    supabase db query "DROP TABLE IF EXISTS query_performance_log CASCADE;" || true
    supabase db query "DROP MATERIALIZED VIEW IF EXISTS industry_businesses CASCADE;" || true
    supabase db query "DROP MATERIALIZED VIEW IF EXISTS dashboard_businesses CASCADE;" || true
    
    log_warning "Rollback completed"
}

# Main execution
main() {
    local start_time=$(date +%s)
    
    log_info "🚀 Starting SOAR Architecture Review - Database Migration Deployment"
    
    # Pre-flight checks
    check_supabase_cli
    check_supabase_project
    wait_for_database
    
    # Deploy migrations
    deploy_all_migrations
    
    # Verify deployment
    verify_deployment
    
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    
    log_success "🎉 Migration deployment completed in ${total_time} seconds"
    
    echo ""
    echo "=== NEXT STEPS ==="
    echo "1. Test the optimized API: curl http://localhost:3000/api/businesses-optimized?page=1&limit=5"
    echo "2. Check monitoring dashboard: http://localhost:3000/api/monitoring/dashboard"
    echo "3. Run test suite: ./scripts/test-coverage-report.sh"
    echo "4. Build optimized Docker images: ./scripts/docker-build-optimized.sh"
}

# Handle script arguments
case "${1:-}" in
    "rollback")
        rollback_migrations
        ;;
    "verify")
        verify_deployment
        ;;
    *)
        main
        ;;
esac
