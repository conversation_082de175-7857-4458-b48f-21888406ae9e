#!/usr/bin/env node

/**
 * Upload Redlands Chiropractic Logo via API
 * This script uploads the logo using the existing API endpoints
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

// Business ID for Redlands Chiropractic
const REDLANDS_BUSINESS_ID = '26641328-6b07-4a56-b7f1-3550bb5ffb1f';

async function uploadRedlandsLogoViaAPI() {
  try {
    console.log('🚀 Starting Redlands Chiropractic logo upload via API...');

    // Read the SVG file
    const logoPath = path.join(__dirname, '../temp/redlands-chiropractic-logo.svg');
    
    if (!fs.existsSync(logoPath)) {
      throw new Error(`Logo file not found at: ${logoPath}`);
    }

    const logoBuffer = fs.readFileSync(logoPath);
    console.log(`📁 Read logo file: ${logoBuffer.length} bytes`);

    // Create form data for upload
    const formData = new FormData();
    formData.append('file', logoBuffer, {
      filename: 'redlands-chiropractic-logo.svg',
      contentType: 'image/svg+xml'
    });
    formData.append('bucket', 'business-assets');
    formData.append('pathPrefix', 'business-logos');

    console.log('📤 Uploading via storage API...');

    // Upload via API
    const uploadResponse = await fetch('http://localhost:3000/api/storage/upload', {
      method: 'POST',
      body: formData
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      throw new Error(`Upload API failed: ${uploadResponse.status} - ${errorText}`);
    }

    const uploadResult = await uploadResponse.json();
    console.log('✅ Upload successful:', uploadResult);

    const publicUrl = uploadResult.data.url;
    console.log(`🔗 Public URL: ${publicUrl}`);

    // Update business record via direct database query
    console.log(`📝 Updating business record: ${REDLANDS_BUSINESS_ID}`);
    
    const updateResponse = await fetch('http://localhost:3000/api/businesses/update-logo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        businessId: REDLANDS_BUSINESS_ID,
        logoUrl: publicUrl,
        logoFileSize: logoBuffer.length,
        logoMimeType: 'image/svg+xml',
        logoWidth: 400,
        logoHeight: 100
      })
    });

    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      throw new Error(`Update API failed: ${updateResponse.status} - ${errorText}`);
    }

    const updateResult = await updateResponse.json();
    console.log('✅ Business record updated successfully:', updateResult);

    console.log('🎉 Redlands Chiropractic logo upload completed successfully!');

  } catch (error) {
    console.error('❌ Error uploading logo:', error.message);
    
    // If localhost API is not available, provide manual instructions
    if (error.message.includes('ECONNREFUSED') || error.message.includes('fetch failed')) {
      console.log('\n📋 Manual Upload Instructions:');
      console.log('1. Start the development server: npm run dev');
      console.log('2. Navigate to the admin panel or business management page');
      console.log('3. Upload the logo file: temp/redlands-chiropractic-logo.svg');
      console.log(`4. Update business ID: ${REDLANDS_BUSINESS_ID}`);
    }
    
    process.exit(1);
  }
}

// Run the upload
uploadRedlandsLogoViaAPI();
