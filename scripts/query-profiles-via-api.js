#!/usr/bin/env node

/**
 * Script to query profiles and business ownership via API endpoints
 * This helps identify the current authenticated user and resolve business ownership for "The Art of Pitching Corp"
 */

import fetch from 'node-fetch';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

async function makeAPIRequest(endpoint, options = {}) {
  const url = `${BASE_URL}/api${endpoint}`;
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(url, defaultOptions);
  const data = await response.json();
  
  return { response, data };
}

async function queryArtOfPitchingBusiness() {
  console.log('🎯 Querying "The Art of Pitching" business and user profiles...\n');
  
  try {
    const { response, data } = await makeAPIRequest('/update-art-of-pitching');
    
    if (!data.success) {
      console.error('❌ Failed to query business:', data.error);
      return null;
    }
    
    console.log('✅ Successfully retrieved business and profile data\n');
    
    // Display business information
    if (data.businesses && data.businesses.length > 0) {
      console.log('🏢 BUSINESS INFORMATION:');
      console.log('========================\n');
      
      data.businesses.forEach((business, index) => {
        console.log(`--- Business ${index + 1} ---`);
        console.log(`ID: ${business.id}`);
        console.log(`Name: ${business.name}`);
        console.log(`Current Owner User ID: ${business.user_id || 'NULL'}`);
        console.log(`Category: ${business.category || 'N/A'}`);
        console.log(`Website: ${business.website || 'N/A'}`);
        console.log(`Contact Email: ${business.contact_email || 'N/A'}`);
        console.log(`Contact Name: ${business.contact_name || 'N/A'}`);
        console.log(`Contact Phone: ${business.contact_phone || 'N/A'}`);
        console.log(`Address: ${business.business_address || 'N/A'}`);
        console.log(`Premium Discount: ${business.premium_discount || 'N/A'}%`);
        console.log(`Logo URL: ${business.logo_url || 'N/A'}`);
        console.log(`Active: ${business.is_active}`);
        console.log(`Created: ${business.created_at}`);
        console.log(`Updated: ${business.updated_at}`);
        console.log('');
      });
    } else {
      console.log('📭 No business found with name containing "The Art of Pitching"\n');
    }
    
    // Display recent user profiles
    if (data.recentProfiles && data.recentProfiles.length > 0) {
      console.log('👥 RECENT USER PROFILES:');
      console.log('========================\n');
      
      data.recentProfiles.forEach((profile, index) => {
        console.log(`--- Profile ${index + 1} ---`);
        console.log(`ID: ${profile.id}`);
        console.log(`Name: ${profile.first_name || ''} ${profile.last_name || ''}`);
        console.log(`Email: ${profile.user_email || 'N/A'}`);
        console.log(`Card Holder: ${profile.is_card_holder}`);
        console.log(`Business Applicant: ${profile.is_business_applicant}`);
        console.log(`Created: ${profile.created_at}`);
        console.log('');
      });
    }
    
    return data;
    
  } catch (error) {
    console.error('❌ Error querying business and profiles:', error.message);
    return null;
  }
}

async function searchUserByEmail(email) {
  console.log(`📧 Searching for user profiles by email: "${email}"...\n`);
  
  try {
    const { response, data } = await makeAPIRequest(`/search-user?email=${encodeURIComponent(email)}`);
    
    if (!data.success) {
      console.error('❌ Failed to search user:', data.error);
      return null;
    }
    
    console.log(`✅ ${data.message}\n`);
    
    if (data.profiles && data.profiles.length > 0) {
      data.profiles.forEach((profile, index) => {
        console.log(`--- Profile ${index + 1} ---`);
        console.log(`ID: ${profile.id}`);
        console.log(`Name: ${profile.first_name || ''} ${profile.last_name || ''}`);
        console.log(`Email: ${profile.user_email || 'N/A'}`);
        console.log(`Phone: ${profile.phone || 'N/A'}`);
        console.log(`XRP Wallet: ${profile.xrp_wallet_address || 'N/A'}`);
        console.log(`Card Holder: ${profile.is_card_holder}`);
        console.log(`Business Applicant: ${profile.is_business_applicant}`);
        console.log(`Card Tier: ${profile.card_tier || 'N/A'}`);
        console.log(`Membership Start: ${profile.membership_start_date || 'N/A'}`);
        console.log(`Membership End: ${profile.membership_end_date || 'N/A'}`);
        console.log(`Referring Business ID: ${profile.referring_business_id || 'N/A'}`);
        console.log(`Created: ${profile.created_at}`);
        console.log('');
      });
    }
    
    return data.profiles;
    
  } catch (error) {
    console.error(`❌ Error searching for user by email "${email}":`, error.message);
    return null;
  }
}

async function searchUserById(userId) {
  console.log(`🔍 Searching for user profile by ID: "${userId}"...\n`);
  
  try {
    const { response, data } = await makeAPIRequest(`/search-user?user_id=${encodeURIComponent(userId)}`);
    
    if (!data.success) {
      console.error('❌ Failed to search user:', data.error);
      return null;
    }
    
    console.log(`✅ ${data.message}\n`);
    
    if (data.profiles && data.profiles.length > 0) {
      const profile = data.profiles[0];
      console.log(`ID: ${profile.id}`);
      console.log(`Name: ${profile.first_name || ''} ${profile.last_name || ''}`);
      console.log(`Email: ${profile.user_email || 'N/A'}`);
      console.log(`Phone: ${profile.phone || 'N/A'}`);
      console.log(`XRP Wallet: ${profile.xrp_wallet_address || 'N/A'}`);
      console.log(`Card Holder: ${profile.is_card_holder}`);
      console.log(`Business Applicant: ${profile.is_business_applicant}`);
      console.log(`Card Tier: ${profile.card_tier || 'N/A'}`);
      console.log(`Membership Start: ${profile.membership_start_date || 'N/A'}`);
      console.log(`Membership End: ${profile.membership_end_date || 'N/A'}`);
      console.log(`Referring Business ID: ${profile.referring_business_id || 'N/A'}`);
      console.log(`Created: ${profile.created_at}`);
      console.log('');
    }
    
    return data.profiles;
    
  } catch (error) {
    console.error(`❌ Error searching for user by ID "${userId}":`, error.message);
    return null;
  }
}

async function updateBusinessOwnership(businessId, newUserId) {
  console.log(`🔄 Updating business ownership...\n`);
  console.log(`Business ID: ${businessId}`);
  console.log(`New User ID: ${newUserId}\n`);
  
  try {
    const { response, data } = await makeAPIRequest('/update-art-of-pitching', {
      method: 'POST',
      body: JSON.stringify({
        businessId: businessId,
        newUserId: newUserId
      })
    });
    
    if (!data.success) {
      console.error('❌ Failed to update business ownership:', data.error);
      return null;
    }
    
    console.log('✅ Successfully updated business ownership!\n');
    console.log(`${data.message}\n`);
    
    console.log('Updated Business Details:');
    console.log(`- ID: ${data.business.id}`);
    console.log(`- Name: ${data.business.name}`);
    console.log(`- New Owner User ID: ${data.business.user_id}`);
    console.log(`- Updated At: ${data.business.updated_at}\n`);
    
    console.log('New Owner Details:');
    console.log(`- ID: ${data.user.id}`);
    console.log(`- Name: ${data.user.first_name} ${data.user.last_name}`);
    console.log(`- Email: ${data.user.user_email}\n`);
    
    return data;
    
  } catch (error) {
    console.error('❌ Error updating business ownership:', error.message);
    return null;
  }
}

async function main() {
  try {
    console.log('🔍 FUSE VIP - Profile and Business Ownership Investigation');
    console.log('=========================================================\n');
    
    // Step 1: Query "The Art of Pitching" business and recent profiles
    console.log('STEP 1: Querying business and recent profiles...');
    const businessData = await queryArtOfPitchingBusiness();
    
    if (!businessData) {
      console.log('❌ Could not retrieve business data. Exiting.');
      return;
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Step 2: Search for specific users mentioned in the issue
    console.log('STEP 2: Searching for specific users...');
    
    // Search for the current assigned user (Raul Salazar)
    console.log('Current assigned user:');
    await searchUserById('58dc33cf-44eb-4fa2-9da2-789db8a12913');
    
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // Search for users with "<EMAIL>" email
    console.log('Users with lam10studios email:');
    await searchUserByEmail('<EMAIL>');
    
    console.log('\n' + '-'.repeat(40) + '\n');
    
    // Search for any users with "fuse.vip" domain
    console.log('Users with fuse.vip domain:');
    await searchUserByEmail('fuse.vip');
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Step 3: Provide update instructions
    console.log('STEP 3: Business Ownership Update Instructions');
    console.log('==============================================\n');
    
    if (businessData.businesses && businessData.businesses.length > 0) {
      const business = businessData.businesses[0];
      console.log('📋 TO UPDATE BUSINESS OWNERSHIP:');
      console.log(`   Business ID: ${business.id}`);
      console.log(`   Business Name: ${business.name}`);
      console.log(`   Current Owner: ${business.user_id}`);
      console.log('');
      console.log('   To update ownership, you can either:');
      console.log('   1. Run this script with update parameters:');
      console.log(`      node scripts/query-profiles-via-api.js update ${business.id} NEW_USER_ID`);
      console.log('');
      console.log('   2. Use the API directly:');
      console.log(`      curl -X POST "${BASE_URL}/api/update-art-of-pitching" \\`);
      console.log('           -H "Content-Type: application/json" \\');
      console.log(`           -d '{"businessId":"${business.id}","newUserId":"NEW_USER_ID"}'`);
      console.log('');
      console.log('   Make sure to replace NEW_USER_ID with the correct user ID from the profiles above.');
    }
    
    // Check if script was run with update parameters
    const args = process.argv.slice(2);
    if (args.length === 3 && args[0] === 'update') {
      const businessId = args[1];
      const newUserId = args[2];
      
      console.log('\n' + '='.repeat(60) + '\n');
      console.log('EXECUTING BUSINESS OWNERSHIP UPDATE...');
      
      const updateResult = await updateBusinessOwnership(businessId, newUserId);
      
      if (updateResult) {
        console.log('🎉 Business ownership update completed successfully!');
      } else {
        console.log('❌ Business ownership update failed. Please check the logs above.');
      }
    }
    
    console.log('\n📝 SUMMARY:');
    console.log('===========');
    console.log('• Review the profiles and business information above');
    console.log('• Identify which user should own "The Art of Pitching Corp" business');
    console.log('• Use the update command provided above to transfer ownership');
    console.log('• The currently logged in user (Raul Salazar) can verify their identity');
    console.log('  and confirm they should own this business');
    console.log('');
    
  } catch (error) {
    console.error('❌ Script error:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});