/**
 * <PERSON><PERSON><PERSON> to update the user_id for "The Art of Pitching" business
 * This script will:
 * 1. Find the business record for "The Art of Pitching"
 * 2. Show current details including user_id
 * 3. Allow updating the user_id to a specific value
 */

const { executeQuery } = require('../lib/database-direct.ts');

async function findArtOfPitchingBusiness() {
  console.log('🔍 Searching for "The Art of Pitching" business...\n');
  
  const query = `
    SELECT 
      id,
      name,
      user_id,
      category,
      website,
      contact_email,
      contact_name,
      contact_phone,
      business_address,
      premium_discount,
      logo_url,
      is_active,
      created_at,
      updated_at
    FROM businesses 
    WHERE name ILIKE $1
    ORDER BY created_at DESC
  `;
  
  const { data, error } = await executeQuery(query, ['%The Art of Pitching%']);
  
  if (error) {
    console.error('❌ Error finding business:', error);
    return null;
  }
  
  if (!data || data.length === 0) {
    console.log('📭 No business found with name containing "The Art of Pitching"');
    return null;
  }
  
  console.log(`✅ Found ${data.length} business(es) matching "The Art of Pitching":\n`);
  
  data.forEach((business, index) => {
    console.log(`--- Business ${index + 1} ---`);
    console.log(`ID: ${business.id}`);
    console.log(`Name: ${business.name}`);
    console.log(`User ID: ${business.user_id || 'NULL'}`);
    console.log(`Category: ${business.category || 'N/A'}`);
    console.log(`Website: ${business.website || 'N/A'}`);
    console.log(`Contact Email: ${business.contact_email || 'N/A'}`);
    console.log(`Contact Name: ${business.contact_name || 'N/A'}`);
    console.log(`Contact Phone: ${business.contact_phone || 'N/A'}`);
    console.log(`Address: ${business.business_address || 'N/A'}`);
    console.log(`Premium Discount: ${business.premium_discount || 'N/A'}%`);
    console.log(`Logo URL: ${business.logo_url || 'N/A'}`);
    console.log(`Active: ${business.is_active}`);
    console.log(`Created: ${business.created_at}`);
    console.log(`Updated: ${business.updated_at}`);
    console.log('');
  });
  
  return data;
}

async function findUserProfiles(searchTerm = '') {
  console.log('👥 Searching for user profiles...\n');
  
  let query = `
    SELECT 
      id,
      first_name,
      last_name,
      user_email,
      is_card_holder,
      is_business_applicant,
      created_at
    FROM profiles 
  `;
  
  const params = [];
  
  if (searchTerm) {
    query += ` WHERE 
      first_name ILIKE $1 OR 
      last_name ILIKE $1 OR 
      user_email ILIKE $1
    `;
    params.push(`%${searchTerm}%`);
  }
  
  query += ` ORDER BY created_at DESC LIMIT 10`;
  
  const { data, error } = await executeQuery(query, params);
  
  if (error) {
    console.error('❌ Error finding user profiles:', error);
    return null;
  }
  
  if (!data || data.length === 0) {
    console.log('📭 No user profiles found');
    return null;
  }
  
  console.log(`✅ Found ${data.length} user profile(s):\n`);
  
  data.forEach((profile, index) => {
    console.log(`--- Profile ${index + 1} ---`);
    console.log(`ID: ${profile.id}`);
    console.log(`Name: ${profile.first_name || ''} ${profile.last_name || ''}`);
    console.log(`Email: ${profile.user_email || 'N/A'}`);
    console.log(`Card Holder: ${profile.is_card_holder}`);
    console.log(`Business Applicant: ${profile.is_business_applicant}`);
    console.log(`Created: ${profile.created_at}`);
    console.log('');
  });
  
  return data;
}

async function updateBusinessUserId(businessId, newUserId) {
  console.log(`🔄 Updating business ${businessId} to set user_id to ${newUserId}...\n`);
  
  const query = `
    UPDATE businesses 
    SET 
      user_id = $1,
      updated_at = NOW()
    WHERE id = $2
    RETURNING 
      id,
      name,
      user_id,
      updated_at
  `;
  
  const { data, error } = await executeQuery(query, [newUserId, businessId]);
  
  if (error) {
    console.error('❌ Error updating business:', error);
    return null;
  }
  
  if (!data || data.length === 0) {
    console.log('📭 No business was updated');
    return null;
  }
  
  console.log('✅ Business updated successfully:');
  console.log(`ID: ${data[0].id}`);
  console.log(`Name: ${data[0].name}`);
  console.log(`New User ID: ${data[0].user_id}`);
  console.log(`Updated: ${data[0].updated_at}`);
  
  return data[0];
}

async function main() {
  try {
    console.log('🎯 Art of Pitching Business User ID Update Script');
    console.log('================================================\n');
    
    // Step 1: Find the business
    const businesses = await findArtOfPitchingBusiness();
    
    if (!businesses || businesses.length === 0) {
      console.log('❌ Could not find "The Art of Pitching" business. Exiting.');
      return;
    }
    
    // Step 2: Show some user profiles to help identify the correct user
    console.log('📋 Recent user profiles for reference:');
    await findUserProfiles();
    
    // For now, just show the current state - you can uncomment the update line below
    // and provide the correct user_id when you identify the right user
    
    console.log('📝 To update the business, modify this script and provide the correct user_id');
    console.log('Example: await updateBusinessUserId(businessId, "correct-user-id-here");');
    
    // UNCOMMENT THE LINES BELOW AND PROVIDE THE CORRECT IDs WHEN READY:
    /*
    const businessId = businesses[0].id; // Use the correct business ID
    const newUserId = "USER_ID_HERE"; // Replace with the actual user ID
    
    await updateBusinessUserId(businessId, newUserId);
    */
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run the script
main().catch(console.error);