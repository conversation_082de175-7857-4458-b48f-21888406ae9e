#!/usr/bin/env node

/**
 * Upload Redlands Chiropractic Logo to Vercel Blob Storage
 * This script uploads the logo to Vercel Blob and updates the business record
 */

const fs = require('fs');
const path = require('path');
const { put } = require('@vercel/blob');

// Business ID for Redlands Chiropractic
const REDLANDS_BUSINESS_ID = '26641328-6b07-4a56-b7f1-3550bb5ffb1f';

async function uploadToVercelBlob() {
  try {
    console.log('🚀 Starting Redlands Chiropractic logo upload to Vercel Blob...');

    // Read the SVG file
    const logoPath = path.join(__dirname, '../temp/redlands-chiropractic-logo.svg');
    
    if (!fs.existsSync(logoPath)) {
      throw new Error(`Logo file not found at: ${logoPath}`);
    }

    const logoContent = fs.readFileSync(logoPath, 'utf8');
    console.log(`📁 Read logo file: ${logoContent.length} characters`);

    // Upload to Vercel Blob
    console.log('📤 Uploading to Vercel Blob...');
    
    const blob = await put('redlands-chiropractic-logo.svg', logoContent, {
      access: 'public',
      contentType: 'image/svg+xml'
    });

    console.log('✅ Upload successful:', blob);
    console.log(`🔗 Public URL: ${blob.url}`);

    // Update business record via API call
    console.log(`📝 Updating business record: ${REDLANDS_BUSINESS_ID}`);
    
    const updateResponse = await fetch('http://localhost:3000/api/businesses/update-logo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        businessId: REDLANDS_BUSINESS_ID,
        logoUrl: blob.url,
        logoFileSize: logoContent.length,
        logoMimeType: 'image/svg+xml',
        logoWidth: 400,
        logoHeight: 100
      })
    });

    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      console.warn(`⚠️ API update failed: ${updateResponse.status} - ${errorText}`);
      console.log('📋 Manual update required:');
      console.log(`   Business ID: ${REDLANDS_BUSINESS_ID}`);
      console.log(`   Logo URL: ${blob.url}`);
    } else {
      const updateResult = await updateResponse.json();
      console.log('✅ Business record updated successfully:', updateResult);
    }

    console.log('🎉 Redlands Chiropractic logo upload completed successfully!');
    console.log(`🔗 Final Logo URL: ${blob.url}`);

  } catch (error) {
    console.error('❌ Error uploading logo:', error.message);
    
    // Provide manual instructions
    console.log('\n📋 Manual Upload Instructions:');
    console.log('1. Go to https://vercel.com/dashboard');
    console.log('2. Navigate to your project storage');
    console.log('3. Upload the file: temp/redlands-chiropractic-logo.svg');
    console.log(`4. Update business ID: ${REDLANDS_BUSINESS_ID} with the new URL`);
    
    process.exit(1);
  }
}

// Run the upload
uploadToVercelBlob();
