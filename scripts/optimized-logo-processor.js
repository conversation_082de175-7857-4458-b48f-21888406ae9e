#!/usr/bin/env node

/**
 * Optimized Business Logo Processing Script
 * High-performance processing of 74 logos with direct PostgreSQL connection
 * Includes performance monitoring and batch processing
 */

import { config } from 'dotenv'
import sql from '../lib/postgres-direct.js'
import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

// Load environment variables
config({ path: '.env.local' })

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Configuration
const LOGOS_DIRECTORY = path.join(__dirname, '../public/images/BusinessLogos/FuseLogos')
const SUPPORTED_FORMATS = ['.jpg', '.jpeg', '.png', '.svg', '.webp']
const BATCH_SIZE = 5 // Process 5 logos at a time
const DELAY_BETWEEN_BATCHES = 1000 // 1 second delay between batches

class OptimizedLogoProcessor {
  constructor() {
    this.results = []
    this.processed = 0
    this.failed = 0
    this.startTime = Date.now()
  }

  async log(message) {
    const elapsed = ((Date.now() - this.startTime) / 1000).toFixed(2)
    console.log(`[${elapsed}s] ${message}`)
  }

  async findLogos() {
    try {
      const files = await fs.readdir(LOGOS_DIRECTORY)
      const logoFiles = []

      for (const file of files) {
        const filePath = path.join(LOGOS_DIRECTORY, file)
        const stats = await fs.stat(filePath)
        
        if (stats.isFile()) {
          const ext = path.extname(file).toLowerCase()
          if (SUPPORTED_FORMATS.includes(ext)) {
            logoFiles.push({
              filename: file,
              path: filePath,
              size: stats.size,
              ext: ext,
              relativePath: `images/BusinessLogos/FuseLogos/${file}`
            })
          }
        }
      }

      await this.log(`📁 Found ${logoFiles.length} logo files`)
      return logoFiles
    } catch (error) {
      throw new Error(`Failed to read directory ${LOGOS_DIRECTORY}: ${error.message}`)
    }
  }

  async copyToPublicAccess(logoFile) {
    try {
      // Create a web-accessible path
      const publicPath = path.join(__dirname, '../public/processed-logos')
      await fs.mkdir(publicPath, { recursive: true })
      
      // Copy file to public directory with optimized name
      const sanitizedName = logoFile.filename.replace(/[^a-zA-Z0-9.-]/g, '_')
      const publicFilePath = path.join(publicPath, sanitizedName)
      
      await fs.copyFile(logoFile.path, publicFilePath)
      
      // Generate web URL
      const webUrl = `/processed-logos/${sanitizedName}`
      
      return {
        success: true,
        filename: logoFile.filename,
        publicUrl: webUrl,
        localPath: publicFilePath,
        size: logoFile.size,
        mimeType: this.getMimeType(logoFile.ext)
      }
    } catch (error) {
      return {
        success: false,
        filename: logoFile.filename,
        error: error.message
      }
    }
  }

  async updateBusinessWithLogo(logoResult, businessName) {
    try {
      if (!logoResult.success) return false

      // Try to find and update business record
      const businesses = await sql`
        UPDATE businesses 
        SET 
          logo_url = ${logoResult.publicUrl},
          logo_optimized_url = ${logoResult.publicUrl},
          logo_file_size = ${logoResult.size},
          logo_mime_type = ${logoResult.mimeType},
          updated_at = NOW()
        WHERE LOWER(name) LIKE LOWER(${'%' + businessName + '%'})
        RETURNING id, name
      `
      
      if (businesses.length > 0) {
        await this.log(`📝 Updated business: ${businesses[0].name}`)
        return true
      } else {
        await this.log(`⚠️  No business found for: ${businessName}`)
        return false
      }
    } catch (error) {
      await this.log(`❌ Database update failed for ${businessName}: ${error.message}`)
      return false
    }
  }

  extractBusinessName(filename) {
    // Extract business name from filename
    const nameWithoutExt = path.parse(filename).name
    
    // Handle special cases
    const specialCases = {
      'JaynesBikinis': 'Jayne Bikinis',
      'Jeffbarlow': 'Jeff Barlow',
      'Guadamuz': 'Guadamuz'
    }
    
    if (specialCases[nameWithoutExt]) {
      return specialCases[nameWithoutExt]
    }
    
    // For logo_rX_cY format, we'll need to map these to actual business names
    if (nameWithoutExt.match(/^logo_r\d+_c\d+$/)) {
      return `Business_${nameWithoutExt}` // Placeholder - you may want to provide a mapping
    }
    
    return nameWithoutExt.replace(/[_-]/g, ' ')
  }

  getMimeType(ext) {
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.svg': 'image/svg+xml',
      '.webp': 'image/webp'
    }
    return mimeTypes[ext.toLowerCase()] || 'application/octet-stream'
  }

  async processBatch(logoFiles) {
    const batchResults = []
    
    for (const logoFile of logoFiles) {
      await this.log(`🔄 Processing: ${logoFile.filename}`)
      
      // Copy logo to public access
      const result = await this.copyToPublicAccess(logoFile)
      
      if (result.success) {
        // Extract business name and try to update database
        const businessName = this.extractBusinessName(logoFile.filename)
        const dbUpdated = await this.updateBusinessWithLogo(result, businessName)
        
        result.businessName = businessName
        result.dbUpdated = dbUpdated
        
        this.processed++
        await this.log(`✅ Processed: ${logoFile.filename} -> ${result.publicUrl}`)
      } else {
        this.failed++
        await this.log(`❌ Failed: ${logoFile.filename} - ${result.error}`)
      }
      
      batchResults.push(result)
    }
    
    return batchResults
  }

  async processAllLogos() {
    await this.log('🚀 Starting optimized business logo processing...')
    
    // Find all logo files
    const logoFiles = await this.findLogos()
    
    if (logoFiles.length === 0) {
      await this.log('❌ No logo files found')
      return { processed: 0, failed: 0, results: [] }
    }

    await this.log(`📊 Processing ${logoFiles.length} logos in batches of ${BATCH_SIZE}...`)
    
    const allResults = []
    
    // Process in batches
    for (let i = 0; i < logoFiles.length; i += BATCH_SIZE) {
      const batch = logoFiles.slice(i, i + BATCH_SIZE)
      const batchNumber = Math.floor(i / BATCH_SIZE) + 1
      const totalBatches = Math.ceil(logoFiles.length / BATCH_SIZE)
      
      await this.log(`📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} files)`)
      
      const batchResults = await this.processBatch(batch)
      allResults.push(...batchResults)
      
      // Delay between batches to avoid overwhelming the system
      if (i + BATCH_SIZE < logoFiles.length) {
        await this.log(`⏳ Waiting ${DELAY_BETWEEN_BATCHES}ms before next batch...`)
        await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES))
      }
    }

    this.results = allResults
    
    await this.log(`\n📊 Processing Summary:`)
    await this.log(`   ✅ Successfully processed: ${this.processed}`)
    await this.log(`   ❌ Failed: ${this.failed}`)
    await this.log(`   📁 Total files: ${logoFiles.length}`)
    await this.log(`   ⏱️  Total time: ${((Date.now() - this.startTime) / 1000).toFixed(2)}s`)

    return { processed: this.processed, failed: this.failed, results: allResults }
  }

  async generateReport() {
    const reportPath = path.join(__dirname, '../processed-logos/optimized-processing-report.json')
    await fs.mkdir(path.dirname(reportPath), { recursive: true })
    
    const report = {
      timestamp: new Date().toISOString(),
      processingTime: ((Date.now() - this.startTime) / 1000).toFixed(2) + 's',
      summary: {
        total: this.results.length,
        successful: this.processed,
        failed: this.failed,
        dbUpdated: this.results.filter(r => r.dbUpdated).length
      },
      results: this.results,
      performance: {
        batchSize: BATCH_SIZE,
        delayBetweenBatches: DELAY_BETWEEN_BATCHES,
        avgTimePerLogo: ((Date.now() - this.startTime) / this.results.length / 1000).toFixed(3) + 's'
      }
    }

    await fs.writeFile(reportPath, JSON.stringify(report, null, 2))
    await this.log(`📄 Report saved to: ${reportPath}`)
    
    return reportPath
  }
}

// Main execution
async function main() {
  try {
    const processor = new OptimizedLogoProcessor()
    const result = await processor.processAllLogos()
    await processor.generateReport()
    
    console.log('\n🎉 Optimized logo processing completed!')
    
    // Close database connection
    await sql.end()
    
    process.exit(result.failed > 0 ? 1 : 0)
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`)
    console.error(error.stack)
    
    // Close database connection on error
    try {
      await sql.end()
    } catch (closeError) {
      console.error('Failed to close database connection:', closeError.message)
    }
    
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export default OptimizedLogoProcessor
