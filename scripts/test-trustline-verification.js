/**
 * Test script for XRPL Trustline Verification
 * 
 * This script tests the XRPL transaction verification functionality
 * by directly connecting to the XRPL and verifying a transaction hash.
 * 
 * Usage: node scripts/test-trustline-verification.js <transaction_hash>
 * Example: node scripts/test-trustline-verification.js F01B00D710FE8CF27E565D007D2E2131520B1BF8FF30DDB9BE7C7746516CB1F4
 */

const { Client } = require('xrpl');

// Configuration
const XRPL_SERVER = 'wss://xrplcluster.com';
const DEFAULT_TX_HASH = 'F01B00D710FE8CF27E565D007D2E2131520B1BF8FF30DDB9BE7C7746516CB1F4';
const EXPECTED_CURRENCY = '4655534500000000000000000000000000000000'; // 'FUSE' in hex
const EXPECTED_ISSUER = 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo';

// Get transaction hash from command line or use default
const txHash = process.argv[2] || DEFAULT_TX_HASH;

async function verifyTrustlineTransaction(transactionHash) {
  console.log(`🔍 Verifying transaction: ${transactionHash}`);
  
  // Initialize XRPL client
  const client = new Client(XRPL_SERVER);
  
  try {
    // Connect to XRPL
    await client.connect();
    console.log('✅ Connected to XRPL');
    
    // Fetch transaction by hash
    const txResponse = await client.request({
      command: 'tx',
      transaction: transactionHash,
      binary: false
    });
    
    if (!txResponse.result) {
      console.error('❌ Transaction not found on XRPL ledger');
      return null;
    }
    
    const txData = txResponse.result;

    // Print full transaction data for debugging
    console.log('\n📄 Full Transaction Data:');
    console.log(JSON.stringify(txData, null, 2));

    // Print basic transaction info
    console.log('\n📄 Transaction Information:');
    console.log(`Type: ${txData.TransactionType}`);
    console.log(`Account (Wallet Address): ${txData.Account}`);
    console.log(`Result: ${txData.meta?.TransactionResult}`);
    console.log(`Ledger Index: ${txData.ledger_index}`);
    console.log(`Date: ${new Date(txData.date ? (txData.date + *********) * 1000 : Date.now()).toISOString()}`);
    
    // Verify transaction was successful
    if (txData.meta?.TransactionResult !== 'tesSUCCESS') {
      console.error(`❌ Transaction failed with result: ${txData.meta?.TransactionResult}`);
      return null;
    }
    
    // Verify this is a TrustSet transaction
    if (txData.TransactionType !== 'TrustSet') {
      console.error(`❌ Expected TrustSet transaction, but got: ${txData.TransactionType}`);
      return null;
    }
    
    // Extract wallet address (the account that initiated the transaction)
    const walletAddress = txData.Account;
    
    if (!walletAddress) {
      console.error('❌ Could not extract wallet address from transaction');
      return null;
    }
    
    // Verify the trustline is for the expected token
    const limitAmount = txData.LimitAmount;
    
    console.log('\n🔐 Trustline Details:');
    console.log(`Currency: ${limitAmount?.currency}`);
    console.log(`Issuer: ${limitAmount?.issuer}`);
    console.log(`Limit: ${limitAmount?.value}`);
    
    if (EXPECTED_CURRENCY && EXPECTED_ISSUER) {
      if (limitAmount?.currency !== EXPECTED_CURRENCY) {
        console.error(`❌ Trustline is for wrong currency. Expected ${EXPECTED_CURRENCY}, but got ${limitAmount?.currency}`);
        return null;
      }
      
      if (limitAmount?.issuer !== EXPECTED_ISSUER) {
        console.error(`❌ Trustline is for wrong issuer. Expected ${EXPECTED_ISSUER}, but got ${limitAmount?.issuer}`);
        return null;
      }
      
      console.log('✅ Trustline currency and issuer verified successfully');
    }
    
    console.log('\n🎉 Transaction verified successfully!');
    console.log(`Wallet Address: ${walletAddress}`);
    
    return {
      success: true,
      walletAddress,
      transactionDetails: {
        hash: transactionHash,
        type: txData.TransactionType,
        result: txData.meta?.TransactionResult,
        currency: limitAmount?.currency,
        issuer: limitAmount?.issuer,
        limit: limitAmount?.value,
        ledgerIndex: txData.ledger_index,
        date: txData.date
      }
    };
    
  } catch (error) {
    console.error('❌ Error verifying transaction:', error);
    return null;
  } finally {
    // Always disconnect from XRPL
    if (client.isConnected()) {
      await client.disconnect();
      console.log('🔌 Disconnected from XRPL');
    }
  }
}

// Run the verification
verifyTrustlineTransaction(txHash)
  .then(result => {
    if (result) {
      console.log('\n✅ Verification completed successfully');
      console.log('This wallet address should be stored in the user profile:', result.walletAddress);
    } else {
      console.error('\n❌ Verification failed');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Unexpected error:', error);
    process.exit(1);
  });
