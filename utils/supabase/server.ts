import { createServerClient, type CookieOptions } from "@supabase/ssr";

export const createClient = async (cookieStore?: any) => {
  // Dynamic import of next/headers to avoid client-side issues
  let store = cookieStore;
  
  if (!store) {
    try {
      // Only import next/headers when actually running on server
      if (typeof window === 'undefined') {
        const { cookies } = await import("next/headers");
        store = await cookies();
      } else {
        // Client-side fallback - return null store
        store = null;
      }
    } catch (error) {
      console.warn('Failed to import next/headers:', error);
      store = null;
    }
  }

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return store?.getAll() || []
        },
        setAll(cookiesToSet) {
          try {
            if (store) {
              cookiesToSet.forEach(({ name, value, options }) => store.set(name, value, options || {}))
            }
          } catch (error) {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
            console.warn('Cookie setting failed:', error)
          }
        },
      },
    },
  );
};
