/**
 * Utility functions for checking Supabase service health
 */

import { getSupabaseClient } from '@/lib/supabase'

export interface HealthCheckResult {
  isHealthy: boolean
  error?: string
  responseTime?: number
}

/**
 * Check if Supabase auth service is responding
 */
export async function checkAuthHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now()

  try {
    const supabase = getSupabaseClient();
    if (!supabase) {
      return {
        isHealthy: false,
        error: 'Supabase client not initialized',
        responseTime: Date.now() - startTime
      }
    }

    // Try a simple auth operation that doesn't require credentials
    const { error } = await supabase.auth.getSession()
    
    const responseTime = Date.now() - startTime
    
    if (error && (error.message?.includes('503') || error.message === '{}')) {
      return {
        isHealthy: false,
        error: 'Authentication service is temporarily unavailable (503)',
        responseTime
      }
    }
    
    return {
      isHealthy: true,
      responseTime
    }
  } catch (error: any) {
    const responseTime = Date.now() - startTime
    
    if (error.status === 503 || error.message?.includes('503') || error.message === '{}') {
      return {
        isHealthy: false,
        error: 'Authentication service is temporarily unavailable (503)',
        responseTime
      }
    }
    
    return {
      isHealthy: false,
      error: error.message || 'Unknown error occurred',
      responseTime
    }
  }
}

/**
 * Check if Supabase database is responding
 */
export async function checkDatabaseHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now()

  try {
    const supabase = getSupabaseClient();
    if (!supabase) {
      return {
        isHealthy: false,
        error: 'Supabase client not initialized',
        responseTime: Date.now() - startTime
      }
    }

    // Try a simple database query - just select id to check if database is responding
    const { error } = await supabase
      .from('profiles')
      .select('id')
      .limit(1)

    const responseTime = Date.now() - startTime

    // It's okay if the query returns no results or permission errors
    // We just want to know if the service is responding
    if (error && (error.message?.includes('503') || error.message?.includes('Service Unavailable'))) {
      return {
        isHealthy: false,
        error: 'Database service is temporarily unavailable (503)',
        responseTime
      }
    }

    return {
      isHealthy: true,
      responseTime
    }
  } catch (error: any) {
    const responseTime = Date.now() - startTime

    if (error.status === 503 || error.message?.includes('503')) {
      return {
        isHealthy: false,
        error: 'Database service is temporarily unavailable (503)',
        responseTime
      }
    }

    return {
      isHealthy: false,
      error: error.message || 'Unknown error occurred',
      responseTime
    }
  }
}

/**
 * Perform a comprehensive health check
 */
export async function performHealthCheck(): Promise<{
  auth: HealthCheckResult
  database: HealthCheckResult
  overall: boolean
}> {
  const [authHealth, dbHealth] = await Promise.all([
    checkAuthHealth(),
    checkDatabaseHealth()
  ])
  
  return {
    auth: authHealth,
    database: dbHealth,
    overall: authHealth.isHealthy && dbHealth.isHealthy
  }
}

/**
 * Wait for services to become healthy with exponential backoff
 */
export async function waitForHealthyServices(
  maxRetries = 5,
  initialDelay = 1000
): Promise<boolean> {
  for (let i = 0; i < maxRetries; i++) {
    const health = await performHealthCheck()
    
    if (health.overall) {
      return true
    }
    
    if (i < maxRetries - 1) {
      const delay = initialDelay * Math.pow(2, i)
      console.log(`Services not healthy, retrying in ${delay}ms...`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  return false
}
