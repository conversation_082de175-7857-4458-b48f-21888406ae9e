#!/usr/bin/env node

/**
 * Test script to verify the authentication fix
 * This script will test the API endpoints with proper authentication
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'your-password-here'; // You'll need to provide the actual password

async function testAuthenticationFlow() {
  console.log('🧪 Testing Authentication Fix...\n');

  try {
    // Step 1: Test login
    console.log('1️⃣ Testing login...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_EMAIL,
        password: TEST_PASSWORD
      })
    });

    if (!loginResponse.ok) {
      console.error('❌ Login failed:', loginResponse.status, await loginResponse.text());
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful:', loginData.message);

    // Extract cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('🍪 Cookies received:', cookies ? 'Yes' : 'No');

    // Step 2: Test authenticated API call
    console.log('\n2️⃣ Testing authenticated API call...');
    const testAuthResponse = await fetch(`${BASE_URL}/api/test-auth`, {
      method: 'GET',
      headers: {
        'Cookie': cookies || '',
        'Content-Type': 'application/json',
      }
    });

    console.log('🔍 Test Auth Response Status:', testAuthResponse.status);
    const testAuthData = await testAuthResponse.json();
    console.log('📊 Test Auth Response:', JSON.stringify(testAuthData, null, 2));

    // Step 3: Test business data API
    console.log('\n3️⃣ Testing business data API...');
    const businessResponse = await fetch(`${BASE_URL}/api/dashboard/business`, {
      method: 'GET',
      headers: {
        'Cookie': cookies || '',
        'Content-Type': 'application/json',
      }
    });

    console.log('🔍 Business API Response Status:', businessResponse.status);
    const businessData = await businessResponse.json();
    console.log('📊 Business API Response:', JSON.stringify(businessData, null, 2));

    if (businessResponse.ok) {
      console.log('\n🎉 SUCCESS: Authentication fix is working!');
      console.log('✅ User can now access business data without 401 errors');
    } else {
      console.log('\n❌ FAILED: Business API still returning errors');
      console.log('🔍 Response:', businessData);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testAuthenticationFlow();
