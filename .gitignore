node_modules
.env
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local
dist
.vscode
.DS_Store
package-lock.json
npm-debug.log
.next/

# Supabase
.branches
.temp

# dotenvx
.env.keys

local.env

# IDE and Editor Configuration Files with Secrets
.cursor/
.claude/
.vscode/settings.json
.idea/

# MCP Server Configuration (contains API keys/tokens)
**/mcp.json
**/claude_desktop_config.json

# Docker environment files
.env.docker
